import { HttpService } from '@nestjs/axios';
import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';
import { of } from 'rxjs';
import { Readable } from 'stream';

import { UploadFileModel } from '~/modules/document-file/upload-file.model';
import { LocationService } from '~/modules/location/location.service';
import { LOCATION_MESSAGE_KEYS } from '~/shared/message-keys/location.message-key';
import { LOCATION_FILE_MESSAGE_KEYS } from '~/shared/message-keys/location-file.message-key';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import {
  initMockLocationFile,
  mockLocationFileData,
} from '~/test/mocks/locationfile.mock';
import {
  initMockUploadFile,
  mockUploadFileData,
} from '~/test/mocks/uploadfile.mock';

import {
  LocationFileCheckExistedParamsDto,
  LocationFileCreateDto,
} from '../dtos/location-file.dto';
import { LocationFileModel } from '../location-file.model';
import { LocationFileService } from '../location-file.service';
import { locationFileTest } from './location-file.dto.test';

describe('LocationFileService', () => {
  let service: LocationFileService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        LocationFileService,
        ...testInjectProviders([
          LocationFileModel,
          UploadFileModel,
          HttpService,
        ]),
        {
          provide: LocationService,
          useValue: {
            findOne: jest.fn().mockImplementation((id: string | ObjectId) => {
              if (id.toString() === mockLocationData._id.toString())
                return Promise.resolve(mockLocationData);
              return Promise.resolve(null);
            }),
          },
        },
      ],
    }).compile();

    service = module.get(LocationFileService);

    // Init data
    await Promise.all([
      initMockLocation(),
      initMockLocationFile(),
      initMockUploadFile(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getUploadFilesOfLocation', () => {
    it('should throw error if location not found', async () => {
      await expect(
        service.getUploadFilesOfLocation({ id: new ObjectId().toString() }),
      ).rejects.toThrow(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should call fn with payload and return list data', async () => {
      const result = await service.getUploadFilesOfLocation({
        id: mockLocationData._id.toString(),
      });
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(
        locationFileTest.getUploadFilesOfLocationSchema,
      );
    });

    it('should return an empty array if data not found', async () => {
      const result = await service.getUploadFilesOfLocation({
        id: mockLocationData._id.toString(),
        _id: new ObjectId().toString(),
      } as any);
      expect(result.docs).toEqual([]);
    });
  });

  describe('uploadFileToLocation', () => {
    const payload: LocationFileCreateDto = {
      description: 'Test file upload',
      location: mockLocationData._id,
      fileId: mockUploadFileData._id,
    };

    it('should throw error if location not found', async () => {
      await expect(
        service.uploadFileToLocation({
          ...payload,
          location: new ObjectId(),
        }),
      ).rejects.toThrow(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw error if upload file not found', async () => {
      await expect(
        service.uploadFileToLocation({
          ...payload,
          fileId: new ObjectId(),
        }),
      ).rejects.toThrow('File not found');
    });

    it('should successfully upload a file to a location', async () => {
      const result = await service.uploadFileToLocation(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(locationFileTest.modelSchema);
    });
  });

  describe('deleteLocationFile', () => {
    it('should throw error if location file not found', async () => {
      await expect(
        service.deleteLocationFile(new ObjectId().toString(), {}),
      ).rejects.toThrow(LOCATION_FILE_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should call fn with payload and delete data', async () => {
      jest
        .spyOn(service['httpService'], 'delete')
        .mockReturnValue(of({} as any));

      const result = await service.deleteLocationFile(
        mockLocationFileData._id.toString(),
        {},
      );
      expect(result).toBeDefined();
      expect(result).toHaveProperty('acknowledged', true);
    });
  });

  describe('checkExistedLocationFile', () => {
    const payload: LocationFileCheckExistedParamsDto = {
      id: mockLocationData._id.toString(),
      fileName: mockUploadFileData.originalFilename,
    };

    it('should return true if location file exists', async () => {
      const result = await service.checkExistedLocationFile(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('isExisted', true);
      expect(result).toMatchSchema(
        locationFileTest.checkExistedLocationFileSchema,
      );
    });

    it('should return false if location file does not exist', async () => {
      const result = await service.checkExistedLocationFile({
        id: new ObjectId().toString(),
        fileName: 'nonexistent-file.pdf',
      });
      expect(result).toBeDefined();
      expect(result).toHaveProperty('isExisted', false);
      expect(result).toMatchSchema(
        locationFileTest.checkExistedLocationFileSchema,
      );
    });
  });

  describe('downloadAllFilesOfLocation', () => {
    it('should throw error if location not found', async () => {
      await expect(
        service.downloadAllFilesOfLocation(new ObjectId().toString(), {}),
      ).rejects.toThrow(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw error if no files or uploadFile is missing', async () => {
      jest.spyOn(service['locationFileModel'], 'find').mockReturnValue({
        populate: jest.fn().mockReturnThis(),
        lean: jest.fn().mockResolvedValue([]),
      } as any);

      await expect(
        service.downloadAllFilesOfLocation(mockLocationData._id.toString(), {}),
      ).rejects.toThrow('No files or invalid file');
    });

    it('should return zip file info when download and upload succeed', async () => {
      const mockStream = new Readable();
      mockStream._read = () => {};

      jest.spyOn(service['locationService'], 'findOne').mockResolvedValue({
        ...mockLocationData,
        address: {
          city: 'Gein 63',
          street: 'Gein 63',
          number: 10,
        },
      });
      jest
        .spyOn(service['httpService'], 'get')
        .mockReturnValue(of({ data: mockStream } as any));
      jest.spyOn(service as any, 'uploadFileStream').mockResolvedValue({
        data: {
          publicUrl: 'http://example.com/location.zip',
        },
      });

      const result = await service.downloadAllFilesOfLocation(
        mockLocationData._id.toString(),
        {},
      );
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        locationFileTest.downloadAllFilesOfLocationSchema,
      );
    });

    it('should throw error if uploadFileStream fails', async () => {
      jest.spyOn(service['locationService'], 'findOne').mockResolvedValue({
        ...mockLocationData,
        address: {
          city: 'Gein 63',
          street: 'Gein 63',
          number: 10,
        },
      });

      const mockStream = new Readable();
      mockStream._read = () => {};

      jest
        .spyOn(service['httpService'], 'get')
        .mockReturnValue(of({ data: mockStream } as any));
      jest
        .spyOn(service as any, 'uploadFileStream')
        .mockRejectedValue(new Error());

      await expect(
        service.downloadAllFilesOfLocation(mockLocationData._id.toString(), {}),
      ).rejects.toThrow();
    });
  });
});
