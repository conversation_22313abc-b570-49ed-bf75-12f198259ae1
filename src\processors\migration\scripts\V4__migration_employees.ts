import _ from 'lodash';
import { Collection } from 'mongodb';
import mongoose, { mongo, Types } from 'mongoose';
import * as path from 'path';

import { sumDecimal } from '~/utils';

import migration, {
  migrationDataFromCoreToGeneral,
} from '../helpers/merge-data';
import MyWritable from '../helpers/writable';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldEmployee {
  _id: string;
  blocked: boolean;
  confirmed: boolean;
  gender: 'Female' | 'Male' | 'Other';
  language: 'EN' | 'NL';
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber1: string;
  phoneNumber2: string;
  username: string;
  password: string;
  createdDate: Date;
  updatedDate: Date;
  evenWeeks: string[];
  oddWeeks: string[];
  position: number;
  changedPasswordDate: Date;
  roles: any[];
  team?: {
    _id: Types.ObjectId;
  };
}

const employeePipeLineAggregate = (skip: number, limit: number) => {
  return [
    {
      $lookup: {
        from: 'users-permissions_role',
        localField: 'roles',
        foreignField: '_id',
        as: 'roles',
      },
    },
    {
      $lookup: {
        from: 'team',
        localField: 'team',
        foreignField: '_id',
        as: 'team',
      },
    },
    {
      $unwind: {
        path: '$team',
        preserveNullAndEmptyArrays: true,
      },
    },
    { $skip: skip },
    {
      $limit: limit,
    },
  ];
};

const tranformDataFunc = async ({
  data,
  context,
}: {
  data: OldEmployee[];
  context: any;
}) => {
  const sourceCursor = await context
    .destinationClient!.db()
    .collection('tenants')
    .find({});

  const writable = new MyWritable({ objectMode: true });
  await new Promise((resolve, reject) => {
    sourceCursor
      .stream()
      .pipe(writable)
      .on('finish', resolve)
      .on('error', reject);
  });
  const tenant = writable.data[0];
  const result = await Promise.all(
    data.map(async (item) => {
      const tranformed = {
        _id: item._id,
        isActive: !item.blocked,
        isRoot: false,
        image: '',
        displayName: `${item.firstName} ${item.lastName}`,
        firstName: item.firstName || '',
        lastName: item.lastName || '',
        username: item.username,
        password: item.password,
        email: item.email,
        gender: item.gender.toLowerCase() as any,
        language: item.language.toLowerCase() as any,
        phone1: item.phoneNumber1 || '',
        phone2: item.phoneNumber2 || '',
        oddWeeks: item.oddWeeks as any,
        evenWeeks: item.evenWeeks as any,
        position: item.position || 0,
        lastChangePasswordAt: item.changedPasswordDate || new Date(),
        team: item.team?._id,
        tenant: tenant._id,
        createdAt: item.createdDate,
        updatedAt: item.updatedDate,
        isDeleted: false,
      };

      const roles = await context
        .destinationClient!.db()
        .collection('tenantroles')
        .find({
          key: {
            $in: item.roles.map((role: any) => role.type),
          },
        })
        .toArray();

      const roleDecimalSum = sumDecimal(roles.map((role: any) => role.decimal));

      return {
        ...tranformed,
        roles: roleDecimalSum,
      };
    }),
  );
  const filterDatas = result.filter((item) => item.team);
  const convertDatas = filterDatas.map((item) => {
    return {
      ...item,
      team: item?.team ? item?.team.toString() : '',
    };
  });
  const groupedDatas = _.groupBy(convertDatas, 'team');
  for (const teamId in groupedDatas) {
    const userData = groupedDatas[teamId];
    const userIds = userData.map((item) => item._id);
    await context
      .destinationClient!.db()
      .collection('teams')
      .findOneAndUpdate(
        { _id: new mongoose.Types.ObjectId(teamId) },
        {
          $push: {
            tenantUsers: {
              $each: userIds,
            },
          },
        },
      );
  }
  return result;
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('users-permissions_user');

  const pipeline = employeePipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'users-permissions_user',
      destinationCollectionName: 'tenantusers',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const pagingUserTable = ({
      nextId = new Types.ObjectId('000000000000000000000000'),
      limit,
      collection,
    }: {
      nextId?: Types.ObjectId;
      limit: number;
      collection: Collection<mongo.Document>;
    }) => {
      return collection
        .aggregate()
        .match({
          _id: { $gt: nextId },
        })
        .sort({ _id: 1 })
        .limit(limit);
    };

    const tranferDataUser = async ({ data }: { data: any[]; context: any }) => {
      return Promise.all(
        data.map(async (item) => {
          await context
            .generalClient!.db()
            .collection('tenantuserprofiles')
            .updateOne(
              { tenant: item.tenant, tenantUser: item._id },
              {
                $set: {
                  isActive: item.isActive,
                  isDeleted: item.isDeleted,
                  createdAt: item.createdAt,
                  updatedAt: item.updatedAt,
                  tenant: item.tenant,
                  tenantUser: item._id,
                },
              },
              {
                upsert: true,
              },
            );
          return {
            _id: item._id,
            username: item.username,
            tenant: item.tenant,
            isActive: item.isActive,
            isDeleted: item.isDeleted,
            createdAt: item.createdAt,
            updatedAt: item.updatedAt,
            lastChangePasswordAt: item.lastChangePasswordAt,
          };
        }),
      );
    };

    await migrationDataFromCoreToGeneral({
      context,
      coreSourceCollectionName: 'tenantusers',
      generalDestinationCollectionName: 'tenantusers',
      pagingFunc: pagingUserTable,
      tranformDataFunc: tranferDataUser,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
