import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { equipmentTest } from '~/modules/equipment/test/equipment.dto.test';
import { teamTest } from '~/modules/team/test/team.dto.test';
import { tenantUserTest } from '~/modules/tenant-user/test/tenant-user.dto.test';
import { TaskCategory, TaskType } from '~/shared/enums/task.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    title: z.string().optional(),
    description: z.string().optional(),
    destination: z.string().optional(),
    type: z.nativeEnum(TaskType),
    category: z.nativeEnum(TaskCategory),
    startDate: z.date().optional(),
    endDate: z.date().optional(),
    cars: z.array(z.instanceof(ObjectId)).optional(),
    devices: z.array(z.instanceof(ObjectId)).optional(),
    employees: z.array(z.instanceof(ObjectId)),
  })
  .extend(baseModelTestSchema);

const fineOneSchema = modelSchema.extend({
  cars: z
    .array(
      equipmentTest.modelSchema.pick({
        _id: true,
        description: true,
        type: true,
      }),
    )
    .nullish()
    .optional(),
  devices: z
    .array(
      equipmentTest.modelSchema.pick({
        _id: true,
        description: true,
        type: true,
      }),
    )
    .nullish()
    .optional(),
  employees: z.array(
    tenantUserTest.modelSchema
      .pick({
        _id: true,
        displayName: true,
        email: true,
      })
      .extend({
        team: teamTest.modelSchema.pick({
          _id: true,
          name: true,
          isActive: true,
          position: true,
        }),
      }),
  ),
});

const checkOverlapTaskSchema = z.object({
  startDate: z.string().datetime(),
  endDate: z.string().datetime(),
  employees: z
    .array(
      tenantUserTest.modelSchema
        .pick({
          oddWeeks: true,
          evenWeeks: true,
          displayName: true,
        })
        .passthrough(),
    )
    .nullish()
    .optional(),
  equipments: z
    .array(
      equipmentTest.modelSchema
        .pick({
          type: true,
          description: true,
          isActive: true,
        })
        .passthrough(),
    )
    .nullish()
    .optional(),
});

export const taskTest = {
  modelSchema,
  fineOneSchema,
  checkOverlapTaskSchema,
};
