import { Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(
      async (articleStorage: { _id: Types.ObjectId; [key: string]: any }) =>
        omitNull({
          _id: articleStorage._id,
          article: articleStorage.article,
          storage: articleStorage.storage,
          availableAmount: articleStorage.availableAmount,
          isDeleted: false,
          createdAt: articleStorage._id.getTimestamp(),
          updatedAt: articleStorage.updatedAt,
        }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'articlestorage',
      destinationCollectionName: 'articlestorage',
      tranformDataFunc: transformData,
      inventoryMode: true,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
