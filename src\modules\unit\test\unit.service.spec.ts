import { Test, TestingModule } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';

import { UpdateLocationUnitDto, UpdateUnitDto } from '../dtos/unit.dto';
import { UnitModel } from '../unit.model';
import { UnitService } from '../unit.service';
import { unitTest } from './unit.dto.test';

describe('UnitService', () => {
  let service: UnitService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [UnitService, ...testInjectModel([UnitModel])],
    }).compile();
    service = module.get(UnitService);

    // Init test data
    await initMockUnit();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('update', () => {
    it('should call updateOne with correct parameters', async () => {
      const payload: UpdateUnitDto = { _id: mockUnitData._id.toString() };
      const result = await service.update(payload);
      expect(result).toHaveProperty('acknowledged', true);
    });
  });

  describe('updateRootUnitOfLocation', () => {
    it('should update the root unit of a location', async () => {
      const location = mockUnitData.location.toString();
      const updateDto: UpdateUnitDto = { name: 'New name' };
      const result = await service.updateRootUnitOfLocation(
        location,
        updateDto,
      );
      expect(result).toHaveProperty('acknowledged', true);
    });
  });

  describe('findRootUnitOfLocation', () => {
    it('should find the root unit of a location', async () => {
      const location = mockUnitData.location.toString();
      const result = await service.findRootUnitOfLocation(location);
      expect(result).toMatchSchema(unitTest.findRootUnitOfLocationSchema);
    });

    it('should return null if no root unit is found', async () => {
      const location = new ObjectId().toString();
      const result = await service.findRootUnitOfLocation(location);
      expect(result).toBeNull();
    });
  });

  describe('bulkUpdate', () => {
    it('should create and update units in bulk', async () => {
      const location = mockUnitData.location.toString();
      const units: UpdateLocationUnitDto[] = [
        {
          ...mockUnitData,
          _id: mockUnitData._id.toString(),
          parent: mockUnitData.parent.toString(),
        },
      ];

      const result = await service.bulkUpdate(location, units);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('insertedIds');
      expect(result).toHaveProperty('upsertedIds');
    });
  });

  describe('findUnitsOfLocation', () => {
    it('should find all units of a location', async () => {
      const locationId = mockUnitData.location.toString();
      const result = await service.findUnitsOfLocation(locationId);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(unitTest.findUnitsOfLocationSchema);
    });
  });

  describe('findChildrenOfUnit', () => {
    it('should find all children of a unit', async () => {
      const unitId = mockUnitData._id.toString();
      const result = await service.findChildrenOfUnit(unitId);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(unitTest.findChildrenOfUnitSchema);
    });
  });
});
