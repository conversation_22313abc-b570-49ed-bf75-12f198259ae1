import { Test } from '@nestjs/testing';

import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockRegion } from '~/test/mocks/region.mock';

import { RegionModel } from '../region.model';
import { RegionService } from '../region.service';
import { regionTest } from './region.dto.test';

describe('RegionController', () => {
  let service: RegionService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [RegionService, ...testInjectModel([RegionModel])],
    }).compile();

    service = module.get(RegionService);

    // Init data
    await initMockRegion();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getList', () => {
    it('should call fn with payload and return list data', async () => {
      const result = await service.getList({});
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(regionTest.getListSchema);
    });

    it('should call fn and return empty list when data not exist', async () => {
      const result = await service.getList({ pageIndex: 999 });
      expect(result).toBeDefined();
      expect(result.docs.length).toEqual(0);
    });
  });
});
