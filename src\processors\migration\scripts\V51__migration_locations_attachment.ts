import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldDocument {
  _id: string;
  description: string;
  location: string;
  createdAt: Date;
  updatedAt: Date;
}

const PipeLineAggregate = (skip: number, limit: number) => {
  return [{ $skip: skip }, { $limit: limit }];
};

const transformDataFunc = ({
  data,
  context,
}: {
  data: OldDocument[];
  context: any;
}) => {
  return Promise.all(
    data.map(async (item) => {
      const transformedItem = {
        _id: item._id,
        description: item.description,
        location: item.location,
        uploadFile: null,
        createdAt: item.createdAt || new Date(),
        updatedAt: item.updatedAt || new Date(),
        isDeleted: false,
      };

      // search upload_file with documentId from old system
      const file = await context
        .sourceClient!.db()
        .collection('upload_file')
        .findOne({ 'related.ref': item._id });

      if (file?._id) {
        transformedItem.uploadFile = file._id;
      }

      return transformedItem;
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('attachments');

  const pipeline = PipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'attachments',
      destinationCollectionName: 'locationfiles',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: transformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
