import path from 'path';

import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const html = `<!doctype html>
<html xmlns='http://www.w3.org/1999/xhtml' xmlns:v='urn:schemas-microsoft-com:vml'
    xmlns:o='urn:schemas-microsoft-com:office:office'>

<head>
    <title></title>
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
    <meta name='viewport' content='width=device-width,initial-scale=1'>
    <style>
       td {
        padding-bottom: 5px;
        vertical-align: top;
      }
      .left {
        width: 150px;
      }
      .right {
        width: 450px;
      }
      .code-block {
        background-color: #f5f5f5;
        border-radius: 4px;
        padding-left: 5px;
        padding-right: 5px;
        border: 1px solid #e0e0e0;
      }
    </style>
</head>

<body style='word-spacing:normal; background-color: #ffffff;border: 1px solid #e0e0e0;border-radius: 6px;'>
    <div style='margin:0 auto;max-width:600px;padding-top: 10px'>
      <div style='padding-bottom: 5px'>The system failed to complete the <%= PROCESS_NAME %> process received from Lento.</div>
    </div>
    <div>
        <div style='margin:0 auto;max-width:600px;padding-top: 10px'>
            <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation' style='width:100%'>
                <tbody>
                    <tr>
                      <td class='left'>
                        <strong>Tenant:</strong> 
                      </td>
                      <td class='right'>
                        <%= TENANT %>
                      </td>
                    </tr>
                    <tr>
                      <td class='left'>
                        <strong>Error Code:</strong> 
                      </td>
                      <td class='right'>
                        <%= ERROR_CODE %>
                      </td>
                    </tr>
                    <tr>
                      <td class='left'>
                        <strong>Error Message:</strong> 
                      </td>
                      <td class='right'>
                        <%= ERROR_MESSAGE %>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <strong>Error Detail:</strong> 
                      </td>
                      <td class='right; code-block'>
                         <%= ERROR_DETAIL %>
                      </td>
                    </tr>
                    <tr>
                      <td class='left'>
                        <strong>Request Body:</strong> 
                      </td>
                      <td class='right; code-block'>
                         <%= REQUEST_BODY %>
                      </td>
                    </tr>
                    <tr>
                      <td class='left'>
                        <strong>Time:</strong> 
                      </td>
                      <td class='right'>
                        <%= TIME %>
                      </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div style='margin:0 auto;max-width:600px;padding-top: 20px'>
          <div>
            This is for your information only — no action is required in the system.
          </div>
          <div style='padding-top: 10px'>
            Best regards,
          </div>
          <div>
            HomEE System Notification
          </div>
        </div>

    </div>
    <!-- Footer -->
    
    <div style='height:20px;line-height:20px;'>&#8202;</div>
</body>

</html>`;

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const destinationCollectionName = 'emailtemplates';
    const destinationCollection = context
      .destinationClient!.db()
      .collection(destinationCollectionName)!;

    if (!(await destinationCollection.indexExists('name_1'))) {
      destinationCollection.createIndex({ name: 1 }, { unique: true });
    }

    const doc = {
      name: 'error_email_send_to_lento',
      subject: 'Error Notification: Lento Integration',
      html,
      bcc: [],
      cc: [],
      to: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await destinationCollection
      .findOneAndUpdate(
        { name: doc.name },
        { $set: doc },
        { upsert: true, returnDocument: 'after' }, // Use returnDocument: 'after' to get the updated document
      )
      .then(() => {
        console.log(
          `Migrated error email send to Lento with name=${doc.name} into collection ${destinationCollectionName}`,
        );
      })
      .catch((error) => {
        console.error(`Error upserting document with name=${doc.name}:`, error);
      });
    const after = new Date().getTime();
    console.log(
      `Migration script ${fileName} completed in ${after - before}ms`,
    );
  } catch (error) {
    console.error(`Error in migration script ${fileName}: ${error}`);
  }
};
export default up;
