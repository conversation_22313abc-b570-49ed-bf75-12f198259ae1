import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { CONTRACT_TYPE_MESSAGES } from '~/shared/messages/contract-type.message';
import { QueryParams } from '~/utils';

import { ContractTypeService } from './contract-type.service';

@Controller('contract-type')
export class ContractTypeController {
  constructor(private readonly contractTypeService: ContractTypeService) {}

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(QueryParamsDto))
  @MessagePattern({ cmd: CONTRACT_TYPE_MESSAGES.GET_ALL_CONTRACT_TYPES })
  public async findAll(@Payload() payload: QueryParams) {
    return this.contractTypeService.findAll(payload);
  }
}
