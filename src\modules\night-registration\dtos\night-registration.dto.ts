import dayjs from 'dayjs';
import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { CompanyInformationSchemas } from '~/modules/tenant/tenant.dto';
import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import {
  NightRegistrationWarningLevel,
  NRMaximumStayDurationLevel,
} from '~/shared/enums/night-registration.enum';

const ReservationQueryParamsSchema = z.strictObject({
  location: z.string().refine((v) => isValidObjectId(v)),
  contact: z
    .string()
    .refine((v) => isValidObjectId(v))
    .optional(),
  checkOut: z.enum(['true', 'false']).default('false'),
  maximumStayDurationLevel: z.nativeEnum(NRMaximumStayDurationLevel).optional(),
  filter: z.string().optional(),
});

const ResidentCreateAndUpdateSchema = z.strictObject({
  _id: z
    .string()
    .refine((v) => isValidObjectId(v))
    .optional(),
  firstName: z.string().max(256),
  lastName: z.string().max(256),
  dateOfBirth: z.dateString(),
  gender: z.enum(['Male', 'Female']),
  clientId: z.string().nullish(),
  phoneNumber: z.string().nullish(),
  email: z.string().email().nullish(),
  nationality: z.string().refine((v) => isValidObjectId(v)),
  isBRP: z.boolean().optional(),
});

const VirtualReservationCreateBodySchema = z.strictObject({
  unit: z.string().refine((v) => isValidObjectId(v)),
  bed: z.number().positive(),
  isVirtual: z.boolean().default(true),
});

const ReservationCreateBodySchema = z
  .strictObject({
    resident: ResidentCreateAndUpdateSchema,
    arrivalDate: z.dateString(),
    departureDate: z.dateString().optional(),
    contact: z.string().refine((v) => isValidObjectId(v)),
    remarks: z.string().nullish(),
    unit: z.string().refine((v) => isValidObjectId(v)),
    bed: z.number().positive(),
  })
  .merge(CompanyInformationSchemas)
  .superRefine((v, ctx) => {
    if (!v.departureDate) {
      return;
    }
    if (
      !dayjs(v.arrivalDate)
        .utc()
        .startOf('day')
        .isSameOrBefore(dayjs(v.departureDate).utc().startOf('day'))
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Arrival date must be before departure date',
      });
    }
  });

const ReservationUpdateBodySchema = z
  .strictObject({
    _id: z.string().refine((v) => isValidObjectId(v)),
    resident: ResidentCreateAndUpdateSchema,
    arrivalDate: z.dateString(),
    departureDate: z.dateString().nullish(),
    contact: z.string().refine((v) => isValidObjectId(v)),
    remarks: z.string().nullish(),
    unit: z.string().refine((v) => isValidObjectId(v)),
    bed: z.number().positive(),
  })
  .merge(CompanyInformationSchemas)
  .superRefine((v, ctx) => {
    if (!v.departureDate) {
      return;
    }
    if (
      !dayjs(v.arrivalDate)
        .utc()
        .startOf('day')
        .isSameOrBefore(dayjs(v.departureDate).utc().startOf('day'))
    ) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Arrival date must be before departure date',
      });
    }
  });

const WarningCreateBodySchema = z
  .strictObject({
    resident: z.string().refine((v) => isValidObjectId(v)),
    reservation: z.string().refine((v) => isValidObjectId(v)),
    // dayToLeave: z.number().positive().min(1).max(14).optional(),
    // change dayToLeave to Date with optional and min date is today
    //  and max date is 14 days from today: hide maxDate for now
    dayToLeave: z
      .dateString()
      .refine(
        (v) => {
          const today = dayjs().utc().startOf('day');
          // hide maxDate for now
          // const maxDate = today.add(14, 'day').utc().startOf('day');
          const date = dayjs(v).utc().startOf('day');
          return date.isSameOrAfter(today);
          // hide maxDate for now
          // return date.isSameOrAfter(today) && date.isSameOrBefore(maxDate);
        },
        {
          message: 'Day to leave must be today or in the future',
        },
      )
      .optional(),
    level: z.enum([
      NightRegistrationWarningLevel.VERBAL,
      NightRegistrationWarningLevel.OFFICIAL,
      NightRegistrationWarningLevel.REMOVAL,
    ]),
    warningDate: z.dateString(),
    warningTime: z
      .string()
      .optional()
      .refine((v) => {
        const regex = /^(0\d|1\d|2[0-3]):([0-5]\d)$/;
        return !v || regex.test(v);
      }, 'Warning time must be in HH:mm format'),
    description: z.string().max(2048).optional(),
    warningCategory: z.string().refine((v) => isValidObjectId(v)),
    images: z.array(z.string()).max(8).default([]),
    emailTemplateOption: z.number().min(1).max(2).optional(),
  })
  .merge(CompanyInformationSchemas)
  .passthrough();

const WarningQueryParamsSchema = z.strictObject({
  resident: z.string().refine((v) => isValidObjectId(v)),
});

const GetNightRegistrationReportQuerySchema = z
  .strictObject({
    location: z.string().refine((v) => isValidObjectId(v)),
    dateFrom: z.dateString(),
    dateTo: z.dateString(),
  })
  .merge(QueryParamsSchema.pick({ sortBy: true, sortDir: true }));

const ResidentHasExceededMaximumLengthOfStaySchema = z.strictObject({
  fakeLocationIds: z
    .array(z.string().refine((v) => isValidObjectId(v)))
    .optional(),
  fakeCurrentDate: z.dateString().optional(),
});

//#region Dto

export class ReservationQueryParamsDto extends createZodDto(
  ReservationQueryParamsSchema,
) {}

export class ReservationCreateBodyDto extends createZodDto(
  ReservationCreateBodySchema,
) {}

export class VirtualReservationCreateBodyDto extends createZodDto(
  VirtualReservationCreateBodySchema,
) {}

export class ReservationUpdateBodyDto extends createZodDto(
  ReservationUpdateBodySchema,
) {}

export class WarningCreateBodyDto extends createZodDto(
  WarningCreateBodySchema,
) {}

export class WarningQueryParamsDto extends createZodDto(
  WarningQueryParamsSchema,
) {}

export class GetNightRegistrationReportQueryDto extends createZodDto(
  GetNightRegistrationReportQuerySchema,
) {}

export class ResidentHasExceededMaximumLengthOfStayDto extends createZodDto(
  ResidentHasExceededMaximumLengthOfStaySchema,
) {}
//#endregion
