import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection
    .aggregate()
    .match({
      _id: { $gt: nextId },
    })
    .lookup({
      from: 'CreditorContact',
      localField: 'supplier',
      foreignField: '_id',
      as: 'supplier',
    })
    .unwind({
      path: '$supplier',
    })
    .sort({ _id: 1 })
    .limit(limit);
};

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(async (order: { _id: Types.ObjectId; [key: string]: any }) =>
      omitNull({
        _id: order._id,
        identifier: order.identifier,
        description: order.description,
        note: order.note,
        completed: order.completed,
        deliveryDate: order.deliveryDate,
        orderDetails: order.orderDetails,
        isDeleted: order.deleted ?? false,
        storage: order.storage,
        supplier: order.supplier?.grouping ?? order.supplier?.person, // Reference to contacts
        createdAt: order.createdAt,
        updatedAt: order.updatedAt,
        createdBy: order.createdBy, // Reference to tenantusers
        updatedBy: order.updatedBy, // Reference to tenantusers
      }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'order',
      destinationCollectionName: 'order',
      tranformDataFunc: transformData,
      pagingFunc,
      inventoryMode: true,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
