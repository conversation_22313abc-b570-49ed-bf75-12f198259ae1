import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { uploadFileTest } from '~/modules/document-file/test/upload-file.dto.test';
import { locationTest } from '~/modules/location/test/location.dto.test';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    description: z.string(),
    location: z.instanceof(ObjectId).optional(),
    uploadFile: z.instanceof(ObjectId),
  })
  .extend(baseModelTestSchema);

const getUploadFilesOfLocationSchema = z.array(
  modelSchema.extend({
    location: locationTest.modelSchema.pick({
      _id: true,
      fullAddress: true,
    }),
    uploadFile: uploadFileTest.modelSchema.pick({
      _id: true,
      originalFilename: true,
      extension: true,
      size: true,
      publicUrl: true,
      provider: true,
    }),
  }),
);

const checkExistedLocationFileSchema = z.object({
  id: z.instanceof(ObjectId).nullable().optional(),
  isExisted: z.boolean(),
});

const downloadAllFilesOfLocationSchema = z.object({
  fileName: z.string(),
  publicUrl: z.string(),
});

export const locationFileTest = {
  modelSchema,
  getUploadFilesOfLocationSchema,
  checkExistedLocationFileSchema,
  downloadAllFilesOfLocationSchema,
};
