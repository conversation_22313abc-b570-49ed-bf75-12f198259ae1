import { Injectable } from '@nestjs/common';
import { Model } from 'mongoose';

import { InjectModel } from '~/transformers/model.transformer';

import { UpdateLocationUnitDto, UpdateUnitDto } from './dtos/unit.dto';
import { UnitModel } from './unit.model';

@Injectable()
export class UnitService {
  constructor(
    @InjectModel(UnitModel)
    private readonly unitModel: Model<UnitModel>,
  ) {}

  async findAll() {}

  async update(data: UpdateUnitDto) {
    return this.unitModel.updateOne({ _id: data._id }, data);
  }

  async updateRootUnitOfLocation(location: string, data: UpdateUnitDto) {
    return this.unitModel.updateOne(
      {
        location,
        isRoot: true,
      },
      data,
    );
  }

  async findRootUnitOfLocation(location: string) {
    return this.unitModel.findOne({ location, isRoot: true }).lean();
  }

  async bulkUpdate(location: string, units: UpdateLocationUnitDto[] = []) {
    const willCreate = units.filter((unit) => !unit._id);
    const willUpdate = units.filter((unit) => unit._id);

    // Create new units
    const createdResult = await this.unitModel.bulkWrite(
      willCreate.map((unit) => ({
        insertOne: {
          document: {
            ...unit,
            location,
          },
        },
      })),
    );

    // Update existing units
    const updatedResult = await this.unitModel.bulkWrite(
      willUpdate.map((unit) => ({
        updateOne: {
          filter: { _id: unit._id },
          update: {
            ...unit,
            location,
          },
          upsert: true,
        },
      })),
    );

    return {
      insertedIds: createdResult.insertedIds,
      upsertedIds: updatedResult.upsertedIds,
    };
  }

  async findUnitsOfLocation(locationId: string) {
    return this.unitModel
      .find({ location: locationId }, { sort: { position: 1 } })
      .select('_id name maxOccupants maxArea position parent isActive isRoot');
  }

  async findChildrenOfUnit(unitId: string) {
    return this.unitModel
      .find({ parent: unitId })
      .select('_id name maxOccupants maxArea position parent isActive isRoot');
  }
}
