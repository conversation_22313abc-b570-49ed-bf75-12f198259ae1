import { ContactRole } from '~/shared/enums/contact.enum';
import { ContractType } from '~/shared/enums/contract.enum';

// TODO: Might need to move this to config OR database
export const INTERNAL_CONTACT_IDENTIFIERS: readonly string[] = [
  'CON-11', // HomEE
  '10006', // KaFra
  'DORG-00001', // Logejo
];

export const AggregateGetListContactWithLocation = (
  location: any,
  contactRole: any,
) => {
  let matchType: any;

  if (contactRole == ContactRole.DEBTOR) {
    matchType = {
      $match: {
        $and: [
          { type: { $in: [ContractType.RENTING, ContractType.SERVICE] } },
          {
            $or: [
              { location: location._id },
              { costCenter: location.costCenter },
            ],
          },
          { isActive: true, isDeleted: false },
        ],
      },
    };
  } else if (contactRole == ContactRole.SUPPLIER) {
    matchType = {
      $match: {
        location: location._id,
        type: ContractType.CREDITOR,
        isActive: true,
        isDeleted: false,
      },
    };
  }

  const query: any = [
    {
      $lookup: {
        from: 'contacts',
        localField: 'contact',
        foreignField: '_id',
        pipeline: [{ $match: { contactRole } }],
        as: 'contact',
      },
    },
    { $unwind: { path: '$contact', preserveNullAndEmptyArrays: true } },
    { $replaceRoot: { newRoot: '$contact' } },
    {
      $group: {
        _id: '$_id',
        isActive: { $first: '$isActive' },
        name: { $first: '$name' },
        displayName: { $first: '$displayName' },
        email: { $first: '$email' },
        phone1: { $first: '$phone1' },
        supplierType: { $first: '$supplierType' },
        supplierCategory: { $first: '$supplierCategory' },
        contactType: { $first: '$contactType' },
      },
    },
  ];

  return [matchType, ...query];
};
