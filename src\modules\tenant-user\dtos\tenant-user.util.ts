export const GenericDisplayMapping: {
  field: string;
  isShown: boolean;
  mutable: boolean;
}[] = [
  {
    field: 'fIdentifier',
    isShown: true,
    mutable: false,
  },
  {
    field: 'identifier',
    isShown: true,
    mutable: false,
  },
  {
    field: 'title',
    isShown: true,
    mutable: true,
  },
  {
    field: 'plannedDate',
    isShown: true,
    mutable: true,
  },
  {
    field: 'location.fullAddress',
    isShown: true,
    mutable: true,
  },
  {
    field: 'units',
    isShown: true,
    mutable: true,
  },
  {
    field: 'status',
    isShown: true,
    mutable: true,
  },
  {
    field: 'type',
    isShown: true,
    mutable: true,
  },
  {
    field: 'assignee',
    isShown: true,
    mutable: true,
  },
  {
    field: 'location.team.name',
    isShown: true,
    mutable: true,
  },
] as const;
