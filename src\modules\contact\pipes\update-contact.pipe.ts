import { ArgumentMetadata, Injectable } from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';

import { ContactRole, ContactType } from '~/shared/enums/contact.enum';

import { ContactTypeDto } from '../dtos/contact.dto';
import {
  UpdateDebtorOrganizationDto,
  UpdateDebtorPersonDto,
} from '../dtos/debtor-contact.dto';
import { UpdatePersonContactDto } from '../dtos/person-contact.dto';
import {
  UpdateSupplierOrganizationDto,
  UpdateSupplierPersonDto,
} from '../dtos/supplier-contact.dto';

@Injectable()
export class UpdateContactZodValidationPipe extends ZodValidationPipe {
  async transform(value: any, metadata: ArgumentMetadata) {
    // Select the appropriate schema based on the type in the request body
    let schema;

    switch (value.contactRole) {
      case ContactRole.PERSON:
        schema = UpdatePersonContactDto;
        break;
      case ContactRole.DEBTOR:
        if (!value.contactType) {
          schema = ContactTypeDto;
        } else {
          schema =
            value.contactType === ContactType.ORGANIZATION
              ? UpdateDebtorOrganizationDto
              : UpdateDebtorPersonDto;
        }
        break;
      case ContactRole.SUPPLIER:
        if (!value.contactType) {
          schema = ContactTypeDto;
        } else {
          schema =
            value.contactType === ContactType.ORGANIZATION
              ? UpdateSupplierOrganizationDto
              : UpdateSupplierPersonDto;
        }
        break;
      default:
        throw new Error('Invalid type');
    }

    // Call the parent class's transform() method with the selected schema
    return super.transform(value, { ...metadata, metatype: schema });
  }
}
