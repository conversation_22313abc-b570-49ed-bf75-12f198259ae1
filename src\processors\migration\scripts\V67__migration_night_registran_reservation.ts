import dayjs from 'dayjs';
import _ from 'lodash';
import { ObjectId } from 'mongoose';
import * as path from 'path';

import migration from '../helpers/merge-data';
import MyWritable from '../helpers/writable';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldAgency {
  grouping: ObjectId;
  person: ObjectId;
}

interface OldReservation {
  _id: string;
  isVirtual: boolean;
  remark: string;
  dateFrom: Date;
  dateTo: Date;
  bedNo: number;
  agency: OldAgency;
  resident: ObjectId;
  unit: ObjectId;
  inspection: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const ReservationPipeLineAggregate = (skip: number, limit: number) => {
  // get all resident
  return [
    {
      $lookup: {
        from: 'debtorcontact',
        localField: 'agency',
        foreignField: '_id',
        as: 'agency',
      },
    },
    {
      $set: {
        agency: {
          $arrayElemAt: ['$agency', 0],
        },
      },
    },
    { $skip: skip },
    { $limit: limit },
  ];
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldReservation[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (item) => {
      const jobs: any[] = [];
      if (item.inspection) {
        const currentDate = dayjs().utc();
        const sourceCursor = await context
          .sourceClient!.db()
          .collection('night-registration_reservation')
          .aggregate([
            {
              $match: {
                unit: item.unit,
                isVirtual: false,
                $or: [
                  {
                    dateFrom: { $lte: currentDate.toDate() },
                    dateTo: { $gt: currentDate.toDate() },
                  },
                  {
                    dateFrom: { $lte: currentDate.toDate() },
                    dateTo: null,
                  },
                ],
              },
            },
            {
              $sort: { dateFrom: -1, createdAt: -1 },
            },
            {
              $limit: 99999,
            },
          ]);
        const writable = new MyWritable({ objectMode: true });
        await new Promise((resolve, reject) => {
          sourceCursor
            .stream()
            .pipe(writable)
            .on('finish', resolve)
            .on('error', reject);
        });
        const lastCheckIns = writable.data;
        const mappedLastCheckIns = _.uniqWith(
          lastCheckIns,
          (arrVal: any, othVal: any) => {
            return _.isEqual(
              {
                unit: othVal.unit,
                bedNo: othVal.bedNo,
              },
              {
                unit: arrVal.unit,
                bedNo: arrVal.bedNo,
              },
            );
          },
        );
        const lastCheckInIds = mappedLastCheckIns.map(
          (mappedLastCheckIn: any) => mappedLastCheckIn._id.toString(),
        );
        if (lastCheckInIds.includes(item._id.toString())) {
          jobs.push({
            job: item.inspection,
            type: 'checkIn',
          });
        } else {
          jobs.push({
            job: item.inspection,
            type: 'checkOut',
          });
        }
      }
      return {
        _id: item._id,
        resident: item.resident,
        contact: item.agency?.grouping ?? item.agency?.person,
        unit: item.unit,
        remark: item.remark,
        bed: item.bedNo,
        arrivalDate: item.dateFrom,
        departureDate: item.dateTo,
        isVirtual: item.isVirtual,
        job: jobs,
        isDeleted: false,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      };
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('night-registration_reservation');

  const pipeline = ReservationPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'night-registration_reservation',
      destinationCollectionName: 'nightregistrationreservations',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
