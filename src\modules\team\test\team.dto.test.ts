import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isActive: z.boolean(),
    position: z.number(),
    name: z.string(),
    description: z.string(),
    isEquipment: z.boolean(),
    tenantUsers: z.array(z.instanceof(ObjectId)),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(
  modelSchema.omit({
    description: true,
    tenantUsers: true,
  }),
);

const findOneSchema = modelSchema.omit({
  tenantUsers: true,
  isEquipment: true,
  description: true,
});

const updateSchema = findOneSchema;

export const teamTest = {
  modelSchema,
  findAllSchema,
  findOneSchema,
  updateSchema,
};
