import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { countryTest } from '~/modules/country/test/country.dto.test';
import { regionTest } from '~/modules/region/test/region.dto.test';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    region: z.instanceof(ObjectId),
    country: z.instanceof(ObjectId),
    city: z.string(),
    street: z.string(),
    number: z.string(),
    suffix: z.string(),
    postalCode: z.string(),
    contact: z.instanceof(ObjectId).optional(),
    location: z.instanceof(ObjectId).optional(),
  })
  .extend(baseModelTestSchema);

const validationAddressCountryRegionSchema = z.object({
  country: countryTest.modelSchema,
  region: regionTest.modelSchema,
});

export const addressTest = {
  modelSchema,
  validationAddressCountryRegionSchema,
};
