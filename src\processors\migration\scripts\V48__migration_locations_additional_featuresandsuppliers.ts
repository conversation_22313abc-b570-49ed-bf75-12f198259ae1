import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const defaultType = 'feature_and_supplier';

interface OldLocationAdditional {
  _id: string;
  name: string;
  description: string;
  position: number;
  group_name: string;
  employee: string;
  grouping_id: string;
  contact: string;
  location: string;
  rentingContract: string;
  type: 'owner' | 'eeac' | 'customer' | 'none';
  code: string;
  createdAt: Date;
  updatedAt: Date;
}

const LocationAdditionalPipeLineAggregate = (skip: number, limit: number) => {
  return [{ $skip: skip }, { $limit: limit }];
};

const transformDataFunc = ({
  data,
  context,
}: {
  data: OldLocationAdditional[];
  context: any;
}) => {
  return Promise.all(
    data.map(async (item) => {
      const { group_name, grouping_id, contact } = item;

      const transformedItem = {
        _id: item._id,
        type: defaultType,
        position: item.position,
        name: item.name,
        description: item.description,
        groupType: item.type,
        contact: grouping_id ? grouping_id : contact,
        location: item.location,
        contract: item.rentingContract,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        isDeleted: false,
      };

      if (group_name) {
        const foundGroupName = await context
          .destinationClient!.db()
          .collection('locationadditionalgroupnames')
          .findOne({ key: group_name, type: defaultType });

        if (foundGroupName) {
          return {
            ...transformedItem,
            groupName: foundGroupName._id,
          };
        }
      } else {
        return transformedItem;
      }
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('location_additional_featuresandsuppliers');

  const pipeline = LocationAdditionalPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'location_additional_featuresandsuppliers',
      destinationCollectionName: 'locationadditionals',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: transformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
