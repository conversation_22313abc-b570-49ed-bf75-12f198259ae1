import { ConfigService } from '@nestjs/config';
import { Test } from '@nestjs/testing';
import dayjs from 'dayjs';

import { ContractModel } from '~/modules/contract/contract.model';
import { InvoiceModel } from '~/modules/invoice/invoice.model';
import { JobModel } from '~/modules/job/job.model';
import { IdentifierType } from '~/shared/enums/identifier.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockContract } from '~/test/mocks/contract.mock';
import { initMockInvoice } from '~/test/mocks/invoice.mock';
import { initMockJob } from '~/test/mocks/job.mock';
import { getModelToken } from '~/transformers/model.transformer';

import { IdentifierModel } from '../identifier.model';
import { IdentifierService } from '../identifier.service';

describe('IdentifierService', () => {
  let service: IdentifierService;
  let configService: ConfigService;
  let identifierModel: MongooseModel<IdentifierModel>;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        IdentifierService,
        ...testInjectProviders([
          JobModel,
          IdentifierModel,
          ContractModel,
          InvoiceModel,
        ]),
        ConfigService,
      ],
    }).compile();

    service = module.get(IdentifierService);
    configService = module.get(ConfigService);
    identifierModel = module.get(getModelToken(IdentifierModel.name));

    // Init data
    await Promise.all([
      initMockContract({ identifier: '20200000' }),
      initMockJob(),
      initMockInvoice({ identifier: '202000000001' }),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('generateIdentifierForJob', () => {
    const today = dayjs().format('YYYYMMDD');

    it('should return YYYYMMDD0001 when no previous identifier exists', async () => {
      const result = await service.generateIdentifier(IdentifierType.JOB);
      expect(result).toBeDefined();
      expect(result).toBe(`${today}0001`);
    });

    it('should increment identifier if previous one is same day', async () => {
      const result = await service.generateIdentifier(IdentifierType.JOB);
      expect(result).toBeDefined();
      expect(result).toBe(`${today}0002`);
    });

    it('should reset counter if date has changed', async () => {
      // Prepare the mock data
      const yesterday = dayjs().subtract(1, 'day').format('YYYYMMDD');
      await identifierModel.deleteMany({});
      await identifierModel.create({
        type: IdentifierType.JOB,
        maxIdentifier: `${yesterday}9999`,
        year: Number(today.slice(0, 4)),
        isUsed: true,
      });

      const result = await service.generateIdentifier(IdentifierType.JOB);
      expect(result).toBeDefined();
      expect(result).toBe(`${today}0001`);
    });

    it('should skip identifier if already used once', async () => {
      const result = await service.generateIdentifier(IdentifierType.JOB);
      expect(result).toBeDefined();
      expect(result).toBe(`${today}0002`);
    });

    it('should skip multiple used identifiers', async () => {
      await identifierModel.insertMany(
        ['0003', '0004', '0005'].map((suffix) => ({
          type: IdentifierType.JOB,
          maxIdentifier: `${today}${suffix}`,
          year: Number(today.slice(0, 4)),
          isUsed: true,
        })),
      );

      const result = await service.generateIdentifier(IdentifierType.JOB);
      expect(result).toBeDefined();
      expect(result).toBe(`${today}0006`);
    });
  });

  describe('generateIdentifierForScheduler', () => {
    it('should return 00001 when no previous identifier exists', async () => {
      const result = await service.generateIdentifier(IdentifierType.SCHEDULER);
      expect(result).toBeDefined();
      expect(result).toBe('00001');
    });

    it('should increment identifier by 1 if previous one exists', async () => {
      // Prepare the mock data
      await initMockJob({ fIdentifier: '00001' });

      const result = await service.generateIdentifier(IdentifierType.SCHEDULER);
      expect(result).toBeDefined();
      expect(result).toBe('00002');
    });
  });

  describe('generateIdentifierForInvoice', () => {
    const invoiceIdentifierDigit = 123;
    const currentYear = new Date().getFullYear();

    it('should return current year with 1st invoice identifier when no previous identifier exists', async () => {
      jest.spyOn(configService, 'get').mockReturnValue(invoiceIdentifierDigit);

      const result = await service.generateIdentifier(IdentifierType.INVOICE);
      expect(result).toBeDefined();
      expect(result).toBe(`${currentYear}${invoiceIdentifierDigit}0000001`);
    });

    it('should return next invoice identifier when one exists', async () => {
      jest.spyOn(configService, 'get').mockReturnValue(invoiceIdentifierDigit);

      const result = await service.generateIdentifier(IdentifierType.INVOICE);
      expect(result).toBeDefined();
      expect(result).toBe(`${currentYear}${invoiceIdentifierDigit}0000002`);
    });

    it('should reset the identifier if year is different', async () => {
      jest.spyOn(configService, 'get').mockReturnValue(invoiceIdentifierDigit);

      const result = await service.generateIdentifier(IdentifierType.INVOICE);
      expect(result).toBeDefined();
      expect(result).toBe(`${currentYear}${invoiceIdentifierDigit}0000003`);
    });
  });

  describe('deleteIdentifier', () => {
    it('should call fn with payload and delete data', async () => {
      const result = await service.deleteIdentifier(
        IdentifierType.INVOICE,
        '20251230000003',
      );
      expect(result).toBeUndefined();
    });
  });
});
