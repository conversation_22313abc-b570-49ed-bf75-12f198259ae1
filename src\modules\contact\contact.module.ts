import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { SyncHistoryModule } from '~/modules/sync-history/sync-history.module';
import { LoggerModule } from '~/processors/logger/logger.module';
import { ThirdPartyConnectorModule } from '~/processors/third-party-connector/third-party-connector.module';

import { CountryModule } from '../country/country.module';
import { ContactController } from './contact.controller';
import { ContactService } from './contact.service';
import { ContactContext } from './strategies/contact-context';
import { ContactDebtorStrategy } from './strategies/contact-debtor.strategy';
import { ContactOrganizationStrategy } from './strategies/contact-organization.strategy';
import { ContactPersonStrategy } from './strategies/contact-person.strategy';
import { ContactSupplierStrategy } from './strategies/contact-supplier.strategy';

@Module({
  imports: [
    CountryModule,
    ThirdPartyConnectorModule,
    LoggerModule,
    SyncHistoryModule,
    ConfigModule,
  ],
  controllers: [ContactController],
  providers: [
    ContactService,
    ContactContext,
    ContactPersonStrategy,
    ContactOrganizationStrategy,
    ContactDebtorStrategy,
    ContactSupplierStrategy,
  ],
  exports: [ContactService],
})
export class ContactModule {}
