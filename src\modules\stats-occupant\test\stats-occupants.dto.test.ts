import { z } from 'nestjs-zod/z';

import { costCenterTest } from '~/modules/costcenter/test/costcenter.dto.test';
import { locationTest } from '~/modules/location/test/location.dto.test';
import { teamTest } from '~/modules/team/test/team.dto.test';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    location: z.string().optional(),
    isLocationActive: z.boolean(),
    isLocationService: z.boolean(),
    maxCount: z.number(),
    hiredCount: z.number(),
    emptyCount: z.number(),
    hiredUnits: z.array(z.string()).optional(),
    unHiredUnits: z.array(z.string()).optional(),
    creditorContracts: z.array(z.string()).optional(),
    debtorContracts: z.array(z.string()).optional(),
    locationInfo: locationTest.modelSchema.pick({
      _id: true,
      fullAddress: true,
    }),
    costCenterInfo: costCenterTest.modelSchema.pick({
      _id: true,
      name: true,
    }),
    teamInfo: teamTest.modelSchema.pick({
      _id: true,
      name: true,
    }),
    reportCreatedDate: z.date(),
  })
  .extend(baseModelTestSchema);

export const statsOccupantTest = {
  modelSchema,
};
