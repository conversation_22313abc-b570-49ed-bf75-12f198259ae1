import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { JobPointModel } from '~/modules/job-point/job-point.model';
import { jobPointTest } from '~/modules/job-point/test/job-point.dto.test';
import { JobPointStatusEnum } from '~/shared/enums/job.enum';

import { mockCostlineData } from './costline.mock';
import { mockJobData } from './job.mock';
import { mockUnitData } from './unit.mock';

const jobPointModel = getModelForClass(JobPointModel);
type jobPointType = z.infer<typeof jobPointTest.modelSchema>;

export const mockJobPointData = {
  _id: new ObjectId(),
  isDeleted: false,
  isGrouped: false,
  status: JobPointStatusEnum.GREEN,
  description: 'Opmerkingen ten aanzien van het GWE verbruik',
  position: 0,
  images: [
    'https://example.com/images/image_1.jpg',
    'https://example.com/images/image_2.jpg',
    'https://example.com/images/image_3.jpg',
  ],
  unit: mockUnitData._id,
  job: mockJobData._id,
  actions: [],
  costLines: [mockCostlineData._id],
  createdAt: new Date(),
  updatedAt: new Date(),
  notes: '',
};

export async function initMockJobPoint(doc?: Partial<jobPointType>) {
  const data = { ...mockJobPointData, ...doc };
  await jobPointModel.replaceOne({ _id: data._id }, data, { upsert: true });
}
