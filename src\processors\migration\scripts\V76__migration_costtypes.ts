import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

interface OldCostType {
  _id: Types.ObjectId;
  active: boolean;
  itemCode: string;
  name: string;
  isSynced: boolean;
  type: 'Job / Custom' | 'Contract';
  createdDate?: Date;
  updatedDate?: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection
    .find({
      $and: [
        { _id: { $gt: nextId } },
        { itemCode: { $exists: true } },
        { itemCode: { $ne: '' } },
        { itemCode: { $ne: null } },
      ],
    })
    .limit(limit)
    .sort({ _id: 1 });
};

const transformData = ({ data }: { data: OldCostType[] }) => {
  return Promise.all(
    data.map(async (costType: OldCostType) =>
      omitNull({
        _id: costType._id,
        itemCode: costType.itemCode,
        name: costType.name,
        type:
          costType.type === 'Job / Custom'
            ? 'job_or_custom'
            : costType.type.toLowerCase(),
        isActive: costType.active,
        isDeleted: !costType.active,
        isSynced: costType.isSynced,
        createdAt: costType.createdDate,
        updatedAt: costType.updatedDate,
      }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'costtype',
      destinationCollectionName: 'costtypes',
      pagingFunc: pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
