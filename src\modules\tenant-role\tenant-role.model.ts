import {
  DocumentType,
  index,
  mongoose,
  plugin,
  prop,
  Severity,
} from '@typegoose/typegoose';
import { modelOptions } from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { RoleGroupModel } from '~/modules/role-group/role-group.model';
import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import { TenantDocument, TenantModel } from '../tenant/tenant.model';

export type TenantRoleDocument = DocumentType<TenantRoleModel>;

@modelOptions({
  options: { customName: 'TenantRole', allowMixed: Severity.ALLOW },
})
@index({ key: 1, name: 1 })
@index({ decimal: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class TenantRoleModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({ required: true, trim: true, maxlength: 64 })
  key!: string;

  @prop({ required: true, trim: true, maxlength: 128 })
  name!: string;

  @prop()
  decimal!: mongoose.Types.Decimal128;

  @prop()
  permissions!: mongoose.Types.Decimal128;

  @prop({ ref: () => TenantModel })
  tenant!: TenantDocument;

  @prop({ ref: () => RoleGroupModel })
  roleGroup?: RoleGroupModel;
}
