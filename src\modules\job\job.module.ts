import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { IdentifierModule } from '~/modules/identifier/identifier.module';
import { EmailModule } from '~/processors/email/email.module';

import { ContactModule } from '../contact/contact.module';
import { EquipmentService } from '../equipment/equipment.service';
import { NightRegistrationModule } from '../night-registration/night-registration.module';
import { TenantModule } from '../tenant/tenant.module';
import { TenantUserModule } from '../tenant-user/tenant-user.module';
import { UnitService } from '../unit/unit.service';
import { JobController } from './job.controller';
import { JobService } from './job.service';

@Module({
  imports: [
    IdentifierModule,
    TenantUserModule,
    ContactModule,
    HttpModule,
    TenantModule,
    EmailModule,
    NightRegistrationModule,
  ],
  controllers: [JobController],
  providers: [JobService, UnitService, EquipmentService],
  exports: [JobService],
})
export class JobModule {}
