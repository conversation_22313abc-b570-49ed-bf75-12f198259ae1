import * as path from 'path';

import { ContactRole, ContactType } from '~/shared/enums/contact.enum';
import { parseObjectId } from '~/utils';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldPerson {
  _id: string;
  active: boolean;
  gender: 'Female' | 'Male' | 'Other';
  language: 'EN' | 'NL';
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber1: string;
  phoneNumber2: string;
  address1: string;
  address2: string;
  remark: string;
  debtorcontact: Record<string, any>;
}

const organizationPeoplePipeLineAggregate = (contactId: string) => {
  return [
    {
      $match: {
        person: parseObjectId(contactId),
        _replace: { $exists: false },
      },
    },
    {
      $lookup: {
        from: 'person',
        localField: 'person',
        foreignField: '_id',
        as: 'person',
      },
    },
    {
      $unwind: {
        path: '$person',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'grouping',
        localField: 'grouping',
        foreignField: '_id',
        as: 'grouping',
      },
    },
    {
      $unwind: {
        path: '$grouping',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        grouping: { $ne: null },
        person: { $ne: null },
      },
    },
  ];
};

const personPipeLineAggregate = (skip: number, limit: number) => {
  return [
    {
      $match: {
        debtorcontact: { $ne: null },
      },
    },
    {
      $lookup: {
        from: 'debtorcontact',
        localField: 'debtorcontact',
        foreignField: '_id',
        as: 'debtorcontact',
      },
    },
    {
      $unwind: {
        path: '$debtorcontact',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        debtorcontact: { $ne: null },
      },
    },
    { $skip: skip },
    {
      $limit: limit,
    },
  ];
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldPerson[];
  context: any;
}) => {
  return Promise.all(
    data.map(async (item) => {
      const pipeline = organizationPeoplePipeLineAggregate(item._id);

      const organizationPeople = await context
        .sourceClient!.db()
        .collection('persongrouping')
        .aggregate(pipeline)
        .toArray();

      let organizationNames = '';

      if (organizationPeople.length > 0) {
        organizationNames = organizationPeople
          .map((org: any) => org.grouping.name)
          .join(', ');
      }

      const tranformed: any = {
        _id: item._id,
        isActive: item.active,
        displayName: `${item.firstName} ${item.lastName}`,
        contactType: ContactType.PERSON,
        contactRole: ContactRole.DEBTOR,
        lastName: item.lastName || '',
        firstName: item.firstName || '',
        gender: item.gender?.toLowerCase() || 'male',
        language: item.language?.toLowerCase() || 'en',
        email: item.email || '',
        phone1: item.phoneNumber1 || '',
        phone2: item.phoneNumber2 || '',
        address1: item.address1,
        ...(item.address2 && { address2: item.address2 }),
        remark: item.remark || '',
        contactRefId: item.debtorcontact._id,
        identifier: item.debtorcontact?.accountView,
        invoiceEmail: item.debtorcontact?.invoiceEmail || '',
        invoiceReference: item.debtorcontact?.reference || '',
        collectiveJobInvoice: item.debtorcontact?.collectiveJobInvoice || false,
        collectiveCustomInvoice:
          item.debtorcontact?.collectiveCustomInvoice || false,
        paymentTermJobInvoice: item.debtorcontact?.paymentTermJob || 14,
        paymentTermRentInvoice: item.debtorcontact?.paymentTerm || 30,
        organizationNames,
        isInternal:
          item.debtorcontact?.accountView === 'CON-11' ||
          item.debtorcontact?.isEEAC === true
            ? true
            : false,
        isDeleted: false,
      };

      if (item.address1) {
        const address = await context
          .sourceClient!.db()
          .collection('address')
          .findOne({ _id: item.address1 });

        if (address) {
          await context
            .destinationClient!.db()
            .collection('addresses')
            .findOneAndUpdate(
              { _id: address._id },
              {
                $set: {
                  _id: address._id,
                  isActive: address.active,
                  street: address.street,
                  city: address.city,
                  postalCode: address.postalCode,
                  number: address.number,
                  country: address.country,
                  region: address.region,
                  contact: tranformed._id,
                },
              },
              {
                upsert: true,
              },
            );
        }
      }

      if (item.address2) {
        const address = await context
          .sourceClient!.db()
          .collection('address')
          .findOne({ _id: item.address2 });

        if (address?.country) {
          await context
            .destinationClient!.db()
            .collection('addresses')
            .findOneAndUpdate(
              { _id: address._id },
              {
                $set: {
                  _id: address._id,
                  isActive: address.active,
                  street: address.street,
                  city: address.city,
                  postalCode: address.postalCode,
                  number: address.number,
                  country: address.country,
                  region: address.region,
                  contact: tranformed._id,
                },
              },
              {
                upsert: true,
              },
            );
        } else {
          tranformed.address2 = null;
        }
      }

      return tranformed;
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('person');

  const pipeline = personPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'person',
      destinationCollectionName: 'contacts',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
