import { Injectable } from '@nestjs/common';

import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { RegionQueryParamDto } from './dtos/region.dto';
import { RegionModel } from './region.model';

@Injectable()
export class RegionService {
  constructor(
    @InjectModel(RegionModel)
    private countryModel: MongooseModel<RegionModel>,
  ) {}

  async getList(payload: RegionQueryParamDto) {
    const { query, options } = buildQuery(payload, ['name']);
    return await this.countryModel.aggregatePaginate(
      this.countryModel.aggregate([
        { $match: query },
        { $project: { name: 1 } },
      ]),
      options,
    );
  }
}
