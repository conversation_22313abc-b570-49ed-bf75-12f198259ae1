import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { EmailTemplateModel } from '~/modules/email-template/email-template.model';
import { emailTemplateTest } from '~/modules/email-template/test/email-template.dto.test';

const emailTemplateModel = getModelForClass(EmailTemplateModel);
type emailTemplateType = z.infer<typeof emailTemplateTest.modelSchema>;

export const mockEmailTemplateData = {
  _id: new ObjectId(),
  name: 'night_registration_before_departure',
  bcc: [],
  cc: [],
  html: '<div>hello world</div>',
  subject: 'Notification of departure',
  to: [],
};

export async function initMockEmailTemplate(doc?: Partial<emailTemplateType>) {
  const { _id, ...rest } = { ...mockEmailTemplateData, ...doc };
  await emailTemplateModel.replaceOne({ _id }, rest, { upsert: true });
}
