import { DocumentType, index, modelOptions, prop } from '@typegoose/typegoose';

import { PlanningOrderType } from '~/shared/enums/planning-order.enum';
import { BaseModel } from '~/shared/models/base.model';

export type PlanningOrderDocument = DocumentType<PlanningOrderModel>;

interface JobInterface {
  _id: string;
  position: number;
}

@modelOptions({
  options: {
    customName: 'PlanningOrder',
  },
})
@index({ employee: 1, date: 1 }, { unique: true })
export class PlanningOrderModel extends BaseModel {
  @prop({ required: true })
  employee!: string;

  @prop({ enum: PlanningOrderType, required: true })
  type!: PlanningOrderType;

  @prop({ required: true })
  date!: string;

  @prop({ default: [] })
  jobs!: JobInterface[];
}
