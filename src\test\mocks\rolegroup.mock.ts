import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { RoleGroupModel } from '~/modules/role-group/role-group.model';
import { roleGroupTest } from '~/modules/role-group/test/role-group.dto.test';

import { mockTenantRoleData } from './tenantrole.mock';

const roleGroupModel = getModelForClass(RoleGroupModel);
type roleGroupType = z.infer<typeof roleGroupTest.modelSchema>;

export const mockRoleGroupData = {
  _id: new ObjectId(),
  isActive: true,
  name: 'Cleaners',
  description: 'Cleaners',
  roles: [mockTenantRoleData._id],
  isDeleted: false,
};

export async function initMockRoleGroup(doc?: Partial<roleGroupType>) {
  const { _id, ...rest } = { ...mockRoleGroupData, ...doc };
  await roleGroupModel.replaceOne({ _id }, rest, { upsert: true });
}
