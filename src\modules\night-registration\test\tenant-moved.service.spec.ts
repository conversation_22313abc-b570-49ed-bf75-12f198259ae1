import { Test, TestingModule } from '@nestjs/testing';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';

import { LocationModel } from '~/modules/location/location.model';
import { NightRegistrationReservationModel } from '~/modules/night-registration/models/night-registration-reservation.model';
import { NightRegistrationResidentModel } from '~/modules/night-registration/models/night-registration-resident.model';
import { UnitModel } from '~/modules/unit/unit.model';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { initMockNightRegistrationReservation } from '~/test/mocks/nightregistrationreservation.mock';
import {
  initMockNightRegistrationResident,
  mockNightRegistrationResidentData,
} from '~/test/mocks/nightregistrationresident.mock';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';
import { getModelToken } from '~/transformers/model.transformer';

import { CheckOutService } from '../check-out/check-out.service';
import { TenantMovedService } from '../tenant-moved.service';

describe('TenantMovedService', () => {
  let service: TenantMovedService;
  let nightRegistrationReservationModel: MongooseModel<NightRegistrationReservationModel>;

  const fromLocationId = new ObjectId();
  const toLocationId = new ObjectId();
  const fromUnitId = new ObjectId();
  const toUnitId = new ObjectId();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        TenantMovedService,
        CheckOutService,
        ...testInjectModel([
          NightRegistrationReservationModel,
          LocationModel,
          UnitModel,
          NightRegistrationResidentModel,
        ]),
      ],
    }).compile();

    service = module.get<TenantMovedService>(TenantMovedService);
    nightRegistrationReservationModel = module.get(
      getModelToken(NightRegistrationReservationModel.name),
    );

    // Setup mock data
    await Promise.all([
      // From location and unit
      initMockLocation({
        _id: fromLocationId,
        fullAddress: 'From Location Address',
        units: [fromUnitId],
      }),
      initMockUnit({
        _id: fromUnitId,
        name: 'FromRoom',
        location: fromLocationId,
        isActive: true,
        isRoot: false,
        parent: null,
        maxOccupants: 2,
      }),

      // To location and unit
      initMockLocation({
        _id: toLocationId,
        fullAddress: 'To Location Address',
        units: [toUnitId],
      }),
      initMockUnit({
        _id: toUnitId,
        name: 'ToRoom',
        location: toLocationId,
        isActive: true,
        isRoot: false,
        parent: null,
        maxOccupants: 2,
      }),

      // Default location and unit (from mocks)
      initMockLocation({
        units: [mockUnitData._id],
      }),
      initMockUnit({
        location: mockLocationData._id,
      }),

      // Resident
      initMockNightRegistrationResident(),

      // Reservation
      initMockNightRegistrationReservation({
        resident: mockNightRegistrationResidentData._id,
        unit: fromUnitId,
        location: fromLocationId,
        arrivalDate: dayjs().utc().subtract(10, 'day').toDate(),
        isDeleted: false,
      }),
    ]);
  });

  describe('processTenantMoved', () => {
    const basePayload = {
      originalBody: {
        resident: {
          firstName: mockNightRegistrationResidentData.firstName,
          lastName: mockNightRegistrationResidentData.lastName,
        },
        fromLocation: 'From Location Address',
        toLocation: 'To Location Address',
        fromRoom: {
          description: {
            name: 'FromRoom',
          },
        },
        toRoom: {
          description: {
            name: 'ToRoom',
          },
          beds: {
            single: 1,
            double: 0,
          },
        },
        moveDate: dayjs().utc().toDate(),
        remarks: 'Tenant moved successfully',
      },
    };

    it('should throw an error if resident not found', async () => {
      const payload = {
        ...basePayload,
        originalBody: {
          ...basePayload.originalBody,
          resident: {
            firstName: 'Nonexistent',
            lastName: 'Resident',
          },
        },
      };

      await expect(service.processTenantMoved(payload)).rejects.toThrow(
        'Resident not found',
      );
    });

    it('should throw an error if from location not found', async () => {
      const payload = {
        ...basePayload,
        originalBody: {
          ...basePayload.originalBody,
          fromLocation: 'Nonexistent From Location',
        },
      };

      await expect(service.processTenantMoved(payload)).rejects.toThrow(
        `Location not found for address: ${payload.originalBody.fromLocation}`,
      );
    });

    it('should throw an error if from unit not found', async () => {
      const payload = {
        ...basePayload,
        originalBody: {
          ...basePayload.originalBody,
          fromRoom: {
            description: {
              name: 'Nonexistent From Room',
            },
          },
        },
      };

      await expect(service.processTenantMoved(payload)).rejects.toThrow(
        `Unit not found: ${payload.originalBody.fromRoom.description.name}`,
      );
    });

    it('should throw an error if to location not found', async () => {
      const payload = {
        ...basePayload,
        originalBody: {
          ...basePayload.originalBody,
          toLocation: 'Nonexistent To Location',
        },
      };

      await expect(service.processTenantMoved(payload)).rejects.toThrow(
        `Target location not found for address: ${payload.originalBody.toLocation}`,
      );
    });

    it('should throw an error if to unit not found', async () => {
      const payload = {
        ...basePayload,
        originalBody: {
          ...basePayload.originalBody,
          toRoom: {
            description: {
              name: 'Nonexistent To Room',
            },
          },
        },
      };

      await expect(service.processTenantMoved(payload)).rejects.toThrow(
        `Target unit not found: ${payload.originalBody.toRoom.description.name}`,
      );
    });

    it('should throw an error if no reservation found', async () => {
      // Remove all reservations for the resident
      await nightRegistrationReservationModel.deleteMany({
        resident: mockNightRegistrationResidentData._id,
      });

      await expect(service.processTenantMoved(basePayload)).rejects.toThrow(
        `Reservation not found for the resident in the specified unit`,
      );
    });

    it('should update reservation with correct data', async () => {
      const moveDate = dayjs().utc().toDate();
      const remarks = 'Updated tenant move remarks';

      const payload = {
        ...basePayload,
        originalBody: {
          ...basePayload.originalBody,
          moveDate,
          remarks,
        },
      };

      await service.processTenantMoved(payload);

      // Verify the reservation was updated correctly
      const updatedReservation = await nightRegistrationReservationModel
        .findOne({
          resident: mockNightRegistrationResidentData._id,
          isDeleted: false,
        })
        .lean();

      expect(updatedReservation).toBeDefined();
      expect(updatedReservation?.arrivalDate).toEqual(moveDate);
      expect(updatedReservation?.departureDate).toBeNull();
      expect(updatedReservation?.remarks).toBe(remarks);
      expect(updatedReservation?.unit.toString()).toBe(toUnitId.toString());
      expect(updatedReservation?.bed).toBe(1);
      expect(updatedReservation?.isVirtual).toBe(false);
    });
  });
});
