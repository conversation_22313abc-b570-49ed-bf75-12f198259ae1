import { Injectable, Logger } from '@nestjs/common';
import { OnEvent } from '@nestjs/event-emitter';

import { StatsOccupantService } from '~/modules/stats-occupant/stats-occupant.service';
import { OCCUPANT_EVENT } from '~/shared/event/occupant.event';

import { StatsOccupantCalculateHiredLocationEventDto } from './dto/stats-occupant.dto';

@Injectable()
export class EventEmitterListener {
  private readonly logger = new Logger(EventEmitterListener.name);
  constructor(private readonly statsOccupantService: StatsOccupantService) {}

  @OnEvent(OCCUPANT_EVENT.CALCULATE_HIRED_LOCATION, { async: true })
  async occupantCalculateHiredLocations(
    payload: StatsOccupantCalculateHiredLocationEventDto,
  ) {
    this.logger.log('Trigger calculate hired locations by Event Emitter');
    await this.statsOccupantService.recalculateStatsOccupantsMarkNeedToUpdate(
      payload,
    );
  }
}
