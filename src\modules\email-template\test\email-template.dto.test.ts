import { z } from 'zod';

import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    name: z.string(),
    subject: z.string(),
    text: z.string().optional(),
    html: z.string().optional(),
    to: z.array(z.string()),
    cc: z.array(z.string()).optional(),
    bcc: z.array(z.string()).optional(),
  })
  .extend(baseModelTestSchema);

export const emailTemplateTest = {
  modelSchema,
};
