import dayjs from 'dayjs';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { isValidObjectId } from '~/utils';

export const UpdateCostLineGeneralSchema = z
  .strictObject({
    _id: z.string().refine((v) => isValidObjectId(v)),
    description: z.string().min(1).max(256).optional(),
    price: z.number().optional(),
    startDate: z.dateString().optional(),
    endDate: z.dateString().nullish(),
  })
  .refine((schema) => {
    if (schema.startDate && schema.endDate) {
      const dayjsStartDate = dayjs(schema.startDate).utc().startOf('day');
      const dayjsEndDate = dayjs(schema.endDate).utc().startOf('day');
      return dayjsStartDate.isSameOrBefore(dayjsEndDate);
    }
    return true;
  }, 'EndDate must be equal or greater than StartDate');

const BulkEditCostLineGeneralSchema = z.strictObject({
  contractId: z.string().refine((v) => isValidObjectId(v)),
  costLineGenerals: z.array(UpdateCostLineGeneralSchema).min(1),
});

export class BulkEditCostLineGeneralDto extends createZodDto(
  BulkEditCostLineGeneralSchema,
) {}
