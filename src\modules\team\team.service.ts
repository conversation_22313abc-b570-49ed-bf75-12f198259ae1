import {
  Injectable,
  NotAcceptableException,
  NotFoundException,
} from '@nestjs/common';

import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { TEAM_MESSAGE_KEYS } from '~/shared/message-keys/team.message-keys';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { CreateTeamDto, SortTeamDto, UpdateTeamDto } from './dtos/team.dto';
import { TeamModel } from './team.model';

@Injectable()
export class TeamService {
  constructor(
    @InjectModel(TeamModel)
    private readonly teamModel: MongooseModel<TeamModel>,
  ) {}

  async findAll(payload: any) {
    if (!payload.hasEquipment) {
      payload.isEquipment = { $ne: true };
    } else {
      delete payload.hasEquipment;
    }
    const { query, options } = buildQuery(payload, ['name']);

    let { limit } = options;

    if (limit <= -1) {
      limit = await this.teamModel.countDocuments(query);
    }

    return this.teamModel.paginate(query, {
      ...options,
      limit,
      select: 'isActive name position isEquipment',
    });
  }

  async findOne(id: string) {
    return this.teamModel.findById(id).select('isActive name position').lean();
  }

  async create(payload: CreateTeamDto) {
    const foundTeam = await this.teamModel.findOne({ name: payload.name });

    if (foundTeam) {
      throw new NotFoundException(TEAM_MESSAGE_KEYS.ALREADY_EXISTS);
    }

    const team = await this.teamModel.create(payload);
    return this.findOne(team._id.toString());
  }

  async update(payload: UpdateTeamDto) {
    const foundTeam = await this.teamModel.findById(payload.id);

    if (!foundTeam) {
      throw new NotFoundException(TEAM_MESSAGE_KEYS.NOT_FOUND);
    }

    // cannot update isEquipment team
    if (foundTeam.isEquipment) {
      throw new NotAcceptableException(TEAM_MESSAGE_KEYS.IS_EQUIPMENT);
    }

    const foundByName = await this.teamModel.findOne({
      name: payload.name,
      _id: { $ne: payload.id },
    });

    if (foundByName) {
      throw new NotFoundException(TEAM_MESSAGE_KEYS.ALREADY_EXISTS);
    }

    return this.teamModel
      .findByIdAndUpdate(payload.id, payload, {
        new: true,
      })
      .select('isActive name position')
      .lean();
  }

  async sort(payload: SortTeamDto) {
    const { teams } = payload;
    await Promise.all(
      teams.map(async (team) => {
        const foundTeam = await this.teamModel.findById(team._id);

        if (!foundTeam) {
          throw new NotFoundException(TEAM_MESSAGE_KEYS.NOT_FOUND);
        }

        await this.teamModel.findByIdAndUpdate(team._id, {
          position: team.position,
        });
      }),
    );

    return this.findAll({
      pageSize: -1,
      sortBy: 'position',
      sortDir: 'asc',
    });
  }
}
