import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { unitTest } from '~/modules/unit/test/unit.dto.test';
import { JobTemplateTypeEnum } from '~/shared/enums/job-template.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    type: z.nativeEnum(JobTemplateTypeEnum),
    name: z.string(),
    points: z.array(
      z.object({
        _id: z.string().optional(),
        description: z.string(),
        position: z.number(),
      }),
    ),
    unit: z.instanceof(ObjectId),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(modelSchema.omit({ type: true }));

const findOneSchema = modelSchema.omit({ type: true }).extend({
  unit: unitTest.modelSchema.pick({ _id: true, name: true }).optional(),
});

const findByUnitSchema = findOneSchema;
const findByUnitsSchema = z.array(findOneSchema);
const createSchema = findOneSchema;
const updateSchema = findOneSchema;
const deletePointSchema = findOneSchema;
const copyJobTemplatesSchema = findByUnitsSchema;

export const jobTemplateTest = {
  modelSchema,
  findAllSchema,
  findOneSchema,
  findByUnitSchema,
  findByUnitsSchema,
  createSchema,
  updateSchema,
  deletePointSchema,
  copyJobTemplatesSchema,
};
