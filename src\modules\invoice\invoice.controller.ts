import { Controller, NotFoundException, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { INVOICE_MESSAGE_KEYS } from '~/shared/message-keys/invoice.message-key';
import { INVOICE_MESSAGES } from '~/shared/messages/invoice.message';

import {
  ApprovedInvoicesQueryParamsDto,
  InvoiceApproveBodyDto,
} from './dtos/invoice-approve.dto';
import {
  ContractInvoiceReviewQueryParamsDto,
  InvoiceReviewQueryParamsDto,
  JobInvoiceReviewQueryParamsDto,
} from './dtos/invoice-review.dto';
import { InvoiceService } from './invoice.service';

@Controller('invoice')
export class InvoiceController {
  constructor(private readonly invoiceService: InvoiceService) {}

  @UsePipes(new ZodValidationPipe(InvoiceReviewQueryParamsDto))
  @MessagePattern({ cmd: INVOICE_MESSAGES.GET_REVIEW })
  async getReview(
    @Payload()
    query: JobInvoiceReviewQueryParamsDto | ContractInvoiceReviewQueryParamsDto,
  ) {
    switch (query.type) {
      case 'contract':
        return await this.invoiceService.findInvoicesReviewTypeContract(query);
      case 'job':
        return await this.invoiceService.findInvoicesReviewTypeJob(query);
    }
  }

  @UsePipes(new ZodValidationPipe(InvoiceApproveBodyDto))
  @MessagePattern({ cmd: INVOICE_MESSAGES.APPROVE })
  async approveInvoice(@Payload() payload: InvoiceApproveBodyDto) {
    return await this.invoiceService.approveInvoices(payload);
  }

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(ApprovedInvoicesQueryParamsDto))
  @MessagePattern({ cmd: INVOICE_MESSAGES.GET_APPROVED })
  async getApprovedInvoices(@Payload() query: ApprovedInvoicesQueryParamsDto) {
    return await this.invoiceService.findApprovedInvoices(query);
  }

  @MessagePattern({ cmd: INVOICE_MESSAGES.GET_DETAIL_APPROVED })
  async getApprovedInvoiceDetail(@Payload() invoiceId: string) {
    const invoice =
      await this.invoiceService.getApprovedInvoiceDetail(invoiceId);

    if (!invoice) {
      throw new NotFoundException(
        INVOICE_MESSAGE_KEYS.APPROVED_INVOICE_NOT_FOUND,
      );
    }

    return invoice;
  }
}
