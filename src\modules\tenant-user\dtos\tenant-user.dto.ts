import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { TenantRoleEnum } from '~/shared/enums/tenant-role.enum';
import {
  ConfigurablePage,
  Gender,
  Language,
  ThisWeekDefaultDisplay,
} from '~/shared/enums/tenant-user.enum';
import { WorkingDays } from '~/shared/enums/working-days.enum';
import { kebabCaseToCamelCase } from '~/utils';

const genderValues = Object.values(Gender) as [string, ...string[]];
const languageValues = Object.values(Language) as [string, ...string[]];
const workingDaysValues = Object.values(WorkingDays) as [string, ...string[]];
const tenantRoleValue = Object.values(TenantRoleEnum) as [string, ...string[]];
const thisWeekDefaultDisplayValue = Object.values(ThisWeekDefaultDisplay) as [
  string,
  ...string[],
];

const EmployeeOrderSchema = z.strictObject({
  _id: z.string().refine((value) => {
    return isValidObjectId(value);
  }),
  position: z.number().min(0),
});

const CreateTenantUserSchema = z.strictObject({
  isActive: z.boolean(),
  username: z
    .string()
    .min(5)
    .max(64)
    .trim()
    .regex(/^[a-zA-Z0-9]+$/),
  email: z.string().email().max(128),
  password: z
    .string()
    .min(8)
    .regex(/[a-z]/)
    .regex(/[A-Z]/)
    .regex(/\d/)
    .regex(/[!"#$%&'()*+,-./:;<=>?@[\]\\^_`{|}~]+/)
    .max(128),
  isGeneratePassword: z.boolean().optional(),
  firstName: z.string().min(1).max(64),
  lastName: z.string().min(1).max(64),
  phone1: z
    .string()
    .min(8)
    .max(24)
    .regex(/^[\+\-\\\(\)\.0-9 ]+$/),
  phone2: z
    .string()
    .max(24)
    .refine((value) => {
      if (value) {
        if (value.length < 8) {
          return false;
        }

        return /^[\+\-\\\(\)\.0-9 ]+$/.test(value);
      }

      return true;
    })
    .optional(),
  gender: z.enum(genderValues),
  language: z.enum(languageValues),
  roles: z
    .array(z.string())
    .min(1)
    .refine((value) => {
      return new Set(value).size === value.length;
    }, 'Duplicate roles are not allowed'),
  team: z.string().refine((value) => {
    return isValidObjectId(value);
  }),
  oddWeeks: z.array(z.enum(workingDaysValues)).refine((value) => {
    return new Set(value).size === value.length;
  }, 'Duplicate working days are not allowed'),
  evenWeeks: z.array(z.enum(workingDaysValues)).refine((value) => {
    return new Set(value).size === value.length;
  }, 'Duplicate working days are not allowed'),
  thisWeekDefaultDisplay: z
    .enum(thisWeekDefaultDisplayValue)
    .nullish()
    .default(ThisWeekDefaultDisplay.SHOPPING_LIST),
  saveToPhotos: z
    .boolean()
    .or(z.enum(['true', 'false']))
    .optional(),
});

const UpdateTenantUserSchema = z.strictObject({
  id: z.string().refine((value) => {
    return isValidObjectId(value);
  }),
});

const ChangeTenantPasswordSchema = z.strictObject({
  oldPassword: z.string().max(128),
});

const GetTeamManagementSchema = z.strictObject({
  team: z.string().refine((value) => {
    return isValidObjectId(value);
  }),
});

const UpdateTeamManagementSchema = GetTeamManagementSchema.extend({
  employees: z.array(EmployeeOrderSchema),
});

const EmployeeQueryParamsSchema = z
  .strictObject({
    isActive: z.enum(['true', 'false']).optional(),
    team: z
      .string()
      .refine((value) => {
        return isValidObjectId(value);
      })
      .optional(),
    roles: z
      .array(z.enum(tenantRoleValue))
      .refine((value) => {
        return new Set(value).size === value.length;
      }, 'Duplicate roles are not allowed')
      .optional(),
  })
  .merge(QueryParamsSchema);

const UpdateDisplayConfigSchema = z.strictObject({
  id: z.string().refine(isValidObjectId),
  page: z.nativeEnum(ConfigurablePage).transform(kebabCaseToCamelCase),
  displayConfig: z.strictObject({
    field: z.string(),
    isShown: z.boolean(),
  }),
});

export class CreateTenantUserDto extends createZodDto(
  CreateTenantUserSchema.extend({
    password: CreateTenantUserSchema.shape.password.optional(),
  }).superRefine((data, ctx) => {
    if (!data.isGeneratePassword && !data.password) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Password is required',
        path: ['password'],
      });
    }
  }),
) {}

export class UpdateTenantUserDto extends createZodDto(
  CreateTenantUserSchema.omit({
    username: true,
    password: true,
  }).merge(UpdateTenantUserSchema),
) {}

export class UpdateTenantUserMeDto extends createZodDto(
  CreateTenantUserSchema.omit({
    isActive: true,
    username: true,
    password: true,
    roles: true,
    team: true,
    oddWeeks: true,
    evenWeeks: true,
  }).merge(UpdateTenantUserSchema),
) {}

export class GetTeamManagementDto extends createZodDto(
  GetTeamManagementSchema,
) {}

export class UpdateTeamManagementDto extends createZodDto(
  UpdateTeamManagementSchema,
) {}

export class UpdateTenantUserPasswordDto extends createZodDto(
  CreateTenantUserSchema.pick({
    password: true,
  })
    .merge(UpdateTenantUserSchema)
    .merge(ChangeTenantPasswordSchema),
) {}

export class ChangeTenantUserPasswordDto extends createZodDto(
  CreateTenantUserSchema.pick({
    password: true,
    username: true,
  }).merge(ChangeTenantPasswordSchema),
) {}

export class ResetTenantUserPasswordDto extends createZodDto(
  CreateTenantUserSchema.pick({
    password: true,
  }).merge(UpdateTenantUserSchema),
) {}

export class EmployeeQueryParamsDto extends createZodDto(
  EmployeeQueryParamsSchema,
) {}

export class UpdateDisplayConfigDto extends createZodDto(
  UpdateDisplayConfigSchema,
) {}
