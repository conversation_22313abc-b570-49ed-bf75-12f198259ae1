import dayjs from 'dayjs';
import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import {
  MAX_DESCRIPTION_LENGTH,
  MAX_TEXT_LENGTH,
} from '~/constants/app.constant';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import { TaskCategory, TaskType } from '~/shared/enums/task.enum';

const validateUniqueIds = (val: string[]) => {
  const uniqueIds = new Set(val);
  return uniqueIds.size === val.length;
};

export const BaseTaskSchema = z.strictObject({
  title: z.string().max(MAX_DESCRIPTION_LENGTH).optional(),
  description: z.string().max(MAX_TEXT_LENGTH).optional(),
  destination: z.string().max(MAX_DESCRIPTION_LENGTH).optional(),
  type: z.nativeEnum(TaskType).optional(),
  category: z.nativeEnum(TaskCategory),
  startDate: z.dateString(),
  endDate: z.dateString(),
  cars: z
    .array(z.string().refine(isValidObjectId))
    .optional()
    .refine(
      (cars) => !cars || new Set(cars).size === cars.length,
      'Car ids must be unique',
    ),
  devices: z
    .array(z.string().refine(isValidObjectId))
    .optional()
    .refine(
      (devices) => !devices || new Set(devices).size === devices.length,
      'Device ids must be unique',
    ),
  employees: z
    .array(z.string().refine(isValidObjectId))
    .min(1)
    .refine(
      (employees) => new Set(employees).size === employees.length,
      'Employee ids must be unique',
    ),
});

const CreateTaskSchema = BaseTaskSchema.refine(
  ({ startDate, endDate }) => dayjs(startDate).isBefore(endDate),
  'Start date must be before end date',
);

const UpdateTaskSchema = BaseTaskSchema.extend({
  id: z.string().refine((val) => isValidObjectId(val)),
}).refine(
  ({ startDate, endDate }) => dayjs(startDate).isBefore(endDate),
  'Start date must be before end date',
);

const CheckOverLapTaskSchema = z.strictObject({
  taskId: z
    .string()
    .optional()
    .refine((val) => val === undefined || isValidObjectId(val), {
      message: 'Invalid input',
    }),
  startDate: z.dateString(),
  endDate: z.dateString(),
  timezone: z
    .string()
    .optional()
    .default('+00:00')
    .refine((value) => /^[+-][0-1][0-9]:[0-5][0-9]$/.test(value), {
      message: 'Invalid timezone format',
    }),
  equipmentType: z.nativeEnum(EquipmentEnum).optional(),
  employees: z
    .array(z.string().refine(isValidObjectId))
    .min(1)
    .refine(validateUniqueIds, 'Duplicate employees are not allowed'),
  equipments: z
    .array(z.string().refine(isValidObjectId))
    .min(1)
    .refine(validateUniqueIds, 'Duplicate equipments are not allowed')
    .optional(),
});

export class CreateTaskDto extends createZodDto(CreateTaskSchema) {}
export class UpdateTaskDto extends createZodDto(UpdateTaskSchema) {}
export class CheckOverLapTaskDto extends createZodDto(CheckOverLapTaskSchema) {}
