import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import {
  EquipmentPlanningOverviewQueryParamsDto,
  TeamPlanningOverviewQueryParamsDto,
} from '~/modules/planning/dtos/planning-overview-query-params.dto';
import { PlanningService } from '~/modules/planning/planning.service';
import { PLANNING_MESSAGES } from '~/shared/messages/planning.message';

import { ChangePositionInPlanningOrderSchemaDto } from './dtos/planning-order.dto';

@Controller('planning')
export class PlanningController {
  constructor(private readonly planningService: PlanningService) {}

  @UsePipes(new ZodValidationPipe(TeamPlanningOverviewQueryParamsDto))
  @MessagePattern({ cmd: PLANNING_MESSAGES.GET_TEAM_PLANNING_OVERVIEW })
  async teamOverview(@Payload() query: TeamPlanningOverviewQueryParamsDto) {
    return this.planningService.teamOverview(query);
  }

  @UsePipes(new ZodValidationPipe(EquipmentPlanningOverviewQueryParamsDto))
  @MessagePattern({ cmd: PLANNING_MESSAGES.GET_EQUIPMENT_PLANNING_OVERVIEW })
  async equipmentOerview(
    @Payload() query: EquipmentPlanningOverviewQueryParamsDto,
  ) {
    return this.planningService.equipmentOverview(query);
  }

  @UsePipes(new ZodValidationPipe(ChangePositionInPlanningOrderSchemaDto))
  @MessagePattern({ cmd: PLANNING_MESSAGES.CHANGE_POSITION_IN_PLANNING_ORDER })
  async changePositionInPlanningOrder(
    @Payload() payload: ChangePositionInPlanningOrderSchemaDto,
  ) {
    return this.planningService.changePositionInPlanningOrder(payload);
  }
}
