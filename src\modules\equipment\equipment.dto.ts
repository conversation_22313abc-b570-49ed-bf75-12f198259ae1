import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import { isValidObjectId } from '~/utils';

const equipmentTypes = Object.values(EquipmentEnum) as [string, ...string[]];

const CreateEquipmentSchema = z.strictObject({
  description: z.string().min(1).max(255).trim(),
  isActive: z.boolean().default(true),
  type: z.enum(equipmentTypes),
});

const EquipmentQueryParamsSchema = z
  .strictObject({
    type: z.enum(equipmentTypes).optional(),
    isActive: z.enum(['true', 'false']).optional(),
  })
  .merge(QueryParamsSchema);

const AvailableEquipmentQueryParamsSchema = z
  .strictObject({
    types: z.union([
      z.nativeEnum(EquipmentEnum).transform((v) => [v]),
      z
        .array(z.nativeEnum(EquipmentEnum))
        .min(1)
        .transform((v) => Array.from(new Set(v))),
    ]),
    startDate: z.dateString(),
    endDate: z.dateString(),
    equipmentStatus: z
      .enum(['available', 'unavailable'])
      .optional()
      .default('available'),
  })
  .merge(QueryParamsSchema.omit({ _q: true }));

export class CreateEquipmentDto extends createZodDto(CreateEquipmentSchema) {}

export class UpdateEquipmentDto extends createZodDto(
  CreateEquipmentSchema.pick({ description: true, isActive: true }).merge(
    z.strictObject({
      id: z.string().refine((value) => {
        return isValidObjectId(value);
      }),
    }),
  ),
) {}

export class EquipmentQueryParamsDto extends createZodDto(
  EquipmentQueryParamsSchema,
) {}

export class AvailableEquipmentQueryParamsDto extends createZodDto(
  AvailableEquipmentQueryParamsSchema,
) {}
