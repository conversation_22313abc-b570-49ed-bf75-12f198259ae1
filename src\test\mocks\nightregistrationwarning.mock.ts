import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { NightRegistrationWarningModel } from '~/modules/night-registration/models/night-registration-warning.model';

import { mockNightRegistrationResidentData } from './nightregistrationresident.mock';
import { mockNightRegistrationWarningCategoryData } from './nightregistrationwarningcategory.mock';

const nightRegistrationWarningModel = getModelForClass(
  NightRegistrationWarningModel,
);

export const mockNightRegistrationWarningData = {
  _id: new ObjectId(),
  dayToLeave: 7,
  description: 'Dirty room and bathroom, bicycle in the room',
  emailTemplateOption: 1,
  images: [],
  isDeleted: false,
  level: '2|official',
  resident: mockNightRegistrationResidentData._id,
  warningCategory: mockNightRegistrationWarningCategoryData._id,
  warningDate: new Date(),
};

export async function initMockNightRegistrationWarning(doc?: any) {
  const { _id, ...rest } = { ...mockNightRegistrationWarningData, ...doc };
  await nightRegistrationWarningModel.replaceOne({ _id }, rest, {
    upsert: true,
  });
}
