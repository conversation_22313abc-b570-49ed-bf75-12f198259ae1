import { ContractType } from '~/shared/enums/contract.enum';
import { TenantRoleEnum } from '~/shared/enums/tenant-role.enum';

export const getReadRolesByContractType = (type?: ContractType) => {
  switch (type) {
    case ContractType.RENTING:
      return [
        TenantRoleEnum.DEBTOR_CONTRACT_MANAGER,
        TenantRoleEnum.CONTRACT_VIEWER,
      ];
    case ContractType.CREDITOR:
      return [
        TenantRoleEnum.CREDITOR_CONTRACT_MANAGER,
        TenantRoleEnum.CONTRACT_VIEWER,
      ];
    case ContractType.SERVICE:
      return [
        TenantRoleEnum.SERVICE_CONTRACT_MANAGER,
        TenantRoleEnum.CONTRACT_VIEWER,
      ];
    case ContractType.SUPPLIER:
      return [
        TenantRoleEnum.SUPPLIER_CONTRACT_MANAGER,
        TenantRoleEnum.CONTRACT_VIEWER,
      ];
    default:
      return [];
  }
};

export const getManageRoleByContractType = (type?: ContractType) => {
  switch (type) {
    case ContractType.RENTING:
      return TenantRoleEnum.DEBTOR_CONTRACT_MANAGER;
    case ContractType.CREDITOR:
      return TenantRoleEnum.CREDITOR_CONTRACT_MANAGER;
    case ContractType.SERVICE:
      return TenantRoleEnum.SERVICE_CONTRACT_MANAGER;
    case ContractType.SUPPLIER:
      return TenantRoleEnum.SUPPLIER_CONTRACT_MANAGER;
    default:
      return '';
  }
};
