import {
  DocumentType,
  index,
  modelOptions,
  mongoose,
  prop,
  Ref,
} from '@typegoose/typegoose';

import { BaseModel } from '~/shared/models/base.model';

import { ContractDocument, ContractModel } from '../contract/contract.model';
import { LocationDocument, LocationModel } from '../location/location.model';
import { UnitDocument, UnitModel } from '../unit/unit.model';

interface CostCenterInfo {
  _id: mongoose.Types.ObjectId;
  identifier: string;
  name: string;
}

interface LocationInfo {
  _id: string;
  fullAddress: string;
}

interface TeamInfo {
  _id: string;
  name: string;
}

export type StatsOccupantDocument = DocumentType<StatsOccupantModel>;

@modelOptions({
  options: { customName: 'StatsOccupants' },
})
@index({ location: 1, reportCreatedDate: 1 })
@index({ reportCreatedDate: 1, isLocationActive: 1, isDeleted: 1 })
export class StatsOccupantModel extends BaseModel {
  @prop({ required: true, ref: () => LocationModel })
  location!: Ref<LocationDocument>;

  // Field isActive in LocationModel
  @prop({ required: true })
  isLocationActive!: boolean;

  // Field Service in LocationModel
  @prop({ required: true })
  isLocationService!: boolean;

  @prop({ required: true })
  maxCount!: number;

  @prop({ required: true })
  hiredCount!: number;

  @prop({ required: true })
  emptyCount!: number;

  @prop({ ref: () => UnitModel, default: [] })
  hiredUnits?: Ref<UnitDocument>[];

  @prop({ ref: () => UnitModel, default: [] })
  unHiredUnits?: Ref<UnitDocument>[];

  @prop({ ref: () => ContractModel, default: [] })
  creditorContracts?: Ref<ContractDocument>[];

  @prop({ ref: () => ContractModel, default: [] })
  debtorContracts?: Ref<ContractDocument>[];

  @prop()
  locationInfo?: LocationInfo;

  @prop()
  costCenterInfo?: CostCenterInfo;

  @prop()
  teamInfo?: TeamInfo;

  @prop()
  reportCreatedDate?: Date;

  @prop({ default: false })
  markNeedToUpdate!: boolean;
}
