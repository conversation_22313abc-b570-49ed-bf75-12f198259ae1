import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldResident {
  _id: string;
  firstName: string;
  lastName: string;
  dateOfBirth: string;
  email: string;
  gender: string;
  phoneNumber: string;
  clientId: string;
  createdAt: Date;
  updatedAt: Date;
}

const ResidentPipeLineAggregate = (skip: number, limit: number) => {
  // get all resident
  return [{ $skip: skip }, { $limit: limit }];
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldResident[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (item) => {
      return {
        _id: item._id,
        firstName: item.firstName,
        lastName: item.lastName,
        displayName: `${item.firstName} ${item.lastName}`,
        dateOfBirth: item.dateOfBirth,
        gender: item.gender === 'M' ? 'Male' : 'Female',
        email: item.email,
        phoneNumber: item.phoneNumber,
        clientId: item.clientId,
        isDeleted: false,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      };
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('night-registration_resident');

  const pipeline = ResidentPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'night-registration_resident',
      destinationCollectionName: 'nightregistrationresidents',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
