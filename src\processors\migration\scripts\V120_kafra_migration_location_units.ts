import csv from 'csvto<PERSON>son';
import pathLibrary from 'path';

import { UnitModel } from '~/modules/unit/unit.model';
import { parseObjectId } from '~/utils';

import { MigrationContext } from '../migration.service';

interface LocationRootUnit {
  locationId: string;
  rootUnitId: string;
  position: number;
  maxOccupants: number;
}

const readCSVFile = async (fileName: string) => {
  // get public path
  const csvFilePath = pathLibrary.join(
    process.cwd(),
    '/dist/public/csv/',
    fileName,
  );
  return csv().fromFile(csvFilePath);
};

const baseUnit: UnitModel = {
  isActive: true,
  isRoot: false,
  name: '',
  maxOccupants: 0,
  maxArea: 0,
  position: 0,
  location: parseObjectId('000000000000000000000000'),
  createdAt: new Date(),
  updatedAt: new Date(),
  isDeleted: false,
};

const transformData = async (data: any) => {
  const locationUnits: any = [];
  data.forEach((unit: any) => {
    const { locationId, unitName, maxOccupants = 1 } = unit;
    // check if locationId and unitName exists in locationUnits
    const unitExists = locationUnits.find(
      (unit: any) => unit.location === locationId && unit.name === unitName,
    );
    if (!unitExists) {
      locationUnits.push({
        ...baseUnit,
        name: unitName,
        maxOccupants: parseInt(maxOccupants),
        location: locationId,
      });
    } else {
      unitExists.maxOccupants =
        parseInt(unitExists.maxOccupants) + parseInt(maxOccupants);
    }
  });
  return locationUnits;
};

const getDistinctLocations = async (data: any) => {
  return data.reduce((acc: any[], unit: any) => {
    const { locationId } = unit;
    if (!acc.includes(locationId)) {
      acc.push(locationId);
    }
    return acc;
  }, []);
};

const up = async (context: MigrationContext) => {
  try {
    const units = await readCSVFile('kafra-converted-units.csv');
    const unitCollection = context?.destinationClient?.db().collection('units');
    const locationCollection = context?.destinationClient
      ?.db()
      .collection('locations');
    if (!unitCollection || !locationCollection) {
      throw new Error('Collection not found');
    }

    const transformedData = await transformData(units);

    const distinctLocations = await getDistinctLocations(units);

    const getListRootUnitsByLocation = distinctLocations.map(
      async (location: any) => {
        const rootUnit = await unitCollection.findOne({
          location: parseObjectId(location),
          isRoot: true,
        });
        return {
          locationId: location,
          rootUnitId: rootUnit?._id.toString(),
          position: 0,
          maxOccupants: 0,
        } as LocationRootUnit;
      },
    );
    const listRootUnitsByLocation = await Promise.all(
      getListRootUnitsByLocation,
    );

    const insertOrUpdate = async (unit: any) => {
      try {
        await unitCollection.findOneAndUpdate(
          {
            name: unit.name,
            location: parseObjectId(unit.location),
          },
          {
            $set: {
              ...unit,
              location: parseObjectId(unit.location),
            },
          },
          {
            upsert: true,
          },
        );

        console.log(
          `Migrated document with unit name: ${unit.name} and location: ${unit.location}`,
        );
      } catch (error) {
        console.error(
          `Error migrating document with unit name: ${unit.name} and location: ${unit.location}`,
          error,
        );
      }
    };

    const upsertPromises = transformedData.map(async (unit: any) => {
      const { location } = unit;
      const locationId = parseObjectId(location);
      const foundLocation = await locationCollection.findOne({
        _id: locationId,
      });

      if (!foundLocation) {
        console.error(`Location with id ${location} not found`);
        return;
      }

      const foundIndex = listRootUnitsByLocation.findIndex(
        (rootUnit) => rootUnit.locationId.toString() === location.toString(),
      );

      if (foundIndex !== -1) {
        const nextPosition = listRootUnitsByLocation[foundIndex].position + 1;
        unit.parent = parseObjectId(
          listRootUnitsByLocation[foundIndex].rootUnitId,
        );
        unit.position = nextPosition;
        listRootUnitsByLocation[foundIndex].maxOccupants += unit.maxOccupants;
        listRootUnitsByLocation[foundIndex].position += 1;
        await insertOrUpdate(unit);
      }
    });

    await Promise.all(upsertPromises).then(async () => {
      const updateMaxOccupants = listRootUnitsByLocation.map(async (unit) => {
        await unitCollection.updateOne(
          { _id: parseObjectId(unit.rootUnitId) },
          {
            $set: {
              maxOccupants: unit.maxOccupants,
            },
          },
        );
      });
      await Promise.all(updateMaxOccupants).then(() => {
        console.log('Data successfully imported into Units collection');
      });
    });
  } catch (error) {
    console.error('Error importing data:', error);
  }
};

export default up;
