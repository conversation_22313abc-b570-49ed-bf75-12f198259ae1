import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import {
  MAX_DESCRIPTION_LENGTH,
  MAX_TEXT_LENGTH,
} from '~/constants/app.constant';
import { AddressSchema } from '~/modules/contact/dtos/contact.dto';
import { UpdateLocationUnitSchema } from '~/modules/unit/dtos/unit.dto';
import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { EnergyLabel } from '~/shared/enums/location.enum';
import {
  CertificateAndControlInspectionType,
  LocationAdditionalGroupType,
  LocationAdditionalType,
} from '~/shared/enums/location-additional.enum';

const LocationOfItemSchema = z.strictObject({
  key: z.string().max(1024),
  value: z.string().max(1024),
  position: z.number().min(1),
});

export const LocationIdSchema = z.strictObject({
  id: z.string().refine((value) => isValidObjectId(value)),
});

const CreateLocationSchema = z.strictObject({
  isActive: z.boolean(),
  isRenting: z.boolean().optional(),
  isService: z.boolean().optional(),
  bvCompany: z.string().max(64),
  locationOf: z.array(LocationOfItemSchema).optional(),
  costCenter: z.string().refine((value) => isValidObjectId(value)),
  team: z.string().refine((value) => isValidObjectId(value)),
  address: AddressSchema.omit({ _id: true }),
  email: z
    .string()
    .email()
    .optional()
    .nullable()
    .transform((value) => {
      value === null ? (value = '') : value;
      return value;
    }),
  tenantId: z.string().optional(),
  parkingSpaces: z.number().int().min(0).max(9999).optional(), // Updated to allow integers only
  energyLabel: z
    .enum([
      EnergyLabel.A_THREE_PLUS,
      EnergyLabel.A_TWO_PLUS,
      EnergyLabel.A_PLUS,
      EnergyLabel.A,
      EnergyLabel.B,
      EnergyLabel.C,
      EnergyLabel.D,
      EnergyLabel.E,
      EnergyLabel.F,
      EnergyLabel.G,
    ])
    .optional(),
  maximumStayDuration: z.number().int().min(1).nullish(),
});

const UpdateLocationSchema = CreateLocationSchema.omit({
  bvCompany: true,
}).merge(LocationIdSchema);

const LocationQueryParamsSchema = QueryParamsSchema.extend({
  isActive: z.enum(['true', 'false']).optional(),
  lastSyncedAt: z.dateString().optional(),
  platform: z.string().optional(),
});

const UpdateUnitsOfLocationSchema = z.object({
  id: z.string().refine((value) => isValidObjectId(value)),
  units: z.array(UpdateLocationUnitSchema),
});

const GetLocationGroupNamesSchema = QueryParamsSchema.pick({
  _q: true,
}).extend({
  type: z.enum([
    LocationAdditionalType.FEATURE_AND_SUPPLIER,
    LocationAdditionalType.CERTIFICATE_AND_CONTROL,
    LocationAdditionalType.GWE_AND_METER_READING,
  ]),
});

const GetLocationAdditionalSchema = LocationIdSchema.merge(
  GetLocationGroupNamesSchema,
);

const baseAdditionalObject = {
  _id: z
    .string()
    .refine((value) => isValidObjectId(value))
    .optional(),
  type: z.enum([
    LocationAdditionalType.FEATURE_AND_SUPPLIER,
    LocationAdditionalType.CERTIFICATE_AND_CONTROL,
    LocationAdditionalType.GWE_AND_METER_READING,
  ]),
  groupName: z.string().refine((value) => isValidObjectId(value)),
  position: z.number(),
  isDeleted: z.boolean().optional(),
};

const baseAdditionalSchema = z.strictObject(baseAdditionalObject).passthrough();

const FeatureAndSupplierSchema = z.object({
  ...baseAdditionalObject,
  groupType: z.enum([
    LocationAdditionalGroupType.NONE,
    LocationAdditionalGroupType.OWNER,
    LocationAdditionalGroupType.EEAC,
    LocationAdditionalGroupType.CUSTOMER,
  ]),
  type: z.enum([LocationAdditionalType.FEATURE_AND_SUPPLIER]),
  contact: z
    .string()
    .nullable()
    .refine((value) => isValidObjectId(value) || value === null)
    .optional(),
  contract: z
    .string()
    .nullable()
    .refine((value) => isValidObjectId(value) || value === null)
    .optional(),
  code: z.string().max(MAX_DESCRIPTION_LENGTH).optional(),
  description: z.string().max(MAX_TEXT_LENGTH),
});

const RecordLogsSchema = z.object({
  date: z.dateString(),
  position: z.number(),
  number: z.string().max(MAX_DESCRIPTION_LENGTH),
});

// gwe_and_meter_reading
const GweAndMeterReadingSchema = z.object({
  ...baseAdditionalObject,
  type: z.enum([LocationAdditionalType.GWE_AND_METER_READING]),
  contact: z
    .string()
    .nullable()
    .refine((value) => isValidObjectId(value) || value === null)
    .optional(),
  contract: z
    .string()
    .nullable()
    .refine((value) => isValidObjectId(value) || value === null)
    .optional(),
  smartMeter: z.boolean().optional(),
  meterNumber: z.string().max(MAX_DESCRIPTION_LENGTH).optional(),
  contractNumber: z.string().max(MAX_DESCRIPTION_LENGTH).optional(),
  recordLogs: z.array(RecordLogsSchema).optional(),
});

// certificate_and_control
const CertificateAndControlSchema = z.object({
  ...baseAdditionalObject,
  type: z.enum([LocationAdditionalType.CERTIFICATE_AND_CONTROL]),
  inspectionType: z.enum([
    CertificateAndControlInspectionType.MAINTENANCE,
    CertificateAndControlInspectionType.INSPECTION,
    CertificateAndControlInspectionType.CERTIFICATION,
    CertificateAndControlInspectionType.SAMPLING,
  ]),
  contact: z
    .string()
    .refine((value) => isValidObjectId(value) || value === null)
    .nullable()
    .optional(),
  description: z.string().max(MAX_TEXT_LENGTH),
  dateCheck: z.dateString(),
  name: z.string().max(MAX_DESCRIPTION_LENGTH),
  groupType: z.enum([
    LocationAdditionalGroupType.OWNER,
    LocationAdditionalGroupType.EEAC,
    LocationAdditionalGroupType.CUSTOMER,
  ]),
  brandType: z.string().max(MAX_DESCRIPTION_LENGTH).optional(),
  yearInstallation: z.number().optional().nullable(),
});

const CreateUpdateLocationAdditionalSchema = z
  .object({
    id: z.string().refine((value) => isValidObjectId(value)),
    type: z.enum([
      LocationAdditionalType.FEATURE_AND_SUPPLIER,
      LocationAdditionalType.CERTIFICATE_AND_CONTROL,
      LocationAdditionalType.GWE_AND_METER_READING,
    ]),
    items: z.array(baseAdditionalSchema),
  })
  .superRefine((value, ctx) => {
    switch (value.type) {
      case LocationAdditionalType.CERTIFICATE_AND_CONTROL:
        value.items.forEach((item) => {
          if (!CertificateAndControlSchema.safeParse(item).success) {
            const error = CertificateAndControlSchema.safeParse(item).error;
            error?.issues.forEach((issue) => {
              return ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: issue.message,
                path: issue.path,
              });
            });
          }
        });
        break;

      case LocationAdditionalType.FEATURE_AND_SUPPLIER:
        value?.items?.forEach((item) => {
          if (!FeatureAndSupplierSchema.safeParse(item).success) {
            const error = FeatureAndSupplierSchema.safeParse(item).error;
            error?.issues.forEach((issue) => {
              return ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: issue.message,
                path: issue.path,
              });
            });
          }
        });
        break;

      case LocationAdditionalType.GWE_AND_METER_READING:
        value?.items?.forEach((item) => {
          if (!GweAndMeterReadingSchema.safeParse(item).success) {
            const error = GweAndMeterReadingSchema.safeParse(item).error;
            error?.issues.forEach((issue) => {
              return ctx.addIssue({
                code: z.ZodIssueCode.custom,
                message: issue.message,
                path: issue.path,
              });
            });
          }
        });
        break;

      default:
        break;
    }
  });

//#region  Stats occupants
const GetLocationNearByQuerySchema = z.strictObject({
  centerLng: z.string().optional(),
  centerLat: z.string().optional(),
  distance: z.string().optional(),
  team: z.string().optional(),
  locationType: z.enum(['renting', 'service']).optional(),
  isVacanted: z.enum(['true']).optional(),
});
//#endregion

export class CreateLocationDto extends createZodDto(CreateLocationSchema) {}

export class UpdateLocationDto extends createZodDto(UpdateLocationSchema) {}

export class LocationIdDto extends createZodDto(LocationIdSchema) {}
export class LocationQueryParamDto extends createZodDto(
  LocationQueryParamsSchema.merge(QueryParamsSchema),
) {}

export class GetUnitsOfLocationDto extends createZodDto(LocationIdSchema) {}

export class UpdateUnitsOfLocationDto extends createZodDto(
  UpdateUnitsOfLocationSchema,
) {}

export class GetLocationAdditionalDto extends createZodDto(
  GetLocationAdditionalSchema,
) {}

export class GetLocationGroupNamesDto extends createZodDto(
  GetLocationGroupNamesSchema,
) {}

export class CreateUpdateLocationAdditionalDto extends createZodDto(
  CreateUpdateLocationAdditionalSchema,
) {}

export class GetLocationNearByQueryDto extends createZodDto(
  GetLocationNearByQuerySchema,
) {}
