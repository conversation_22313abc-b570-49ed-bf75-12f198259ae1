import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    description: z.string(),
    position: z.number(),
    price: z.number(),
    quantity: z.number(),
    startDate: z.date(),
    endDate: z.date().nullish(),
    futureGenerationDate: z.date(),
    unit: z.instanceof(ObjectId),
    agreementLine: z.instanceof(ObjectId),
    costType: z.instanceof(ObjectId),
  })
  .extend(baseModelTestSchema);

const bulkEditSchema = z.array(modelSchema);

export const costLineGeneralTest = {
  modelSchema,
  bulkEditSchema,
};
