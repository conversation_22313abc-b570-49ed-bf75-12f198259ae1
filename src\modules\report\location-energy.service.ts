import { Injectable } from '@nestjs/common';
import { ReturnModelType } from '@typegoose/typegoose/lib/types';
import dayjs from 'dayjs';

import { InjectModel } from '~/transformers/model.transformer';

import { LocationModel } from '../location/location.model';

@Injectable()
export class LocationEnergyService {
  constructor(
    @InjectModel(LocationModel)
    private readonly locationModel: ReturnModelType<typeof LocationModel>,
  ) {}

  async getEnergyReport(payload: any) {
    const { sortBy = 'fullAddress', sortDir = 'asc' } = payload;
    const collation = { locale: 'en' };

    return this.locationModel
      .find({}, { fullAddress: 1, energyLabel: 1 })
      .collation(collation)
      .sort({ [sortBy]: sortDir === 'asc' ? 1 : -1 })
      .exec();
  }

  async exportEnergyReport(payload: any) {
    const rawData = await this.getEnergyReport(payload);

    const header = [
      { field: 'fullAddress', title: 'Locatie' },
      { field: 'energyLabel', title: 'Energielabel' },
    ];
    const data = rawData.map((item) => ({
      fullAddress: item.fullAddress,
      energyLabel: item.energyLabel,
    }));
    const dateSuffix = dayjs().format('DD-MM-YYYY');

    return {
      fileName: `location-energy-report-${dateSuffix}.csv`,
      header,
      data,
    };
  }
}
