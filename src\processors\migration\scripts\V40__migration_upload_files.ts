import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import * as path from 'path';

import { migrationV2 } from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldUploadFile {
  _id: string;
  name: string;
  ext: string;
  mime: string;
  size: string;
  url: string;
  provider: string;
  createdAt: Date;
  updatedAt: Date;
}

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldUploadFile[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (item) => {
      const splitData = item.url.split('/');
      let folderPathOnStorage = '';
      for (let i = 4; i < splitData.length - 1; i++) {
        if (folderPathOnStorage === '') {
          folderPathOnStorage = splitData[i];
        } else {
          folderPathOnStorage = folderPathOnStorage + '/' + splitData[i];
        }
      }
      return {
        _id: item._id,
        originalFilename: item.name,
        filenameOnStorage: splitData[splitData.length - 1],
        folderPathOnStorage: folderPathOnStorage,
        extension: item.ext,
        size: Number(item.size),
        publicUrl: item.url,
        provider: 'gcs',
        isDeleted: false,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      };
    }),
  );
};

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection
    .aggregate()
    .match({
      _id: { $gt: nextId },
    })
    .sort({ _id: 1 })
    .limit(limit);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'upload_file',
      destinationCollectionName: 'uploadfiles',
      pagingFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
