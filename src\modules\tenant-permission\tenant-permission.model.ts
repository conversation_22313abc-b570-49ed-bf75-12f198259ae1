import {
  DocumentType,
  modelOptions,
  mongoose,
  plugin,
  prop,
  Severity,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import { TenantDocument, TenantModel } from '../tenant/tenant.model';

export type TenantPermissionDocument = DocumentType<TenantPermissionModel>;

@modelOptions({
  options: { customName: 'TenantPermission', allowMixed: Severity.ALLOW },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class TenantPermissionModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop()
  key!: string;

  @prop()
  decimal!: mongoose.Types.Decimal128;

  @prop({ ref: () => TenantModel })
  tenant!: TenantDocument;
}
