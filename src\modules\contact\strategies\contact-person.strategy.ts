import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import mongoose, { Model } from 'mongoose';
import { nanoid } from 'nanoid';

import { AddressModel } from '~/modules/address/address.model';
import { CountryService } from '~/modules/country/country.service';
import { ContactRole, ContactType } from '~/shared/enums/contact.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { CONTACT_MESSAGE_KEYS } from '~/shared/message-keys/contact.message-key';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery, QueryParams } from '~/utils';

import { ContactModel, ContactOrganizationPersonModel } from '../contact.model';
import { ContactService } from '../contact.service';
import { ContactStrategy } from './contact-strategy.interface';

@Injectable()
export class ContactPersonStrategy implements ContactStrategy {
  constructor(
    @InjectModel(ContactModel)
    private readonly contactModel: MongooseModel<ContactModel>,
    @InjectModel(AddressModel)
    private readonly addressModel: Model<AddressModel>,
    @InjectModel(ContactOrganizationPersonModel)
    private readonly contactOrganizationPersonModel: Model<ContactOrganizationPersonModel>,

    @Inject(forwardRef(() => ContactService))
    private readonly contactService: ContactService,

    private readonly countryService: CountryService,
  ) {}

  async getValidContact(id: string): Promise<any> {
    return await this.contactModel
      .findOne(
        {
          _id: id,
          contactRole: ContactRole.PERSON,
          isActive: true,
          isDeleted: false,
        },
        { _id: 1, displayName: 1, isInternal: 1 },
      )
      .lean();
  }
  async findAll(params: QueryParams): Promise<any> {
    const { query, options } = buildQuery(params, [
      'displayName',
      'lastName',
      'fistName',
      'email',
      'phone1',
      'organizationNames',
    ]);

    return this.contactModel.paginate(
      {
        ...query,
        contactType: ContactType.PERSON,
      },
      {
        ...options,
        select:
          'isActive lastName firstName email phone1 displayName organizationNames contactType contactRole',
      },
    );
  }

  async findOne(id: string): Promise<any> {
    const selectedFields = [
      '_id',
      'isActive',
      'lastName',
      'firstName',
      'email',
      'warningEmail',
      'gender',
      'language',
      'phone1',
      'phone2',
      'address1',
      'address2',
      'organizations',
      'remark',
      'contactType',
      'contactRole',
    ];

    const person = await this.contactModel
      .findOne({
        _id: id,
        contactRole: ContactRole.PERSON,
      })
      .populate([
        {
          path: 'address1',
          select: 'country region city street number suffix postalCode',
          populate: [
            {
              path: 'country',
              select: '_id name code',
            },
            {
              path: 'region',
              select: '_id name',
            },
          ],
        },
        {
          path: 'address2',
          select: 'country region city street number suffix postalCode',
          populate: [
            {
              path: 'country',
              select: '_id name code',
            },
            {
              path: 'region',
              select: '_id name',
            },
          ],
        },
      ])
      .select(selectedFields.join(' '))
      .lean();

    if (!person) {
      throw new NotFoundException(CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND);
    }

    const organizations =
      await this.contactService.getPersonsOrOrganizationsContact(
        ContactType.PERSON,
        person._id.toString(),
      );

    return {
      ...person,
      organizations,
    };
  }

  async create(data: any): Promise<any> {
    const { address1, address2, organizations, contactType, ...rest } = data;

    await this.countryService.validatePostalCode(address1, 'contact');
    await this.countryService.validatePostalCode(address2, 'contact');

    const person = await this.contactModel.create({
      ...rest,
      displayName: `${rest.firstName} ${rest.lastName}`,
      contactType: ContactType.PERSON,
      contactRole: ContactRole.PERSON,
      identifier: nanoid(),
    });

    const referencePayload = {} as {
      address1?: mongoose.Types.ObjectId;
      address2?: mongoose.Types.ObjectId;
      organizations?: mongoose.Types.ObjectId[];
    };

    let organizationNames = '';

    if (address1) {
      const result = await this.addressModel.create({
        contact: person._id,
        ...address1,
      });

      referencePayload.address1 = result._id;
    }

    if (address2) {
      const result = await this.addressModel.create({
        contact: person._id,
        ...address2,
      });

      referencePayload.address2 = result._id;
    }

    if (organizations?.length > 0) {
      const contactOrganization =
        await this.contactService.validateOrganization(organizations);

      await Promise.all(
        contactOrganization.map((item: any) =>
          this.contactOrganizationPersonModel.create({
            person: person._id,
            organization: item.organization,
            roleFunction: item.roleFunction,
          }),
        ),
      );

      organizationNames = contactOrganization.map((org) => org.name).join(', ');
    }

    await this.contactModel.findByIdAndUpdate(
      person._id,
      {
        ...referencePayload,
        organizationNames,
      },
      { new: true },
    );

    return this.findOne(person._id.toString());
  }

  async update(id: string, data: any): Promise<any> {
    const { address1, address2, organizations, contactType, ...rest } = data;

    await this.countryService.validatePostalCode(address1, 'contact');
    await this.countryService.validatePostalCode(address2, 'contact');

    const updatedPerson = await this.contactModel.findByIdAndUpdate(
      id,
      {
        ...rest,
        displayName: `${rest.firstName} ${rest.lastName}`,
        contactType: ContactType.PERSON,
        contactRole: ContactRole.PERSON,
      },
      { new: true },
    );

    if (!updatedPerson) {
      throw new BadRequestException(
        CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND,
      );
    }

    const referencePayload = {} as {
      address1?: mongoose.Types.ObjectId;
      address2?: mongoose.Types.ObjectId | null;
    };

    let organizationNames = '';

    if (address1) {
      const result = await this.addressModel.findOneAndUpdate(
        { _id: updatedPerson.address1 },
        {
          ...address1,
          contact: updatedPerson._id,
        },
        {
          new: true,
        },
      );

      result && (referencePayload.address1 = result._id);
    }

    await this.addressModel.deleteOne({ _id: updatedPerson.address2 });
    if (address2) {
      const result = await this.addressModel.create({
        contact: updatedPerson._id,
        ...address2,
      });

      referencePayload.address2 = result._id;
    } else {
      referencePayload.address2 = null;
    }

    if (organizations?.length > 0) {
      const contactOrganizations =
        await this.contactService.validateOrganization(organizations);

      await this.contactOrganizationPersonModel.deleteMany({ person: id });

      Promise.all(
        contactOrganizations.map((organization) =>
          this.contactOrganizationPersonModel.create({
            person: id,
            organization: organization.organization,
            roleFunction: organization.roleFunction,
          }),
        ),
      );

      organizationNames = contactOrganizations
        .map((org) => org.name)
        .join(', ');
    }

    await this.contactModel.findByIdAndUpdate(
      id,
      {
        ...referencePayload,
        organizationNames,
      },
      { new: true },
    );

    return this.findOne(id);
  }
}
