import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { ContractModel } from '~/modules/contract/contract.model';
import { contractTest } from '~/modules/contract/test/contract.dto.test';
import { ContractType } from '~/shared/enums/contract.enum';

import { mockAgreementLineData } from './agreementline.mock';
import { mockContactData } from './contact.mock';
import { mockContractTypeData } from './contracttype.mock';
import { mockLocationData } from './location.mock';

const contractModel = getModelForClass(ContractModel);
type contractType = z.infer<typeof contractTest.modelSchema>;

export const mockContractData = {
  _id: new ObjectId(),
  agreementLines: [mockAgreementLineData._id],
  attachments: [],
  contact: mockContactData._id,
  contractType: mockContractTypeData._id,
  createdAt: new Date(),
  endDate: new Date(),
  generatePeriod: 3,
  identifier: '20202020',
  isActive: true,
  isDeleted: false,
  isGenerateCostLine: true,
  isNew: false,
  isSigned: true,
  isWholeLocation: true,
  location: mockLocationData._id,
  note: 'This is note for contract',
  noticeDays: '1 month',
  signedAt: new Date(),
  startDate: new Date(),
  type: ContractType.RENTING,
  updatedAt: new Date(),
};

export async function initMockContract(doc?: Partial<contractType>) {
  const { _id, ...rest } = { ...mockContractData, ...doc };
  await contractModel.replaceOne({ _id }, rest, { upsert: true });
}
