import { PluginOptions } from 'ts-patch-mongoose';

export const EV_CREATED = 'created';
export const EV_UPDATED = 'updated';
export const EV_DELETED = 'deleted';

export const getDefaultPluginOptions = <T>(
  extendedOmits?: string[],
): PluginOptions<T> => {
  if (extendedOmits?.length) {
    return {
      omit: ['__v', 'createdAt', 'updatedAt', ...extendedOmits],
      patchHistoryDisabled: false,
    };
  }

  return {
    omit: ['__v', 'createdAt', 'updatedAt'],
    patchHistoryDisabled: false,
  };
};
