import {
  DocumentType,
  index,
  modelOptions,
  prop,
  Ref,
  Severity,
} from '@typegoose/typegoose';

import { UploadFileProviderEnum } from '~/shared/enums/upload-file-provider.enum';
import { BaseModel } from '~/shared/models/base.model';

import {
  LocationFileDocument,
  LocationFileModel,
} from '../location-file/location-file.model';
import { DocumentFileDocument, DocumentFileModel } from './document-file.model';

export type UploadFileDocument = DocumentType<UploadFileModel>;

@modelOptions({
  options: {
    customName: 'UploadFile',
    allowMixed: Severity.ALLOW,
  },
})
@index({ publicUrl: 1 })
export class UploadFileModel extends BaseModel {
  @prop({ required: true, trim: true })
  originalFilename!: string;

  @prop({ required: true, trim: true })
  filenameOnStorage!: string;

  @prop({ required: true, trim: true })
  folderPathOnStorage!: string;

  @prop({ required: true, trim: true })
  extension!: string;

  @prop({ required: true, trim: true })
  mimeType!: string;

  @prop({ required: true })
  size!: number;

  @prop({ trim: true })
  publicUrl!: string;

  @prop({ enum: UploadFileProviderEnum })
  provider!: UploadFileProviderEnum;

  @prop({ ref: () => DocumentFileModel })
  documentFile!: Ref<DocumentFileDocument>;

  // location file
  @prop({ ref: () => LocationFileModel })
  locationFile?: Ref<LocationFileDocument>;
}
