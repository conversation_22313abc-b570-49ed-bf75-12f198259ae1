import { Injectable } from '@nestjs/common';

import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';

import { EmailTemplateModel } from './email-template.model';

@Injectable()
export class EmailTemplateService {
  constructor(
    @InjectModel(EmailTemplateModel)
    private readonly emailTemplateModel: MongooseModel<EmailTemplateModel>,
  ) {}

  async getEmailTemplateByName(
    name: string,
  ): Promise<EmailTemplateModel | null> {
    return await this.emailTemplateModel.findOne({ name }).lean();
  }
}
