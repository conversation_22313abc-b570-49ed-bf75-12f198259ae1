import { Injectable, Logger } from '@nestjs/common';

import { ContractService } from '~/modules/contract/contract.service';
import { CostlineService } from '~/modules/costline/costline.service';
import { JobService } from '~/modules/job/job.service';
import { NightRegistrationService } from '~/modules/night-registration/night-registration.service';
import { StatsOccupantService } from '~/modules/stats-occupant/stats-occupant.service';

@Injectable()
export class ScheduleService {
  private readonly logger = new Logger(ScheduleService.name);

  constructor(
    private readonly jobService: JobService,
    private readonly contractService: ContractService,
    private readonly costLineService: CostlineService,
    private readonly nightRegistrationService: NightRegistrationService,
    private readonly statsOccupantService: StatsOccupantService,
  ) {}

  async generatePeriodicJob(payload: any) {
    this.logger.log('Trigger Generate Periodic Job');
    await this.jobService.generateScheduleJob(payload);

    return {
      ok: 1,
    };
  }

  async deactiveContractByCronJob() {
    this.logger.log('Trigger deactive contract by Schedule Job');
    await this.contractService.deactiveContractByCronJob();

    return {
      ok: 1,
    };
  }

  async generateCostLineByPeriodByCronJob(data?: any) {
    this.logger.log(
      `Trigger generate cost line by period by Schedule Job with fakeCurrentDate: ${data.fakeCurrentDate}`,
    );
    await this.costLineService.generateCostLineByPeriodByCronJob(data);

    return {
      ok: 1,
    };
  }

  async sendMailResident(payload: any) {
    this.logger.log('Trigger send mail resident by Schedule Job');
    await this.nightRegistrationService.CronJobSendMailResident(payload);

    return { ok: 1 };
  }

  async calculateHiredLocations(payload: any) {
    this.logger.log('Trigger calculate hired locations by Schedule Job');
    await this.statsOccupantService.calculateHiredLocations(payload);

    return { ok: 1 };
  }

  async sendEmailResidentHasExceededMaximumLengthOfStay(payload: any) {
    this.logger.log(
      `Trigger calculate resident has exceeded maximum length of stay by Schedule Job with fakeCurrentDate: ${payload.fakeCurrentDate}`,
    );
    await this.nightRegistrationService.sendEmailResidentHasExceededMaximumLengthOfStay(
      payload,
    );

    return { ok: 1 };
  }
}
