import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { CheckOutService } from '~/modules/night-registration/check-out/check-out.service';
import { NIGHT_REGISTRATION_MESSAGES } from '~/shared/messages/night-registration.message';

import {
  ReservationCreateBodyDto,
  ReservationQueryParamsDto,
  ReservationUpdateBodyDto,
  VirtualReservationCreateBodyDto,
  WarningCreateBodyDto,
} from './dtos/night-registration.dto';
import { LentoIntegrateCheckinService } from './lento-integrate-checkin.service';
import { NationalityService } from './nationality.service';
import { NightRegistrationService } from './night-registration.service';
import { TenantMovedService } from './tenant-moved.service';

@Controller('night-registration')
export class NightRegistratrionController {
  constructor(
    private readonly nightRegistrationService: NightRegistrationService,
    private readonly nationalityService: NationalityService,
    private readonly tenantMovedService: TenantMovedService,
    private readonly lentoIntegrateCheckinService: LentoIntegrateCheckinService,
    private readonly checkOutService: CheckOutService,
  ) {}

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_GET_LIST_RESERVATION })
  async getListReservation(@Payload() params: ReservationQueryParamsDto) {
    return this.nightRegistrationService.getListReservation(params);
  }

  @MessagePattern({
    cmd: NIGHT_REGISTRATION_MESSAGES.NR_GET_CONTACT_BY_LOCATION,
  })
  async getContactByLocation(@Payload() payload: any) {
    const { location } = payload;
    return this.nightRegistrationService.getContactsByLocation(location);
  }

  @HTTPDecorators.Paginator
  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_GET_LIST_RESIDENTS })
  async getListResidents(@Payload() payload: any) {
    return this.nightRegistrationService.getListResidents(payload);
  }

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_CREATE_RESERVATION })
  @UsePipes(new ZodValidationPipe(ReservationCreateBodyDto))
  async createReservation(@Payload() payload: ReservationCreateBodyDto) {
    return this.nightRegistrationService.createReservation(payload);
  }

  @MessagePattern({
    cmd: NIGHT_REGISTRATION_MESSAGES.NR_CREATE_VIRTUAL_RESERVATION,
  })
  @UsePipes(new ZodValidationPipe(VirtualReservationCreateBodyDto))
  async createVirtualReservation(
    @Payload() payload: VirtualReservationCreateBodyDto,
  ) {
    return this.nightRegistrationService.createVirtualReservation(payload);
  }

  @MessagePattern({
    cmd: NIGHT_REGISTRATION_MESSAGES.NR_DELETE_VIRTUAL_RESERVATION,
  })
  async deleteVirtualReservation(@Payload() payload: any) {
    const { id } = payload;
    return this.nightRegistrationService.deleteVirtualReservation(id);
  }

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_UPDATE_RESERVATION })
  @UsePipes(new ZodValidationPipe(ReservationUpdateBodyDto))
  async updateReservation(@Payload() payload: ReservationUpdateBodyDto) {
    return this.nightRegistrationService.updateReservation(payload);
  }

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_DELETE_RESERVATION })
  async deleteReservation(@Payload() payload: any) {
    const { id } = payload;
    return this.nightRegistrationService.deleteReservation(id);
  }

  @HTTPDecorators.Paginator
  @MessagePattern({
    cmd: NIGHT_REGISTRATION_MESSAGES.NR_GET_WARNING_CATEGORIES,
  })
  async getWarningCategories(@Payload() payload: any) {
    return this.nightRegistrationService.getWarningCategories(payload);
  }

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_CREATE_WARNING })
  @UsePipes(new ZodValidationPipe(WarningCreateBodyDto))
  async createWarning(@Payload() payload: any) {
    return this.nightRegistrationService.createWarning(payload);
  }

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_GET_LIST_WARNINGS })
  async getListWarnings(@Payload() payload: any) {
    return this.nightRegistrationService.getWarnings(payload);
  }

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_DELETE_WARNING })
  async deleteWarning(@Payload() payload: any) {
    const { id } = payload;
    return this.nightRegistrationService.deleteWarning(id);
  }

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_IMPORT_RESIDENTS })
  async importResidents(@Payload() payload: any) {
    const { file } = payload;
    return this.nightRegistrationService.importResidents(file);
  }

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_EXPORT_RESERVATION })
  async exportReservation(@Payload() payload: any) {
    return this.nightRegistrationService.exportReservation(payload);
  }

  @HTTPDecorators.Paginator
  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_GET_NATIONALITIES })
  async getNationalities(@Payload() payload: any) {
    return this.nationalityService.getNationalities(payload);
  }
  @MessagePattern({
    cmd: NIGHT_REGISTRATION_MESSAGES.NR_LENTO_INTEGRATE_PROCESS_CHECKIN,
  })
  async lentoIntegrateProcessCheckIn(@Payload() payload: any) {
    return this.lentoIntegrateCheckinService.lentoIntegrateProcessCheckIn(
      payload,
    );
  }

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_PROCESS_TENANT_MOVED })
  async processTenantMoved(@Payload() payload: any) {
    console.log(
      'Core service received tenant moved data via TCP:',
      JSON.stringify(payload, null, 2),
    );
    return this.tenantMovedService.processTenantMoved(payload);
  }

  @MessagePattern({ cmd: NIGHT_REGISTRATION_MESSAGES.NR_PROCESS_CHECK_OUT })
  async processCheckOut(@Payload() payload: any) {
    return this.checkOutService.checkOut(payload);
  }
}
