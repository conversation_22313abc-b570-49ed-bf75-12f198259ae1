import { getModelForClass } from '@typegoose/typegoose';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';

import { StatsOccupantModel } from '~/modules/stats-occupant/stats-occupant.model';

import { mockContractData } from './contract.mock';
import { mockCostCenterData } from './costcenter.mock';
import { mockLocationData } from './location.mock';
import { mockTeamData } from './team.mock';
import { mockUnitData } from './unit.mock';

const statsOccupantModel = getModelForClass(StatsOccupantModel);

export const mockStatsOccupantData = {
  _id: new ObjectId(),
  location: mockLocationData._id,
  isLocationActive: true,
  isLocationService: false,
  maxCount: mockLocationData.maxOccupants,
  hiredCount: mockLocationData.maxOccupants,
  emptyCount: 0,
  hiredUnits: [mockUnitData._id],
  unHiredUnits: [],
  creditorContracts: [],
  debtorContracts: [mockContractData._id],
  locationInfo: {
    _id: mockLocationData._id,
    fullAddress: mockLocationData.fullAddress,
  },
  costCenterInfo: {
    _id: mockCostCenterData._id,
    name: mockCostCenterData.name,
  },
  teamInfo: {
    _id: mockTeamData._id,
    name: mockTeamData.name,
  },
  reportCreatedDate: dayjs().utc().startOf('day').toDate(),
};

export const initMockStatsOccupant = async (doc?: any) => {
  const { _id, ...rest } = { ...mockStatsOccupantData, ...doc };
  await statsOccupantModel.replaceOne({ _id }, rest, { upsert: true });
};
