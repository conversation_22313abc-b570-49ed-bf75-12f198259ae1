import { padStart } from 'lodash';
import path from 'path';

import {
  LocationAdditionalGroupName,
  LocationAdditionalType,
} from '~/shared/enums/location-additional.enum';

import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const destinationCollectionName = 'locationadditionalgroupnames';
    const destinationCollection = context
      .destinationClient!.db()
      .collection(destinationCollectionName)!;

    if (!(await destinationCollection.indexExists('identifier_1'))) {
      destinationCollection.createIndex({ identifier: 1 }, { unique: true });
    }

    const padPrefix = (id: number) => padStart(id.toString(), 9, '0');
    const groupNames = [
      {
        identifier: padPrefix(1),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.GARBAGE.toString(),
        dutchDescription: 'Afval',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(2),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.INTERNET.toString(),
        dutchDescription: 'Internet',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(3),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.KITCHEN_APPLIANCES.toString(),
        dutchDescription: 'Keukenapparatuur',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(4),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description:
          LocationAdditionalGroupName.WASHING_MACHINE_AND_DRYER.toString(),
        dutchDescription: 'Wasmachine & Droger',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(5),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.UPHOLSTERY.toString(),
        dutchDescription: 'Stoffering',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(6),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.FURNITURE.toString(),
        dutchDescription: 'Meubilering',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(7),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.GARDENS.toString(),
        dutchDescription: 'Tuinen',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(8),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.LOCKCARD_SYSTEM.toString(),
        dutchDescription: 'Sloten/card systeem',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(9),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.VENDING_MACHINES.toString(),
        dutchDescription: 'Vendingmachines',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(10),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.CAMERA_SYSTEN.toString(),
        dutchDescription: 'Camerasysteem',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(11),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.SECURITY.toString(),
        dutchDescription: 'Beveiliging',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(12),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.KEY_PLAN.toString(),
        dutchDescription: 'Sleutelplan',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(13),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.PEST_CONTROL.toString(),
        dutchDescription: 'Ongediertebestrijding',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(14),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.LEGIONELLA_BEHEER.toString(),
        dutchDescription: 'Legionella beheer',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(15),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        description: LocationAdditionalGroupName.OTHERS.toString(),
        dutchDescription: 'Overig',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(16),
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        description: LocationAdditionalGroupName.HEATING_VERSION.toString(),
        dutchDescription: 'Warmte voorziening',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(17),
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        description: LocationAdditionalGroupName.FIRE_KILL_RESOURCES.toString(),
        dutchDescription: 'Brandblusmiddelen e.d.',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(18),
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        description: LocationAdditionalGroupName.FIRE_INSTALLATION.toString(),
        dutchDescription: 'Brandmeldinstallatie',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(19),
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        description: LocationAdditionalGroupName.LEGIONELLA.toString(),
        dutchDescription: 'Legionella',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(20),
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        description: LocationAdditionalGroupName.THERMOSTAT.toString(),
        dutchDescription: 'Thermostaat',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(21),
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
        description: LocationAdditionalGroupName.OTHERS.toString(),
        dutchDescription: 'Overig',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(22),
        type: LocationAdditionalType.GWE_AND_METER_READING,
        description: LocationAdditionalGroupName.GAS.toString(),
        dutchDescription: 'Gas',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(23),
        type: LocationAdditionalType.GWE_AND_METER_READING,
        description: LocationAdditionalGroupName.WATER.toString(),
        dutchDescription: 'Water',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(24),
        type: LocationAdditionalType.GWE_AND_METER_READING,
        description: LocationAdditionalGroupName.ELEKTRA_1.toString(),
        dutchDescription: 'Elektra 1',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
      {
        identifier: padPrefix(25),
        type: LocationAdditionalType.GWE_AND_METER_READING,
        description: LocationAdditionalGroupName.ELEKTRA_2.toString(),
        dutchDescription: 'Elektra 2',
        isDeleted: false,
        updatedAt: new Date(),
        createdAt: new Date(),
      },
    ];

    const upsertPromises = groupNames.map((doc) =>
      destinationCollection
        .findOneAndUpdate(
          { identifier: doc.identifier },
          { $set: doc },
          { upsert: true, returnDocument: 'after' }, // Use returnDocument: 'after' to get the updated document
        )
        .then(() => {
          console.log(
            `Migrated location additional group name with identifier=${doc.identifier} into collection ${destinationCollectionName}`,
          );
        })
        .catch((error) => {
          console.error(
            `Error upserting document with identifier=${doc.identifier}:`,
            error,
          );
        }),
    );

    await Promise.all(upsertPromises)
      .then(() => {
        console.log(
          `Migrated ${groupNames.length} documents to collection ${destinationCollectionName}`,
        );
      })
      .catch((error) => {
        console.error('Error during upsert operations:', error);
      });
    const after = new Date().getTime();
    console.log(
      `Migration script ${fileName} completed in ${after - before}ms`,
    );
  } catch (error) {
    console.error(`Error in migration script ${fileName}: ${error}`);
  }
};
export default up;
