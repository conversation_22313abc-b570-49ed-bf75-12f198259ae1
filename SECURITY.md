# Security Policy

## Brand Promise

Keeping user information safe and secure is a top priority, and we welcome the contribution of external security researchers.

We take security very seriously. If you discover a security vulnerability within our repository, please report it by sending an email to our security team (<EMAIL>).
We appreciate your efforts to responsibly disclose your findings.

## Safe Harbor

We support safe harbor for security researchers who:

* Make a good faith effort to avoid privacy violations, destruction of data, and interruption or degradation of our services.
* Only interact with accounts you own or with explicit permission of the account holder. 
  If you do encounter Personally Identifiable Information (PII) contact us immediately, do not proceed with access, and immediately purge any local information.
* Provide us with a reasonable amount of time to resolve vulnerabilities prior to any disclosure to the public or a third party.

We will consider activities conducted consistent with this policy to constitute "authorized" conduct and will not pursue civil action or initiate a complaint to law enforcement. 

Please submit a report to us before engaging in conduct that may be inconsistent with or unaddressed by this policy.

**Social engineering (e.g. phishing, vishing, smishing) is prohibited.**

## How to Submit a Report

To submit a vulnerability report, please contact <NAME_EMAIL>.
Your submission will be reviewed and validated by a member of our team.

If English is not your first language, please try to describe the problem and its impact to the best of your ability. 
For greater detail, please use your native language and we will try our best to translate it using online services.

### Preferences

To ensure constructive reporting, please include as much information as possible. 
Ideally, that would involve:
- A description of the vulnerability.
- Steps to reproduce the issue.
- The potential impact of the vulnerability.
- Any potential solutions or mitigations, if known.
- Please also include the code you used to find the problem and the shortest amount of code necessary to reproduce it.
- Please include the version number of the vulnerable package in your report

## Process

We promise to address the issue as quickly as possible. 
After the initial reply to your report, our team will keep you informed about the progress towards a fix and full mitigation.
We will make efforts to acknowledge your contributions.

> Thank you for improving the security of Infodation.
> 
> The Infodation team
