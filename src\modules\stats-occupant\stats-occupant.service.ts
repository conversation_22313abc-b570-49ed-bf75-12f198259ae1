import { BadRequestException, Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import _, { flatMap } from 'lodash';
import mongoose, { Model } from 'mongoose';

import { DATE_FORMAT_HYPHEN } from '~/constants/app.constant';
import {
  AgreementLinePeriodType,
  AgreementLineType,
  ContractType,
} from '~/shared/enums/contract.enum';
import { InjectModel } from '~/transformers/model.transformer';

import { LocationModel } from '../location/location.model';
import { UnitModel } from '../unit/unit.model';
import {
  ListOccupationQueryDto,
  ListVacancyQueryDto,
} from './dtos/stats-occupant.dto';
import { StatsOccupantModel } from './stats-occupant.model';

@Injectable()
export class StatsOccupantService {
  constructor(
    @InjectModel(LocationModel)
    private readonly locationModel: Model<LocationModel>,

    @InjectModel(StatsOccupantModel)
    private readonly statsOccupantModel: Model<StatsOccupantModel>,
    @InjectModel(UnitModel)
    private readonly unitModel: Model<UnitModel>,
  ) {}
  async calculateHiredLocations(payload: any) {
    const { locationIds, fakeCurrentDate } = payload;
    const locationQuery = {
      _id: {
        $exists: true,
      },
      ...(locationIds && locationIds.length > 0
        ? {
            _id: {
              $in: locationIds.map(
                (locationId) => new mongoose.Types.ObjectId(String(locationId)),
              ),
            },
          }
        : {}),
    };

    const currentDate = fakeCurrentDate
      ? dayjs(fakeCurrentDate).utc().startOf('day').toDate()
      : dayjs().utc().startOf('day').toDate();

    const allLocations = await this.locationModel
      .aggregate([
        {
          $match: locationQuery,
        },
        {
          $lookup: {
            from: 'contracts',
            localField: '_id',
            foreignField: 'location',
            as: 'contracts',
            pipeline: [
              {
                $addFields: {
                  startDate: {
                    $dateTrunc: { date: '$startDate', unit: 'day' },
                  },
                  endDate: { $dateTrunc: { date: '$endDate', unit: 'day' } },
                },
              },
              {
                $match: {
                  startDate: { $lte: currentDate },
                  $or: [{ endDate: { $gte: currentDate } }, { endDate: null }],
                },
              },
              {
                $lookup: {
                  from: 'agreementlines',
                  localField: 'agreementLines',
                  foreignField: '_id',
                  as: 'agreementLines',
                  pipeline: [
                    {
                      $match: {
                        type: AgreementLineType.ACCOMMODATION,
                        periodType: AgreementLinePeriodType.PERIODIC,
                      },
                    },
                    {
                      $lookup: {
                        from: 'costlinegenerals',
                        localField: 'costLineGenerals',
                        foreignField: '_id',
                        as: 'costLineGenerals',
                        pipeline: [
                          {
                            $addFields: {
                              startDate: {
                                $dateTrunc: { date: '$startDate', unit: 'day' },
                              },
                              endDate: {
                                $dateTrunc: { date: '$endDate', unit: 'day' },
                              },
                            },
                          },
                          {
                            $match: {
                              startDate: { $lte: currentDate },
                              $or: [
                                { endDate: { $gte: currentDate } },
                                { endDate: null },
                              ],
                            },
                          },
                          {
                            $lookup: {
                              from: 'units',
                              localField: 'unit',
                              foreignField: '_id',
                              as: 'unit',
                              pipeline: [
                                {
                                  $match: {
                                    isDeleted: false,
                                    isActive: true,
                                  },
                                },
                              ],
                            },
                          },
                          {
                            $addFields: {
                              unit: { $arrayElemAt: ['$unit', 0] },
                            },
                          },
                        ],
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'units',
            localField: '_id',
            foreignField: 'location',
            as: 'units',
            pipeline: [
              {
                $match: {
                  isDeleted: false,
                  isActive: true,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'costcenters',
            localField: 'costCenter',
            foreignField: '_id',
            as: 'costCenter',
          },
        },
        {
          $addFields: {
            costCenter: { $arrayElemAt: ['$costCenter', 0] },
          },
        },
        {
          $lookup: {
            from: 'teams',
            localField: 'team',
            foreignField: '_id',
            as: 'team',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  name: 1,
                },
              },
            ],
          },
        },
        {
          $addFields: {
            team: { $arrayElemAt: ['$team', 0] },
          },
        },
        {
          $project: {
            _id: 1,
            fullAddress: 1,
            maxOccupants: 1,
            costCenter: {
              _id: 1,
              identifier: 1,
              name: 1,
            },
            isActive: 1,
            isService: 1,
            contracts: 1,
            units: 1,
            team: 1,
          },
        },
      ])
      .exec();

    if (allLocations.length === 0) {
      return [];
    }

    const promises = allLocations.map(async (location) => {
      const maxCount = location.maxOccupants;
      let hiredCount = 0;
      let emptyCount = 0;
      const hiredUnits: any[] = [];
      const unHiredUnits: any[] = [];
      const allContracts = (location.contracts as any[]) ?? [];

      const creditorContracts = allContracts.filter(
        (contract) => contract.type === ContractType.CREDITOR,
      );

      const debtorContracts = allContracts.filter(
        (contract) => contract.type === ContractType.RENTING,
      );

      const result = {
        location: location._id,
        maxCount,
        hiredCount,
        emptyCount,
        hiredUnits,
        unHiredUnits,
        isLocationActive: location.isActive,
        isLocationService: location.isService ?? false,
        creditorContracts: creditorContracts.map((contract) => contract._id),
        debtorContracts: debtorContracts.map((contract) => contract._id),
        locationInfo: {
          _id: location._id,
          fullAddress: location.fullAddress,
        },
        costCenterInfo: {
          _id: location.costCenter?._id,
          identifier: location.costCenter?.identifier,
          name: location.costCenter?.name,
        },
        teamInfo: {
          _id: location.team?._id,
          name: location.team?.name,
        },
      };

      if (debtorContracts.length === 0) {
        result['emptyCount'] = maxCount;
        return result;
      }

      const costLineGenerals = debtorContracts.flatMap((contract) => {
        const agreementLines = contract.agreementLines ?? [];
        return flatMap(agreementLines, (line) => line.costLineGenerals ?? []);
      });

      if (costLineGenerals.length === 0) {
        result['emptyCount'] = maxCount;
        return result;
      }

      const isHaveDebtorContractRentWholeLocation = debtorContracts.some(
        (contract) => contract.isWholeLocation,
      );

      if (isHaveDebtorContractRentWholeLocation) {
        hiredUnits.push(
          ...location.units
            .filter((unit) => unit.isRoot)
            .map((unit) => unit._id),
        );
        result['hiredCount'] = maxCount;
        result['emptyCount'] = 0;
        result['hiredUnits'] = hiredUnits;
        return result;
      }

      const hiredUnitsMap = costLineGenerals
        .filter((costLineGeneral) => costLineGeneral.unit)
        .map((costLineGeneral) => costLineGeneral.unit);

      const uniqHiredUnits = _.uniqBy(hiredUnitsMap, (unit) =>
        unit._id?.toString(),
      );

      const allUnitsExceptRoot = location.units.filter((unit) => !unit.isRoot);

      const generateUnHiredUnits = await this.generateUnHiredUnits(
        location.units,
        uniqHiredUnits,
      ).then((result: any) => {
        result = this.groupAndSeprateUnitBelongToRootUnit(
          result,
          allUnitsExceptRoot,
        );
        return result;
      });

      const uniqHiredUnitsMap = Object.values(generateUnHiredUnits).flatMap(
        (units) => units,
      );
      unHiredUnits.push(...uniqHiredUnitsMap.map((unit: any) => unit._id));

      hiredCount = this.countHiredUnits(location, uniqHiredUnits);
      emptyCount = maxCount - hiredCount;
      hiredUnits.push(...uniqHiredUnits.map((unit) => unit._id));
      result['hiredCount'] = hiredCount;
      result['emptyCount'] = emptyCount;
      result['hiredUnits'] = hiredUnits;
      result['unHiredUnits'] = unHiredUnits;
      return result;
    });

    let records = await Promise.all(promises);
    records = records.filter((record) => record);

    const createOrUpdatePromises = records.map(async (record) => {
      if (!record) {
        return;
      }
      const result = await this.statsOccupantModel.findOneAndUpdate(
        {
          location: record.location,
          reportCreatedDate: currentDate,
        },
        {
          maxCount: record.maxCount,
          hiredCount: record.hiredCount,
          emptyCount: record.emptyCount,
          hiredUnits: record.hiredUnits,
          isLocationActive: record.isLocationActive,
          isLocationService: record.isLocationService,
          unHiredUnits: record.unHiredUnits,
          creditorContracts: record.creditorContracts,
          debtorContracts: record.debtorContracts,
          locationInfo: record.locationInfo,
          costCenterInfo: record.costCenterInfo,
          teamInfo: record.teamInfo,
          isDeleted: false,
          reportCreatedDate: currentDate,
          markNeedToUpdate: false,
        },
        {
          upsert: true,
          new: true,
        },
      );
      return result;
    });

    return await Promise.all(createOrUpdatePromises);
  }

  async getListOccupation(query: ListOccupationQueryDto) {
    const { dateOfReport, sortBy, sortDir } = query;

    const currentDate = dateOfReport
      ? dayjs(dateOfReport).utc().startOf('day').toDate()
      : dayjs().utc().startOf('day').toDate();

    const checkedDatas = await this.statsOccupantModel
      .find({
        reportCreatedDate: currentDate,
      })
      .lean();

    if (!checkedDatas || checkedDatas.length === 0) {
      await this.calculateHiredLocations({ fakeCurrentDate: currentDate });
    }

    const markNeedToUpdates = checkedDatas.filter((data) => {
      return data.markNeedToUpdate;
    });

    if (markNeedToUpdates.length > 0) {
      await this.calculateHiredLocations({
        locationIds: markNeedToUpdates.map((markNeedToUpdate) =>
          (markNeedToUpdate.location as mongoose.Types.ObjectId).toString(),
        ),
        fakeCurrentDate: currentDate,
      });
    }
    const statsOccupants = await this.statsOccupantModel
      .aggregate([
        {
          $match: {
            reportCreatedDate: currentDate,
            isLocationActive: true,
            isDeleted: false,
            creditorContracts: { $exists: true, $ne: [] },
          },
        },
        {
          $lookup: {
            from: 'units',
            let: {
              unHiredUnits: '$unHiredUnits',
              isLocationService: '$isLocationService',
            },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $in: ['$_id', '$$unHiredUnits'] },
                      { $eq: ['$$isLocationService', false] },
                      { $gt: ['$maxOccupants', 0] },
                    ],
                  },
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  maxOccupants: 1,
                },
              },
            ],
            as: 'unHiredUnits',
          },
        },
        ...(sortBy && sortDir
          ? [
              {
                $sort: {
                  [sortBy]: (sortDir === 'asc' ? 1 : -1) as 1 | -1,
                },
              },
            ]
          : []),
        {
          $project: {
            identifier: '$costCenterInfo.identifier',
            maxCount: 1,
            hiredCount: 1,
            emptyCount: 1,
            minStartDate: 1,
            maxEndDate: 1,
            locationInfo: 1,
            costCenterInfo: 1,
            teamInfo: 1,
            unHiredUnits: 1,
          },
        },
      ])
      .exec();

    return {
      totalMaxCount: _.sumBy(statsOccupants, (item) => item.maxCount),
      totalHiredCount: _.sumBy(statsOccupants, (item) => item.hiredCount),
      totalEmptyCount: _.sumBy(statsOccupants, (item) => item.emptyCount),
      items: statsOccupants,
    };
  }

  async exportListOccupation(query: ListOccupationQueryDto) {
    const { dateOfReport } = query;
    const currentDate = dateOfReport
      ? dayjs(dateOfReport).utc().startOf('day').format(DATE_FORMAT_HYPHEN)
      : dayjs().utc().startOf('day').format(DATE_FORMAT_HYPHEN);
    const occupants = (await this.getListOccupation(query)).items;
    const header = [
      { field: 'dateOfReport', title: 'Date of report' },
      { field: 'costCenter', title: 'Cost Center' },
      { field: 'location', title: 'Location' },
      { field: 'team', title: 'Team' },
      { field: 'maxCount', title: 'Max number of occupants' },
      { field: 'hiredCount', title: 'Hired bed (occupants)' },
      { field: 'unit', title: 'Unit' },
      { field: 'emptyCount', title: 'Empty occupants' },
    ];
    const fileName = `list-occupations-${currentDate}.csv`;
    const csvLines: any[] = [];
    occupants.forEach((item) => {
      csvLines.push({
        dateOfReport: currentDate,
        costCenter: item.costCenterInfo.identifier,
        location: item.locationInfo.fullAddress,
        team: item.teamInfo.name,
        maxCount: item.maxCount,
        hiredCount: item.hiredCount,
        unit: '',
        emptyCount: item.emptyCount,
      });
      item.unHiredUnits.forEach((unit) => {
        csvLines.push({
          dateOfReport: '',
          costCenter: '',
          location: '',
          team: '',
          maxCount: '',
          hiredCount: '',
          unit: unit.name,
          emptyCount: unit.maxOccupants,
        });
      });
    });

    return { data: csvLines, header, fileName };
  }

  async getListVacancies(query: ListVacancyQueryDto) {
    const { dateOfReport, sortBy, sortDir } = query;

    const currentDate = dateOfReport
      ? dayjs(dateOfReport).utc().startOf('day').toDate()
      : dayjs().utc().startOf('day').toDate();

    const checkedDatas = await this.statsOccupantModel
      .find({
        reportCreatedDate: currentDate,
      })
      .exec();

    if (!checkedDatas || checkedDatas.length === 0) {
      await this.calculateHiredLocations({ fakeCurrentDate: currentDate });
    }

    const markNeedToUpdates = checkedDatas.filter((data) => {
      return data.markNeedToUpdate;
    });

    if (markNeedToUpdates.length > 0) {
      this.calculateHiredLocations({
        locationIds: markNeedToUpdates.map((markNeedToUpdate) =>
          (markNeedToUpdate.location as mongoose.Types.ObjectId).toString(),
        ),
        fakeCurrentDate: currentDate,
      });
    }

    const statsOccupants = await this.statsOccupantModel
      .aggregate([
        {
          $match: {
            reportCreatedDate: currentDate,
            isLocationActive: true,
            isDeleted: false,
            creditorContracts: { $exists: true, $ne: [] },
            debtorContracts: { $exists: true, $ne: [] },
            emptyCount: { $gt: 0 },
            isLocationService: false,
          },
        },
        {
          $lookup: {
            from: 'units',
            localField: 'unHiredUnits',
            foreignField: '_id',
            as: 'unHiredUnits',
            pipeline: [
              {
                $match: {
                  maxOccupants: { $gt: 0 },
                },
              },
              {
                $project: {
                  _id: 1,
                  name: 1,
                  maxOccupants: 1,
                },
              },
            ],
          },
        },
        ...(sortBy && sortDir
          ? [
              {
                $sort: {
                  [sortBy]: (sortDir === 'asc' ? 1 : -1) as
                    | 1
                    | -1
                    | mongoose.Expression.Meta,
                },
              },
            ]
          : []),
        {
          $project: {
            locationInfo: 1,
            teamInfo: 1,
            unHiredUnits: 1,
            emptyCount: 1,
          },
        },
      ])
      .exec();
    return statsOccupants;
  }

  async exportListVacancies(query: ListVacancyQueryDto) {
    const { dateOfReport } = query;
    const currentDate = dateOfReport
      ? dayjs(dateOfReport).utc().startOf('day').format(DATE_FORMAT_HYPHEN)
      : dayjs().utc().startOf('day').format(DATE_FORMAT_HYPHEN);
    const vacancies = await this.getListVacancies(query);
    const header = [
      { field: 'dateOfReport', title: 'Date of report' },
      { field: 'locationInfo', title: 'Location' },
      { field: 'teamInfo', title: 'Team' },
      { field: 'unit', title: 'Unit' },
      { field: 'emptyCount', title: 'Number of beds empty (occupants)' },
    ];
    const fileName = `vacancy-report-${currentDate}.csv`;
    const csvLines: any[] = [];
    vacancies.forEach((item) => {
      csvLines.push({
        dateOfReport: currentDate,
        locationInfo: item.locationInfo.fullAddress,
        teamInfo: item.teamInfo.name,
        unit: '',
        emptyCount: item.emptyCount,
      });
      item.unHiredUnits.forEach((unit) => {
        csvLines.push({
          dateOfReport: currentDate,
          locationInfo: '',
          teamInfo: '',
          unit: unit.name,
          emptyCount: unit.maxOccupants,
        });
      });
    });
    return { data: csvLines, header, fileName };
  }

  async recalculateStatsOccupantsMarkNeedToUpdate(payload) {
    const { locationId } = payload;
    if (!locationId) {
      throw new BadRequestException('Location ID is required');
    }
    const locationsStatsOccupants = await this.statsOccupantModel
      .find({
        location: new mongoose.Types.ObjectId(String(locationId)),
      })
      .lean();

    if (locationsStatsOccupants.length > 0) {
      const statsOccupantIds = locationsStatsOccupants.map(
        (statsOccupant) => statsOccupant._id,
      );

      this.statsOccupantModel
        .updateMany(
          {
            _id: { $in: statsOccupantIds },
          },
          {
            markNeedToUpdate: true,
          },
        )
        .exec()
        .then(() => {
          console.log(
            `StatsOccupants with locationId ${locationId} marked as need to update`,
          );
        });
    } else {
      const reportCreatedDates = await this.statsOccupantModel.distinct(
        'reportCreatedDate',
        {},
      );
      if (reportCreatedDates.length > 0) {
        const locations = await this.locationModel
          .aggregate([
            {
              $match: {
                _id: new mongoose.Types.ObjectId(String(locationId)),
              },
            },
            {
              $lookup: {
                from: 'costcenters',
                localField: 'costCenter',
                foreignField: '_id',
                as: 'costCenterInfo',
              },
            },
            {
              $addFields: {
                costCenterInfo: { $arrayElemAt: ['$costCenterInfo', 0] },
              },
            },
            {
              $lookup: {
                from: 'teams',
                localField: 'team',
                foreignField: '_id',
                as: 'teamInfo',
              },
            },
            {
              $addFields: {
                teamInfo: { $arrayElemAt: ['$teamInfo', 0] },
              },
            },
          ])
          .exec();
        const location = locations[0];
        if (!location) {
          throw new BadRequestException(
            `Location with ID ${locationId} not found`,
          );
        }

        const createDatas = reportCreatedDates.map((reportCreatedDate) => {
          return {
            location: location?._id,
            reportCreatedDate: reportCreatedDate,
            maxCount: location?.maxOccupants,
            hiredCount: 0,
            emptyCount: location?.maxOccupants,
            hiredUnits: [],
            isLocationActive: location?.isActive,
            isLocationService: location?.isService ?? false,
            unHiredUnits: [],
            creditorContracts: [],
            debtorContracts: [],
            locationInfo: {
              _id: location?._id,
              fullAddress: location?.fullAddress,
            },
            costCenterInfo: {
              _id: location?.costCenter?._id,
              identifier: location?.costCenter?.identifier,
              name: location?.costCenter?.name,
            },
            teamInfo: {
              _id: location?.team?._id,
              name: location?.team?.name,
            },
            isDeleted: false,
            isActive: true,
            markNeedToUpdate: true,
          };
        });

        this.statsOccupantModel.insertMany(createDatas).then(() => {
          console.log(`StatsOccupants created for locationId ${locationId}`);
        });
      } else {
        console.log(
          `No StatsOccupants found for locationId ${locationId}. Nothing to update.`,
        );
      }
    }

    return {
      message: `StatsOccupants with locationId ${locationId} marked as need to update`,
    };
  }

  //#region  Private methods
  groupAndSeprateUnitBelongToRootUnit = (units, allUnitsExceptRoot) => {
    // group unHired units by parent
    units = _(units)
      .groupBy((unit) => unit.parent.toString())
      .value();

    // seprate unit belong to root unit
    units = _.reduce(
      units,
      (acc, cur, key) => {
        const parentObject = allUnitsExceptRoot.find(
          (unit) => unit._id.toString() === key,
        );

        const newKey = _.get(parentObject, 'name', 'belongToRootUnit').trim();

        if (!newKey) {
          return acc;
        }

        return { ...acc, [newKey]: cur };
      },
      {},
    );

    const { belongToRootUnit = [] } = units;
    if (!belongToRootUnit.length) {
      return units;
    }

    _.forEach(belongToRootUnit, (unit) => {
      if (units.hasOwnProperty(unit.name.trim())) {
        return;
      }
      // If unit in belongToUnit is not in result, add it
      units[unit.name.trim()] = unit;
    });

    delete units.belongToRootUnit;

    return units;
  };

  private async generateUnHiredUnits(unitOfLocations, hiredUnits) {
    const mappedUnitOfLocations = await Promise.all(
      unitOfLocations
        .filter(
          (unit) =>
            !hiredUnits.some(
              (hiredUnit) => hiredUnit._id?.toString() === unit._id?.toString(),
            ) &&
            unit.parent &&
            !_.some(
              hiredUnits,
              (hiredUnit) =>
                hiredUnit._id?.toString() === unit.parent?.toString(),
            ) &&
            unit.isActive,
        )
        .map(async (unit) => {
          return {
            ...unit,
            isAllChildenHired: await this.isAllChildenHired(unit, hiredUnits),
          };
        }),
    );

    const unHiredUnits = _(mappedUnitOfLocations)
      .filter((unit) => !unit.isAllChildenHired)
      .value();
    return unHiredUnits;
  }

  private async isAllChildenHired(unit, hiredUnits) {
    const childenUnits = await this.unitModel.find(
      { parent: unit._id },
      {
        _id: 1,
      },
    );
    if (!childenUnits.length) {
      return false;
    }

    const diff = _.differenceBy(childenUnits, hiredUnits, (unit: any) => {
      return unit._id.toString();
    });

    return !diff.length;
  }

  private countHiredUnits(location, hiredUnits) {
    let count = 0;
    const countAble: any[] = [];
    hiredUnits.forEach((unit) => {
      if (unit.isRoot) {
        return;
      }

      const hasChildenInHiredUnits = hiredUnits.some(
        (hiredUnit) => hiredUnit.parent?.toString() === unit._id?.toString(),
      );

      if (hasChildenInHiredUnits) {
        return;
      }

      countAble.push(unit);
    });

    count = _.sumBy(countAble, (unit) => unit.maxOccupants);

    if (count > location.maxOccupants) {
      count = location.maxOccupants;
    }

    return count;
  }
  //#endregion
}
