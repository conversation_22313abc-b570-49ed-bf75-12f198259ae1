import { Global, Module } from '@nestjs/common';

import { AddressModel } from '~/modules/address/address.model';
import { AgreementLineModel } from '~/modules/agreementline/agreementline.model';
import { ArticleTemplateModel } from '~/modules/article-template/article-template.model';
import { BvCompanyModel } from '~/modules/bvcompany/bvcompany.model';
import {
  ContactModel,
  ContactOrganizationPersonModel,
} from '~/modules/contact/contact.model';
import { ContractModel } from '~/modules/contract/contract.model';
import { ContractTypeModel } from '~/modules/contract-type/contract-type.model';
import { CostCenterModel } from '~/modules/costcenter/costcenter.model';
import { CostLineModel } from '~/modules/costline/costline.model';
import { CostLineGeneralModel } from '~/modules/costlinegeneral/costlinegeneral.model';
import { CostTypeModel } from '~/modules/costtype/costtype.model';
import { CountryModel } from '~/modules/country/country.model';
import { DocumentFileModel } from '~/modules/document-file/document-file.model';
import { UploadFileModel } from '~/modules/document-file/upload-file.model';
import { EmailTemplateModel } from '~/modules/email-template/email-template.model';
import { EquipmentModel } from '~/modules/equipment/equipment.model';
import { InvoiceModel } from '~/modules/invoice/invoice.model';
import { JobModel } from '~/modules/job/job.model';
import { JobEmployeeModel } from '~/modules/job-employee/job-employee.model';
import { JobEquipmentModel } from '~/modules/job-equipment/job-equipment.model';
import { JobPointModel } from '~/modules/job-point/job-point.model';
import { JobTemplateModel } from '~/modules/job-template/job-template.model';
import { LocationModel } from '~/modules/location/location.model';
import { LocationAdditionalGroupNameModel } from '~/modules/location-addtional/location-additional-group-name.model';
import { LocationAdditionalModel } from '~/modules/location-addtional/location-addtional.model';
import { LocationFileModel } from '~/modules/location-file/location-file.model';
import { NightRegistrationNationalityModel } from '~/modules/night-registration/models/night-registration-nationality.model';
import { NightRegistrationReservationModel } from '~/modules/night-registration/models/night-registration-reservation.model';
import { NightRegistrationResidentModel } from '~/modules/night-registration/models/night-registration-resident.model';
import { NightRegistrationWarningModel } from '~/modules/night-registration/models/night-registration-warning.model';
import { NightRegistrationWarningCategoryModel } from '~/modules/night-registration/models/night-registration-warning-category';
import { PlanningOrderModel } from '~/modules/planning/planning-order.model';
import { RegionModel } from '~/modules/region/region.model';
import { RoleGroupModel } from '~/modules/role-group/role-group.model';
import { StatsOccupantModel } from '~/modules/stats-occupant/stats-occupant.model';
import { SyncHistoryModel } from '~/modules/sync-history/sync-history.model';
import { TaskModel } from '~/modules/task/task.model';
import { TeamModel } from '~/modules/team/team.model';
import { TenantModel } from '~/modules/tenant/tenant.model';
import { TenantPermissionModel } from '~/modules/tenant-permission/tenant-permission.model';
import { TenantRoleModel } from '~/modules/tenant-role/tenant-role.model';
import { TenantUserModel } from '~/modules/tenant-user/tenant-user.model';
import { TokenModel } from '~/modules/token/token.model';
import { UnitModel } from '~/modules/unit/unit.model';
import { getProviderByTypegooseClass } from '~/transformers/model.transformer';

import { IdentifierModel } from '../../modules/identifier/identifier.model';
import { MigrationModel } from '../migration/migration.model';
import { DatabaseProvider } from './database.provider';

const models = [
  LocationModel,
  UnitModel,
  JobModel,
  JobPointModel,
  JobTemplateModel,
  CostCenterModel,
  ContactModel,
  ContactOrganizationPersonModel,
  TeamModel,
  CountryModel,
  RegionModel,
  AddressModel,
  TenantUserModel,
  TenantRoleModel,
  TokenModel,
  EquipmentModel,
  DocumentFileModel,
  UploadFileModel,
  BvCompanyModel,
  MigrationModel,
  JobEmployeeModel,
  TenantPermissionModel,
  IdentifierModel,
  EmailTemplateModel,
  TenantModel,
  ContractModel,
  AgreementLineModel,
  CostLineGeneralModel,
  CostLineModel,
  CostTypeModel,
  InvoiceModel,
  ContractTypeModel,
  LocationAdditionalModel,
  LocationAdditionalGroupNameModel,
  NightRegistrationResidentModel,
  NightRegistrationReservationModel,
  NightRegistrationWarningCategoryModel,
  NightRegistrationWarningModel,
  NightRegistrationNationalityModel,
  SyncHistoryModel,
  LocationFileModel,
  TaskModel,
  StatsOccupantModel,
  PlanningOrderModel,
  ArticleTemplateModel,
  JobEquipmentModel,
  RoleGroupModel,
].map((model) => getProviderByTypegooseClass(model));

@Module({
  providers: [DatabaseProvider, ...models],
  exports: [DatabaseProvider, ...models],
})
@Global()
export class DatabaseModule {}
