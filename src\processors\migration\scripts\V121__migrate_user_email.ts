import path from 'path';

import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const createUserHtml = `<p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">Beste <%= FIRSTNAME %> <%= LASTNAME %>,</p>

<p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">Er is voor jou een account aangemaakt voor <a href="https://homee.nu/">HomEE</a>.</p>

<p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">De inloggegevens vind je hieronder:</p>

<p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">Gebruikersnaam: <%= USERNAME %></p>

<p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">Wachtwoord: <%= PASSWORD %></p>

<p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">Mocht je hulp nodig hebben, dan kun je contact opnemen met de support office op <%= COMPANY_TELEPHONE %>.</p>`;

const resetPasswordHtml = `<p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">Beste collega,</p>
      
            <p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">Jouw wachtwoord voor <a href="https://homee.nu/">HomEE.nu</a> is gereset.</p>
            <p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">Gebruikersnaam: <%= USERNAME %></p>
            <p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">Wachtwoord: <%= PASSWORD %></p>
      
            <p style="font-family:Arial, Helvetica, sans-serif; font-size: 14px; line-height: 1.2em;">Mocht je hulp nodig hebben, dan kun je contact opnemen met de support office op <%= COMPANY_TELEPHONE %>.</p>`;
const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const destinationCollectionName = 'emailtemplates';
    const destinationCollection = context
      .destinationClient!.db()
      .collection(destinationCollectionName)!;

    if (!(await destinationCollection.indexExists('name_1'))) {
      destinationCollection.createIndex({ name: 1 }, { unique: true });
    }
    const data = [
      {
        name: 'create_user',
        subject: 'Homee nieuw account',
        to: [],
        html: createUserHtml,
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'reset_password_user',
        subject: 'Homee - reset wachtwoord',
        to: [],
        html: resetPasswordHtml,
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    const upsertPromises = data.map((doc) =>
      destinationCollection
        .findOneAndUpdate(
          { name: doc.name },
          { $set: doc },
          { upsert: true, returnDocument: 'after' }, // Use returnDocument: 'after' to get the updated document
        )
        .then(() => {
          console.log(
            `Migrated email template with name=${doc.name} into collection ${destinationCollectionName}`,
          );
        })
        .catch((error) => {
          console.error(
            `Error upserting document with name=${doc.name}:`,
            error,
          );
        }),
    );

    await Promise.all(upsertPromises)
      .then(() => {
        console.log(
          `Migrated ${data.length} documents to collection ${destinationCollectionName}`,
        );
      })
      .catch((error) => {
        console.error('Error during upsert operations:', error);
      });
    const after = new Date().getTime();
    console.log(
      `Migration script ${fileName} completed in ${after - before}ms`,
    );
  } catch (error) {
    console.error(`Error in migration script ${fileName}: ${error}`);
  }
};
export default up;
