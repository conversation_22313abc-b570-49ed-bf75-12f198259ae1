import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { contactTest } from '~/modules/contact/test/contact.dto.test';
import { locationAdditionalTest } from '~/modules/location-addtional/test/location-additional.dto.test';
import { locationAdditionalGroupNameTest } from '~/modules/location-addtional/test/location-additional-group-name.dto.test';

const getAdditionalLocationSchema = z.array(
  z.object({
    _id: z.instanceof(ObjectId),
    name: z.string(),
    locations: z.array(
      z.object({
        _id: z.instanceof(ObjectId),
        fullAddress: z.string(),
        additionals: z.array(
          locationAdditionalGroupNameTest.modelSchema
            .pick({
              _id: true,
              description: true,
              dutchDescription: true,
            })
            .extend({
              items: z.array(
                locationAdditionalTest.modelSchema
                  .pick({
                    _id: true,
                    brandType: true,
                    yearInstallation: true,
                    dateCheck: true,
                    name: true,
                    code: true,
                  })
                  .extend({
                    inspectionType: z.string().optional(),
                    type: z.string().optional(),
                    groupType: z.string(),
                    contact: contactTest.modelSchema.pick({
                      _id: true,
                      displayName: true,
                    }),
                  }),
              ),
            }),
        ),
      }),
    ),
  }),
);

export const reportLocationTest = { getAdditionalLocationSchema };
