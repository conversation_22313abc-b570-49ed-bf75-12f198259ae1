import path from 'path';

import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const html = `<!doctype html>
<html xmlns='http://www.w3.org/1999/xhtml' xmlns:v='urn:schemas-microsoft-com:vml'
    xmlns:o='urn:schemas-microsoft-com:office:office'>

<head>
    <title></title>
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
    <meta name='viewport' content='width=device-width,initial-scale=1'>
    <style>
       td {
        padding-top: 5px;
        padding-bottom: 5px;
      }
    </style>
</head>

<body style='word-spacing:normal'>
    <div style='margin:0 auto;max-width:800px;padding-top: 10px'>
      <div style='padding-bottom: 5px'>Please note that the following resident has exceeded the maximum allowed length of stay for this location.</div>
      <div style='padding-bottom: 5px'><strong>Location</strong>: <%= LOCATION_NAME %></div>
      <div style='padding-bottom: 5px'><strong>Maxium length of stay</strong>: <%= MAXIUM_LENGTH_OF_STAY %> months</div>
      <div style='padding-bottom: 5px'><strong>Exceeded on</strong>: <%= DATE_EXCEEDED %></div>
      <div><strong>Registration Date</strong>: <%= REGISTRATION_DATE %></div>
    </div>
    <div>
        <div style='margin:0 auto;max-width:800px;padding-top: 10px'>
            <table align='center' border='1' cellpadding='0' cellspacing='0' role='presentation' style='width:100%'>
                <tbody align='center'>
                    <tr>
                      <td style='width: 300px'>
                        <strong>Resident name</strong> 
                      </td>
                      <td style='width: 200px'>
                        <strong>Unit</strong> 
                      </td>
                      <td style='width: 200px'>
                        <strong>Room</strong> 
                      </td>
                    </tr>
                   <%= RESIDENT_INFO %>
                </tbody>
            </table>
        </div>
        <div style='margin:0 auto;max-width:800px;padding-top: 20px'>
          <div>
            This is for your information only — no action is required in the system.
          </div>
          <div style='padding-top: 10px'>
            Best regards,
          </div>
          <div>
            HomEE System Notification
          </div>
        </div>

    </div>
    <!-- Footer -->
    
    <div style='height:20px;line-height:20px;'>&#8202;</div>
</body>

</html>`;

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const destinationCollectionName = 'emailtemplates';
    const destinationCollection = context
      .destinationClient!.db()
      .collection(destinationCollectionName)!;

    if (!(await destinationCollection.indexExists('name_1'))) {
      destinationCollection.createIndex({ name: 1 }, { unique: true });
    }

    const doc = {
      name: 'resident_has_exceeded_maxium_length_of_stay',
      subject: 'Resident Has Exceeded Maximum Length of Stay',
      html,
      bcc: [],
      cc: [],
      to: [],
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    await destinationCollection
      .findOneAndUpdate(
        { name: doc.name },
        { $set: doc },
        { upsert: true, returnDocument: 'after' }, // Use returnDocument: 'after' to get the updated document
      )
      .then(() => {
        console.log(
          `Migrated resident has exceeded maxium length of stay email with name=${doc.name} into collection ${destinationCollectionName}`,
        );
      })
      .catch((error) => {
        console.error(`Error upserting document with name=${doc.name}:`, error);
      });
    const after = new Date().getTime();
    console.log(
      `Migration script ${fileName} completed in ${after - before}ms`,
    );
  } catch (error) {
    console.error(`Error in migration script ${fileName}: ${error}`);
  }
};
export default up;
