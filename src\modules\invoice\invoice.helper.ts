import { Aggregate } from 'mongoose';

import { AgreementLinePeriod } from '~/shared/enums/contract.enum';

import { FOUR_WEEKLY_END_WEEK_NUMBERS } from '../contract/contract.helper';

export const ISO_WEEK_END_MONGO_VARIABLE = 'isoWeekEnd';

export const LookUpLocationPipelineStage = {
  $lookup: {
    from: 'locations',
    localField: 'location',
    foreignField: '_id',
    as: 'location',
    pipeline: [
      {
        $lookup: {
          from: 'addresses',
          localField: 'address',
          foreignField: '_id',
          as: 'address',
          pipeline: [
            {
              $lookup: {
                from: 'countries',
                localField: 'country',
                foreignField: '_id',
                as: 'country',
              },
            },
            {
              $project: {
                countryCode: {
                  $first: '$country.code',
                },
              },
            },
          ],
        },
      },
      {
        $project: {
          fullAddress: 1,
          countryCode: {
            $first: '$address.countryCode',
          },
        },
      },
    ],
  },
};

export const LookUpCostCenterPipelineStage = {
  $lookup: {
    from: 'costcenters',
    localField: 'costCenter',
    foreignField: '_id',
    as: 'costCenter',
    pipeline: [
      {
        $project: {
          name: 1,
          identifier: 1,
          location: {
            $first: '$locations',
          },
        },
      },
      {
        $lookup: {
          from: 'locations',
          localField: 'location',
          foreignField: '_id',
          as: 'location',
          pipeline: [
            {
              $lookup: {
                from: 'addresses',
                localField: 'address',
                foreignField: '_id',
                as: 'address',
                pipeline: [
                  {
                    $lookup: {
                      from: 'countries',
                      localField: 'country',
                      foreignField: '_id',
                      as: 'country',
                    },
                  },
                  {
                    $project: {
                      countryCode: {
                        $first: '$country.code',
                      },
                    },
                  },
                ],
              },
            },
            {
              $project: {
                countryCode: {
                  $first: '$address.countryCode',
                },
              },
            },
          ],
        },
      },
      {
        $project: {
          name: 1,
          identifier: 1,
          countryCode: {
            $first: '$location.countryCode',
          },
        },
      },
    ],
  },
};

export const AddFieldVatPipelineStage = {
  $addFields: {
    vat: {
      $switch: {
        default: 0,
        branches: [
          {
            case: { $eq: ['$itemCodeSuffix', 'H'] },
            then: {
              $switch: {
                branches: [
                  {
                    case: { $eq: ['$countryCode', 'nl'] },
                    then: { $multiply: ['$totalPrice', 0.21] },
                  },
                  {
                    case: { $eq: ['$countryCode', 'de'] },
                    then: { $multiply: ['$totalPrice', 0.19] },
                  },
                ],
                default: 0,
              },
            },
          },
          {
            case: { $eq: ['$itemCodeSuffix', 'L'] },
            then: {
              $switch: {
                default: 0,
                branches: [
                  {
                    case: { $eq: ['$countryCode', 'nl'] },
                    then: { $multiply: ['$totalPrice', 0.09] },
                  },
                  {
                    case: { $eq: ['$countryCode', 'de'] },
                    then: { $multiply: ['$totalPrice', 0.07] },
                  },
                ],
              },
            },
          },
        ],
      },
    },
  },
};

/**
 * Constructs a MongoDB aggregation pipeline to add fields for year, month, and ISO week
 * based on the `endDate` field. If the period is `FOUR_WEEKLY`, it also adds a field
 * for the ISO week end number.
 *
 * @param period - The period type which can be `AgreementLinePeriod.FOUR_WEEKLY`.
 * @returns An array representing the MongoDB aggregation pipeline.
 */
export function addFieldsIsoGroupingPipeline(period?: AgreementLinePeriod) {
  const pipeline: any[] = [
    {
      $addFields: {
        isoWeekYear: { $isoWeekYear: '$endDate' },
        month: { $month: '$endDate' },
        isoWeek: { $isoWeek: '$endDate' },
      },
    },
  ];

  if (period === AgreementLinePeriod.FOUR_WEEKLY) {
    const dafaultWeekNumber = 4;
    // Adds a field for the represents which ISO week number is the end of the 4-weekly period
    pipeline.push({
      $addFields: {
        [ISO_WEEK_END_MONGO_VARIABLE]: {
          // Use $ifNull to provide a default value '4' if the result is null
          $ifNull: [
            {
              $first: {
                $filter: {
                  // Getting the nearest week number that is greater than
                  // or equal to the current week number (ISO Week of endDate)
                  // using the provided week numbers
                  input: FOUR_WEEKLY_END_WEEK_NUMBERS,
                  as: 'endWeekNumber',
                  cond: { $gte: ['$$endWeekNumber', '$isoWeek'] },
                },
              },
            },
            dafaultWeekNumber, // <--- Default value
          ],
        },
      },
    });
  }

  return pipeline;
}

/**
 * Generates a MongoDB aggregation pipeline condition based on the given period.
 *
 * @param period - The period for which the condition is generated. It can be one of the following:
 *   - `AgreementLinePeriod.WEEKLY`: Groups by year and ISO week.
 *   - `AgreementLinePeriod.FOUR_WEEKLY`: Groups by year and a custom ISO week end variable.
 *   - `AgreementLinePeriod.MONTHLY`: Groups by year and month.
 * @returns An object representing the MongoDB aggregation pipeline condition for the specified period.
 */
export function getPeriodGroupConditionPipeline(period: AgreementLinePeriod) {
  switch (period) {
    case AgreementLinePeriod.WEEKLY:
      return {
        isoWeekYear: '$isoWeekYear',
        isoWeek: '$isoWeek',
      };
    case AgreementLinePeriod.FOUR_WEEKLY:
      return {
        isoWeekYear: '$isoWeekYear',
        [ISO_WEEK_END_MONGO_VARIABLE]: `$${ISO_WEEK_END_MONGO_VARIABLE}`,
      };
    case AgreementLinePeriod.MONTHLY:
      return {
        isoWeekYear: '$isoWeekYear',
        month: '$month',
      };
  }
}

export function buildLocationSortStages<T>(
  aggregate: Aggregate<T>,
  sortDir?: string,
) {
  return aggregate
    .addFields({
      locationForOrdering: {
        $ifNull: [
          '$location.fullAddress',
          {
            $concat: ['$costCenter.identifier', ' - ', '$costCenter.name'],
          },
        ],
      },
    })
    .sort({
      locationForOrdering: sortDir === 'desc' ? -1 : 1,
      updatedAt: 1,
    })
    .project({ locationForOrdering: 0 });
}

export function buildBasicSortStage<T>(
  aggregate: Aggregate<T>,
  sortBy: string,
  sortDir?: string,
) {
  return aggregate.sort({
    [sortBy]: sortDir === 'desc' ? -1 : 1,
    updatedAt: 1,
  });
}
