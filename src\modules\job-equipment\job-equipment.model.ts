import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import {
  EquipmentDocument,
  EquipmentModel,
} from '../equipment/equipment.model';
import { JobDocument, JobModel } from '../job/job.model';

export type JobEquipmentDocument = DocumentType<JobEquipmentModel>;

@index({ job: 1 })
@index({ equipment: 1, job: 1 })
@index({ isActive: 1, isDeleted: 1 })
@modelOptions({ options: { customName: 'JobEquipments' } })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class JobEquipmentModel extends BaseModel {
  @prop({ ref: () => EquipmentModel })
  equipment!: Ref<EquipmentDocument>;

  @prop({ ref: JobModel })
  job!: Ref<JobDocument>;

  @prop()
  plannedDate?: Date;
}
