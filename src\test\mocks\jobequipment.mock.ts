import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { JobEquipmentModel } from '~/modules/job-equipment/job-equipment.model';

import { mockEquipmentData } from './equipment.mock';
import { mockJobData } from './job.mock';

const jobEquipmentModel = getModelForClass(JobEquipmentModel);
type jobEquipmentType = InstanceType<typeof JobEquipmentModel>;

export const mockJobEquipmentData = {
  _id: new ObjectId(),
  createdAt: new Date(),
  equipment: mockEquipmentData._id,
  job: mockJobData._id,
  plannedDate: new Date(),
  isActive: true,
  isDeleted: false,
  updatedAt: new Date(),
};

export async function initMockJobEquipment(
  doc?: Partial<jobEquipmentType & { _id: ObjectId }>,
) {
  const data = { ...mockJobEquipmentData, ...doc };
  await jobEquipmentModel.replaceOne({ _id: data._id }, data, { upsert: true });
}
