import {
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';

import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { LOCATION_MESSAGE_KEYS } from '~/shared/message-keys/location.message-key';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { GetLocationGroupNamesDto } from '../location/dtos/location.dto';
import { LocationService } from '../location/location.service';
import { LocationAdditionalGroupNameModel } from './location-additional-group-name.model';
import { LocationAdditionalModel } from './location-addtional.model';

@Injectable()
export class LocationAdditionalService {
  constructor(
    @InjectModel(LocationAdditionalModel)
    private readonly locationAdditionalModel: MongooseModel<LocationAdditionalModel>,
    @InjectModel(LocationAdditionalGroupNameModel)
    private readonly locationAdditionalGroupNameModel: MongooseModel<LocationAdditionalGroupNameModel>,
    @Inject(forwardRef(() => LocationService))
    private readonly locationService: LocationService,
  ) {}

  async findAll() {
    return this.locationAdditionalModel.find().lean();
  }

  async create(data: any) {
    return this.locationAdditionalModel.create(data);
  }

  async update(data: any) {
    return this.locationAdditionalModel.updateOne({ _id: data._id }, data);
  }

  async delete(_id: string) {
    return this.locationAdditionalModel.deleteOne({ _id });
  }

  // function get additional group names
  async getAdditionalGroupNames(payload: GetLocationGroupNamesDto) {
    const { query } = buildQuery(payload, ['description', 'dutchDescription']);

    // add order by identifier
    return this.locationAdditionalGroupNameModel
      .find(query)
      .sort({ position: 1 })
      .lean();
  }

  // function get additional by location
  async getAdditionalByLocation(payload: {
    id: string;
    type: LocationAdditionalType;
  }) {
    const { id, type } = payload;
    const location = await this.locationService.findOne(id);

    if (!location) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }
    const query = {
      location: location._id,
      type,
      isDeleted: false,
    };

    const baseReturnFields = {
      _id: 1,
      type: 1,
      position: 1,
      groupName: { $first: '$groupName' },
    };

    let returnFields = {};

    switch (type) {
      case LocationAdditionalType.FEATURE_AND_SUPPLIER:
        returnFields = {
          ...baseReturnFields,
          groupType: 1,
          description: 1,
          code: 1,
          contact: { $first: '$contact' },
          contract: { $first: '$contract' },
        };
        break;

      case LocationAdditionalType.GWE_AND_METER_READING:
        returnFields = {
          ...baseReturnFields,
          contact: { $first: '$contact' },
          contract: { $first: '$contract' },
          smartMeter: 1,
          meterNumber: 1,
          code: 1,
          contractNumber: 1,
          recordLogs: 1,
        };
        break;

      case LocationAdditionalType.CERTIFICATE_AND_CONTROL:
        returnFields = {
          ...baseReturnFields,
          contact: { $first: '$contact' },
          description: 1,
          dateCheck: 1,
          brandType: 1,
          name: 1, // model for on GUI
          yearInstallation: 1,
          groupType: 1,
          inspectionType: 1,
        };
        break;

      default:
        returnFields = baseReturnFields;
        break;
    }

    const aggregates = [
      { $match: query },
      {
        $lookup: {
          from: 'locationadditionalgroupnames',
          localField: 'groupName',
          foreignField: '_id',
          as: 'groupName',
          pipeline: [
            {
              $project: {
                _id: 1,
                position: 1,
                description: 1,
                dutchDescription: 1,
              },
            },
          ],
        },
      },
      // contacts
      {
        $lookup: {
          from: 'contacts',
          localField: 'contact',
          foreignField: '_id',
          as: 'contact',
          pipeline: [
            {
              $project: {
                _id: 1,
                displayName: 1,
                organizationNames: 1,
              },
            },
          ],
        },
      },
      // contracts
      {
        $lookup: {
          from: 'contracts',
          localField: 'contract',
          foreignField: '_id',
          as: 'contract',
          pipeline: [
            {
              $lookup: {
                from: 'contacts',
                localField: 'contact',
                foreignField: '_id',
                as: 'contact',
                pipeline: [
                  {
                    $project: {
                      _id: 1,
                      displayName: 1,
                      organizationNames: 1,
                    },
                  },
                ],
              },
            },
            {
              $project: {
                _id: 1,
                identifier: 1,
                contact: { $first: '$contact' },
              },
            },
          ],
        },
      },
      {
        $project: returnFields,
      },
    ];

    return await this.locationAdditionalModel
      .aggregate(aggregates)
      .sort({ 'groupName.position': 1, position: 1 })
      .exec();
  }

  // function create / update additional by location
  async createOrUpdateAdditionalByLocation(payload: {
    id: string;
    type: LocationAdditionalType;
    items: any;
  }) {
    const { id, type, items } = payload;
    const location = await this.locationService.findOne(id);

    if (!location) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }

    // Flatten the points
    const listAdditional = items.map((item) => {
      return {
        ...item,
        location: location._id,
      };
    });

    // list update
    const listUpdate = listAdditional.filter((additional) => additional._id);
    const listCreate = listAdditional.filter((additional) => !additional._id);

    // update
    await this.locationAdditionalModel.bulkWrite(
      listUpdate.map((item) => ({
        updateOne: {
          filter: { _id: item._id },
          update: { $set: item },
        },
      })),
    );

    // create
    await this.locationAdditionalModel.bulkWrite(
      listCreate.map((item) => ({
        insertOne: {
          document: item,
        },
      })),
    );

    // return the updated list
    return this.getAdditionalByLocation({ id, type });
  }
}
