import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { mongoose } from '@typegoose/typegoose';
import { stringify } from 'csv-stringify';
import dayjs from 'dayjs';
import _ from 'lodash';
import { AggregatePaginateModel, Model } from 'mongoose';

import { DATE_FORMAT_SLASH } from '~/constants/app.constant';
import { CostlineService } from '~/modules/costline/costline.service';
import { EventEmitterSender } from '~/processors/event-emitter/event-emitter.sender';
import { GoogleMapService } from '~/processors/google-map/google-map.service';
import { ContractType } from '~/shared/enums/contract.enum';
import {
  LocationAdditionalGroupName,
  LocationAdditionalType,
} from '~/shared/enums/location-additional.enum';
import { ModuleNameEnum } from '~/shared/enums/module-name.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { LOCATION_MESSAGE_KEYS } from '~/shared/message-keys/location.message-key';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';
import { validteAndSortPosition } from '~/utils/position.util';

import { AddressModel } from '../address/address.model';
import { AddressService } from '../address/address.service';
import { BvCompanyModel } from '../bvcompany/bvcompany.model';
import { AddressDto } from '../contact/dtos/contact.dto';
import { CostCenterModel } from '../costcenter/costcenter.model';
import { CountryService } from '../country/country.service';
import { LocationAdditionalService } from '../location-addtional/location-additonal.service';
import { TeamModel } from '../team/team.model';
import { TenantModel } from '../tenant/tenant.model';
import { flattenUnits } from '../unit/unit.helper';
import { UnitModel } from '../unit/unit.model';
import { UnitService } from '../unit/unit.service';
import {
  CreateLocationDto,
  GetLocationNearByQueryDto,
  GetUnitsOfLocationDto,
  LocationQueryParamDto,
  UpdateLocationDto,
  UpdateUnitsOfLocationDto,
} from './dtos/location.dto';
import { LocationModel } from './location.model';

@Injectable()
export class LocationService {
  constructor(
    @InjectModel(TenantModel)
    private readonly tenantModel: Model<TenantModel>,
    @InjectModel(UnitModel)
    private readonly unitModel: MongooseModel<UnitModel>,
    @InjectModel(LocationModel)
    private readonly locationModel: AggregatePaginateModel<LocationModel>,
    @InjectModel(AddressModel)
    private readonly addressModel: MongooseModel<AddressModel>,
    @InjectModel(CostCenterModel)
    private readonly costCenterModel: MongooseModel<CostCenterModel>,
    @InjectModel(TeamModel)
    private readonly teamModel: MongooseModel<TeamModel>,
    @InjectModel(BvCompanyModel)
    private readonly bvCompanyModel: Model<BvCompanyModel>,
    @Inject(forwardRef(() => LocationAdditionalService))
    private readonly locationAdditionalService: LocationAdditionalService,

    private readonly unitService: UnitService,
    private readonly addressService: AddressService,
    private readonly googleMapService: GoogleMapService,
    private readonly countryService: CountryService,
    private readonly eventEmitterSender: EventEmitterSender,
    private readonly costlineService: CostlineService,
  ) {}

  async findAll(payload: LocationQueryParamDto) {
    const { platform, lastSyncedAt, ...rest } = payload;
    const { query, options } = buildQuery(rest, ['fullAddress']);

    let limit = options.limit;
    let offset = options.offset;
    let aggregates: any[] = [];
    if (platform === 'mb') {
      offset = 0;

      let customQuery = {};
      if (lastSyncedAt) {
        customQuery = {
          updatedAt: {
            $gte: dayjs(lastSyncedAt).toDate(),
          },
        };
      }
      limit = await this.locationModel.countDocuments(customQuery).lean();
      aggregates = [
        { $match: customQuery },
        {
          $project: {
            _id: 1,
            isActive: 1,
            fullAddress: 1,
          },
        },
      ];
    } else {
      aggregates = [
        { $match: query },
        {
          $lookup: {
            from: 'addresses',
            localField: 'address',
            foreignField: '_id',
            as: 'address',
          },
        },
        {
          $unwind: {
            path: '$address',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'regions',
            localField: 'address.region',
            foreignField: '_id',
            as: 'address.region',
          },
        },
        {
          $unwind: {
            path: '$address.region',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'countries',
            localField: 'address.country',
            foreignField: '_id',
            as: 'address.country',
          },
        },
        {
          $unwind: {
            path: '$address.country',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'teams',
            localField: 'team',
            foreignField: '_id',
            as: 'team',
          },
        },
        { $unwind: { path: '$team', preserveNullAndEmptyArrays: true } },
        {
          $project: {
            _id: 1,
            isActive: 1,
            fullAddress: 1,
            'address._id': 1,
            'address.country._id': 1,
            'address.country.name': 1,
            'address.country.code': 1,
            'address.region._id': 1,
            'address.region.name': 1,
            'team._id': 1,
            'team.name': 1,
          },
        },
      ];
    }

    return this.locationModel.aggregatePaginate(
      this.locationModel.aggregate(aggregates, {
        collation: options.collation,
      }),
      { limit, offset, sort: options.sort },
    );
  }

  async findOne(id: string) {
    const location = await this.locationModel
      .aggregate([
        {
          $match: { _id: new mongoose.Types.ObjectId(id) },
        },
        {
          $lookup: {
            from: 'addresses',
            localField: 'address',
            foreignField: '_id',
            as: 'address',
          },
        },
        { $unwind: { path: '$address', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'countries',
            localField: 'address.country',
            foreignField: '_id',
            as: 'address.country',
          },
        },
        {
          $unwind: {
            path: '$address.country',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'regions',
            localField: 'address.region',
            foreignField: '_id',
            as: 'address.region',
          },
        },
        {
          $unwind: {
            path: '$address.region',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $lookup: {
            from: 'teams',
            localField: 'team',
            foreignField: '_id',
            as: 'team',
          },
        },
        { $unwind: { path: '$team', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'bvcompanies',
            localField: 'bvCompany',
            foreignField: '_id',
            as: 'bvCompany',
          },
        },
        { $unwind: { path: '$bvCompany', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'costcenters',
            localField: 'costCenter',
            foreignField: '_id',
            as: 'costCenter',
          },
        },
        { $unwind: { path: '$costCenter', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'contracts',
            let: { locationId: '$_id' },
            localField: '_id',
            foreignField: 'location',
            pipeline: [
              {
                $match: {
                  $and: [
                    { isActive: true },
                    { type: ContractType.CREDITOR },
                    {
                      $or: [
                        { endDate: { $exists: false } },
                        { endDate: null },
                        {
                          endDate: {
                            $gt: dayjs()
                              .utc()
                              .startOf('day')
                              .subtract(1, 'day')
                              .toDate(),
                          },
                        },
                      ],
                    },
                  ],
                },
              },
              {
                $lookup: {
                  from: 'contacts',
                  localField: 'contact',
                  foreignField: '_id',
                  as: 'contact',
                },
              },
              {
                $unwind: { path: '$contact', preserveNullAndEmptyArrays: true },
              },
              {
                $sort: { startDate: -1 },
              },
            ],
            as: 'contracts',
          },
        },
        {
          $project: {
            address: {
              _id: 1,
              region: { _id: 1, name: 1 },
              country: { _id: 1, name: 1, code: 1 },
              city: 1,
              street: 1,
              number: 1,
              suffix: 1,
              postalCode: 1,
            },
            fullAddress: 1,
            team: { _id: 1, name: 1 },
            bvCompany: { _id: 1, identifier: 1, name: 1 },
            isActive: 1,
            isRenting: 1,
            isService: 1,
            locationOf: 1,
            email: 1,
            geo: { lng: '$geo.coordinates.lng', lat: '$geo.coordinates.lat' },
            costCenter: {
              _id: 1,
              name: 1,
              identifier: 1,
              isActive: 1,
              isSynced: 1,
            },
            contracts: {
              _id: 1,
              contact: {
                _id: 1,
                contactType: 1,
                name: 1,
                displayName: 1,
              },
            },
            parkingSpaces: 1,
            energyLabel: 1,
            maximumStayDuration: 1,
          },
        },
      ])
      .exec();

    if (!location || location.length === 0) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }

    return location[0];
  }

  async create(data: CreateLocationDto) {
    const { address, costCenter, team, locationOf, tenantId, ...rest } = data;
    const tenant = await this.tenantModel.findById(tenantId).lean();

    const foundCostCenter = await this.costCenterModel
      .findById(costCenter)
      .populate({ path: 'locations', select: 'bvCompany' })
      .lean();

    if (!foundCostCenter) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.COST_CENTER_NOT_FOUND);
    }

    // check if current tenant is Kafra
    const isKafra = tenant?.name?.toLowerCase() === 'kafra';

    if (
      foundCostCenter.locations?.some(
        (location: any) => location.bvCompany.toString() !== rest.bvCompany,
      ) &&
      !isKafra // Kafra can have multiple locations with different BV companies
    ) {
      throw new BadRequestException(LOCATION_MESSAGE_KEYS.BV_COMPANY_CONFLICT);
    }

    const fullAddress = await this.getFullAddress(address);
    await this.countryService.validatePostalCode(address, 'location');

    const payloadLocationOf = validteAndSortPosition(
      locationOf,
      ModuleNameEnum.LOCATION,
    );

    const foundTeam = await this.teamModel.findById(team).lean();
    if (!foundTeam) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.TEAM_NOT_FOUND);
    }

    const foundBvCompany = await this.bvCompanyModel.findById(rest.bvCompany);
    if (!foundBvCompany) {
      throw new BadRequestException(LOCATION_MESSAGE_KEYS.BV_COMPANY_NOT_FOUND);
    }

    const location = await this.locationModel.create({
      ...rest,
      team: foundTeam._id,
      locationOf: payloadLocationOf,
      costCenter: foundCostCenter._id,
    });

    await this.costCenterModel.updateOne(
      { _id: costCenter },
      { $addToSet: { locations: location._id } },
    );

    const referencePayload = {} as {
      address?: mongoose.Types.ObjectId;
      fullAddress?: string;
      country?: string;
      region?: string;
    };

    let geo = {
      lng: 0,
      lat: 0,
    };

    if (address) {
      const { country, region } =
        await this.addressService.validationAddressCountryRegion(address);

      const result = await this.addressModel.create({
        location: location._id,
        ...address,
      });

      geo = await this.getLocationGeo(result?._id.toString());

      referencePayload.address = result._id;
      referencePayload.country = country.name;
      referencePayload.region = region.name;
      referencePayload.fullAddress = fullAddress;
    }

    const rootUnit = {
      isActive: true,
      isRoot: true,
      position: 0,
      name: referencePayload.fullAddress || '',
      maxOccupants: 0,
      maxArea: 0,
    };

    const { insertedIds } = await this.unitService.bulkUpdate(
      location._id.toString(),
      [rootUnit],
    );

    const insertedIdArrays = Object.keys(insertedIds).map(
      (key) => insertedIds[key],
    );

    await this.locationModel.updateOne(
      {
        _id: location._id,
      },
      {
        ...referencePayload,
        geo: {
          type: 'Point',
          coordinates: { lng: geo.lng, lat: geo.lat },
        },
        units: insertedIdArrays,
      },
    );

    return this.findOne(location._id.toString());
  }

  async update(data: UpdateLocationDto) {
    const { address, costCenter, team, locationOf, ...rest } = data;

    const location = await this.locationModel.findById(rest.id);
    if (!location) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }

    const foundCostCenter = await this.costCenterModel
      .findById(costCenter)
      .lean();
    if (!foundCostCenter) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.COST_CENTER_NOT_FOUND);
    }

    const foundTeam = await this.teamModel.findById(team).lean();
    if (!foundTeam) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.TEAM_NOT_FOUND);
    }

    const referencePayload = {} as {
      address?: mongoose.Types.ObjectId;
      fullAddress?: string;
      country?: string;
      region?: string;
    };

    let geo = {
      lng: 0,
      lat: 0,
    };

    const payloadLocationOf = validteAndSortPosition(
      locationOf,
      ModuleNameEnum.LOCATION,
    );

    if (address) {
      const { country, region } =
        await this.addressService.validationAddressCountryRegion(address);

      await this.countryService.validatePostalCode(address, 'location');

      const fullAddress = await this.getFullAddress(
        address,
        location._id.toString(),
      );

      const result = await this.addressModel.findOneAndUpdate(
        { _id: location.address },
        {
          ...address,
          location: location._id,
        },
        {
          new: true,
          upsert: true,
        },
      );

      geo = await this.getLocationGeo(result?._id.toString());

      referencePayload.address = result?._id;
      referencePayload.country = country.name;
      referencePayload.region = region.name;
      referencePayload.fullAddress = fullAddress;
    }

    await this.locationModel.updateOne(
      {
        _id: location._id,
      },
      {
        $set: {
          ...rest,
          ...referencePayload,
          locationOf: payloadLocationOf,
          geo: {
            type: 'Point',
            coordinates: { lng: geo.lng, lat: geo.lat },
          },
          team: foundTeam._id,
          costCenter: foundCostCenter._id,
        },
      },
    );

    const currentCostCenter = await this.costCenterModel
      .findOne({
        locations: location._id,
      })
      .lean();

    const bulkOperations: any = [];

    if (costCenter !== currentCostCenter?._id.toString()) {
      if (currentCostCenter) {
        bulkOperations.push({
          updateOne: {
            filter: { _id: currentCostCenter._id },
            update: { $pull: { locations: location._id } },
          },
        });
      }

      bulkOperations.push({
        updateOne: {
          filter: { _id: costCenter },
          update: { $addToSet: { locations: location._id } },
        },
      });

      await this.costCenterModel.bulkWrite(bulkOperations);
      if (currentCostCenter) {
        void this.costlineService.updateCostCenterByLocation(
          location._id,
          currentCostCenter._id,
          costCenter,
        );
      }
    }

    await this.unitService.updateRootUnitOfLocation(location._id.toString(), {
      name: referencePayload.fullAddress || '',
    });

    await this.eventEmitterSender.updateLocationEvent(location);

    return this.findOne(rest.id);
  }

  async export(id: string) {
    const location = (
      await this.locationModel.aggregate([
        {
          $match: {
            _id: new mongoose.Types.ObjectId(id),
          },
        },
        { $addFields: { bvCompanyId: { $toObjectId: '$bvCompany' } } },
        {
          $lookup: {
            from: 'bvcompanies',
            localField: 'bvCompanyId',
            foreignField: '_id',
            as: 'bvCompany',
          },
        },
        { $unwind: { path: '$bvCompany', preserveNullAndEmptyArrays: true } },
        {
          $lookup: {
            from: 'addresses',
            localField: 'address',
            foreignField: '_id',
            as: 'address',
          },
        },
        { $unwind: { path: '$address', preserveNullAndEmptyArrays: true } },
        {
          $project: {
            _id: 1,
            maxOccupants: 1,
            maxArea: 1,
            fullAddress: 1,
            energyLabel: 1,
            bvCompany: {
              _id: 1,
              identifier: 1,
              name: 1,
            },
            address: {
              city: 1,
              street: 1,
              number: 1,
              suffix: 1,
            },
          },
        },
      ])
    )[0];

    const address = location.address;
    address.city = address.city?.replace(/\s+/g, '_') ?? '';
    address.street = address.street?.replace(/\s+/g, '_') ?? '';
    address.number = address.number ?? '';
    address.suffix = address.suffix
      ? `_${address.suffix.replace(/\s+/g, '_')}`
      : '';

    const fileName = `location_information_${address.city}_${address.street}_${address.number}${address.suffix}.csv`;

    const locationAdditional =
      await this.locationAdditionalService.getAdditionalByLocation({
        id,
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
      });

    const units = await this.getUnitsOfLocation({ id });
    const unitTree = this.getUnitsOfTree(units);
    const csvLines: any = [];

    // block 1
    csvLines.push(['Locatie (adres)', location.fullAddress]);
    csvLines.push(['B.V', location.bvCompany.name]);
    csvLines.push([
      'Soort woning appartement/ woonhuis/ recreatiewoning/ wooncomplex',
    ]);

    locationAdditional.forEach((item) => {
      let name = item.name;

      if (
        item.groupName?.description ==
          LocationAdditionalGroupName.FIRE_INSTALLATION ||
        item.groupName?.description == LocationAdditionalGroupName.LEGIONELLA
      ) {
        name = item.brandType
          ? `${item.groupName.dutchDescription} - ${item.brandType}`
          : item.groupName.dutchDescription;
      }

      const formattedDate = dayjs(item.dateCheck).format(DATE_FORMAT_SLASH);
      csvLines.push([name, formattedDate]);
    });

    csvLines.push(['Max aantal personen', location.maxOccupants]);
    csvLines.push([
      'Bruto vloeroppervlakte van het huis (m2)',
      location.maxArea.toFixed(2),
    ]);

    // Energy Label
    csvLines.push(['Energielabel', location.energyLabel]);

    // space
    csvLines.push([], [], [], []);

    // block 2
    csvLines.push(['Kamer', 'Ruimte (m2)', 'Aantal bedden', 'm2/bed']);

    const unitCsvRows = unitTree.map((unit: any) => {
      const m2PerBed =
        unit.maxOccupants > 0 ? unit.maxArea / unit.maxOccupants : 0;

      return [
        unit.name,
        unit.maxArea.toFixed(2),
        unit.maxOccupants.toFixed(2),
        m2PerBed.toFixed(2),
      ];
    });

    csvLines.push(...unitCsvRows);

    // build content
    const content = await new Promise((resolve, reject) => {
      stringify(csvLines, { delimiter: ';' }, (err, output) => {
        if (err) reject(err);
        else resolve(output);
      });
    });

    return { fileName, content };
  }

  getUnitsOfTree(units: any[]) {
    const unitMap = new Map();

    units.forEach((unit) => {
      unitMap.set(unit._id.toString(), unit);
    });

    const buildTree = (unit: any) => {
      const children = units.filter(
        (u) => u.parent?.toString() === unit._id.toString(),
      );

      if (children.length === 0) {
        return [
          {
            name: unit.name,
            maxArea: unit.maxArea,
            maxOccupants: unit.maxOccupants,
          },
        ];
      }

      return children
        .filter((child) => child.maxOccupants > 0)
        .flatMap((child) => {
          const subTree = buildTree(child);
          return subTree.map((sub: any) => ({
            name: `${unit.name} ${sub.name}`,
            maxArea: sub.maxArea,
            maxOccupants: sub.maxOccupants,
          }));
        });
    };

    const rootUnits = units.filter((unit) => unit.isRoot);

    const result = rootUnits.flatMap((rootUnit) => {
      const children = units.filter(
        (u) => u.parent?.toString() === rootUnit._id.toString(),
      );

      if (children.length === 0) {
        return [
          {
            name: rootUnit.name,
            maxArea: rootUnit.maxArea,
            maxOccupants: rootUnit.maxOccupants,
          },
        ];
      }

      return children
        .filter((child) => child.maxOccupants > 0)
        .flatMap((child) => buildTree(child));
    });

    return result;
  }

  async getFullAddress(address: AddressDto, locationId?: string) {
    const { city, street, number, suffix } = address;

    const fullAddress = `${city}, ${street} ${number}${suffix ? ` ${suffix}` : ''}`;

    const existedLocation = await this.locationModel.findOne({
      fullAddress,
      _id: { $ne: locationId },
    });

    if (existedLocation) {
      throw new BadRequestException(LOCATION_MESSAGE_KEYS.ADDRESS_EXISTED);
    }

    return fullAddress;
  }

  async getLocationGeo(address?: string) {
    const defaultGeo = {
      lng: 0,
      lat: 0,
    };

    if (!address) {
      return defaultGeo;
    }

    const foundAddress = await this.addressModel
      .findById(address)
      .populate('country region')
      .lean();

    if (!foundAddress) {
      return defaultGeo;
    }

    const { country, region, city, street, number, postalCode } =
      foundAddress as any;

    const fullAddress =
      [number, street].join(' ') +
      ', ' +
      [city, region.name, postalCode].join(' ') +
      ', ' +
      country.name;

    const geoCode = await this.googleMapService.getGeocode(fullAddress);

    if (!geoCode.data.results.length) {
      return defaultGeo;
    }

    const results = geoCode.data.results[0];

    return results.geometry.location || defaultGeo;
  }

  async getUnitsOfLocation(payload: GetUnitsOfLocationDto) {
    const { id } = payload;
    const location = await this.locationModel.findById(id);
    if (!location) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }

    return this.unitModel.aggregate([
      {
        $match: {
          location: new mongoose.Types.ObjectId(id),
          isActive: true,
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: 'agreementlines',
          localField: '_id',
          foreignField: 'units',
          let: { unit: '$_id' },
          pipeline: [
            { $match: { isDeleted: false } },
            { $unwind: '$units' },
            { $project: { _id: 1, contract: 1, unit: '$units' } },
            { $match: { $expr: { $eq: ['$unit', '$$unit'] } } },
            {
              $lookup: {
                from: 'costlinegenerals',
                localField: '_id',
                foreignField: 'agreementLine',
                as: 'costLineGenerals',
                let: { var__unit: '$$unit' },
                pipeline: [
                  { $match: { $expr: { $eq: ['$unit', '$$var__unit'] } } },
                  {
                    $match: {
                      $or: [
                        { endDate: { $exists: false } },
                        { endDate: null },
                        {
                          endDate: {
                            $gt: dayjs()
                              .utc()
                              .endOf('day')
                              .subtract(1, 'day')
                              .toDate(),
                          },
                        },
                      ],
                    },
                  },
                  { $project: { _id: 1 } },
                ],
              },
            },
            { $match: { 'costLineGenerals.0': { $exists: true } } },
          ],
          as: 'agreementlines',
        },
      },
      {
        $lookup: {
          from: 'contracts',
          localField: 'agreementlines.contract',
          foreignField: '_id',
          pipeline: [
            { $match: { type: ContractType.RENTING, isActive: true } },
            {
              $lookup: {
                from: 'contacts',
                localField: 'contact',
                foreignField: '_id',
                as: 'contact',
                pipeline: [{ $project: { displayName: 1, identifier: 1 } }],
              },
            },
            { $unwind: { path: '$contact', preserveNullAndEmptyArrays: true } },
            { $project: { type: 1, startDate: 1, contact: 1 } },
          ],
          as: 'contracts',
        },
      },
      { $project: { agreementlines: 0 } },
    ]);
  }

  async updateUnitsOfLocation(payload: UpdateUnitsOfLocationDto) {
    const { id, units } = payload;

    const location = await this.locationModel.findById(id);
    if (!location) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }

    const rootUnit = await this.unitService.findRootUnitOfLocation(id);

    const flattenedUnits = flattenUnits(units, rootUnit?._id.toString()).filter(
      (unit) => unit._id !== rootUnit?._id.toString(),
    );

    // Check duplicate unit _id
    const unitIds = flattenedUnits
      .filter((unit) => unit._id)
      .map((unit) => unit._id);

    if (unitIds.length !== new Set(unitIds).size) {
      throw new BadRequestException(LOCATION_MESSAGE_KEYS.DUPLICATE_UNIT_ID);
    }

    const { insertedIds, upsertedIds } = await this.unitService.bulkUpdate(
      id,
      flattenedUnits,
    );

    const insertedIdArrays = Object.keys(insertedIds).map(
      (key) => insertedIds[key],
    );

    const upsertedIdArrays = Object.keys(upsertedIds).map(
      (key) => upsertedIds[key],
    );

    const { maxOccupants, maxArea } =
      await this.reCalculateMaxOccupantsArea(id);

    await Promise.all([
      this.unitService.updateRootUnitOfLocation(id, {
        maxOccupants,
        maxArea,
      }),
      this.locationModel.updateOne(
        {
          _id: id,
        },
        {
          $set: {
            maxOccupants,
            maxArea,
          },
          $addToSet: {
            units: { $each: [...insertedIdArrays, ...upsertedIdArrays] },
          },
        },
      ),
    ]);

    await this.eventEmitterSender.updateLocationEvent(location);

    return this.unitService.findUnitsOfLocation(id);
  }

  async reCalculateMaxOccupantsArea(locationId: string) {
    const location = await this.locationModel.findById(locationId).lean();

    if (!location) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }

    const rootUnit = await this.unitService.findRootUnitOfLocation(locationId);

    if (!rootUnit) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.ROOT_UNIT_NOT_FOUND);
    }

    const units = await this.unitModel
      .find({
        parent: rootUnit._id,
        isActive: true,
      })
      .lean();

    // Calculate maxOccupants and maxArea excluding root unit
    const { maxOccupants, maxArea } = units.reduce(
      (acc, unit) => {
        if (unit.isRoot) {
          return acc;
        }

        return {
          maxOccupants: acc.maxOccupants + unit.maxOccupants,
          maxArea: acc.maxArea + unit.maxArea,
        };
      },
      { maxOccupants: 0, maxArea: 0 },
    );

    return {
      maxOccupants,
      maxArea,
    };
  }

  async getLocationNearBy(payload: GetLocationNearByQueryDto) {
    const {
      centerLng = 5.21266,
      centerLat = 51.69186,
      distance = Infinity,
      team,
      locationType,
      isVacanted,
    } = payload;

    const currentDate = dayjs().utc().startOf('day').toDate();

    await this.locationModel.collection.createIndex({ geo: '2dsphere' });
    const locationAggregates: any[] = [
      {
        $geoNear: {
          near: { type: 'Point', coordinates: [+centerLng, +centerLat] },
          maxDistance: +distance,
          key: 'geo',
          distanceField: 'distance',
        },
      },
      {
        $match: {
          isActive: true,
          ...(team && {
            team: new mongoose.Types.ObjectId(team),
          }),
          ...(locationType === 'renting' && {
            isRenting: true,
          }),
          ...(locationType === 'service' && {
            isService: true,
          }),
        },
      },
      {
        $lookup: {
          from: 'statsoccupants',
          localField: '_id',
          foreignField: 'location',
          as: 'statsOccupants',
          pipeline: [
            {
              $match: {
                reportCreatedDate: currentDate,
              },
            },
            {
              $limit: 1,
            },
          ],
        },
      },
      {
        $addFields: {
          statsOccupants: {
            $cond: {
              if: { $eq: [{ $size: '$statsOccupants' }, 0] },
              then: null,
              else: { $arrayElemAt: ['$statsOccupants', 0] },
            },
          },
        },
      },
      {
        $addFields: {
          vacantBeds: {
            $cond: {
              if: { $eq: ['$statsOccupants', null] },
              then: 0,
              else: '$statsOccupants.emptyCount',
            },
          },
        },
      },
      {
        $addFields: {
          totalBeds: {
            $cond: {
              if: { $eq: ['$statsOccupants', null] },
              then: '$maxOccupants',
              else: '$statsOccupants.maxCount',
            },
          },
        },
      },
      {
        $lookup: {
          from: 'teams',
          localField: 'team',
          foreignField: '_id',
          as: 'team',
          pipeline: [
            {
              $project: {
                _id: 1,
                name: 1,
              },
            },
          ],
        },
      },
      {
        $addFields: {
          team: { $arrayElemAt: ['$team', 0] },
        },
      },
      ...(isVacanted
        ? [
            {
              $match: {
                vacantBeds: {
                  $gt: 0,
                },
              },
            },
          ]
        : []),
      {
        $project: {
          _id: 1,
          fullAddress: 1,
          isRenting: 1,
          isService: 1,
          geo: 1,
          team: 1,
          vacantBeds: 1,
          totalBeds: 1,
        },
      },
    ];
    const locations = await this.locationModel
      .aggregate(locationAggregates)
      .exec();
    return locations;
  }

  async getTotalStatsOccupantOfAllActiveLocations() {
    const activeLocations = await this.getLocationNearBy({
      centerLng: '5.21266',
      centerLat: '51.69186',
      //distance: '577790.5767',
    });

    const vaccantLocations = activeLocations.filter(
      (activeLocation) => activeLocation.vacantBeds > 0,
    );

    return {
      totalActive: activeLocations.length,
      totalBeds: _.sumBy(activeLocations, 'totalBeds'),
      totalVacantBeds: _.sumBy(activeLocations, 'vacantBeds'),
      vacantLocations: vaccantLocations.map(
        (vacantLocation) => vacantLocation._id,
      ),
    };
  }
}
