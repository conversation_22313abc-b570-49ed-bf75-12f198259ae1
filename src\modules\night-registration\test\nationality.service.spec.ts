import { Test, TestingModule } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockNightRegistrationNationality } from '~/test/mocks/nightregistrationnationality.mock';

import { NightRegistrationNationalityModel } from '../models/night-registration-nationality.model';
import { NationalityService } from '../nationality.service';
import { nightRegistrationTest } from './night-registration.dto.test';

describe('NationalityService', () => {
  let service: NationalityService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        NationalityService,
        ...testInjectModel([NightRegistrationNationalityModel]),
      ],
    }).compile();

    service = module.get(NationalityService);

    // Init data
    await initMockNightRegistrationNationality({
      _id: new ObjectId(),
      name: 'Vietnamese',
      code: 'VN',
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getNationalities', () => {
    it('should return a list of nationalities', async () => {
      const result = await service.getNationalities({});
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(
        nightRegistrationTest.getNationalitiesSchema,
      );
    });

    it('should return list empty when data not exist', async () => {
      const result = await service.getNationalities({
        query: { code: 'ABC' },
      });
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });
});
