import { ContactRole, ContactType } from '~/shared/enums/contact.enum';

export interface AFASDebtor {
  Verkooprelatie: string;
  Adres: number;
  Type: string;
  Name: string;
  OrgNumber: string;
  PerNumber: string;
  Department: string;
  Blocked: boolean;
  AddressLine1: string;
  AddressLine3: string;
  AddressLine4: string;
  TelWork: string;
  MobWork: string;
  MailWork: string;
  Note: string;
  Function: string;
  FunctionCard: string;
  FullName: string;
  Gender: string;
  TelPrivate: string;
  MobPrivate: string;
  MailPrivate: string;
  CreateDate: string;
  ModifiedDate: string;
  ContactId: number;
  Postcode: string;
  Straat: string;
  Huisnummer: number;
  Woonplaats: string;
  Regio: string;
  Land: string;
  Land_2: string;
  Provincie: string;
  Omschrijving: string;
}

export interface TransformedAFASDebtor {
  identifier: string;
  name: string;
  displayName: string;
  contactRole: ContactRole;
  contactType: ContactType;
  orgNumber: string;
  isActive: boolean;
  isDeleted: boolean;
  isSynced: boolean;
  phone1: string;
  email: string;
  address1: {
    street: string;
    houseNumber: number;
    postCode: string;
    city: string;
  };
  country: {
    code: string;
    name: string;
  };
  region?: {
    name: string;
  };
}
