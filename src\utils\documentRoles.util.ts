import { DocumentFileTypeEnum } from '~/shared/enums/document-file-type.enum';
import { TenantRoleEnum } from '~/shared/enums/tenant-role.enum';

export const getRoleByDocumentType = (type?: DocumentFileTypeEnum) => {
  switch (type) {
    case DocumentFileTypeEnum.HR:
      return TenantRoleEnum.HR_UPLOADER;
    case DocumentFileTypeEnum.NIGHT_REGISTRATIONS:
      return TenantRoleEnum.NIGHT_REGISTRATION_UPLOADER;
    case DocumentFileTypeEnum.FACILITIES:
      return TenantRoleEnum.FACILITIES_UPLOADER;
    case DocumentFileTypeEnum.SUPPORT:
      return TenantRoleEnum.SUPPORT_UPLOADER;
    default:
      return TenantRoleEnum.HR_UPLOADER;
  }
};
