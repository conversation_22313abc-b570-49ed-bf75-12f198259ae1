import { ZodSchema } from 'zod';

export const zodMatchers = {
  toMatchSchema: (received: unknown, schema: ZodSchema<any>) => {
    const result = schema.safeParse(received);

    const pass = result.success;
    if (pass) {
      return {
        message: () =>
          `expected object **not** to match the schema, but it did`,
        pass: true,
      };
    } else {
      return {
        message: () =>
          `expected object to match the schema but got errors: ${result.error.message}`,
        pass: false,
      };
    }
  },
};
