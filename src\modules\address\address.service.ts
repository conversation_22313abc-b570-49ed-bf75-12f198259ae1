import { BadRequestException, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';

import { ADDRESS_MESSAGE_KEYS } from '~/shared/message-keys/address.message-keys';
import { InjectModel } from '~/transformers/model.transformer';

import { AddressDto } from '../contact/dtos/contact.dto';
import { CountryModel } from '../country/country.model';
import { RegionModel } from '../region/region.model';

@Injectable()
export class AddressService {
  constructor(
    @InjectModel(RegionModel)
    private readonly regionModel: Model<RegionModel>,
    @InjectModel(CountryModel)
    private readonly countryModel: Model<CountryModel>,
  ) {}

  async validationAddressCountryRegion(address: AddressDto) {
    const country = await this.countryModel.findById(address.country).lean();
    if (!country) {
      throw new BadRequestException(ADDRESS_MESSAGE_KEYS.INVALID_COUNTRY);
    }

    const region = await this.regionModel.findById(address.region).lean();
    if (!region) {
      throw new BadRequestException(ADDRESS_MESSAGE_KEYS.INVALID_REGION);
    }

    if (region.country.toString() !== address.country) {
      throw new BadRequestException(ADDRESS_MESSAGE_KEYS.INVALID_REGION);
    }

    return {
      country,
      region,
    };
  }
}
