import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { addressTest } from '~/modules/address/test/address.dto.test';
import { bvCompanyTest } from '~/modules/bvcompany/test/bvcompany.dto.test';
import { contactTest } from '~/modules/contact/test/contact.dto.test';
import { countryTest } from '~/modules/country/test/country.dto.test';
import { regionTest } from '~/modules/region/test/region.dto.test';
import { teamTest } from '~/modules/team/test/team.dto.test';
import { EnergyLabel } from '~/shared/enums/location.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isActive: z.boolean(),
    isRenting: z.boolean(),
    isService: z.boolean(),
    maxOccupants: z.number(),
    maxArea: z.number(),
    bvCompany: z.instanceof(ObjectId),
    fullAddress: z.string(),
    address: z.instanceof(ObjectId),
    email: z.string(),
    locationOf: z.array(
      z
        .object({
          key: z.string(),
          value: z.string(),
          position: z.number(),
        })
        .nullish(),
    ),
    geo: z
      .object({
        type: z.string(),
        coordinates: z.object({
          lng: z.number(),
          lat: z.number(),
        }),
      })
      .optional(),
    team: z.instanceof(ObjectId),
    units: z.array(z.instanceof(ObjectId)),
    costCenter: z.instanceof(ObjectId),
    uploadFiles: z.array(z.instanceof(ObjectId)).optional(),
    parkingSpaces: z.number().optional(),
    energyLabel: z.nativeEnum(EnergyLabel),
    maximumStayDuration: z.number().int().nullish(),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(
  modelSchema
    .pick({
      _id: true,
      isActive: true,
      fullAddress: true,
    })
    .extend({
      address: z
        .object({
          _id: z.instanceof(ObjectId),
          region: regionTest.modelSchema.pick({ _id: true, name: true }),
          country: countryTest.modelSchema.pick({
            _id: true,
            name: true,
            code: true,
          }),
        })
        .nullish(),
      team: teamTest.modelSchema.pick({ _id: true, name: true }).nullish(),
    }),
);

const findAllMbSchema = z.array(
  z.object({
    _id: z.instanceof(ObjectId),
    isActive: z.boolean(),
    fullAddress: z.string(),
  }),
);

const findOneSchema = modelSchema
  .omit({ maxOccupants: true, maxArea: true, units: true })
  .extend({
    address: addressTest.modelSchema
      .omit({
        location: true,
        contact: true,
      })
      .extend({
        region: regionTest.modelSchema.pick({ _id: true, name: true }),
        country: countryTest.modelSchema.pick({
          _id: true,
          name: true,
          code: true,
        }),
      }),
    team: teamTest.modelSchema.pick({ _id: true, name: true }),
    bvCompany: bvCompanyTest.modelSchema.pick({
      _id: true,
      identifier: true,
      name: true,
    }),
    geo: z.object({
      lng: z.number(),
      lat: z.number(),
    }),
    costCenter: z
      .object({
        _id: z.instanceof(ObjectId),
        name: z.string(),
        identifier: z.string(),
        isActive: z.boolean(),
        isSynced: z.boolean(),
      })
      .nullish(),
    contracts: z.array(
      z
        .object({
          _id: z.instanceof(ObjectId),
          contact: contactTest.modelSchema.pick({
            _id: true,
            contactType: true,
            name: true,
            displayName: true,
          }),
        })
        .nullish(),
    ),
  });

const reCalculateMaxOccupantsAreaSchema = z.object({
  maxOccupants: z.number(),
  maxArea: z.number(),
});

const getLocationNearBySChema = z.array(
  modelSchema
    .pick({
      _id: true,
      isRenting: true,
      isService: true,
      fullAddress: true,
      geo: true,
    })
    .extend({
      team: teamTest.modelSchema.pick({ _id: true, name: true }),
      vacantBeds: z.number().optional(),
      totalBeds: z.number().optional(),
    }),
);

export const locationTest = {
  modelSchema,
  findAllSchema,
  findAllMbSchema,
  findOneSchema,
  reCalculateMaxOccupantsAreaSchema,
  getLocationNearBySChema,
};
