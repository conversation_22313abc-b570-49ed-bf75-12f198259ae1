import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { COSTLINE_GENERAL_MESSAGES } from '~/shared/messages/costlinegeneral.message';

import { CostlinegeneralService } from './costlinegeneral.service';
import { BulkEditCostLineGeneralDto } from './dtos/update-costlinegeneral.dto';

@Controller('costlinegeneral')
export class CostlinegeneralController {
  constructor(
    private readonly costlinegeneralService: CostlinegeneralService,
  ) {}

  @MessagePattern({ cmd: COSTLINE_GENERAL_MESSAGES.BULK_EDIT_COSTLINE_GENERAL })
  @UsePipes(new ZodValidationPipe(BulkEditCostLineGeneralDto))
  async bulkEdit(@Payload() payload: BulkEditCostLineGeneralDto) {
    return this.costlinegeneralService.bulkEdit(payload);
  }

  @MessagePattern({ cmd: COSTLINE_GENERAL_MESSAGES.DELETE_COSTLINE_GENERAL })
  async softDelete(@Payload() payload: any) {
    return this.costlinegeneralService.softDelete(payload);
  }
}
