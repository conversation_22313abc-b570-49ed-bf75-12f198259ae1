import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection
    .aggregate()
    .match({
      _id: { $gt: nextId },
    })
    .lookup({
      from: 'CreditorContact',
      localField: 'supplier',
      foreignField: '_id',
      as: 'supplier',
    })
    .unwind({
      path: '$supplier',
    })
    .sort({ _id: 1 })
    .limit(limit);
};

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(async (article: { _id: Types.ObjectId; [key: string]: any }) =>
      omitNull({
        _id: article._id,
        identifier: article.identifier,
        description: article.description,
        category: article.category, // Reference to articlecategory
        supplier: article.supplier?.grouping ?? article.supplier?.person, // Reference to contacts
        removalFee: article.removalFee,
        salePrice: article.salePrice,
        purchasePrice: article.purchasePrice,
        margin: article.margin,
        laborPricePerHour: article.laborPricePerHour,
        workingTime: article.workingTime,
        minimumStock: article.minimumStock,
        expectedDelivery: article.expectedDelivery,
        batchSize: article.batchSize,
        isActive: article.active,
        isDeleted: !article.active,
        createdAt: article._id.getTimestamp(),
        updatedAt: article.updatedAt,
      }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'article',
      destinationCollectionName: 'article',
      tranformDataFunc: transformData,
      pagingFunc,
      inventoryMode: true,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
