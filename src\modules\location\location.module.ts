import { Module } from '@nestjs/common';

import { AddressModule } from '~/modules/address/address.module';
import { CostlineModule } from '~/modules/costline/costline.module';
import { JobTemplateModule } from '~/modules/job-template/job-template.module';
import { LocationAdditionalModule } from '~/modules/location-addtional/location-additional.module';
import { UnitModule } from '~/modules/unit/unit.module';
import { CoreEventEmitterModule } from '~/processors/event-emitter/event-emitter.module';

import { CountryModule } from '../country/country.module';
import { TenantUserModule } from '../tenant-user/tenant-user.module';
import { LocationController } from './location.controller';
import { LocationService } from './location.service';

@Module({
  imports: [
    CountryModule,
    TenantUserModule,
    CoreEventEmitterModule,
    CostlineModule,
    LocationAdditionalModule,
    UnitModule,
    AddressModule,
    JobTemplateModule,
  ],
  providers: [LocationService],
  controllers: [LocationController],
  exports: [LocationService],
})
export class LocationModule {}
