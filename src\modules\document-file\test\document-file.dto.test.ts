import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { DocumentFileTypeEnum } from '~/shared/enums/document-file-type.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

import { uploadFileTest } from './upload-file.dto.test';

const modelSchema = z
  .object({
    type: z.nativeEnum(DocumentFileTypeEnum),
    fileName: z.string(),
    uploaderName: z.string(),
    createdBy: z.instanceof(ObjectId),
    updatedBy: z.instanceof(ObjectId).optional(),
    location: z.instanceof(ObjectId).optional(),
    uploadFile: z.instanceof(ObjectId),
    bvCompany: z.string().optional(),
    migratedAt: z.date().optional(),
  })
  .extend(baseModelTestSchema);

const baseUploadFileSchema = uploadFileTest.modelSchema.pick({
  _id: true,
  extension: true,
  mimeType: true,
  size: true,
  publicUrl: true,
  provider: true,
});

const baseDocumentFileSchema = modelSchema.extend({
  location: z
    .object({
      _id: z.instanceof(ObjectId),
      fullAddress: z.string(),
    })
    .nullable()
    .optional(),
  uploadFile: baseUploadFileSchema,
});

const createDocumentFileSchema = z.array(baseDocumentFileSchema);

const getListDocumentFileSchema = z.array(
  baseDocumentFileSchema
    .omit({
      createdBy: true,
    })
    .extend({
      uploadFile: baseUploadFileSchema.extend({
        originalFilename: z.string(),
      }),
    }),
);

export const documentFileTest = {
  modelSchema,
  createDocumentFileSchema,
  getListDocumentFileSchema,
};
