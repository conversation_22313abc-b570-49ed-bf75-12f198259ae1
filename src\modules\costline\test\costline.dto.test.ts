import { ObjectId } from 'mongodb';
import { z } from 'zod';

import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  ContractType,
  CostLineStatus,
} from '~/shared/enums/contract.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isCustom: z.boolean(),
    isCredit: z.boolean(),
    status: z.nativeEnum(CostLineStatus),
    description: z.string(),
    position: z.number(),
    totalPrice: z.number(),
    price: z.number(),
    quantity: z.number(),
    startDate: z.date().nullable().optional(),
    endDate: z.date().nullable().optional(),
    type: z.nativeEnum(ContractType),
    period: z.nativeEnum(AgreementLinePeriod),
    periodType: z.nativeEnum(AgreementLinePeriodType),
    canceledAt: z.date(),
    approvedAt: z.date(),
    unit: z.instanceof(ObjectId),
    costLineGeneral: z.instanceof(ObjectId),
    agreementLine: z.instanceof(ObjectId),
    costType: z.instanceof(ObjectId),
    invoice: z.instanceof(ObjectId),
    contact: z.instanceof(ObjectId),
    location: z.instanceof(ObjectId),
    costCenter: z.instanceof(ObjectId),
    job: z.instanceof(ObjectId),
    parent: z.instanceof(ObjectId),
  })
  .extend(baseModelTestSchema);

const baseCostLineSchema = modelSchema
  .pick({
    _id: true,
    contact: true,
    description: true,
    price: true,
    quantity: true,
    totalPrice: true,
    costType: true,
    isCustom: true,
    isCredit: true,
    type: true,
  })
  .extend({
    location: z.instanceof(ObjectId).optional(),
    costCenter: z.instanceof(ObjectId).optional(),
  });

const createCustomCostLine = z.array(baseCostLineSchema);

const deleteCostLineSchema = baseCostLineSchema.passthrough();

const createCreditCostLinesSchema = z.array(
  baseCostLineSchema.merge(
    modelSchema.pick({
      status: true,
      position: true,
      costType: true,
      contact: true,
      location: true,
      parent: true,
    }),
  ),
);

export const costlineTest = {
  modelSchema,
  createCustomCostLine,
  deleteCostLineSchema,
  createCreditCostLinesSchema,
};
