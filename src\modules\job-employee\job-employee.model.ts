import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import { JobDocument, JobModel } from '../job/job.model';
import {
  TenantUserDocument,
  TenantUserModel,
} from '../tenant-user/tenant-user.model';

export type JobEmployeeDocument = DocumentType<JobEmployeeModel>;

@index({ job: 1 })
@index({ employee: 1, job: 1 })
@index({ isActive: 1, isDeleted: 1 })
@modelOptions({ options: { customName: 'JobEmployees' } })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class JobEmployeeModel extends BaseModel {
  @prop({ ref: () => TenantUserModel })
  employee!: Ref<TenantUserDocument>;

  @prop({ ref: JobModel })
  job!: Ref<JobDocument>;

  @prop({ default: 0 })
  estimatedHours!: number;

  @prop({ default: 0 })
  actualHours?: number;

  @prop()
  plannedDate?: Date;
}
