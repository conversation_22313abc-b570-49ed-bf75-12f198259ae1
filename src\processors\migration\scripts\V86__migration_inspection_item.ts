import { Collection } from 'mongodb';
import { mongo, ObjectId, Types } from 'mongoose';
import * as path from 'path';

import { migrationV2 } from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface Action {
  desciption: string;
  images: string[];
  order: number;
  isGrouped: boolean;
  addNewJob: any;
}

interface OldInspectionItem {
  _id: string;
  status: string;
  unit: ObjectId;
  inspection: ObjectId;
  description: string;
  position: number;
  actions: Action[];
  images: string[];
  findings: string;
  costline: any[];
  createdAt: Date;
  updatedAt: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection
    .aggregate()
    .match({
      _id: { $gt: nextId },
    })
    .sort({ _id: 1 })
    .limit(limit)
    .lookup({
      from: 'costline',
      localField: '_id',
      foreignField: 'inspectionItem',
      as: 'costline',
    });
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldInspectionItem[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (item) => {
      const actions = Array.isArray(item.actions) ? item.actions : [];
      return {
        _id: item._id,
        isGrouped: actions.some((action) => action.isGrouped),
        status: item.status.toLowerCase(),
        description: item.description,
        position: item.position,
        images: item.images,
        unit: item.unit,
        job: item.inspection,
        notes: item.findings,
        actions: actions.map((action) => ({
          images: action.images,
          description: action.desciption,
          isGrouped: action.isGrouped ?? false,
          type: action.addNewJob?.jobType
            ? action.addNewJob?.jobType.toLowerCase()
            : '',
        })),
        costLines: item.costline.map((costline: any) => costline._id),
        isDeleted: false,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      };
    }),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'inspectionitem',
      destinationCollectionName: 'jobpoints',
      pagingFunc,
      tranformDataFunc: tranformDataFunc,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
