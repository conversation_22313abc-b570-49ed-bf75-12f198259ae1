import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import {
  TenantUserDocument,
  TenantUserModel,
} from '../tenant-user/tenant-user.model';

export type TeamDocument = DocumentType<TeamModel>;

@modelOptions({
  options: { customName: 'Team' },
})
@index({ name: 1 }, { unique: true })
@index({ isActive: 1, isDeleted: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class TeamModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({ default: 0 })
  position!: number;

  @prop({ required: true, unique: true, trim: true, maxlength: 128 })
  name!: string;

  @prop({ trim: true, maxlength: 256 })
  description!: string;

  @prop({ default: false })
  isEquipment!: boolean;

  @prop({ ref: () => TenantUserModel, default: [] })
  tenantUsers!: Ref<TenantUserDocument>[];
}
