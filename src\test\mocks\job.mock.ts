import { getModelForClass } from '@typegoose/typegoose';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { z } from 'zod';

import { JobModel } from '~/modules/job/job.model';
import { jobTest } from '~/modules/job/test/job.dto.test';
import {
  JobFDaysEnum,
  JobPeriodTypeEnum,
  JobReportTypeEnum,
  JobStatusEnum,
  JobTypeEnum,
} from '~/shared/enums/job.enum';

import { mockContactData } from './contact.mock';
import { mockLocationData } from './location.mock';
import { mockTeamData } from './team.mock';
import { mockTenantUserData } from './tenantuser.mock';
import { mockUnitData } from './unit.mock';

const jobModel = getModelForClass(JobModel);
type jobType = z.infer<typeof jobTest.modelSchema>;

export const mockJobData = {
  _id: new ObjectId(),
  assignee: mockTenantUserData._id,
  assigneeInfo: {
    _id: mockTenantUserData._id,
    displayName: mockTenantUserData.displayName,
    email: mockTenantUserData.email,
  },
  reportType: JobReportTypeEnum.INTERNAL,
  createdAt: new Date(),
  createdBy: mockContactData._id,
  equipments: [],
  fDays: Object.values(JobFDaysEnum),
  fEndDate: dayjs().add(1, 'year').toDate(),
  fIdentifier: '00000',
  fInterval: 1,
  fRule: 'daily',
  fStartDate: new Date(),
  identifier: nanoid(12),
  images: [],
  instructions: '',
  invoiceContact: mockContactData._id,
  isActive: true,
  isDeleted: false,
  isSendRC: false,
  isSendRR: false,
  jobType: JobTypeEnum.INSPECTION,
  location: mockLocationData._id,
  locationInfo: {
    _id: mockLocationData._id.toString(),
    fullAddress: 'Hedel, Prins Bernhardstraat 1',
  },
  locationTeamInfo: {
    _id: mockTeamData._id.toString(),
    name: '3',
  },
  plannedDate: new Date(),
  planner: mockContactData._id,
  plannerInfo: {
    _id: mockContactData._id,
    displayName: 'Nicole',
    email: '<EMAIL>',
  },
  rrContacts: [],
  rtContacts: [],
  status: JobStatusEnum.COMPLETE,
  title: '',
  type: JobPeriodTypeEnum.PERIODIC,
  units: [mockUnitData._id],
  updatedAt: new Date(),
  updatedBy: mockContactData._id,
};

export async function initMockJob(doc?: Partial<jobType>) {
  const { _id, ...rest } = { ...mockJobData, ...doc };
  await jobModel.replaceOne({ _id }, rest, { upsert: true });
}
