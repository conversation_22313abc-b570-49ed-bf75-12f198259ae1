{"compilerOptions": {"jsx": "react", "module": "commonjs", "declaration": false, "noImplicitAny": false, "removeComments": true, "noLib": false, "importHelpers": true, "allowSyntheticDefaultImports": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "resolveJsonModule": true, "esModuleInterop": true, "target": "ES2022", "sourceMap": true, "outDir": "./dist", "baseUrl": ".", "incremental": true, "strictNullChecks": true, "skipLibCheck": true, "noUnusedParameters": true, "noUnusedLocals": true, "strict": true, "paths": {"~": ["./src"], "~/*": ["./src/*"]}}, "include": ["src/**/*", ".d.ts", "scripts/**/*", "jest.config.ts"], "exclude": ["node_modules"]}