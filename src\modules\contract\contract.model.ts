import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { ContractType } from '~/shared/enums/contract.enum';
import { BaseModel } from '~/shared/models/base.model';

import {
  AgreementLineDocument,
  AgreementLineModel,
} from '../agreementline/agreementline.model';
import { ContactDocument, ContactModel } from '../contact/contact.model';
import {
  ContractTypeDocument,
  ContractTypeModel,
} from '../contract-type/contract-type.model';
import {
  CostCenterDocument,
  CostCenterModel,
} from '../costcenter/costcenter.model';
import { LocationDocument, LocationModel } from '../location/location.model';

export type ContractDocument = DocumentType<ContractModel>;

@modelOptions({
  options: { customName: 'Contract' },
})
@index({ isActive: 1, isGenerateCostLine: 1, endDate: 1, isSigned: 1 })
@index({ identifier: 1 })
@index({ location: 1 })
@index({ costCenter: 1 })
@index({ contact: 1 })
@index({ type: 1, contact: 1, location: 1, costCenter: 1, startDate: 1 })
@index({ startDate: 1 })
@index({ endDate: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class ContractModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({ default: true })
  isGenerateCostLine!: boolean;

  @prop({ default: true })
  isSigned!: boolean;

  @prop({ default: true })
  isWholeLocation!: boolean;

  @prop({ trim: true, required: true })
  identifier!: string;

  @prop({ enum: ContractType })
  type!: ContractType;

  @prop({ required: true })
  startDate!: Date;

  @prop()
  endDate!: Date;

  @prop()
  note!: string;

  @prop({ required: true, default: 3 })
  generatePeriod!: number;

  @prop()
  noticeDays!: string;

  @prop()
  signedAt!: Date;

  @prop()
  isNew?: boolean;

  @prop({ type: () => [Attachment], _id: false })
  attachments!: Attachment[];

  @prop({ ref: () => ContactModel })
  contact!: Ref<ContactDocument>;

  @prop({ ref: () => LocationModel })
  location!: Ref<LocationDocument>;

  @prop({ ref: () => AgreementLineModel })
  agreementLines!: Ref<AgreementLineDocument>[];

  @prop({ ref: () => CostCenterModel })
  costCenter?: Ref<CostCenterDocument>;

  @prop({ ref: () => ContractTypeModel })
  contractType?: Ref<ContractTypeDocument>;
}

class Attachment {
  @prop({ required: true })
  originalFilename!: string;

  @prop({ required: true })
  publicUrl!: string;
}
