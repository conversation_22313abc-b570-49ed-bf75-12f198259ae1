import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { contactTest } from '~/modules/contact/test/contact.dto.test';
import { contractTest } from '~/modules/contract/test/contract.dto.test';
import {
  CertificateAndControlInspectionType,
  LocationAdditionalGroupType,
  LocationAdditionalType,
} from '~/shared/enums/location-additional.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

import { locationAdditionalGroupNameTest } from './location-additional-group-name.dto.test';

const modelSchema = z
  .object({
    type: z.nativeEnum(LocationAdditionalType),
    inspectionType: z.nativeEnum(CertificateAndControlInspectionType),
    groupName: z.instanceof(ObjectId),
    groupType: z.nativeEnum(LocationAdditionalGroupType),
    position: z.number(),
    description: z.string().optional(),
    contact: z.instanceof(ObjectId).optional(),
    location: z.instanceof(ObjectId).optional(),
    contract: z.instanceof(ObjectId).optional(),
    code: z.string().optional(),
    dateCheck: z.date().optional(),
    brandType: z.string().optional(),
    name: z.string().optional(),
    yearInstallation: z.number().optional(),
    smartMeter: z.boolean().optional(),
    meterNumber: z.string().optional(),
    recordLogs: z
      .array(
        z.object({
          date: z.date(),
          position: z.number(),
          number: z.string(),
        }),
      )
      .optional(),
    contractNumber: z.string().optional(),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(modelSchema);

const getAdditionalGroupNamesSchema = z.array(
  locationAdditionalGroupNameTest.modelSchema,
);

const contactBaseSchema = contactTest.modelSchema.pick({
  _id: true,
  displayName: true,
  organizationNames: true,
});

const contractBaseSchema = contractTest.modelSchema
  .pick({
    _id: true,
    identifier: true,
  })
  .extend({
    contact: contactBaseSchema.nullable().optional(),
  });

const getAdditionalByLocationBaseSchema = modelSchema
  .pick({
    _id: true,
    type: true,
    position: true,
  })
  .extend({
    groupName: locationAdditionalGroupNameTest.modelSchema
      .pick({
        _id: true,
        position: true,
        description: true,
        dutchDescription: true,
      })
      .nullable()
      .optional(),
    contact: contactBaseSchema.nullable().optional(),
  });

const getAdditionalByLocationTypeCertificateSchema = z.array(
  getAdditionalByLocationBaseSchema.merge(
    modelSchema.pick({
      description: true,
      dateCheck: true,
      brandType: true,
      name: true,
      yearInstallation: true,
      groupType: true,
      inspectionType: true,
    }),
  ),
);

const getAdditionalByLocationTypeGweSchema = z.array(
  getAdditionalByLocationBaseSchema.merge(
    modelSchema
      .pick({
        smartMeter: true,
        meterNumber: true,
        code: true,
        contractNumber: true,
        recordLogs: true,
      })
      .extend({
        contract: contractBaseSchema.nullable().optional(),
      }),
  ),
);

const getAdditionalByLocationTypeFeatureSchema = z.array(
  getAdditionalByLocationBaseSchema.merge(
    modelSchema
      .pick({
        groupType: true,
        description: true,
        code: true,
      })
      .extend({
        contract: contractBaseSchema.nullable().optional(),
      }),
  ),
);

export const locationAdditionalTest = {
  modelSchema,
  findAllSchema,
  getAdditionalGroupNamesSchema,
  getAdditionalByLocationTypeCertificateSchema,
  getAdditionalByLocationTypeGweSchema,
  getAdditionalByLocationTypeFeatureSchema,
};
