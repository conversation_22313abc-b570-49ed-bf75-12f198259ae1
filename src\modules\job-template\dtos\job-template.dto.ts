import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { JobTemplateTypeEnum } from '~/shared/enums/job-template.enum';

const jobTemplateEnum = Object.values(JobTemplateTypeEnum) as [
  string,
  ...string[],
];

export const CreateJobTemplateSchema = z.strictObject({
  type: z.enum(jobTemplateEnum),
  name: z.string().max(256).optional(),
  points: z.array(
    z.strictObject({
      _id: z.string().optional(),
      description: z.string().max(256),
      position: z.number().min(0).optional(),
    }),
  ),
  unit: z
    .string()
    .refine((v) => isValidObjectId(v))
    .optional(),
  user: z.string().refine((v) => isValidObjectId(v)),
});

export const GetJobTemplateByUnits = z.strictObject({
  units: z
    .array(z.string().refine((v) => isValidObjectId(v)))
    .min(1)
    .refine((v) => {
      return new Set(v).size === v.length;
    }),
});

export const JobTemplateIdSchema = z.strictObject({
  id: z.string().refine((v) => isValidObjectId(v)),
});

export const UpdateJobTemplateSchema = CreateJobTemplateSchema.omit({
  type: true,
  unit: true,
}).merge(JobTemplateIdSchema);

export const DeletePointJobTemplateSchema = JobTemplateIdSchema.extend({
  pointId: z.string().refine((val) => val.length === 21, {
    message: 'ID must be 21 characters long, as generated by nanoid',
  }),
  user: z.string().refine((v) => isValidObjectId(v)),
});

export const CopyJobTemplateSchema = z.strictObject({
  sourceUnitIds: z.array(z.string().refine((v) => isValidObjectId(v))).min(1),
  targetUnitIds: z.array(z.string().refine((v) => isValidObjectId(v))).min(1),
  user: z.string().refine((v) => isValidObjectId(v)),
});

export class JobTemplateIdDto extends createZodDto(JobTemplateIdSchema) {}

export class GetJobTemplateByUnitsDto extends createZodDto(
  GetJobTemplateByUnits,
) {}

export class CreateJobTemplateDto extends createZodDto(
  CreateJobTemplateSchema,
) {}

export class UpdateJobTemplateDto extends createZodDto(
  UpdateJobTemplateSchema,
) {}

export class DetailJobTemplateDto extends createZodDto(JobTemplateIdSchema) {}

export class DeleteJobTemplateDto extends createZodDto(
  JobTemplateIdSchema.extend({
    user: z.string().refine((v) => isValidObjectId(v)),
  }),
) {}

export class DeletePointJobTemplateDto extends createZodDto(
  DeletePointJobTemplateSchema,
) {}

export class CopyJobTemplateDto extends createZodDto(CopyJobTemplateSchema) {}
