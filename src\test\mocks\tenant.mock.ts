import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { TenantModel } from '~/modules/tenant/tenant.model';

const tenantModel = getModelForClass(TenantModel);

export const mockTenantData = {
  _id: new ObjectId(),
  isDeleted: false,
  isActive: true,
  name: 'BlueTech Solutions',
  tenantConfigs: {
    endpoint: 'https://api.bluetech.io',
    uploadProvider: 'aws',
    aws: {
      accessKeyId: 'AKIAIOSFODNN7EXAMPLE',
      secretAccessKey: 'wJalrXUtnFEMI/K7MDENG/bPxRfiCYEXAMPLEKEY',
      region: 'ap-southeast-1',
      bucketName: 'bluetech-uploads',
      sizeLimit: '5GB',
      bucketLocation: 'Singapore',
      storageClass: 'standard',
      folderName: 'tenant-assets',
    },
    companyInfomation: {
      name: 'BlueTech Solutions Pte. Ltd.',
      brandName: 'BlueTech',
      sign: 'BlueTechSign',
      telephone: '+65 6123 4567',
      email: '<EMAIL>',
      website: 'https://www.bluetech.io',
      address1: '8 Marina Boulevard',
      address2: 'Level 11',
      logo: 'https://cdn.bluetech.io/assets/logo.svg',
      linkedinUrl: 'https://www.linkedin.com/company/bluetech',
      banner: 'https://cdn.bluetech.io/assets/banner.jpg',
      linkedinLogo: 'https://cdn.bluetech.io/assets/linkedin-logo.png',
      sfnLogo: 'https://cdn.bluetech.io/assets/sfn-logo.png',
      typography: {
        colors: {
          primary: '#0057D9',
          secondary: '#00C49A',
          background: '#F4F6F8',
        },
      },
    },
    thirdParty: {
      example: {
        baseUrl: 'https://api.thirdparty.io',
        token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
        companyIdentifier: 'bt-solutions-101',
        dlad: 'DLAD-3041-BTS',
      },
    },
  },
};

export async function initMockTenant(doc?: any) {
  const { _id, ...rest } = { ...mockTenantData, ...doc };
  await tenantModel.replaceOne({ _id }, rest, { upsert: true });
}
