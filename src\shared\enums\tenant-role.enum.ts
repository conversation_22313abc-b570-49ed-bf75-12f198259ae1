export enum TenantRoleEnum {
  INSPECTOR = 'inspector',
  <PERSON>LEANER = 'cleaner',
  MECHANIC = 'mechanic',
  INSPECTION_PLANNER = 'inspection_planner',
  MAINTENANCE_PLANNER = 'maintenance_planner',
  CLEANING_PLANNER = 'cleaning_planner',
  INSPECTION_REVIEWER_ALL_INSPECTIONS = 'inspection_reviewer_all_inspections',
  MAINTENANCE_REVIEWER = 'maintenance_reviewer',
  CLEANING_REVIEWER = 'cleaning_reviewer',
  INSPECTION_VIEWER = 'inspection_viewer',
  MAINTENANCE_VIEWER = 'maintenance_viewer',
  CLEANING_VIEWER = 'cleaning_job_viewer',
  INSPECTION_TEMPLATE_MANAGER = 'inspection_template_manager',
  HR_UPLOADER = 'hr_uploader',
  NIGHT_REGISTRATION_UPLOADER = 'night_registration_uploader',
  FACILITIES_UPLOADER = 'facilities_uploader',
  SUPPORT_UPLOADER = 'support_uploader',

  // contract roles
  CONTRACT_VIEWER = 'contract_viewer',
  DEBTOR_CONTRACT_MANAGER = 'debtor_contract_manager',
  CREDITOR_CONTRACT_MANAGER = 'creditor_contract_manager',
  SERVICE_CONTRACT_MANAGER = 'service_contract_manager',
  SUPPLIER_CONTRACT_MANAGER = 'supplier_contract_manager',
}
