import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';

import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockContractType } from '~/test/mocks/contracttype.mock';

import { ContractTypeModel } from '../contract-type.model';
import { ContractTypeService } from '../contract-type.service';
import { ContractTypeTest } from './contract-type.dto.test';

describe('ContractTypeService', () => {
  let service: ContractTypeService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [ContractTypeService, ...testInjectModel([ContractTypeModel])],
    }).compile();

    service = module.get(ContractTypeService);

    // Init data
    await initMockContractType({
      _id: new ObjectId(),
      name: 'Test Name 1',
      identifier: nanoid(9),
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should call fn and return list data', async () => {
      const result = await service.findAll({});
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(ContractTypeTest.findAllSchema);
    });

    it('should return all data when pageSize is -1', async () => {
      const result = await service.findAll({ pageSize: -1 });
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(1);
      expect(result.docs).toMatchSchema(ContractTypeTest.findAllSchema);
    });

    it('should return empty list when data not exist', async () => {
      const result = await service.findAll({ limit: 50, pageIndex: 9 });
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });
});
