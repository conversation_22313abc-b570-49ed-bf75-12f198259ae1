import { BadRequestException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';

import { EquipmentModel } from '~/modules/equipment/equipment.model';
import { EquipmentService } from '~/modules/equipment/equipment.service';
import { JobModel } from '~/modules/job/job.model';
import { TenantUserModel } from '~/modules/tenant-user/tenant-user.model';
import { TenantUserService } from '~/modules/tenant-user/tenant-user.service';
import { TaskCategory, TaskType } from '~/shared/enums/task.enum';
import { EQUIPMENT_MESSAGE_KEYS } from '~/shared/message-keys/equipment.message-key';
import { TASK_MESSAGE_KEYS } from '~/shared/message-keys/task.message-key';
import { TENANT_USER_MESSAGE_KEYS } from '~/shared/message-keys/tenant-user.message-key';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import {
  initMockEquipment,
  mockEquipmentData,
} from '~/test/mocks/equipment.mock';
import { initMockTask, mockTaskData } from '~/test/mocks/task.mock';
import { initMockTeam } from '~/test/mocks/team.mock';
import {
  initMockTenantUser,
  mockTenantUserData,
} from '~/test/mocks/tenantuser.mock';

import { CreateTaskDto, UpdateTaskDto } from '../dtos/create-task.dto';
import { TaskModel } from '../task.model';
import { TaskService } from '../task.service';
import { taskTest } from './task.dto.test';

describe('TaskService', () => {
  let service: TaskService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        TaskService,
        ...testInjectProviders([
          TaskModel,
          TenantUserModel,
          EquipmentModel,
          JobModel,
          // Service
          TenantUserService,
        ]),
        EquipmentService,
      ],
    }).compile();

    service = module.get(TaskService);

    // Init data
    await Promise.all([
      initMockTask(),
      initMockEquipment(),
      initMockTenantUser(),
      initMockTeam({ tenantUsers: [mockTenantUserData._id] }),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('create', () => {
    it('should throw error if equipment type car in payload is not valid', async () => {
      const payload: any = {
        cars: [new ObjectId().toString()],
        category: TaskCategory.PUBLIC_HOLIDAY,
        startDate: dayjs().toISOString(),
        endDate: dayjs().add(1, 'day').toISOString(),
      };

      await expect(service.create(payload)).rejects.toThrow(
        EQUIPMENT_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if equipment type device in payload is not valid', async () => {
      const payload: any = {
        cars: [mockEquipmentData._id.toString()],
        devices: [new ObjectId().toString()],
        category: TaskCategory.PUBLIC_HOLIDAY,
        startDate: dayjs().toISOString(),
        endDate: dayjs().add(1, 'day').toISOString(),
      };

      await expect(service.create(payload)).rejects.toThrow(
        EQUIPMENT_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if employee in payload is not valid', async () => {
      // Mock private method
      jest
        .spyOn(service['tenantUserService'], 'existsOrThrow')
        .mockImplementation(() => {
          throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
        });

      const payload: any = {
        cars: [mockEquipmentData._id.toString()],
        category: TaskCategory.PUBLIC_HOLIDAY,
        startDate: dayjs().toISOString(),
        endDate: dayjs().add(1, 'day').toISOString(),
        employees: [new ObjectId().toString()],
      };

      await expect(service.create(payload)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should call fn with payload and create new task', async () => {
      // Mock private method
      jest
        .spyOn(service['tenantUserService'], 'existsOrThrow')
        .mockImplementation(() => Promise.resolve());

      const payload: CreateTaskDto = {
        title: 'Test Task',
        type: TaskType.PLANNING,
        category: TaskCategory.PUBLIC_HOLIDAY,
        startDate: dayjs().toISOString(),
        endDate: dayjs().add(1, 'day').toISOString(),
        cars: [mockEquipmentData._id.toString()],
        employees: [new ObjectId().toString()],
      };

      const result = await service.create(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('title', payload.title);
      expect(result).toMatchSchema(taskTest.modelSchema);
    });
  });

  describe('findOne', () => {
    it('should return null if task not found', async () => {
      const result = await service.findOne(new ObjectId().toString());
      expect(result).toBeUndefined();
    });

    it('should call fn with id and return task', async () => {
      const id = mockTaskData._id!.toString();

      const result = await service.findOne(id);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockTaskData._id);
      expect(result).toMatchSchema(taskTest.fineOneSchema);
    });
  });

  describe('update', () => {
    const payload: UpdateTaskDto = {
      id: mockTaskData._id!.toString(),
      title: 'Updated Task',
      type: TaskType.PLANNING,
      category: TaskCategory.PUBLIC_HOLIDAY,
      startDate: dayjs().toISOString(),
      endDate: dayjs().add(1, 'day').toISOString(),
      employees: [mockTenantUserData._id.toString()],
    };

    it('should throw error if task not found', async () => {
      await expect(
        service.update({ ...payload, id: new ObjectId().toString() }),
      ).rejects.toThrow(TASK_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw error if equipment type car in payload is not valid', async () => {
      const invalidPayload: any = {
        ...payload,
        cars: [new ObjectId().toString()],
      };

      await expect(service.update(invalidPayload)).rejects.toThrow(
        EQUIPMENT_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if equipment type device in payload is not valid', async () => {
      const invalidPayload: any = {
        ...payload,
        devices: [new ObjectId().toString()],
      };

      await expect(service.update(invalidPayload)).rejects.toThrow(
        EQUIPMENT_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if employee in payload is not valid', async () => {
      // Mock private method
      jest
        .spyOn(service['tenantUserService'], 'existsOrThrow')
        .mockImplementation(() => {
          throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
        });

      await expect(
        service.update({
          ...payload,
          employees: [new ObjectId().toString()],
        }),
      ).rejects.toThrow(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should call fn with payload and update task', async () => {
      // Mock private method
      jest
        .spyOn(service['tenantUserService'], 'existsOrThrow')
        .mockImplementation(() => Promise.resolve());

      const result = await service.update(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockTaskData._id);
      expect(result).toHaveProperty('title', payload.title);
      expect(result).toMatchSchema(taskTest.modelSchema);
    });
  });

  describe('delete', () => {
    it('should throw error if task not found', async () => {
      await expect(service.delete(new ObjectId().toString())).rejects.toThrow(
        TASK_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should call fn with id and delete task', async () => {
      // Prepare data
      const id = new ObjectId();
      await initMockTask({ _id: id });

      const result = await service.delete(id.toString());
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', id);
      expect(result).toMatchSchema(taskTest.modelSchema);
    });
  });

  describe('checkOverlapTask', () => {
    it('should return empty employees and equipments when all available', async () => {
      const payload = {
        employees: [mockTenantUserData._id.toString()],
        equipments: [mockEquipmentData._id.toString()],
        startDate: dayjs().toISOString(),
        endDate: dayjs().add(1, 'day').toISOString(),
        timezone: '+00:00',
        equipmentType: mockEquipmentData.type,
      };

      const result = await service.checkOverlapTask(payload);
      expect(result.employees).toEqual([]);
      expect(result.equipments).toEqual([]);
      expect(result).toMatchSchema(taskTest.checkOverlapTaskSchema);
    });

    it('should return overlap employees if employee not available in period', async () => {
      await initMockTenantUser({
        oddWeeks: [],
        evenWeeks: [],
      });

      const payload = {
        employees: [mockTenantUserData._id.toString()],
        equipments: [mockEquipmentData._id.toString()],
        startDate: dayjs().toISOString(),
        endDate: dayjs().add(1, 'day').toISOString(),
        timezone: '+00:00',
        equipmentType: mockEquipmentData.type,
      };

      const result = await service.checkOverlapTask(payload);
      expect(result.employees.length).toBe(1);
      expect(result.equipments).toEqual([]);
      expect(result).toMatchSchema(taskTest.checkOverlapTaskSchema);
    });

    it('should return overlap equipments if equipment not available', async () => {
      await Promise.all([
        initMockTenantUser(),
        initMockEquipment({ isActive: false }),
      ]);

      jest
        .spyOn(service['equipmentService'], 'getAvailableEquipments')
        .mockResolvedValue([
          { ...mockEquipmentData, _id: new ObjectId() },
        ] as any[]);

      const payload = {
        employees: [mockTenantUserData._id.toString()],
        equipments: [mockEquipmentData._id.toString()],
        startDate: dayjs().toISOString(),
        endDate: dayjs().add(1, 'day').toISOString(),
        timezone: '+00:00',
        equipmentType: mockEquipmentData.type,
      };

      const result = await service.checkOverlapTask(payload);
      expect(result.employees).toEqual([]);
      expect(result.equipments.length).toBe(1);
      expect(result).toMatchSchema(taskTest.checkOverlapTaskSchema);
    });
  });

  describe('getEmployeesSchedules', () => {
    it('should return schedules for employees in period', async () => {
      const employeeId = mockTenantUserData._id;
      const startDate = dayjs().startOf('day').toDate();
      const endDate = dayjs().add(1, 'day').endOf('day').toDate();

      await initMockTask({
        startDate,
        endDate,
        title: '2e Pinksterdag',
      });

      const result = await service.getEmployeesSchedules(
        {
          startDate,
          endDate,
          timezone: '+00:00',
        },
        [employeeId],
      );

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty('employee');
      expect(result[0].employee._id.toString()).toBe(employeeId.toString());
      expect(result[0]).toHaveProperty('schedules');
      expect(result[0].schedules.length).toBeGreaterThan(0);
      expect(result[0].schedules[0]).toHaveProperty('date');
      expect(result[0].schedules[0]).toHaveProperty('items');
      expect(result[0].schedules[0].items[0]).toHaveProperty(
        'title',
        '2e Pinksterdag',
      );
    });

    it('should return empty schedules if employee has no tasks in period', async () => {
      const employeeId = mockTenantUserData._id;
      const startDate = dayjs().add(10, 'day').startOf('day').toDate();
      const endDate = dayjs().add(11, 'day').endOf('day').toDate();

      const result = await service.getEmployeesSchedules(
        {
          startDate,
          endDate,
          timezone: '+00:00',
        },
        [employeeId],
      );

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });
  });

  describe('getEquipmentSchedules', () => {
    it('should return schedules for equipments in period', async () => {
      const equipmentId = mockEquipmentData._id;
      const startDate = dayjs().startOf('day').toDate();
      const endDate = dayjs().add(1, 'day').endOf('day').toDate();

      await initMockTask({
        startDate,
        endDate,
        title: '2e Pinksterdag',
      });

      const result = await service.getEquipmentSchedules(
        {
          startDate,
          endDate,
          timezone: '+00:00',
        },
        mockEquipmentData.type,
      );

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty('equipment');
      expect(result[0].equipment._id.toString()).toBe(equipmentId.toString());
      expect(result[0].equipment).toHaveProperty('description');
      expect(result[0]).toHaveProperty('schedules');
      expect(result[0].schedules.length).toBeGreaterThan(0);
      expect(result[0].schedules[0]).toHaveProperty('date');
      expect(result[0].schedules[0]).toHaveProperty('items');
      expect(result[0].schedules[0].items[0]).toHaveProperty(
        'title',
        '2e Pinksterdag',
      );
    });

    it('should return empty schedules if equipment has no tasks in period', async () => {
      const startDate = dayjs().add(10, 'day').startOf('day').toDate();
      const endDate = dayjs().add(11, 'day').endOf('day').toDate();

      const result = await service.getEquipmentSchedules(
        {
          startDate,
          endDate,
          timezone: '+00:00',
        },
        mockEquipmentData.type,
      );

      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBe(0);
    });
  });
});
