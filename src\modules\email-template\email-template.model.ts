import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Severity,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

export type EmailTemplateDocument = DocumentType<EmailTemplateModel>;

@modelOptions({
  options: { customName: 'EmailTemplate', allowMixed: Severity.ALLOW },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
@index({ name: 1 }, { unique: true })
export class EmailTemplateModel extends BaseModel {
  @prop({ required: true, trim: true, lowercase: true })
  name!: string;

  @prop({ required: true, trim: true })
  subject!: string;

  @prop({ trim: true, default: '' })
  text?: string;

  @prop({ trim: true, default: '' })
  html?: string;

  @prop({ required: true })
  to!: string[];

  @prop()
  cc?: string[];

  @prop()
  bcc?: string[];
}
