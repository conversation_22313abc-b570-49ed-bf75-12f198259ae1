import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isActive: z.boolean(),
    isSynced: z.boolean(),
    itemCode: z.string(),
    name: z.string(),
    type: z.string(),
    country: z.instanceof(ObjectId).optional(),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(modelSchema);

export const costTypeTest = {
  modelSchema,
  findAllSchema,
};
