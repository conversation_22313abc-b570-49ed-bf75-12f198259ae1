import { INestApplication } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { MicroserviceOptions, Transport } from '@nestjs/microservices';
import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import utc from 'dayjs/plugin/utc';

import { measure } from '~/utils/measure.util';

import { AppModule } from './app.module';
import { MyLogger } from './processors/logger/logger.service';

dayjs.extend(utc);
dayjs.extend(isoWeek);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

async function initApp(app: INestApplication<any>) {
  const logger = app.get(MyLogger);
  const configService = app.get(ConfigService);

  const env = configService.get('app.env') || 'development';

  const tcpHost = configService.get('app.tcpHost');
  const tcpPort = configService.get('app.tcpPort');

  const databaseUri = configService.get('database.uri');

  const isDev = configService.get('app.env') === 'development';

  await measure('Connect Microservice', logger, async () => {
    app.connectMicroservice<MicroserviceOptions>(
      {
        transport: Transport.TCP,
        options: {
          host: tcpHost,
          port: tcpPort,
        },
      },
      { inheritAppConfig: true },
    );
  });

  app.useLogger(app.get(MyLogger));

  await measure('Start All Microservices', logger, async () => {
    await app
      .startAllMicroservices()
      .then(async () => {
        logger.log(`[Environment]: ${env}`);

        if (isDev) {
          logger.log(`[P${process.pid}] Database: ${databaseUri}`);
        }
      })
      .catch(logger.error);
  });

  await measure('App Initialization', logger, app.init);
}

async function bootstrap() {
  performance.mark('app-start');

  const app = await NestFactory.create(AppModule, {
    logger: ['error', 'debug'],
  });

  const logger = app.get(MyLogger);
  await measure(
    'Bootstrap Application',
    logger,
    async () => await initApp(app),
  );

  performance.mark('app-end');

  const measurement = performance.measure(
    'App Startup Time',
    'app-start',
    'app-end',
  );

  logger.log(
    `[P${process.pid}] Core service is up: ${measurement.duration.toFixed(2)}ms`,
  );
}

bootstrap();
