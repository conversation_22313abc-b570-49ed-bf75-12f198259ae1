import { Injectable } from '@nestjs/common';
import { isNullOrUndefined } from '@typegoose/typegoose/lib/internal/utils';
import { omit } from 'lodash';

import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery, QueryParams } from '~/utils';

import { ContractTypeModel } from './contract-type.model';

@Injectable()
export class ContractTypeService {
  constructor(
    @InjectModel(ContractTypeModel)
    private readonly contractTypeModel: MongooseModel<ContractTypeModel>,
  ) {}

  public async findAll(payload: QueryParams) {
    const { query, options } = buildQuery(payload);
    const finalOptions =
      isNullOrUndefined(payload.pageSize) || options.limit === -1
        ? { ...omit(options, ['limit']), pagination: false }
        : options;

    return await this.contractTypeModel.paginate(query, {
      ...finalOptions,
    });
  }
}
