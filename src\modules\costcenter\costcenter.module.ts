import { Module } from '@nestjs/common';

import { SyncHistoryModule } from '~/modules/sync-history/sync-history.module';
import { ThirdPartyConnectorModule } from '~/processors/third-party-connector/third-party-connector.module';

import { CostCenterController } from './costcenter.controller';
import { CostCenterService } from './costcenter.service';

@Module({
  imports: [ThirdPartyConnectorModule, SyncHistoryModule],
  controllers: [CostCenterController],
  providers: [CostCenterService],
})
export class CostcenterModule {}
