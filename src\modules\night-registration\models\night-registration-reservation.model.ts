import {
  DocumentType,
  Index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { ContactDocument, ContactModel } from '~/modules/contact/contact.model';
import { UnitDocument, UnitModel } from '~/modules/unit/unit.model';
import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import {
  NightRegistrationResidentDocument,
  NightRegistrationResidentModel,
} from './night-registration-resident.model';

export type NightRegistrationReservationDocument =
  DocumentType<NightRegistrationReservationModel>;

interface JobCheckInOrCheckOutData {
  id: string;
  type: 'check-in' | 'check-out';
}

@modelOptions({
  options: { customName: 'NightRegistrationReservation' },
})
@Index({ unit: 1, bed: 1 })
@Index({ arrivalDate: 1 })
@Index({ departureDate: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class NightRegistrationReservationModel extends BaseModel {
  @prop({ ref: () => NightRegistrationResidentModel })
  resident?: Ref<NightRegistrationResidentDocument>;

  @prop({ ref: () => ContactModel })
  contact?: Ref<ContactDocument>;

  @prop({ required: true, ref: () => UnitModel })
  unit!: Ref<UnitDocument>;

  @prop({ default: '' })
  remarks?: string;

  @prop({ required: true })
  bed!: number;

  @prop({ default: null })
  arrivalDate?: Date;

  @prop({ default: null })
  departureDate?: Date;

  @prop({ default: false })
  isVirtual?: boolean;

  @prop()
  job?: JobCheckInOrCheckOutData[];
}
