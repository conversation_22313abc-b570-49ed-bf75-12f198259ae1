import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { CONTACT_MESSAGES } from '~/shared/messages/contact.message';

import { ContactService } from './contact.service';
import { GetContactDetailDto, GetContactDto } from './dtos/contact.dto';
import { CreateContactZodValidationPipe } from './pipes/create-contact.pipe';
import { UpdateContactZodValidationPipe } from './pipes/update-contact.pipe';

@Controller('contacts')
export class ContactController {
  constructor(private readonly contactService: ContactService) {}

  @HTTPDecorators.Paginator
  @MessagePattern({ cmd: CONTACT_MESSAGES.GET_CONTACTS })
  public async findAll(@Payload() payload: GetContactDto) {
    return this.contactService.findAll(payload);
  }

  @MessagePattern({ cmd: CONTACT_MESSAGES.GET_CONTACT_DETAIL })
  public async findOne(@Payload() payload: GetContactDetailDto) {
    return this.contactService.findOne(payload);
  }

  @UsePipes(new CreateContactZodValidationPipe())
  @MessagePattern({ cmd: CONTACT_MESSAGES.CREATE_CONTACT })
  public async create(@Payload() payload: any) {
    return this.contactService.create(payload);
  }

  @UsePipes(new UpdateContactZodValidationPipe())
  @MessagePattern({ cmd: CONTACT_MESSAGES.UPDATE_CONTACT })
  public async update(@Payload() payload: any) {
    return this.contactService.update(payload);
  }

  @MessagePattern({ cmd: CONTACT_MESSAGES.SYNC_DEBTORS_FROM_3RD_PARTY })
  public async syncDebtorsFrom3rdParty(@Payload() payload: any) {
    const { type, actionType, thirdParty } = payload;
    await this.contactService.syncDebtorsFrom3rdParty({
      type,
      actionType,
      ...thirdParty,
    });

    return { data: 'Debtors synced successfully' };
  }
}
