import { ArgumentMetadata, Injectable } from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';

import { JobPeriodTypeEnum } from '~/shared/enums/job.enum';

import {
  CreateJobDto,
  CreateMultipleDaysJobDto,
  CreateScheduleJobDto,
  MobileCreateJobDto,
} from '../dtos/job.dto';

@Injectable()
export class CreateJobZodValidationPipe extends ZodValidationPipe {
  async transform(value: any, metadata: ArgumentMetadata) {
    // Select the appropriate schema based on the type in the request body
    let schema;

    const { platform, type } = value;

    if (platform === 'mb') {
      schema = MobileCreateJobDto;
    } else {
      switch (type) {
        case JobPeriodTypeEnum.MULTIPLE_DAYS:
          schema = CreateMultipleDaysJobDto;
          break;
        case JobPeriodTypeEnum.PERIODIC:
          schema = CreateScheduleJobDto;
          break;
        default:
          schema = CreateJobDto;
          break;
      }
    }

    // Call the parent class's transform() method with the selected schema
    return super.transform(value, { ...metadata, metatype: schema });
  }
}
