import { getModelForClass } from '@typegoose/typegoose';
import { Types } from 'mongoose';

import { UploadFileModel } from '~/modules/document-file/upload-file.model';

const uploadFileModel = getModelForClass(UploadFileModel);

export const mockUploadFileData = {
  _id: new Types.ObjectId(),
  createdAt: new Date(),
  extension: '.png',
  filenameOnStorage: 'Example.png',
  folderPathOnStorage: 'images',
  isDeleted: false,
  originalFilename: 'Example.png',
  provider: 'gcs',
  publicUrl:
    'https://storage.googleapis.com/ee-acco-production/images/example.png',
  size: 168.22,
  mimeType: 'image/png',
  updatedAt: new Date(),
};

export async function initMockUploadFile(doc?: any) {
  const { _id, ...rest } = { ...mockUploadFileData, ...doc };
  await uploadFileModel.replaceOne({ _id }, rest, {
    upsert: true,
  });
}
