import { Client } from '@googlemaps/google-maps-services-js';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
@Injectable()
export class GoogleMapService {
  private client: Client;
  constructor(private readonly configService: ConfigService) {
    this.client = new Client({});
  }
  async getGeocode(address: string) {
    return this.client.geocode({
      params: {
        address,
        key: this.configService.get('app.googleMapApiKey') || '',
      },
    });
  }
}
