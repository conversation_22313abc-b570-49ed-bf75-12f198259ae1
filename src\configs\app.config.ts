import { registerAs } from '@nestjs/config';

export default registerAs('app', () => ({
  env: `${process.env.NODE_ENV ?? 'development'}`,
  tcpHost: `${process.env.TCP_HOST ?? 'localhost'}`,
  tcpPort: parseInt(process.env.TCP_PORT ?? '2345', 10),
  googleMapApiKey: `${process.env.GOOGLE_MAP_API_KEY ?? ''}`,
  maskingData: (process.env.MASKING_DATA ?? 'true').toLowerCase() !== 'false',
  internalContactIdentifier: `${process.env.INTERNAL_CONTACT_IDENTIFIER ?? ''}`,
  invoiceIdentifierDigit: parseInt(
    process.env.INVOICE_IDENTIFIER_DIGIT ?? '0',
    10,
  ),
  lentoErrorReciverEmail: `${process.env.LENTO_ERROR_RECEIVER_EMAIL ?? ''}`,
}));
