import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { EMAIL_MESSAGES } from '~/shared/messages/email.message';

import { SendEmailDto, SendTestEmailZodDto } from './dtos/send-email.dto';
import { EmailService } from './email.service';

@Controller('email')
export class EmailController {
  constructor(private readonly emailService: EmailService) {}

  @UsePipes(new ZodValidationPipe(SendTestEmailZodDto))
  @MessagePattern({ cmd: EMAIL_MESSAGES.SEND_EMAIL })
  public async sendEmailTest(@Payload() payload: SendEmailDto) {
    payload = {
      ...payload,
      subject: 'Test Email',
      text: 'This Email sent to you is a test',
      html: `<div style="text-align: center; padding: 0 3%;">
                <h1>Lorem Ipsum</h1>
                <h4><i>"Neque porro quisquam est qui dolorem ipsum quia dolor sit amet, consectetur, adipisci velit..."</i></h4>
                <hr style="clear: both; border: 0; height: 1px; background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, .75), rgba(0, 0, 0, 0));" />
                <p style="text-align: justify">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. 
                    Praesent cursus augue arcu, id iaculis neque varius vel. 
                    Etiam iaculis tempor risus, vel dignissim massa ultrices ut. 
                    Fusce non tellus viverra, molestie odio at, fermentum dolor. 
                    Nullam neque leo, rhoncus lobortis interdum non, blandit ac mi. 
                    Mauris ut pulvinar metus, non dapibus ligula. 
                    Praesent nec gravida mi, id finibus dolor. 
                    Ut rutrum ex felis, ac finibus sapien facilisis in. 
                    Aliquam sodales mauris purus, in dapibus lectus cursus nec. 
                    Vivamus ultrices risus ac finibus tempor. 
                    Maecenas hendrerit velit non mauris tristique ultrices nec non metus. 
                    Mauris euismod, dui ut congue lacinia, nisi ante lacinia ligula, ac ornare lorem felis eu mauris. 
                    Integer efficitur lacinia varius. Curabitur commodo aliquet mattis. 
                    Pellentesque habitant morbi tristique senectus et netus et malesuada fames ac turpis egestas.
                </p>
            </div>`,
    };

    const { ehlo, ...rest } = await this.emailService.sendEmail(payload);

    return rest;
  }

  @MessagePattern({ cmd: EMAIL_MESSAGES.SEND_EMAIL_WITH_TEMPLATE })
  public async sendEmailWithTemplateTest(
    @Payload() payload: { template: string; options: { [key: string]: any } },
  ) {
    const { ehlo, ...rest } = await this.emailService.sendEmailWithTemplate(
      payload.template,
      payload.options,
    );

    return rest;
  }
}
