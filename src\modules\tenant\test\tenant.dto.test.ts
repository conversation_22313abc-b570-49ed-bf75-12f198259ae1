import { z } from 'zod';

import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isActive: z.boolean(),
    name: z.string(),
    tenantConfigs: z.record(z.union([z.object({}), z.string()])),
  })
  .extend(baseModelTestSchema);

const getCompanyInfomationSchema = modelSchema.shape['tenantConfigs'];

export const tenantTest = {
  modelSchema,
  getCompanyInfomationSchema,
};
