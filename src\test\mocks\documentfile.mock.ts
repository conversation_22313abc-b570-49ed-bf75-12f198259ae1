import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { DocumentFileModel } from '~/modules/document-file/document-file.model';
import { DocumentFileTypeEnum } from '~/shared/enums/document-file-type.enum';

import { mockLocationData } from './location.mock';
import { mockUploadFileData } from './uploadfile.mock';

const documentFileModel = getModelForClass(DocumentFileModel);

export const mockDocumentFileData = {
  _id: new ObjectId(),
  createdDate: new Date(),
  fileName: mockUploadFileData.filenameOnStorage,
  isDeleted: false,
  location: mockLocationData._id,
  type: DocumentFileTypeEnum.NIGHT_REGISTRATIONS,
  updatedDate: new Date(),
  uploadFile: mockUploadFileData._id,
  uploaderName: 'Alphen KM & Boskoop',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export async function initMockDocumentFile(doc?: any) {
  const { _id, ...rest } = { ...mockDocumentFileData, ...doc };
  await documentFileModel.replaceOne({ _id }, rest, { upsert: true });
}
