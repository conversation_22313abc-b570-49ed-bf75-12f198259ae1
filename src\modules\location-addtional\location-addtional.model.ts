import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import {
  CertificateAndControlInspectionType,
  LocationAdditionalGroupType,
  LocationAdditionalType,
} from '~/shared/enums/location-additional.enum';
import { BaseModel } from '~/shared/models/base.model';

import { ContactDocument, ContactModel } from '../contact/contact.model';
import { ContractDocument, ContractModel } from '../contract/contract.model';
import { LocationDocument, LocationModel } from '../location/location.model';
import {
  LocationAdditionalGroupNameDocument,
  LocationAdditionalGroupNameModel,
} from './location-additional-group-name.model';

interface RecordLogs {
  date: Date;
  position: number;
  number: string;
}

export type LocationAdditionalDocument = DocumentType<LocationAdditionalModel>;
@modelOptions({
  options: { customName: 'locationAdditional' },
})
@index({ location: 1 })
@index({ contact: 1 })
@index({ groupName: 1 })
@index({ isDeleted: 1, type: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class LocationAdditionalModel extends BaseModel {
  @prop({ required: true, enum: LocationAdditionalType })
  type!: LocationAdditionalType;

  @prop({
    required: true,
    enum: [
      CertificateAndControlInspectionType.MAINTENANCE,
      CertificateAndControlInspectionType.INSPECTION,
      CertificateAndControlInspectionType.CERTIFICATION,
      CertificateAndControlInspectionType.SAMPLING,
    ],
    default: CertificateAndControlInspectionType.MAINTENANCE,
  })
  inspectionType!: CertificateAndControlInspectionType;

  @prop({ required: true, ref: () => LocationAdditionalGroupNameModel })
  groupName!: Ref<LocationAdditionalGroupNameDocument>;

  @prop({
    required: true,
    enum: LocationAdditionalGroupType,
    default: LocationAdditionalGroupType.NONE,
  })
  groupType!: LocationAdditionalGroupType;

  @prop({ required: true, default: 0 })
  position!: number;

  @prop()
  description?: string;

  @prop({ ref: () => ContactModel })
  contact?: Ref<ContactDocument>;

  @prop({ ref: () => LocationModel })
  location?: Ref<LocationDocument>;

  @prop({ ref: () => ContractModel })
  contract?: Ref<ContractDocument>;

  @prop()
  code?: string;

  @prop()
  dateCheck?: Date;

  @prop()
  brandType?: string;

  @prop()
  name?: string;

  @prop()
  yearInstallation?: number;

  @prop()
  smartMeter?: boolean;

  @prop()
  meterNumber?: string;

  @prop({ default: [] })
  recordLogs?: RecordLogs[];

  @prop()
  contractNumber?: string;
}
