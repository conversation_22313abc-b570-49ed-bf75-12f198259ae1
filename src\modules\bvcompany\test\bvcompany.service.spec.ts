import { Test } from '@nestjs/testing';

import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockBvCompany } from '~/test/mocks/bvcompany.mock';

import { BvCompanyModel } from '../bvcompany.model';
import { BvCompanyService } from '../bvcompany.service';
import { bvCompanyTest } from './bvcompany.dto.test';

describe('BvCompanyService', () => {
  let service: BvCompanyService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [BvCompanyService, ...testInjectModel([BvCompanyModel])],
    }).compile();

    service = module.get(BvCompanyService);

    // Init data
    await initMockBvCompany();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getList', () => {
    it('should return empty list when no data exists', async () => {
      const payload: QueryParamsDto = { pageIndex: 999 };
      const result = await service.getList(payload);
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });

    it('should call getList and return', async () => {
      const payload: QueryParamsDto = { pageSize: 50 };
      const result = await service.getList(payload);
      expect(result).toBeDefined();
      expect(result.docs).toBeInstanceOf(Array);
      expect(result.docs).toMatchSchema(bvCompanyTest.getListSchema);
    });
  });
});
