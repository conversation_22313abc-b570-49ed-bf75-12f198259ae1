import { modelOptions, plugin, prop } from '@typegoose/typegoose';
import AggregatePaginate from 'mongoose-aggregate-paginate-v2';
import Paginate from 'mongoose-paginate-v2';

@plugin(Paginate)
@plugin(AggregatePaginate)
@modelOptions({
  schemaOptions: {
    id: false,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
    timestamps: true,
  },
})
export class BaseModel {
  @prop({ default: false, select: false })
  isDeleted?: boolean;

  createdAt?: Date;

  updatedAt?: Date;
  @prop({ select: false })
  deletedAt?: Date;

  static get protectedKeys() {
    return ['createdAt', 'updatedAt', 'deletedAt', 'isDeleted', 'id', '_id'];
  }
}
