import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldTeam {
  _id: string;
  name: string;
  position: number;
}

const tranformDataFunc = ({ data }: { data: OldTeam[]; context: any }) => {
  return Promise.all(
    data.map(async (item) => {
      const tranformed = {
        _id: item._id,
        isActive: true,
        name: item.name,
      };

      return tranformed;
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('team');

  return sourceCollection.find().skip(skip).limit(limit);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'team',
      destinationCollectionName: 'teams',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
