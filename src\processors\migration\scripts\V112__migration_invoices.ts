import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

interface OldInvoice {
  _id: Types.ObjectId;
  identifier: string;
  billedTo?: Types.ObjectId;
  costCenters?: Types.ObjectId[];
  locations?: Types.ObjectId[];
  debtorContact?: { _id: Types.ObjectId; grouping?: Types.ObjectId };
  destination?: string;
  gross: number;
  net?: number;
  reference?: { _id: Types.ObjectId; invoiceReference?: Types.ObjectId };
  type: string;
  costLines: Types.ObjectId[];
  credits: Types.ObjectId[];
  startDate?: Date;
  endDate?: Date;
  createdBy: Types.ObjectId;
  createdDate: Date;
  updatedBy: Types.ObjectId;
  updatedDate: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection.aggregate([
    {
      $match: {
        _id: { $gt: nextId },
      },
    },
    { $sort: { _id: 1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'costline',
        localField: '_id',
        foreignField: 'invoice',
        as: 'costLines',
      },
    },
    {
      $lookup: {
        from: 'credit',
        localField: '_id',
        foreignField: 'creditInvoices',
        as: 'credits',
      },
    },
    {
      $lookup: {
        from: 'debtorcontact',
        localField: 'debtorContact',
        foreignField: '_id',
        as: 'debtorContact',
      },
    },
    {
      $lookup: {
        from: 'invoice_reference',
        localField: 'reference',
        foreignField: '_id',
        as: 'reference',
      },
    },
    {
      $addFields: {
        costLines: '$costLines._id',
        credits: '$credits._id',
        debtorContact: { $arrayElemAt: ['$debtorContact', 0] },
        reference: { $arrayElemAt: ['$reference', 0] },
        type: {
          $switch: {
            branches: [
              { case: { $eq: ['$type', 'Rent'] }, then: 'debtor-renting' },
              { case: { $eq: ['$type', 'Service'] }, then: 'debtor-service' },
            ],
            default: { $toLower: '$type' },
          },
        },
      },
    },
  ]);
};

const transformData = ({ data }: { data: OldInvoice[] }) => {
  return Promise.all(
    data.map(async (invoice: OldInvoice) =>
      omitNull({
        _id: invoice._id,
        identifier: invoice.identifier,
        type: invoice.type,
        net: invoice.net ?? 0,
        startDate: invoice.startDate,
        endDate: invoice.endDate,
        approvedAt: invoice.createdDate,
        contact: invoice.debtorContact?.grouping,
        locations: invoice.locations,
        costCenters: invoice.costCenters,
        costLines: invoice.costLines.concat(invoice.credits) ?? [],
        invoiceReference: invoice.reference?.invoiceReference,
        createdAt: invoice.createdDate,
        updatedAt: invoice.updatedDate,
      }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'invoice',
      destinationCollectionName: 'invoices',
      pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
