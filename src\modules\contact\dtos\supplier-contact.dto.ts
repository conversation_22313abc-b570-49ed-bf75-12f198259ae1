import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import {
  ContactType,
  SupplierCategoryEnum,
  SupplierType,
} from '~/shared/enums/contact.enum';

import {
  BaseContactSchema,
  ContactIdSchema,
  ContactRoleSchema,
} from './contact.dto';
import { CreateOrganizationContactSchema } from './organization-contact.dto';
import { CreatePersonContactSchema } from './person-contact.dto';

const supplierTypeValues = Object.values(SupplierType) as [string, ...string[]];
const supplierCategoryValues = Object.values(SupplierCategoryEnum) as [
  string,
  ...string[],
];
const contactTypeValues = Object.values(ContactType) as [string, ...string[]];

export const CreateSupplierSchema = z
  .strictObject({
    contactType: z.enum(contactTypeValues),
    supplierType: z.enum(supplierTypeValues),
    supplierCategory: z.enum(supplierCategoryValues).nullable().optional(),
  })
  .merge(BaseContactSchema)
  .merge(ContactRoleSchema);

export const UpdateDebtorSchema = CreateSupplierSchema.merge(ContactIdSchema);

export class CreateSupplierPersonDto extends createZodDto(
  CreateSupplierSchema.merge(CreatePersonContactSchema),
) {}

export class CreateSupplierOrganizationDto extends createZodDto(
  CreateSupplierSchema.merge(CreateOrganizationContactSchema),
) {}

export class UpdateSupplierPersonDto extends createZodDto(
  UpdateDebtorSchema.merge(CreatePersonContactSchema),
) {}

export class UpdateSupplierOrganizationDto extends createZodDto(
  UpdateDebtorSchema.merge(CreateOrganizationContactSchema),
) {}
