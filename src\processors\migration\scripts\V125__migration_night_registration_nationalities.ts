import { MigrationContext } from '../migration.service';

const nationalities = [
  { name: 'Unknown', code: 'NOCODE', nameDutch: 'Onbekend' },
  { name: 'Afghan', code: 'AF', nameDutch: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { name: 'Albanian', code: 'AL', nameDutch: 'Albanees' },
  { name: 'Algerian', code: 'DZ', nameDutch: 'Algerijn<PERSON>' },
  { name: 'American', code: 'US', nameDutch: 'Amerikaans' },
  { name: 'Andorran', code: 'AD', nameDutch: 'And<PERSON><PERSON>s' },
  { name: 'Angolan', code: 'AO', nameDutch: 'Angole<PERSON>' },
  {
    name: 'Antiguan or Barbudan',
    code: 'AG',
    nameDutch: 'Antiguaans of Barbudiaans',
  },
  { name: 'Argentine', code: 'AR', nameDutch: 'Argentijns' },
  { name: 'Armenian', code: 'AM', nameDutch: 'Arm<PERSON><PERSON>' },
  { name: 'Australian', code: 'AU', nameDutch: 'Australisch' },
  { name: 'Austrian', code: 'AT', nameDutch: '<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { name: 'Azerbaijani', code: 'A<PERSON>', nameDutch: 'Azerbeidzja<PERSON>' },
  { name: 'Bahamian', code: 'BS', nameDutch: 'Bahamaans' },
  { name: 'Bahraini', code: 'BH', nameDutch: 'Bahreins' },
  { name: 'Bangladeshi', code: 'BD', nameDutch: 'Bengaals' },
  { name: 'Barbadian', code: 'BB', nameDutch: 'Barbadiaans' },
  { name: 'Belarusian', code: 'BY', nameDutch: 'Wit-Russisch' },
  { name: 'Belgian', code: 'BE', nameDutch: 'Belgisch' },
  { name: 'Belizean', code: 'BZ', nameDutch: 'Beliziaans' },
  { name: 'Beninese', code: 'BJ', nameDutch: 'Benins' },
  { name: 'Bhutanese', code: 'BT', nameDutch: 'Bhutaans' },
  { name: 'Bolivian', code: 'BO', nameDutch: 'Boliviaans' },
  {
    name: 'Bosnian or Herzegovinian',
    code: 'BA',
    nameDutch: 'Bosnisch of Herzegovijns',
  },
  { name: 'Botswanan', code: 'BW', nameDutch: 'Botswanees' },
  { name: 'Brazilian', code: 'BR', nameDutch: 'Braziliaans' },
  { name: 'British', code: 'GB', nameDutch: 'Brits' },
  { name: 'Bruneian', code: 'BN', nameDutch: 'Bruneis' },
  { name: 'Bulgarian', code: 'BG', nameDutch: 'Bulgaars' },
  { name: 'Burkinabé', code: 'BF', nameDutch: 'Burkinees' },
  { name: 'Burmese', code: 'MM', nameDutch: 'Myanmarees' },
  { name: 'Burundian', code: 'BI', nameDutch: 'Burundees' },
  { name: 'Cabo Verdean', code: 'CV', nameDutch: 'Kaapverdisch' },
  { name: 'Cambodian', code: 'KH', nameDutch: 'Cambodjaans' },
  { name: 'Cameroonian', code: 'CM', nameDutch: 'Kameroens' },
  { name: 'Canadian', code: 'CA', nameDutch: 'Canadees' },
  { name: 'Central African', code: 'CF', nameDutch: 'Centraal-Afrikaans' },
  { name: 'Chadian', code: 'TD', nameDutch: 'Tsjadisch' },
  { name: 'Chilean', code: 'CL', nameDutch: 'Chileens' },
  { name: 'Chinese', code: 'CN', nameDutch: 'Chinees' },
  { name: 'Colombian', code: 'CO', nameDutch: 'Colombiaans' },
  { name: 'Comorian', code: 'KM', nameDutch: 'Comorees' },
  {
    name: 'Congolese (Congo-Brazzaville)',
    code: 'CG',
    nameDutch: 'Congolees (Brazzaville)',
  },
  {
    name: 'Congolese (Congo-Kinshasa)',
    code: 'CD',
    nameDutch: 'Congolees (Kinshasa)',
  },
  { name: 'Costa Rican', code: 'CR', nameDutch: 'Costa Ricaans' },
  { name: 'Croatian', code: 'HR', nameDutch: 'Kroatisch' },
  { name: 'Cuban', code: 'CU', nameDutch: 'Cubaans' },
  { name: 'Cypriot', code: 'CY', nameDutch: 'Cypriotisch' },
  { name: 'Czech', code: 'CZ', nameDutch: 'Tsjechisch' },
  { name: 'Danish', code: 'DK', nameDutch: 'Deens' },
  { name: 'Djiboutian', code: 'DJ', nameDutch: 'Djiboutiaans' },
  { name: 'Dominican', code: 'DO', nameDutch: 'Dominicaans' },
  {
    name: 'Dominican (Dominica)',
    code: 'DM',
    nameDutch: 'Dominicaans (Dominica)',
  },
  { name: 'Dutch', code: 'NL', nameDutch: 'Nederlands' },
  { name: 'East Timorese', code: 'TL', nameDutch: 'Oost-Timorees' },
  { name: 'Ecuadorean', code: 'EC', nameDutch: 'Ecuadoraans' },
  { name: 'Egyptian', code: 'EG', nameDutch: 'Egyptisch' },
  { name: 'Salvadoran', code: 'SV', nameDutch: 'Salvadoraans' },
  { name: 'Equatorial Guinean', code: 'GQ', nameDutch: 'Equatoriaal-Guinees' },
  { name: 'Eritrean', code: 'ER', nameDutch: 'Eritrees' },
  { name: 'Estonian', code: 'EE', nameDutch: 'Ests' },
  { name: 'Eswatini', code: 'SZ', nameDutch: 'Swazisch' },
  { name: 'Ethiopian', code: 'ET', nameDutch: 'Ethiopisch' },
  { name: 'Fijian', code: 'FJ', nameDutch: 'Fijisch' },
  { name: 'Finnish', code: 'FI', nameDutch: 'Fins' },
  { name: 'French', code: 'FR', nameDutch: 'Frans' },
  { name: 'Gabonese', code: 'GA', nameDutch: 'Gabonees' },
  { name: 'Gambian', code: 'GM', nameDutch: 'Gambiaans' },
  { name: 'Georgian', code: 'GE', nameDutch: 'Georgisch' },
  { name: 'German', code: 'DE', nameDutch: 'Duits' },
  { name: 'Ghanaian', code: 'GH', nameDutch: 'Ghanees' },
  { name: 'Greek', code: 'GR', nameDutch: 'Grieks' },
  { name: 'Grenadian', code: 'GD', nameDutch: 'Grenadiaans' },
  { name: 'Guatemalan', code: 'GT', nameDutch: 'Guatemalteeks' },
  { name: 'Guinean', code: 'GN', nameDutch: 'Guinees' },
  { name: 'Guinea-Bissauan', code: 'GW', nameDutch: 'Guinee-Bissaus' },
  { name: 'Guyanese', code: 'GY', nameDutch: 'Guyaans' },
  { name: 'Haitian', code: 'HT', nameDutch: 'Haïtiaans' },
  { name: 'Honduran', code: 'HN', nameDutch: 'Hondurees' },
  { name: 'Hungarian', code: 'HU', nameDutch: 'Hongaars' },
  { name: 'Icelandic', code: 'IS', nameDutch: 'IJslands' },
  { name: 'Indian', code: 'IN', nameDutch: 'Indiaas' },
  { name: 'Indonesian', code: 'ID', nameDutch: 'Indonesisch' },
  { name: 'Iranian', code: 'IR', nameDutch: 'Iraans' },
  { name: 'Iraqi', code: 'IQ', nameDutch: 'Iraaks' },
  { name: 'Irish', code: 'IE', nameDutch: 'Iers' },
  { name: 'Israeli', code: 'IL', nameDutch: 'Israëlisch' },
  { name: 'Italian', code: 'IT', nameDutch: 'Italiaans' },
  { name: 'Ivorian', code: 'CI', nameDutch: 'Ivoriaans' },
  { name: 'Jamaican', code: 'JM', nameDutch: 'Jamaicaans' },
  { name: 'Japanese', code: 'JP', nameDutch: 'Japans' },
  { name: 'Jordanian', code: 'JO', nameDutch: 'Jordaans' },
  { name: 'Kazakhstani', code: 'KZ', nameDutch: 'Kazachs' },
  { name: 'Kenyan', code: 'KE', nameDutch: 'Keniaans' },
  { name: 'Kiribati', code: 'KI', nameDutch: 'Kiribatisch' },
  { name: 'Korean', code: 'KR', nameDutch: 'Koreaans' },
  { name: 'Kosovar', code: 'XK', nameDutch: 'Kosovaars' },
  { name: 'Kuwaiti', code: 'KW', nameDutch: 'Koeweits' },
  { name: 'Kyrgyzstani', code: 'KG', nameDutch: 'Kirgizisch' },
  { name: 'Lao', code: 'LA', nameDutch: 'Laotiaans' },
  { name: 'Latvian', code: 'LV', nameDutch: 'Lets' },
  { name: 'Lebanese', code: 'LB', nameDutch: 'Libanees' },
  { name: 'Lesotho', code: 'LS', nameDutch: 'Lesothaans' },
  { name: 'Liberian', code: 'LR', nameDutch: 'Liberiaans' },
  { name: 'Libyan', code: 'LY', nameDutch: 'Libisch' },
  { name: 'Liechtenstein', code: 'LI', nameDutch: 'Liechtensteins' },
  { name: 'Lithuanian', code: 'LT', nameDutch: 'Litouws' },
  { name: 'Luxembourgish', code: 'LU', nameDutch: 'Luxemburgs' },
  { name: 'Madagascan', code: 'MG', nameDutch: 'Malagassisch' },
  { name: 'Malawian', code: 'MW', nameDutch: 'Malawiaans' },
  { name: 'Malaysian', code: 'MY', nameDutch: 'Maleisisch' },
  { name: 'Maldivian', code: 'MV', nameDutch: 'Maldivisch' },
  { name: 'Malian', code: 'ML', nameDutch: 'Malinees' },
  { name: 'Maltese', code: 'MT', nameDutch: 'Maltees' },
  { name: 'Marshallese', code: 'MH', nameDutch: 'Marshallees' },
  { name: 'Mauritanian', code: 'MR', nameDutch: 'Mauritaans' },
  { name: 'Mauritian', code: 'MU', nameDutch: 'Mauritiaans' },
  { name: 'Mexican', code: 'MX', nameDutch: 'Mexicaans' },
  { name: 'Micronesian', code: 'FM', nameDutch: 'Micronesisch' },
  { name: 'Moldovan', code: 'MD', nameDutch: 'Moldavisch' },
  { name: 'Monacan', code: 'MC', nameDutch: 'Monegaskisch' },
  { name: 'Mongolian', code: 'MN', nameDutch: 'Mongools' },
  { name: 'Montenegrin', code: 'ME', nameDutch: 'Montenegrijns' },
  { name: 'Moroccan', code: 'MA', nameDutch: 'Marokkaans' },
  { name: 'Mozambican', code: 'MZ', nameDutch: 'Mozambikaans' },
  { name: 'Namibian', code: 'NA', nameDutch: 'Namibisch' },
  { name: 'Nauruan', code: 'NR', nameDutch: 'Nauruaans' },
  { name: 'Nepalese', code: 'NP', nameDutch: 'Nepalees' },
  { name: 'New Zealander', code: 'NZ', nameDutch: 'Nieuw-Zeelands' },
  { name: 'Nicaraguan', code: 'NI', nameDutch: 'Nicaraguaans' },
  { name: 'Nigerien', code: 'NE', nameDutch: 'Nigerees' },
  { name: 'Nigerian', code: 'NG', nameDutch: 'Nigeriaans' },
  { name: 'North Macedonian', code: 'MK', nameDutch: 'Noord-Macedonisch' },
  { name: 'Norwegian', code: 'NO', nameDutch: 'Noors' },
  { name: 'Omani', code: 'OM', nameDutch: 'Omaans' },
  { name: 'Pakistani', code: 'PK', nameDutch: 'Pakistaans' },
  { name: 'Palauan', code: 'PW', nameDutch: 'Palauaans' },
  { name: 'Palestinian', code: 'PS', nameDutch: 'Palestijns' },
  { name: 'Panamanian', code: 'PA', nameDutch: 'Panamees' },
  { name: 'Papua New Guinean', code: 'PG', nameDutch: 'Papoea-Nieuw-Guinees' },
  { name: 'Paraguayan', code: 'PY', nameDutch: 'Paraguayaans' },
  { name: 'Peruvian', code: 'PE', nameDutch: 'Peruaans' },
  { name: 'Philippine', code: 'PH', nameDutch: 'Filipijns' },
  { name: 'Polish', code: 'PL', nameDutch: 'Pools' },
  { name: 'Portuguese', code: 'PT', nameDutch: 'Portugees' },
  { name: 'Qatari', code: 'QA', nameDutch: 'Qatarees' },
  { name: 'Romanian', code: 'RO', nameDutch: 'Roemeens' },
  { name: 'Russian', code: 'RU', nameDutch: 'Russisch' },
  { name: 'Rwandan', code: 'RW', nameDutch: 'Rwandees' },
  {
    name: 'Saint Kitts and Nevis',
    code: 'KN',
    nameDutch: 'Saint Kitts en Nevis',
  },
  { name: 'Saint Lucian', code: 'LC', nameDutch: 'Saint Luciaans' },
  { name: 'Saint Vincentian', code: 'VC', nameDutch: 'Saint Vincentiaans' },
  { name: 'Samoan', code: 'WS', nameDutch: 'Samoaans' },
  { name: 'San Marinese', code: 'SM', nameDutch: 'San Marinees' },
  { name: 'Sao Tomean', code: 'ST', nameDutch: 'Sao Toméaans' },
  { name: 'Saudi', code: 'SA', nameDutch: 'Saoedi-Arabisch' },
  { name: 'Senegalese', code: 'SN', nameDutch: 'Senegalees' },
  { name: 'Serbian', code: 'RS', nameDutch: 'Servisch' },
  { name: 'Seychellois', code: 'SC', nameDutch: 'Seychels' },
  { name: 'Sierra Leonean', code: 'SL', nameDutch: 'Sierra Leoons' },
  { name: 'Singaporean', code: 'SG', nameDutch: 'Singaporees' },
  { name: 'Slovak', code: 'SK', nameDutch: 'Slowaaks' },
  { name: 'Slovenian', code: 'SI', nameDutch: 'Sloveens' },
  { name: 'Solomon Islander', code: 'SB', nameDutch: 'Salomonseilands' },
  { name: 'Somali', code: 'SO', nameDutch: 'Somalisch' },
  { name: 'South African', code: 'ZA', nameDutch: 'Zuid-Afrikaans' },
  { name: 'South Sudanese', code: 'SS', nameDutch: 'Zuid-Soedanees' },
  { name: 'Spanish', code: 'ES', nameDutch: 'Spaans' },
  { name: 'Sri Lankan', code: 'LK', nameDutch: 'Sri Lankaans' },
  { name: 'Sudanese', code: 'SD', nameDutch: 'Soedanees' },
  { name: 'Surinamese', code: 'SR', nameDutch: 'Surinaams' },
  { name: 'Swedish', code: 'SE', nameDutch: 'Zweeds' },
  { name: 'Swiss', code: 'CH', nameDutch: 'Zwitser' },
  { name: 'Syrian', code: 'SY', nameDutch: 'Syrisch' },
  { name: 'Tajikistani', code: 'TJ', nameDutch: 'Tadzjieks' },
  { name: 'Tanzanian', code: 'TZ', nameDutch: 'Tanzaniaans' },
  { name: 'Thai', code: 'TH', nameDutch: 'Thais' },
  { name: 'Togolese', code: 'TG', nameDutch: 'Togolees' },
  { name: 'Tongan', code: 'TO', nameDutch: 'Tongaans' },
  {
    name: 'Trinidadian or Tobagonian',
    code: 'TT',
    nameDutch: 'Trinidadiaans of Tobagaans',
  },
  { name: 'Tunisian', code: 'TN', nameDutch: 'Tunesisch' },
  { name: 'Turkish', code: 'TR', nameDutch: 'Turks' },
  { name: 'Turkmen', code: 'TM', nameDutch: 'Turkmeens' },
  { name: 'Tuvaluan', code: 'TV', nameDutch: 'Tuvaluaans' },
  { name: 'Ugandan', code: 'UG', nameDutch: 'Oegandees' },
  { name: 'Ukrainian', code: 'UA', nameDutch: 'Oekraïens' },
  { name: 'Emirati', code: 'AE', nameDutch: 'Emiraats' },
  { name: 'Uruguayan', code: 'UY', nameDutch: 'Uruguayaans' },
  { name: 'Uzbekistani', code: 'UZ', nameDutch: 'Oezbeeks' },
  { name: 'Vanuatuan', code: 'VU', nameDutch: 'Vanuatuaans' },
  { name: 'Venezuelan', code: 'VE', nameDutch: 'Venezolaans' },
  { name: 'Vietnamese', code: 'VN', nameDutch: 'Vietnamees' },
  { name: 'Yemeni', code: 'YE', nameDutch: 'Jemenitisch' },
  { name: 'Zambian', code: 'ZM', nameDutch: 'Zambiaans' },
  { name: 'Zimbabwean', code: 'ZW', nameDutch: 'Zimbabwaans' },
];

const up = async (context: MigrationContext) => {
  try {
    const collection = context.destinationClient
      ?.db()
      .collection('nightregistrationnationalities');

    if (!collection) {
      throw new Error('nightregistrationnationalities collection not found');
    }

    for (const nationality of nationalities) {
      await collection.insertOne({ ...nationality });
      console.log(`Inserted nationality: ${nationality.name}`);
    }
  } catch (error) {
    console.error('Error importing nationalities: ', error);
  }
};

export default up;
