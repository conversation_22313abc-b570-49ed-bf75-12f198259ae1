import {
  BadRequestException,
  ForbiddenException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import _, { flatMap } from 'lodash';
import { Model } from 'mongoose';

import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { JobTemplateTypeEnum } from '~/shared/enums/job-template.enum';
import { ModuleNameEnum } from '~/shared/enums/module-name.enum';
import { TenantRoleEnum } from '~/shared/enums/tenant-role.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { JOB_TEMPLATE_MESSAGE_KEY } from '~/shared/message-keys/job-template.message-keys';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery, parseObjectId } from '~/utils';
import { validteAndSortPosition } from '~/utils/position.util';

import { TenantUserService } from '../tenant-user/tenant-user.service';
import { UnitModel } from '../unit/unit.model';
import {
  CopyJobTemplateDto,
  CreateJobTemplateDto,
  DeleteJobTemplateDto,
  DeletePointJobTemplateDto,
  GetJobTemplateByUnitsDto,
  UpdateJobTemplateDto,
} from './dtos/job-template.dto';
import { JobTemplateModel } from './job-template.model';

@Injectable()
export class JobTemplateService {
  constructor(
    @InjectModel(JobTemplateModel)
    private readonly jobTemplateModel: MongooseModel<JobTemplateModel>,
    @InjectModel(UnitModel)
    private readonly unitModel: Model<UnitModel>,

    private readonly tenantUserService: TenantUserService,
  ) {}

  async findAll(params: QueryParamsDto) {
    const { _id } = params;
    delete params._id;
    const { query, options } = buildQuery(params, ['name']);

    if (_id) {
      query._id = { $ne: _id };
    }

    let offset = options.offset;
    let limit = options.limit;

    if (limit === -1) {
      offset = 0;
      limit = await this.jobTemplateModel.countDocuments({ ...query });
    }

    let queryOption: any = {
      offset: offset,
      limit: limit,
      sort: options.sort,
      select: 'name points unit',
    };

    if (params.sortBy === 'name') {
      queryOption = {
        offset: offset,
        limit: limit,
        sort: options.sort,
        select: 'name points unit',
        collation: { locale: 'en', strength: 2 },
      };
    }

    return this.jobTemplateModel.paginate(query, queryOption);
  }

  async findOne(id: string) {
    return this.jobTemplateModel
      .findById(id)
      .populate([
        {
          path: 'unit',
          select: '_id name',
        },
      ])
      .select('name points unit')
      .lean();
  }

  async findByUnit(id: string) {
    const jobTemplates = await this.jobTemplateModel
      .aggregate([
        {
          $match: {
            unit: parseObjectId(id),
          },
        },
        {
          $lookup: {
            from: 'units',
            localField: 'unit',
            foreignField: '_id',
            as: 'unit',
          },
        },
        {
          $unwind: '$unit',
        },
        {
          $project: {
            name: 1,
            points: {
              $sortArray: { input: '$points', sortBy: { position: 1 } },
            },
            unit: {
              _id: 1,
              name: 1,
            },
          },
        },
      ])
      .exec();
    return jobTemplates.length > 0 ? jobTemplates[0] : null;
  }

  async findByUnits(data: GetJobTemplateByUnitsDto) {
    const { units } = data;

    return await this.jobTemplateModel
      .find({ unit: { $in: units } })
      .populate([
        {
          path: 'unit',
          select: '_id name',
        },
      ])
      .select('name points unit')
      .lean();
  }

  async create(data: CreateJobTemplateDto) {
    await this.validateCreatedPayload(data);
    switch (data.type) {
      case JobTemplateTypeEnum.GENERAL: {
        data.points = validteAndSortPosition(
          data.points,
          ModuleNameEnum.JOB_TEMPLATE,
          false,
        );

        const jobTemplate = await this.jobTemplateModel.create(data);
        return this.findOne(jobTemplate._id.toString());
      }
      case JobTemplateTypeEnum.INSPECTION:
        const existedJobTemplate = await this.jobTemplateModel
          .findOne({
            unit: data.unit,
          })
          .lean();

        if (existedJobTemplate) {
          let points = existedJobTemplate.points as any[];
          points.push(...data.points);

          points = validteAndSortPosition(
            points,
            ModuleNameEnum.JOB_TEMPLATE,
            false,
          );

          await this.jobTemplateModel.updateOne(
            {
              _id: existedJobTemplate._id,
            },
            {
              points: points,
            },
          );
          return this.findOne(existedJobTemplate._id.toString());
        } else {
          data.points = validteAndSortPosition(
            data.points,
            ModuleNameEnum.JOB_TEMPLATE,
            false,
          );

          const jobTemplate = await this.jobTemplateModel.create(data);
          return this.findOne(jobTemplate._id.toString());
        }
    }
  }

  async update(data: UpdateJobTemplateDto) {
    const { id, ...rest } = data;

    rest.points = validteAndSortPosition(
      rest.points,
      ModuleNameEnum.JOB_TEMPLATE,
      false,
    );

    const jobTemplate = await this.jobTemplateModel.findById(id);
    if (!jobTemplate) {
      throw new NotFoundException(JOB_TEMPLATE_MESSAGE_KEY.NOT_FOUND);
    }

    await this.validatePermissionForJobTemplate(rest.user);

    const jobTemplatePointIds = jobTemplate.points.map((point) => point._id);
    const payloadPointIds = rest.points
      .filter((point) => !!point._id)
      .map((point) => point._id);
    const isDifference =
      _.difference(payloadPointIds, jobTemplatePointIds).length > 0;

    switch (jobTemplate.type) {
      case JobTemplateTypeEnum.GENERAL:
        if (rest.name) {
          const existedName = await this.jobTemplateModel
            .aggregate([
              {
                $match: {
                  $expr: {
                    $eq: [
                      { $toLower: { $trim: { input: '$name' } } },
                      rest.name.trim().toLowerCase(),
                    ],
                  },
                },
              },
              {
                $match: {
                  _id: {
                    $ne: jobTemplate._id,
                  },
                },
              },
              { $limit: 1 },
            ])
            .exec();
          if (existedName.length > 0) {
            throw new BadRequestException(
              JOB_TEMPLATE_MESSAGE_KEY.EXISTED_IN_SYSTEM,
            );
          }

          if (isDifference) {
            throw new BadRequestException(
              JOB_TEMPLATE_MESSAGE_KEY.POINTS_NOT_FOUND,
            );
          }
        }
        break;
      case JobTemplateTypeEnum.INSPECTION: {
        if (rest.name) {
          throw new BadRequestException(
            JOB_TEMPLATE_MESSAGE_KEY.TYPE_INSPECTION_CANNOT_CUSTOM_NAME,
          );
        }

        if (
          isDifference ||
          jobTemplatePointIds.length !== payloadPointIds.length
        ) {
          throw new BadRequestException(
            JOB_TEMPLATE_MESSAGE_KEY.CANNOT_ADD_OR_REMOVE_POINTS,
          );
        }
        break;
      }
      default:
        break;
    }

    await this.jobTemplateModel.updateOne({ _id: id }, rest);

    return this.findOne(id);
  }

  async delete(payload: DeleteJobTemplateDto) {
    const deleted = await this.jobTemplateModel.findByIdAndDelete(payload.id);

    if (!deleted) {
      throw new NotFoundException(JOB_TEMPLATE_MESSAGE_KEY.NOT_FOUND);
    }

    await this.validatePermissionForJobTemplate(payload.user);

    return {};
  }

  async deletePoint(payload: DeletePointJobTemplateDto) {
    const jobTemplate = await this.jobTemplateModel.findById(payload.id).lean();
    if (!jobTemplate) {
      throw new NotFoundException(JOB_TEMPLATE_MESSAGE_KEY.NOT_FOUND);
    }

    await this.validatePermissionForJobTemplate(payload.user);

    const points = jobTemplate.points;

    const pointIndex = points.findIndex(
      (point) => point._id === payload.pointId,
    );
    if (pointIndex < 0) {
      throw new BadRequestException(JOB_TEMPLATE_MESSAGE_KEY.POINT_NOT_FOUND);
    }

    points.splice(pointIndex, 1);

    let position = 1;
    points.forEach((point) => {
      point.position = position;
      position++;
    });

    if (points.length === 0) {
      return await this.delete({ id: payload.id, user: payload.user });
    }

    await this.jobTemplateModel.updateOne(
      { _id: payload.id },
      {
        points: points,
      },
    );

    return this.findOne(payload.id);
  }

  async copyJobTemplates(payload: CopyJobTemplateDto) {
    const { sourceUnitIds, targetUnitIds } = payload;

    await this.validatePermissionForJobTemplate(payload.user);

    const jobTemplates = await this.jobTemplateModel
      .find({
        unit: {
          $in: sourceUnitIds,
        },
      })
      .lean();

    const sourcePoints = flatMap(
      jobTemplates,
      (jobTemplate) => jobTemplate.points,
    );

    const countUnits = await this.unitModel
      .countDocuments({
        _id: {
          $in: targetUnitIds,
        },
      })
      .lean();

    if (countUnits !== targetUnitIds.length) {
      throw new BadRequestException(JOB_TEMPLATE_MESSAGE_KEY.UNITS_NOT_FOUND);
    }
    for (const targetUnitId of targetUnitIds) {
      const unit = await this.unitModel.findById(targetUnitId).lean();

      const name = `INSPECTION POINTS FOR ${unit?.name.toUpperCase()}`;
      const updatedPoints: any[] = [];
      const targetJobTemplate = await this.jobTemplateModel
        .findOne({
          unit: targetUnitId,
        })
        .lean();

      if (targetJobTemplate) {
        updatedPoints.push(...targetJobTemplate.points);
      }

      updatedPoints.push(...sourcePoints);
      let position = 1;
      updatedPoints.forEach((updatedPoint) => {
        updatedPoint.position = position;
        position++;
      });

      await this.jobTemplateModel
        .updateOne(
          { unit: targetUnitId },
          {
            unit: targetUnitId,
            type: JobTemplateTypeEnum.INSPECTION,
            name: name,
            points: updatedPoints,
          },
          {
            upsert: true,
          },
        )
        .exec();
    }
    return this.findByUnits({ units: targetUnitIds });
  }

  private async validateCreatedPayload(payload: CreateJobTemplateDto) {
    await this.validatePermissionForJobTemplate(payload.user);
    switch (payload.type) {
      case JobTemplateTypeEnum.GENERAL:
        {
          if (payload.unit && payload.unit.length) {
            throw new BadRequestException(
              JOB_TEMPLATE_MESSAGE_KEY.TYPE_GENERAL_DOES_NOT_HAVE_UNIT,
            );
          }

          if (!payload.name) {
            throw new BadRequestException(
              JOB_TEMPLATE_MESSAGE_KEY.TYPE_GENERAL_MUST_HAVE_NAME,
            );
          }

          const existedName = await this.jobTemplateModel
            .aggregate([
              {
                $match: {
                  $expr: {
                    $eq: [
                      { $toLower: { $trim: { input: '$name' } } },
                      payload.name.trim().toLowerCase(),
                    ],
                  },
                },
              },
              { $limit: 1 },
            ])
            .exec();
          if (existedName.length > 0) {
            throw new BadRequestException(
              JOB_TEMPLATE_MESSAGE_KEY.EXISTED_IN_SYSTEM,
            );
          }
        }

        break;
      case JobTemplateTypeEnum.INSPECTION:
        {
          if (!payload.unit || !payload.unit.length) {
            throw new BadRequestException(
              JOB_TEMPLATE_MESSAGE_KEY.TYPE_INSPECTION_MUST_HAVE_UNIT,
            );
          }

          if (payload.name) {
            throw new BadRequestException(
              JOB_TEMPLATE_MESSAGE_KEY.TYPE_INSPECTION_CANNOT_CUSTOM_NAME,
            );
          }

          const unit = await this.unitModel.findById(payload.unit).lean();
          if (!unit) {
            throw new BadRequestException(
              JOB_TEMPLATE_MESSAGE_KEY.UNIT_NOT_FOUND,
            );
          }

          payload.name = `INSPECTION POINTS FOR ${unit.name.toUpperCase()}`;
        }
        break;
      default:
        break;
    }
  }

  private async validatePermissionOfUser(
    userId: string,
    roles: TenantRoleEnum[],
  ) {
    const isValid = await this.tenantUserService.verifyRoles(userId, roles);

    return isValid;
  }

  private async validatePermissionForJobTemplate(user: string) {
    const isValid = await this.validatePermissionOfUser(user, [
      TenantRoleEnum.INSPECTION_TEMPLATE_MANAGER,
    ]);

    if (!isValid) {
      throw new ForbiddenException();
    }
  }
}
