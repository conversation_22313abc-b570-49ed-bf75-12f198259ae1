import { Injectable } from '@nestjs/common';
import { mongoose } from '@typegoose/typegoose';
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
import { isNumber } from 'lodash';
import { Model, Types } from 'mongoose';

import {
  DATE_FORMAT_HYPHEN,
  DATE_FORMAT_SLASH,
  DEPOSIT_COST_TYPE_ITEM_CODE,
} from '~/constants/app.constant';
import { buildDateRangeQueryParams } from '~/modules/report/report.helper';
import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  AgreementLineType,
  ContractType,
  OwnerType,
} from '~/shared/enums/contract.enum';
import { EmployeeReportSortField } from '~/shared/enums/report.enum';
import { InjectModel } from '~/transformers/model.transformer';
import { parseObjectId } from '~/utils';

import { ContractModel } from '../contract/contract.model';
import { CostTypeModel } from '../costtype/costtype.model';
import { JobModel } from '../job/job.model';
import { Language, TenantUserModel } from '../tenant-user/tenant-user.model';
import {
  DebtorAndCreditorReportQueryDto,
  ReportEmployeeQueryParamsDto,
  RevenueHiredLocationQueryDto,
} from './dtos/report.dto';

dayjs.extend(duration);

@Injectable()
export class ReportService {
  constructor(
    @InjectModel(ContractModel)
    private readonly contractModel: Model<ContractModel>,
    @InjectModel(TenantUserModel)
    private readonly tenantUserModel: Model<TenantUserModel>,
    @InjectModel(JobModel)
    private readonly jobModel: Model<JobModel>,
    @InjectModel(CostTypeModel)
    private readonly costTypeModel: Model<CostTypeModel>,
  ) {}

  async getRevenueHiredLocations(payload: RevenueHiredLocationQueryDto) {
    const {
      location,
      costCenter,
      bvCompany,
      month,
      year,
      contractType,
      contact,
      costType,
      sortBy,
      sortDir,
    } = payload;

    const sortDirection = sortDir === 'desc' ? -1 : 1;
    const isRenting = contractType === ContractType.RENTING;
    let endDate: Date;
    let startDate: Date;

    if (month && year) {
      startDate = dayjs()
        .utc()
        .year(Number(year))
        .month(Number(month) - 1)
        .startOf('month')
        .toDate();
      endDate = dayjs()
        .utc()
        .year(Number(year))
        .month(Number(month) - 1)
        .endOf('month')
        .toDate();
    } else {
      startDate = dayjs().utc().year(Number(year)).startOf('year').toDate();
      endDate = dayjs().utc().year(Number(year)).endOf('year').toDate();
    }

    const costTypeQuery: any = {};
    if (costType) {
      costTypeQuery.costType = Array.isArray(costType)
        ? { $in: costType.map((v) => new Types.ObjectId(v)) }
        : new Types.ObjectId(costType);
    }
    const contractQuery: any = {
      type: contractType,
      ...(contact && { contact: new Types.ObjectId(contact) }),
      startDate: { $lte: endDate },
    };

    if (isRenting && location) {
      contractQuery.location = Array.isArray(location)
        ? { $in: location.map((v: string) => new Types.ObjectId(v)) }
        : new Types.ObjectId(location as string);
    }

    if (!isRenting && costCenter) {
      contractQuery.costCenter = Array.isArray(costCenter)
        ? { $in: costCenter.map((v: string) => new Types.ObjectId(v)) }
        : new Types.ObjectId(costCenter as string);
    }

    let aggregate = this.contractModel
      .aggregate()
      .match(contractQuery)
      .lookup({
        from: 'costlines',
        localField: 'agreementLines',
        foreignField: 'agreementLine',
        as: 'costLine',
        pipeline: [
          {
            $match: {
              periodType: 'periodic',
              approvedAt: { $ne: null },
              startDate: { $lte: endDate },
              endDate: { $gte: startDate },
              ...(costType && costTypeQuery),
            },
          },
          {
            $project: {
              isCredit: 1,
              costType: 1,
              period: 1,
              price: 1,
              totalPrice: 1,
              startDate: 1,
              endDate: 1,
            },
          },
          {
            $sort: { costType: 1 },
          },
        ],
      })
      .project({
        _id: 0,
        contact: 1,
        location: 1,
        costCenter: 1,
        isNew: { $ifNull: ['$isNew', true] },
        costLine: 1,
        updatedAt: 1,
      })
      .unwind({
        path: '$costLine',
        preserveNullAndEmptyArrays: false,
      })
      .replaceRoot({ $mergeObjects: ['$$ROOT', '$costLine'] })
      .append({ $unset: 'costLine' })
      .addFields({
        customPeriod: {
          $mergeObjects: [
            {
              $cond: [
                { $lt: ['$startDate', startDate] },
                { startDate: startDate, startDateOOB: true }, // OOB = out of bound
                '$$REMOVE',
              ],
            },
            {
              $cond: [
                { $gt: ['$endDate', endDate] },
                { endDate: endDate, endDateOOB: true }, // OOB = out of bound
                '$$REMOVE',
              ],
            },
          ],
        },
      })
      .addFields({
        isWholeMonth: {
          $cond: [
            {
              $and: [
                { $eq: ['$period', 'monthly'] },
                { $eq: [{ $dayOfMonth: '$startDate' }, 1] },
                {
                  $eq: [
                    {
                      $dayOfMonth: {
                        $dateAdd: {
                          startDate: '$endDate',
                          unit: 'day',
                          amount: 1,
                        },
                      },
                    },
                    1,
                  ],
                },
              ],
            },
            true,
            false,
          ],
        },
        dateDiff: {
          $cond: [
            { $or: ['$customPeriod.startDateOOB', '$customPeriod.endDateOOB'] },
            {
              $add: [
                {
                  $abs: {
                    $dateDiff: {
                      startDate: {
                        $ifNull: ['$customPeriod.startDate', '$startDate'],
                      },
                      endDate: {
                        $ifNull: ['$customPeriod.endDate', '$endDate'],
                      },
                      unit: 'day',
                    },
                  },
                },
                1,
              ],
            },
            '$$REMOVE',
          ],
        },
      })
      .group({
        _id: {
          location: isRenting ? '$location' : '$costCenter',
          contact: '$contact',
          costType: '$costType',
          isCredit: '$isCredit',
        },
        updatedAt: {
          $max: '$updatedAt',
        },
        costLines: {
          $push: {
            $cond: [{ $not: '$isCredit' }, '$$ROOT', '$$REMOVE'],
          },
        },
        credits: {
          $push: {
            $cond: ['$isCredit', '$$ROOT', '$$REMOVE'],
          },
        },
        ...(isRenting && {
          location: {
            $first: '$location',
          },
        }),
        ...(!isRenting && {
          costCenter: {
            $first: '$costCenter',
          },
        }),
        contact: {
          $first: '$contact',
        },
        costType: {
          $first: '$costType',
        },
      })
      .addFields({
        costLines: {
          $map: {
            input: '$costLines',
            as: 'c',
            in: {
              $let: {
                vars: {
                  baseDateDiff: {
                    $add: [
                      {
                        $abs: {
                          $dateDiff: {
                            startDate: '$$c.startDate',
                            endDate: '$$c.endDate',
                            unit: 'day',
                          },
                        },
                      },
                      1,
                    ],
                  },
                  multiplier: {
                    $cond: [{ $gt: ['$$c.totalPrice', 0] }, 1, -1],
                  },
                },
                in: {
                  $cond: [
                    { $gt: ['$$c.dateDiff', 0] },
                    {
                      $multiply: [
                        { $divide: [{ $abs: '$$c.price' }, 7] },
                        '$$c.dateDiff',
                        '$$multiplier',
                      ],
                    },
                    {
                      $multiply: [
                        {
                          $abs: {
                            $cond: [
                              '$$c.isWholeMonth',
                              {
                                $cond: [
                                  '$$c.isNew',
                                  {
                                    $multiply: [
                                      {
                                        $divide: [{ $abs: '$$c.price' }, 7],
                                      },
                                      365 / 12,
                                    ],
                                  },
                                  {
                                    $multiply: [
                                      { $abs: '$$c.price' },
                                      4.333333,
                                    ],
                                  },
                                ],
                              },
                              {
                                $multiply: [
                                  { $divide: [{ $abs: '$$c.price' }, 7] },
                                  '$$baseDateDiff',
                                ],
                              },
                            ],
                          },
                        },
                        '$$multiplier',
                      ],
                    },
                  ],
                },
              },
            },
          },
        },
        credits: {
          $map: {
            input: '$credits',
            as: 'c',
            in: {
              $let: {
                vars: {
                  baseDateDiff: {
                    $add: [
                      {
                        $abs: {
                          $dateDiff: {
                            startDate: '$$c.startDate',
                            endDate: '$$c.endDate',
                            unit: 'day',
                          },
                        },
                      },
                      1,
                    ],
                  },
                },
                in: {
                  $cond: [
                    { $gt: ['$$c.dateDiff', 0] },
                    {
                      $multiply: [
                        { $divide: ['$$c.totalPrice', '$$baseDateDiff'] },
                        '$$c.dateDiff',
                      ],
                    },
                    '$$c.totalPrice',
                  ],
                },
              },
            },
          },
        },
      })
      .lookup({
        from: 'costtypes',
        localField: 'costType',
        foreignField: '_id',
        as: 'costType',
        pipeline: [
          {
            $project: {
              _id: 1,
              itemCode: 1,
              name: 1,
              description: {
                $concat: ['$itemCode', ' - ', '$name'],
              },
            },
          },
        ],
      })
      .project({
        location: 1,
        contact: 1,
        costCenter: 1,
        updatedAt: 1,
        costType: { $first: '$costType' },
        costLine: {
          $cond: [
            { $eq: [{ $size: '$costLines' }, 0] },
            '$$REMOVE',
            {
              $reduce: {
                input: '$costLines',
                initialValue: 0,
                in: { $add: ['$$value', '$$this'] },
              },
            },
          ],
        },
        credit: {
          $cond: [
            { $eq: [{ $size: '$credits' }, 0] },
            '$$REMOVE',
            {
              $reduce: {
                input: '$credits',
                initialValue: 0,
                in: { $add: ['$$value', '$$this'] },
              },
            },
          ],
        },
      })
      .group({
        _id: {
          location: '$_id.location',
          contact: '$_id.contact',
        },
        revenues: {
          $push: {
            costType: '$costType',
            totalPricePerCostType: { $ifNull: ['$costLine', '$credit'] },
          },
        },
        updatedAt: {
          $first: '$updatedAt',
        },
        contact: {
          $first: '$contact',
        },
        location: {
          $first: '$location',
        },
        costCenter: {
          $first: '$costCenter',
        },
      });

    if (isRenting) {
      aggregate = aggregate.lookup({
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        as: 'location',
        pipeline: [
          {
            $lookup: {
              from: 'costcenters',
              localField: 'costCenter',
              foreignField: '_id',
              as: 'costCenter',
              pipeline: [{ $project: { name: 1, identifier: 1 } }],
            },
          },
          {
            $lookup: {
              from: 'bvcompanies',
              localField: 'bvCompany',
              foreignField: '_id',
              as: 'bvCompany',
              pipeline: [{ $project: { name: 1 } }],
            },
          },
          {
            $project: {
              fullAddress: 1,
              bvCompany: { $first: '$bvCompany' },
              costCenter: { $first: '$costCenter' },
            },
          },
        ],
      });
    } else {
      aggregate = aggregate.lookup({
        from: 'costcenters',
        localField: 'costCenter',
        foreignField: '_id',
        as: 'costCenter',
        pipeline: [
          {
            $lookup: {
              from: 'locations',
              localField: 'locations',
              foreignField: '_id',
              as: 'locations',
              pipeline: [{ $project: { bvCompany: 1 } }],
            },
          },
          {
            $addFields: {
              firstLocation: { $first: '$locations' },
              locations: '$$REMOVE',
            },
          },
          {
            $lookup: {
              from: 'bvcompanies',
              localField: 'firstLocation.bvCompany',
              foreignField: '_id',
              as: 'bvCompany',
              pipeline: [{ $project: { name: 1, identifier: 1 } }],
            },
          },
          {
            $project: {
              name: 1,
              identifier: 1,
              bvCompany: { $first: '$bvCompany' },
            },
          },
        ],
      });
    }

    aggregate = aggregate
      .lookup({
        from: 'contacts',
        localField: 'contact',
        foreignField: '_id',
        as: 'contact',
        pipeline: [{ $project: { displayName: 1, name: 1 } }],
      })
      .addFields({
        location: { $first: '$location' },
        costCenter: { $first: '$costCenter' },
        contact: { $first: '$contact' },
        totalPricePerLocation: {
          $sum: '$revenues.totalPricePerCostType',
        },
      })
      .addFields({
        costCenter: {
          $ifNull: ['$costCenter', '$location.costCenter'],
        },
        bvCompany: {
          $ifNull: ['$location.bvCompany', '$costCenter.bvCompany'],
        },
      });

    if (bvCompany) {
      aggregate = aggregate.match({
        'bvCompany._id': new mongoose.Types.ObjectId(bvCompany),
      });
    }

    if (sortBy) {
      aggregate = aggregate.sort({ [sortBy]: sortDirection });
    }

    aggregate = aggregate
      .group({
        _id: null,
        items: {
          $push: '$$ROOT',
        },
        totalPrice: {
          $sum: '$totalPricePerLocation',
        },
      })
      .project({
        _id: 0,
        totalPrice: 1,
        items: {
          location: {
            _id: 1,
            fullAddress: 1,
          },
          costCenter: 1,
          bvCompany: 1,
          contact: 1,
          revenues: 1,
          totalPricePerLocation: 1,
        },
      });

    return (await aggregate.exec())[0] ?? { items: [], totalPrice: 0.0 };
  }

  async exportRevenueHiredLocations(payload: RevenueHiredLocationQueryDto) {
    const { year, month, contractType } = payload;
    const monthAndYearStr = month
      ? `${month.padStart(2, '0')}-${year}`
      : `${year}`;
    const datas = (await this.getRevenueHiredLocations(payload)).items;
    const currentDate = dayjs().utc().format(DATE_FORMAT_HYPHEN);
    const isRenting = contractType === ContractType.RENTING;

    const header = [
      { field: 'month', title: 'Month' },
      { field: 'costCenter', title: 'Cost Center' },
      { field: 'itemCode', title: 'Item Code' },
      { field: 'costType', title: 'Cost Type' },
      ...(isRenting ? [{ field: 'location', title: 'Location' }] : []),
      { field: 'bvCompany', title: 'BV Company' },
      { field: 'contact', title: 'Customer' },
      { field: 'totalPrice', title: 'Total' },
    ];
    const fileName = `revenue-hired-locations-${currentDate}.csv`;
    const csvDatas = datas.flatMap((data: any) => {
      return data.revenues.map((revenue: any) => {
        return {
          month: monthAndYearStr,
          costCenter: data.costCenter?.identifier,
          itemCode: revenue.costType?.itemCode,
          costType: revenue.costType?.name,
          ...(isRenting && { location: data.location?.fullAddress }),
          bvCompany: data.bvCompany?.name,
          contact: data.contact?.name,
          totalPrice: this.currencyNL(revenue.totalPricePerCostType),
        };
      });
    });
    return { data: csvDatas, header, fileName };
  }

  async getDebtorAndRentingReport(payload: DebtorAndCreditorReportQueryDto) {
    const {
      year,
      month,
      week,
      team,
      location,
      bvCompany,
      customer,
      creditor,
      owner,
    } = payload;

    const { startDate, endDate } = buildDateRangeQueryParams({
      year,
      month,
      week,
    });

    const contactQuery: Types.ObjectId[] = [];
    if (customer) {
      contactQuery.push(new Types.ObjectId(customer));
    }
    if (creditor) {
      contactQuery.push(new Types.ObjectId(creditor));
    }

    const contractQuery: any = {
      type: { $in: [ContractType.RENTING, ContractType.CREDITOR] },
      ...(location && { location: new Types.ObjectId(location) }),
      ...(owner && { isGenerateCostLine: owner === OwnerType.HOMEE }),
      startDate: { $lte: endDate },
      $or: [{ endDate: { $gte: startDate } }, { endDate: null }],
      ...(contactQuery.length > 0 && { contact: { $in: contactQuery } }),
    };

    const depositCostTypeIds: Types.ObjectId[] = await this.costTypeModel
      .find({ itemCode: DEPOSIT_COST_TYPE_ITEM_CODE })
      .distinct('_id');

    let aggregate = this.contractModel
      .aggregate()
      .match(contractQuery)
      .project({
        identifier: 1,
        contact: 1,
        isGenerateCostLine: 1,
        isWholeLocation: 1,
        isNew: { $ifNull: ['$isNew', true] },
        startDate: 1,
        endDate: 1,
        type: 1,
        noticeDays: 1,
        location: 1,
        agreementLines: 1,
      })
      .sort({ _id: 1 })
      .lookup({
        from: 'agreementlines',
        localField: 'agreementLines',
        foreignField: '_id',
        as: 'agreementLines',
        let: { isNew: '$isNew' },
        pipeline: [
          {
            $project: {
              type: 1,
              period: 1,
              periodType: 1,
              costLineGenerals: 1,
              agreementLineUnits: '$units',
            },
          },
          { $sort: { _id: 1 } },
          {
            $lookup: {
              from: 'costlinegenerals',
              localField: 'costLineGenerals',
              foreignField: '_id',
              as: 'costLineGenerals',
              pipeline: [
                {
                  $match: {
                    startDate: { $lte: endDate },
                    $or: [{ endDate: { $gte: startDate } }, { endDate: null }],
                  },
                },
                {
                  $addFields: {
                    costTypeId: '$costType',
                  },
                },
                { $project: { unit: 1, price: 1, costType: 1, costTypeId: 1 } },
              ],
            },
          },
          {
            $addFields: {
              units: {
                $reduce: {
                  input: '$costLineGenerals',
                  initialValue: [],
                  in: { $concatArrays: ['$$value', ['$$this.unit']] },
                },
              },
              price: { $sum: '$costLineGenerals.price' },
            },
          },
          {
            $addFields: {
              depositPrice: {
                // add condition only sum if agreement line is one-time
                $cond: [
                  { $eq: ['$periodType', AgreementLinePeriodType.ONE_TIME] },
                  {
                    $sum: {
                      $map: {
                        input: '$costLineGenerals',
                        as: 'clg',
                        in: {
                          $cond: [
                            { $in: ['$$clg.costType', depositCostTypeIds] },
                            '$$clg.price',
                            0,
                          ],
                        },
                      },
                    },
                  },
                  0,
                ],
              },
            },
          },
          {
            $addFields: {
              units: {
                $cond: [{ $eq: [0, { $size: '$units' }] }, [null], '$units'],
              },
              monthlyTypePrice: {
                $cond: [
                  {
                    $in: [
                      '$period',
                      [AgreementLinePeriod.MONTHLY, AgreementLinePeriod.WEEKLY],
                    ],
                  },
                  {
                    $cond: [
                      '$$isNew',
                      { $multiply: [{ $divide: ['$price', 7.0] }, 365 / 12] },
                      { $multiply: ['$price', 4.333333] },
                    ],
                  },
                  0.0,
                ],
              },
            },
          },
        ],
      });

    aggregate = aggregate
      .match({ 'agreementLines.0': { $exists: true } })
      .addFields({
        accommodationAgreementLines: {
          $filter: {
            input: '$agreementLines',
            as: 'agreementLine',
            cond: {
              $and: [
                {
                  $eq: [
                    '$$agreementLine.type',
                    AgreementLineType.ACCOMMODATION,
                  ],
                },
                { $gt: [{ $size: '$$agreementLine.costLineGenerals' }, 0] },
              ],
            },
          },
        },
        otherAgreementLines: {
          $filter: {
            input: '$agreementLines',
            as: 'agreementLine',
            cond: {
              $and: [
                {
                  $in: [
                    '$$agreementLine.type',
                    [AgreementLineType.PRODUCT, AgreementLineType.SERVICE],
                  ],
                },
                { $gt: [{ $size: '$$agreementLine.costLineGenerals' }, 0] },
              ],
            },
          },
        },
      })
      .addFields({
        distinctUnits: {
          $setDifference: [
            {
              $reduce: {
                input: {
                  $cond: [
                    '$isWholeLocation',
                    '$accommodationAgreementLines.agreementLineUnits',
                    '$accommodationAgreementLines.units',
                  ],
                },
                initialValue: [],
                in: { $concatArrays: ['$$value', '$$this'] },
              },
            },
            [],
          ],
        },
        otherDistinctUnits: {
          $setDifference: [
            {
              $reduce: {
                input: '$otherAgreementLines.units',
                initialValue: [],
                in: { $concatArrays: ['$$value', '$$this'] },
              },
            },
            [],
          ],
        },
      })
      .addFields({
        useRootUnit: {
          $let: {
            vars: {
              emptySubUnitUsed: {
                $size: {
                  $filter: {
                    input: '$otherDistinctUnits',
                    as: 'v',
                    cond: { $eq: ['$$v', null] },
                  },
                },
              },
            },
            in: {
              $cond: [
                {
                  $or: [
                    { $eq: [0, { $size: '$otherDistinctUnits' }] },
                    { $eq: [0, '$$emptySubUnitUsed'] },
                  ],
                },
                false,
                true,
              ],
            },
          },
        },
      })
      .addFields({
        otherDistinctUnits: {
          $cond: ['$useRootUnit', '$$REMOVE', '$otherDistinctUnits'],
        },
      })
      .lookup({
        from: 'units',
        localField: 'distinctUnits',
        foreignField: '_id',
        as: 'distinctUnits',
        pipeline: [
          {
            $project: {
              maxOccupants: 1,
            },
          },
        ],
      })
      .lookup({
        from: 'units',
        localField: 'otherDistinctUnits',
        foreignField: '_id',
        as: 'otherDistinctUnits',
        pipeline: [
          {
            $project: {
              maxOccupants: 1,
            },
          },
        ],
      })
      .lookup({
        from: 'contacts',
        localField: 'contact',
        foreignField: '_id',
        as: 'contact',
        pipeline: [{ $project: { displayName: 1 } }],
      })
      .addFields({
        contact: { $first: '$contact' },
        totalBeds: { $sum: '$distinctUnits.maxOccupants' },
        otherTotalBeds: { $sum: '$otherDistinctUnits.maxOccupants' },
        totalDeposit: {
          $sum: '$agreementLines.depositPrice',
        },
      })
      .group({
        _id: '$location',
        location: { $first: '$location' },

        debtorRentingContracts: {
          $push: {
            $cond: [
              { $eq: ['$type', ContractType.RENTING] },
              '$$ROOT',
              '$$REMOVE',
            ],
          },
        },
        creditorRentingContracts: {
          $push: {
            $cond: [
              { $eq: ['$type', ContractType.CREDITOR] },
              '$$ROOT',
              '$$REMOVE',
            ],
          },
        },
      })
      .lookup({
        from: 'locations',
        localField: 'location',
        foreignField: '_id',
        as: 'location',
        pipeline: [
          {
            $project: {
              fullAddress: 1,
              costCenter: 1,
              bvCompany: 1,
              team: 1,
              maxOccupants: 1,
            },
          },
        ],
      })
      .addFields({ location: { $first: '$location' } });

    if (bvCompany) {
      aggregate = aggregate.match({
        'location.bvCompany': new Types.ObjectId(bvCompany),
      });
    }
    if (team) {
      aggregate = aggregate.match({
        'location.team': new Types.ObjectId(team),
      });
    }

    aggregate = aggregate
      .lookup({
        from: 'bvcompanies',
        localField: 'location.bvCompany',
        foreignField: '_id',
        as: 'bvCompany',
        pipeline: [{ $project: { name: 1 } }],
      })
      .lookup({
        from: 'teams',
        localField: 'location.team',
        foreignField: '_id',
        as: 'team',
        pipeline: [{ $project: { name: 1 } }],
      })
      .lookup({
        from: 'costcenters',
        localField: 'location.costCenter',
        foreignField: '_id',
        as: 'costCenter',
        pipeline: [{ $project: { identifier: 1 } }],
      })
      .project({
        _id: 0,
        location: { _id: 1, fullAddress: 1 },
        costCenter: { $first: '$costCenter' },
        bvCompany: { $first: '$bvCompany' },
        team: { $first: '$team' },
        debtorRentingContracts: {
          $map: {
            input: '$debtorRentingContracts',
            as: 'contract',
            in: {
              _id: '$$contract._id',
              identifier: '$$contract.identifier',
              contact: '$$contract.contact',
              totalBeds: '$$contract.totalBeds',
              startDate: '$$contract.startDate',
              endDate: '$$contract.endDate',
              noticeDays: '$$contract.noticeDays',
              owner: {
                $cond: [
                  '$$contract.isGenerateCostLine',
                  OwnerType.HOMEE,
                  OwnerType.LENTO,
                ],
              },
              monthlyPrice: {
                $sum: '$$contract.accommodationAgreementLines.monthlyTypePrice',
              },
              deposit: '$$contract.totalDeposit',
              pricePerBed: {
                $cond: [
                  { $gt: ['$$contract.totalBeds', 0] },
                  {
                    $divide: [
                      {
                        $sum: '$$contract.accommodationAgreementLines.price',
                      },
                      '$$contract.totalBeds',
                    ],
                  },
                  0.0,
                ],
              },
              otherCostsPerBed: {
                $let: {
                  vars: {
                    totalBeds: {
                      $cond: [
                        '$$contract.useRootUnit',
                        '$location.maxOccupants',
                        '$$contract.otherTotalBeds',
                      ],
                    },
                  },
                  in: {
                    $cond: [
                      { $gt: ['$$totalBeds', 0] },
                      {
                        $divide: [
                          { $sum: '$$contract.otherAgreementLines.price' },
                          '$$totalBeds',
                        ],
                      },
                      0.0,
                    ],
                  },
                },
              },
            },
          },
        },
        creditorRentingContracts: {
          $map: {
            input: '$creditorRentingContracts',
            as: 'contract',
            in: {
              _id: '$$contract._id',
              identifier: '$$contract.identifier',
              contact: '$$contract.contact',
              totalBeds: '$$contract.totalBeds',
              startDate: '$$contract.startDate',
              endDate: '$$contract.endDate',
              noticeDays: '$$contract.noticeDays',
              monthlyPrice: {
                $sum: '$$contract.accommodationAgreementLines.monthlyTypePrice',
              },
              deposit: '$$contract.totalDeposit',
              pricePerBed: {
                $cond: [
                  { $gt: ['$$contract.totalBeds', 0] },
                  {
                    $divide: [
                      {
                        $sum: '$$contract.accommodationAgreementLines.price',
                      },
                      '$$contract.totalBeds',
                    ],
                  },
                  0.0,
                ],
              },
              otherCostsPerBed: {
                $let: {
                  vars: {
                    totalBeds: {
                      $cond: [
                        '$$contract.useRootUnit',
                        '$location.maxOccupants',
                        '$$contract.otherTotalBeds',
                      ],
                    },
                  },
                  in: {
                    $cond: [
                      { $gt: ['$$totalBeds', 0] },
                      {
                        $divide: [
                          { $sum: '$$contract.otherAgreementLines.price' },
                          '$$totalBeds',
                        ],
                      },
                      0.0,
                    ],
                  },
                },
              },
            },
          },
        },
      })
      .sort({ 'costCenter.identifier': 1, 'location.fullAddress': 1 });

    return await aggregate.exec();
  }

  async exportDebtorAndRentingReport(payload: DebtorAndCreditorReportQueryDto) {
    const reports = await this.getDebtorAndRentingReport(payload);
    const currentDate = dayjs().utc().format(DATE_FORMAT_HYPHEN);

    const header = [
      { field: 'costCenter', title: 'Kostenplaatsen' },
      { field: 'location', title: 'Locatie' },
      { field: 'bvCompany', title: 'B.V.' },
      { field: 'debtorContractIdentifier', title: 'Contractnummer' },
      { field: 'debtorContractTeam', title: 'Team' },
      { field: 'debtorContractCustomer', title: 'Klant' },
      { field: 'debtorContractTotalBeds', title: 'Bedden' },
      { field: 'debtorContractStartDate', title: 'Startdatum' },
      { field: 'debtorContractEndDate', title: 'Einddatum' },
      { field: 'debtorContractNoticeDays', title: 'Opzeg termijn' },
      { field: 'debtorContractPricePerBed', title: 'Huur/bed' },
      { field: 'debtorContractOtherCostsPerBed', title: 'Overigen/bed' },
      { field: 'debtorContractMonthlyPrice', title: 'Maandelijkse prijs' },
      { field: 'debtorContractDeposit', title: 'Borg' },
      { field: 'debtorContractOwner', title: 'Eigenaar' },
      { field: 'creditorContractIdentifier', title: 'Contractnummer' },
      { field: 'creditorContractTeam', title: 'Team' },
      { field: 'creditorContractCreditor', title: 'Crediteur' },
      { field: 'creditorContractTotalBeds', title: 'Bedden' },
      { field: 'creditorContractStartDate', title: 'Startdatum' },
      { field: 'creditorContractEndDate', title: 'Einddatum' },
      { field: 'creditorContractNoticeDays', title: 'Opzeg termijn' },
      { field: 'creditorContractPricePerBed', title: 'Huur/bed' },
      { field: 'creditorContractMonthlyPrice', title: 'Maandelijkse prijs' },
      { field: 'creditorContractDeposit', title: 'Borg' },
    ];
    const fileName = `debtor-and-creditor-report-${currentDate}.csv`;
    const csvContent: any[] = [];

    for (const report of reports) {
      const { debtorRentingContracts, creditorRentingContracts } = report;
      const maxLength =
        debtorRentingContracts.length > creditorRentingContracts.length
          ? debtorRentingContracts.length
          : creditorRentingContracts.length;

      for (let i = 0; i < maxLength; i++) {
        let csvRow: any = {
          costCenter: report.costCenter?.identifier,
          location: report.location?.fullAddress,
          bvCompany: report.bvCompany?.name,
        };
        const debtorContract = debtorRentingContracts[i];
        const creditorContract = creditorRentingContracts[i];

        if (debtorContract) {
          csvRow = {
            ...csvRow,
            debtorContractIdentifier: debtorContract.identifier,
            debtorContractTeam: report.team?.name,
            debtorContractCustomer: debtorContract.contact?.displayName,
            debtorContractTotalBeds: debtorContract.totalBeds,
            debtorContractStartDate:
              debtorContract.startDate &&
              dayjs(debtorContract.startDate).utc().format(DATE_FORMAT_SLASH),
            debtorContractEndDate:
              debtorContract.endDate &&
              dayjs(debtorContract.endDate).utc().format(DATE_FORMAT_SLASH),
            debtorContractNoticeDays: debtorContract.noticeDays,
            debtorContractPricePerBed: this.currencyNL(
              debtorContract.pricePerBed,
            ),
            debtorContractOtherCostsPerBed: this.currencyNL(
              debtorContract.otherCostsPerBed,
            ),
            debtorContractMonthlyPrice: this.currencyNL(
              debtorContract.monthlyPrice,
            ),
            debtorContractDeposit: this.currencyNL(debtorContract.deposit ?? 0),
            debtorContractOwner: debtorContract.owner,
          };
        }

        if (creditorContract) {
          csvRow = {
            ...csvRow,
            creditorContractIdentifier: creditorContract.identifier,
            creditorContractTeam: report.team?.name,
            creditorContractCreditor: creditorContract.contact?.displayName,
            creditorContractTotalBeds: creditorContract.totalBeds,
            creditorContractStartDate:
              creditorContract.startDate &&
              dayjs(creditorContract.startDate).utc().format(DATE_FORMAT_SLASH),
            creditorContractEndDate:
              creditorContract.endDate &&
              dayjs(creditorContract.endDate).utc().format(DATE_FORMAT_SLASH),
            creditorContractNoticeDays: creditorContract.noticeDays,
            creditorContractPricePerBed: this.currencyNL(
              creditorContract.pricePerBed,
            ),
            creditorContractMonthlyPrice: this.currencyNL(
              creditorContract.monthlyPrice,
            ),
            creditorContractDeposit: this.currencyNL(
              creditorContract.deposit ?? 0,
            ),
          };
        }

        csvContent.push(csvRow);
      }
    }

    return { data: csvContent, header, fileName };
  }

  async getListEmployees(payload: ReportEmployeeQueryParamsDto) {
    const { team, employees, jobType, location, month, year, ...rest } =
      payload;

    const sortDir = rest.sortDir === 'desc' ? -1 : 1;

    const parseIds = (items: string | string[]) =>
      Array.isArray(items) ? items.map(parseObjectId) : [parseObjectId(items)];

    const teamFilter = team ? { _id: parseObjectId(team) } : {};
    const employeeFilter = employees
      ? { employee: { $in: parseIds(employees) } }
      : {};
    const jobTypeFilter = jobType ? { jobType } : {};
    const locationFilter = location
      ? { location: parseObjectId(location) }
      : {};
    let plannedDateFilter: any = {};
    if (year && month) {
      const startDate = dayjs()
        .utc()
        .year(year)
        .month(month - 1)
        .startOf('month')
        .toDate();
      const endDate = dayjs()
        .utc()
        .year(year)
        .month(month - 1)
        .endOf('month')
        .toDate();

      plannedDateFilter = {
        $or: [
          {
            plannedDate: { $gte: startDate, $lt: endDate },
            plannedEndDate: { $eq: null },
          },
          {
            plannedDate: { $lt: endDate },
            plannedEndDate: { $gte: startDate },
          },
        ],
      };
    }

    const jobSortFieldMap = {
      [EmployeeReportSortField.LOCATION]: 'location.fullAddress',
      [EmployeeReportSortField.JOB_TYPE]: 'jobType',
      [EmployeeReportSortField.ESTIMATED_HOURS]: 'estimatedHours',
      [EmployeeReportSortField.ACTUAL_HOURS]: 'actualHours',
    };
    const jobSortField = rest.sortBy && jobSortFieldMap[rest.sortBy];

    const aggregate: any = [
      {
        $match: {
          ...plannedDateFilter,
          ...locationFilter,
          ...jobTypeFilter,
          isDeleted: false,
        },
      },
      {
        $lookup: {
          from: 'jobemployees',
          localField: '_id',
          foreignField: 'job',
          as: 'jobemployees',
          pipeline: [
            {
              $match: {
                ...employeeFilter,
                ...plannedDateFilter,
                isDeleted: false,
              },
            },
            {
              $group: {
                _id: {
                  employee: '$employee',
                  job: '$job',
                },
                employee: { $first: '$employee' },
                job: { $first: '$job' },
                estimatedHours: { $sum: '$estimatedHours' },
                actualHours: { $sum: '$actualHours' },
              },
            },
          ],
        },
      },
      { $unwind: { path: '$jobemployees', preserveNullAndEmptyArrays: false } },
      {
        $lookup: {
          from: 'tenantusers',
          localField: 'jobemployees.employee',
          foreignField: '_id',
          as: 'employee',
          pipeline: [{ $match: { isDeleted: false } }],
        },
      },
      { $unwind: { path: '$employee', preserveNullAndEmptyArrays: false } },
      {
        $lookup: {
          from: 'teams',
          localField: 'employee.team',
          foreignField: '_id',
          as: 'team',
          pipeline: [{ $match: { ...teamFilter } }],
        },
      },
      { $unwind: { path: '$team', preserveNullAndEmptyArrays: false } },
      {
        $addFields: {
          'locationInfo._id': {
            $toObjectId: '$locationInfo._id',
          },
        },
      },
      {
        $group: {
          _id: '$locationInfo._id',
          data: {
            $push: '$$ROOT',
          },
        },
      },
      {
        $lookup: {
          from: 'costcenters',
          localField: '_id',
          foreignField: 'locations',
          as: 'costCenter',
          pipeline: [{ $project: { identifier: 1 } }],
        },
      },
      {
        $addFields: {
          costCenter: {
            $first: '$costCenter',
          },
        },
      },
      {
        $unwind: { path: '$data' },
      },
      {
        $replaceRoot: {
          newRoot: {
            $mergeObjects: ['$$ROOT', '$data'],
          },
        },
      },
      { $unset: 'data' },
      {
        $group: {
          _id: { teamId: '$team._id', employeeId: '$employee._id' },
          teamName: { $first: '$team.name' },
          displayName: { $first: '$employee.displayName' },
          jobs: {
            $push: {
              estimatedHours: '$jobemployees.estimatedHours',
              actualHours: '$jobemployees.actualHours',
              diffHours: {
                $subtract: [
                  '$jobemployees.actualHours',
                  '$jobemployees.estimatedHours',
                ],
              },
              location: {
                _id: '$locationInfo._id',
                fullAddress: '$locationInfo.fullAddress',
              },
              _id: '$_id',
              jobType: '$jobType',
              plannedDate: '$plannedDate',
              plannedEndDate: '$plannedEndDate',
              jobIdentifier: '$identifier',
              costCenter: '$costCenter',
            },
          },
        },
      },
      {
        $project: {
          teamName: 1,
          displayName: 1,
          jobs: {
            $sortArray: {
              input: '$jobs',
              sortBy: {
                ...(jobSortField
                  ? { [jobSortField]: sortDir }
                  : { _id: sortDir }),
              },
            },
          },
        },
      },
      {
        $group: {
          _id: '$_id.teamId',
          name: { $first: '$teamName' },
          employees: {
            $push: {
              _id: '$_id.employeeId',
              displayName: '$displayName',
              jobs: '$jobs',
            },
          },
        },
      },
      {
        $project: {
          name: 1,
          employees: {
            $sortArray: {
              input: '$employees',
              sortBy: { displayName: sortDir },
            },
          },
        },
      },
      { $sort: { name: sortDir } },
    ];

    return this.jobModel.aggregate(aggregate, { collation: { locale: 'en' } });
  }

  async exportListEmployees(payload: ReportEmployeeQueryParamsDto) {
    const { user, month, year } = payload;
    const foundUser = await this.tenantUserModel.findById(user);
    const language = foundUser!.language;

    const fileName = `employees-report-${dayjs()
      .month(month - 1)
      .year(year)
      .format('MM-YYYY')}.csv`;

    const header = [
      { field: 'team', title: 'Team' },
      { field: 'employee', title: 'Employee' },
      { field: 'costCenter', title: 'Identifier' },
      { field: 'location', title: 'Location' },
      { field: 'jobType', title: 'Job Type' },
      { field: 'jobIdentifier', title: 'Job number' },
      { field: 'estimatedHours', title: 'Estimated Hour(s)' },
      { field: 'actualHours', title: 'Actual Hour(s)' },
      { field: 'diffHours', title: 'Difference' },
    ];

    const jobTypeDutch = {
      INSPECTION: 'Inspectie',
      MAINTENANCE: 'Onderhoudstaak',
      CLEANING: 'Schoonmaaktaak',
    };

    const rawData = await this.getListEmployees(payload);

    const data = rawData.flatMap((team: any) =>
      team.employees.flatMap((employee: any) =>
        employee.jobs.map((job: any) => {
          const diffHrsPrefix =
            job.diffHours === 0 ? '' : job.diffHours < 0 ? '-' : '+';
          return {
            team: team.name,
            employee: employee.displayName,
            location: job.location.fullAddress,
            costCenter: job.costCenter?.identifier,
            jobType:
              language == Language.EN
                ? job.jobType
                : jobTypeDutch[job.jobType.toUpperCase()],
            jobIdentifier: job.jobIdentifier,
            estimatedHours: dayjs
              .duration(job.estimatedHours, 'minutes')
              .format('HH:mm'),
            actualHours: dayjs
              .duration(job.actualHours, 'minutes')
              .format('HH:mm'),
            diffHours: dayjs
              .duration(Math.abs(job.diffHours), 'minutes')
              .format(`${diffHrsPrefix}HH:mm`),
          };
        }),
      ),
    );

    return { fileName, header, data };
  }

  //#region Private methods

  private currencyNL(num: any) {
    const number = isNumber(num) ? num : parseFloat(num);
    return number
      .toFixed(2) // always two decimal digits
      .replace('.', ',') // replace decimal point character with ,
      .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');
  }

  //#endregion
}
