export interface Pagination<T> {
  data: T[];

  total: number;

  size: number;

  currentPage: number;

  totalPage: number;
  hasNextPage: boolean | undefined;
  hasPrevPage: boolean | undefined;
}
export class Paginator {
  readonly total!: number;

  readonly size!: number;

  readonly currentPage!: number;

  readonly totalPage!: number;
  readonly hasNextPage: boolean | undefined;
  readonly hasPrevPage: boolean | undefined;
}
