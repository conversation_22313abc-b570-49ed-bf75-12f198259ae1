import { isValidObjectId } from 'mongoose';
import { z } from 'nestjs-zod/z';

import {
  CreateCostLineGeneralDto,
  CreateRentingCostLineGeneralSchema,
  CreateServiceCostLineGeneralSchema,
} from '~/modules/costlinegeneral/dtos/create-costlinegeneral.dto';
import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  AgreementLineType,
} from '~/shared/enums/contract.enum';

export interface CreateAgreementLineDto {
  type: AgreementLineType;
  periodType: AgreementLinePeriodType;
  period?: AgreementLinePeriod;
  position?: number;
  units: string[];
  costLineGenerals: CreateCostLineGeneralDto[];
}

const validatePeriod = (schema: any, ctx: z.RefinementCtx) => {
  if (
    schema.periodType === AgreementLinePeriodType.PERIODIC &&
    !schema.period
  ) {
    ctx.addIssue({
      code: 'custom',
      path: ['period'],
      message: 'Period must be defined for periodic agreement line',
    });

    return;
  }

  if (schema.periodType !== AgreementLinePeriodType.ONE_TIME) {
    return;
  }

  if (schema.period) {
    ctx.addIssue({
      code: 'custom',
      path: ['period'],
      message: 'Period must be null for one-time agreement line',
    });
  }

  if (schema.costLineGenerals.some(({ endDate }) => endDate)) {
    ctx.addIssue({
      code: 'custom',
      path: ['costLineGenerals'],
      message: 'End date must be null for one-time agreement line',
    });
  }
};

export const CreateRentingAgreementLineSchema = z
  .strictObject({
    type: z.nativeEnum(AgreementLineType),
    periodType: z.enum([
      AgreementLinePeriodType.ONE_TIME,
      AgreementLinePeriodType.PERIODIC,
    ]),
    period: z.nativeEnum(AgreementLinePeriod).optional(),
    position: z.number().int().min(0).optional(),
    units: z
      .array(
        z
          .string()
          .trim()
          .refine((item) => isValidObjectId(item)),
      )
      .transform((arr) => Array.from(new Set(arr)))
      .optional()
      .default([]),
    costLineGenerals: z.array(CreateRentingCostLineGeneralSchema).min(1),
  })
  .superRefine(validatePeriod)
  .refine(
    (schema) => {
      if (schema.type === AgreementLineType.ACCOMMODATION) {
        return schema.units.length >= 1;
      }

      return true;
    },
    {
      message: `${AgreementLineType.ACCOMMODATION} type must contain at least 1 unit`,
      path: ['units'],
    },
  );

export const CreateServiceAgreementLineSchema = z
  .strictObject({
    type: z.enum([AgreementLineType.PRODUCT, AgreementLineType.SERVICE]),
    periodType: z.enum([
      AgreementLinePeriodType.ONE_TIME,
      AgreementLinePeriodType.PERIODIC,
    ]),
    period: z.nativeEnum(AgreementLinePeriod).optional(),
    position: z.number().int().min(0).optional(),
    units: z.array(z.string()).max(0).optional().default([]),
    costLineGenerals: z.array(CreateServiceCostLineGeneralSchema).min(1),
  })
  .superRefine(validatePeriod);

export const CreateSupplierAgreementLineSchema = z
  .strictObject({
    type: z.enum([AgreementLineType.SERVICE]),
    periodType: z.enum([AgreementLinePeriodType.PERIODIC]),
    period: z
      .enum([AgreementLinePeriod.MONTHLY, AgreementLinePeriod.WEEKLY])
      .optional(),
    position: z.number().int().min(0).optional(),
    units: z.array(z.string()).max(0).optional().default([]),
    costLineGenerals: z.array(CreateServiceCostLineGeneralSchema).min(1),
  })
  .superRefine(validatePeriod);
