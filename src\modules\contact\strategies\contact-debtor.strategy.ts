import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { isNullOrUndefined } from '@typegoose/typegoose/lib/internal/utils';
import { omit } from 'lodash';
import pick from 'lodash/pick';
import mongoose, { AggregatePaginateModel, Model } from 'mongoose';
import { nanoid } from 'nanoid';

import { AddressModel } from '~/modules/address/address.model';
import { ContractModel } from '~/modules/contract/contract.model';
import { CountryService } from '~/modules/country/country.service';
import { LocationModel } from '~/modules/location/location.model';
import { ContactRole, ContactType } from '~/shared/enums/contact.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { CONTACT_MESSAGE_KEYS } from '~/shared/message-keys/contact.message-key';
import { LOCATION_MESSAGE_KEYS } from '~/shared/message-keys/location.message-key';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery, QueryParams } from '~/utils';

import { AggregateGetListContactWithLocation } from '../contact.helper';
import { ContactModel, ContactOrganizationPersonModel } from '../contact.model';
import { ContactService } from '../contact.service';
import { ContactStrategy } from './contact-strategy.interface';

@Injectable()
export class ContactDebtorStrategy implements ContactStrategy {
  constructor(
    @InjectModel(ContactModel)
    private readonly contactModel: MongooseModel<ContactModel>,
    @InjectModel(ContractModel)
    private readonly contractModel: AggregatePaginateModel<ContractModel>,
    @InjectModel(LocationModel)
    private readonly locationModel: MongooseModel<LocationModel>,
    @InjectModel(AddressModel)
    private readonly addressModel: Model<AddressModel>,
    @InjectModel(ContactOrganizationPersonModel)
    private readonly contactOrganizationPersonModel: Model<ContactOrganizationPersonModel>,

    @Inject(forwardRef(() => ContactService))
    private readonly contactService: ContactService,

    private readonly countryService: CountryService,
  ) {}
  async getValidContact(id: string): Promise<any> {
    return await this.contactModel
      .findOne(
        {
          _id: id,
          contactRole: ContactRole.DEBTOR,
          isActive: true,
          isDeleted: false,
        },
        { _id: 1, displayName: 1, isInternal: 1 },
      )
      .lean();
  }

  async findAll(params: QueryParams): Promise<any> {
    const { query, options } = buildQuery(omit(params, ['location']), [
      'name',
      'displayName',
      'email',
      'phone1',
    ]);

    if (!isNullOrUndefined(params.location)) {
      const location = await this.locationModel
        .findOne({ _id: params.location })
        .select('_id, costCenter')
        .lean();

      if (!location) {
        throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
      }

      return await this.contractModel.aggregatePaginate(
        this.contractModel.aggregate(
          AggregateGetListContactWithLocation(location, params.contactRole),
        ),
        options,
      );
    }

    return this.contactModel.paginate(query, {
      ...options,
      select: 'isActive name displayName email phone1 snf contactType',
    });
  }

  async findOne(id: string, contactType?: ContactType): Promise<any> {
    const baseFields = [
      '_id',
      'isActive',
      'contactType',
      'contactRole',
      'displayName',
      'remark',
      'phone1',
      'phone2',
      'address1',
      'address2',
      'email',
      'warningEmail',
      'paymentTermRentInvoice',
      'paymentTermJobInvoice',
      'invoiceEmail',
      'invoiceReference',
      'identifier',
      'collectiveJobInvoice',
      'collectiveCustomInvoice',
    ];

    const contact = await this.contactModel
      .findOne({
        _id: id,
        contactRole: ContactRole.DEBTOR,
      })
      .populate([
        {
          path: 'address1',
          select: 'country region city street number suffix postalCode',
          populate: [
            {
              path: 'country',
              select: '_id name code',
            },
            {
              path: 'region',
              select: '_id name',
            },
          ],
        },
        {
          path: 'address2',
          select: 'country region city street number suffix postalCode',
          populate: [
            {
              path: 'country',
              select: '_id name code',
            },
            {
              path: 'region',
              select: '_id name',
            },
          ],
        },
        {
          path: 'parentOrganization',
          select: 'name displayName',
        },
      ])

      .lean();

    if (!contact) {
      throw new NotFoundException(CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND);
    }

    contactType = contact.contactType;

    const selectedFields =
      contactType === ContactType.ORGANIZATION
        ? baseFields.concat([
            'name',
            'kvk',
            'snf',
            'vatCode',
            'parentOrganization',
            'website',
          ])
        : baseFields.concat([
            'gender',
            'language',
            'lastName',
            'firstName',
            'organizations',
          ]);

    let persons = [] as any[];
    let organizations = [] as any[];

    if (contactType === ContactType.PERSON) {
      organizations =
        await this.contactService.getPersonsOrOrganizationsContact(
          ContactType.PERSON,
          contact._id.toString(),
        );
      selectedFields.push('organizations');
    } else {
      persons = await this.contactService.getPersonsOrOrganizationsContact(
        ContactType.ORGANIZATION,
        contact._id.toString(),
      );
      selectedFields.push('persons');
    }

    return pick(
      {
        ...contact,
        ...(contactType === ContactType.PERSON && {
          organizations,
        }),
        ...(contactType === ContactType.ORGANIZATION && {
          persons,
        }),
      },
      selectedFields,
    );
  }

  async create(data: any): Promise<any> {
    const { address1, address2, organizations, ...rest } = data;

    await this.countryService.validatePostalCode(address1, 'contact');
    await this.countryService.validatePostalCode(address2, 'contact');

    if (rest.contactType === ContactType.ORGANIZATION) {
      await this.contactService.validateDisplayNameForOrganization(
        rest.name,
        ContactRole.DEBTOR,
      );
    }

    const contact = await this.contactModel.create({
      ...rest,
      contactRole: ContactRole.DEBTOR,
      identifier: nanoid(),
    });

    const referencePayload = {} as {
      address1?: mongoose.Types.ObjectId;
      address2?: mongoose.Types.ObjectId;
    };

    let organizationNames = '';

    if (address1) {
      const result = await this.addressModel.create({
        contact: contact._id,
        ...address1,
      });

      referencePayload.address1 = result._id;
    }

    if (address2) {
      const result = await this.addressModel.create({
        contact: contact._id,
        ...address2,
      });

      referencePayload.address2 = result._id;
    }

    const contactType = contact.contactType;
    if (contactType === ContactType.PERSON && organizations?.length > 0) {
      const contactOrganization =
        await this.contactService.validateOrganization(organizations);

      await Promise.all(
        contactOrganization.map((item) =>
          this.contactOrganizationPersonModel.create({
            person: contact._id,
            organization: item.organization,
            roleFunction: item.roleFunction,
          }),
        ),
      );

      organizationNames = contactOrganization.map((org) => org.name).join(', ');
    }

    await this.contactModel.findByIdAndUpdate(
      contact._id,
      {
        ...referencePayload,
        organizationNames,
        displayName:
          rest.contactType === ContactType.PERSON
            ? `${rest.firstName} ${rest.lastName}`
            : rest.name,
      },
      { new: true },
    );

    return this.findOne(contact._id.toString(), contactType);
  }

  async update(id: string, data: any): Promise<any> {
    const { address1, address2, organizations, parentOrganization, ...rest } =
      data;

    const contact = await this.contactModel.findById(id);

    if (!contact) {
      throw new BadRequestException(
        CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND,
      );
    }

    if (rest.contactType !== contact.contactType) {
      throw new BadRequestException(
        CONTACT_MESSAGE_KEYS.UPDATE_CONTACT_TYPE_NOT_ALLOWED,
      );
    }

    await this.countryService.validatePostalCode(address1, 'contact');
    await this.countryService.validatePostalCode(address2, 'contact');

    if (contact.contactType === ContactType.ORGANIZATION) {
      await this.contactService.validateDisplayNameForOrganization(
        rest.name,
        ContactRole.DEBTOR,
        contact._id,
      );
    }

    // EEAC-4090: Sync Debtor to HomEE, won't update displayName, website, email, phone1, phone2 & address1 (except suffix)
    const updatedContact = await this.contactModel.findByIdAndUpdate(
      contact._id,
      {
        ...omit(rest, ['displayName', 'name', 'email', 'phone1', 'isActive']),
        contactRole: ContactRole.DEBTOR,
      },
      { new: true },
    );

    if (!updatedContact) {
      throw new BadRequestException(
        CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND,
      );
    }

    const contactType = updatedContact.contactType;

    const referencePayload = {} as {
      address1?: mongoose.Types.ObjectId;
      address2?: mongoose.Types.ObjectId | null;
    };

    let organizationNames = '';

    // EEAC-4090: Sync Debtor to HomEE, won't update address1 (except suffix)
    if (address1) {
      const result = await this.addressModel.findOneAndUpdate(
        { _id: updatedContact.address1 },
        {
          suffix: address1.suffix,
          contact: updatedContact._id,
        },
        {
          new: true,
        },
      );

      result && (referencePayload.address1 = result._id);
    }

    await this.addressModel.deleteOne({ _id: updatedContact.address2 });
    if (address2) {
      const result = await this.addressModel.create({
        contact: contact._id,
        ...address2,
      });

      referencePayload.address2 = result._id;
    } else {
      referencePayload.address2 = null;
    }

    if (contactType === ContactType.PERSON && organizations?.length > 0) {
      const contactOrganization =
        await this.contactService.validateOrganization(organizations);

      await this.contactOrganizationPersonModel.deleteMany({ person: id });

      await Promise.all(
        contactOrganization.map((item) =>
          this.contactOrganizationPersonModel.create({
            person: id,
            organization: item.organization,
            roleFunction: item.roleFunction,
          }),
        ),
      );

      organizationNames = contactOrganization.map((org) => org.name).join(', ');
    }

    // EEAC-4090: Sync Debtor to HomEE, won't update displayName, website, email, phone1, phone2 & address1 (except suffix)
    await this.contactModel.findByIdAndUpdate(
      id,
      {
        ...referencePayload,
        parentOrganization,
        organizationNames,
      },
      { new: true },
    );

    return this.findOne(id, contactType);
  }
}
