import {
  BadRequestException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import dayjs from 'dayjs';
import mongoose, { FlattenMaps, Model, Types } from 'mongoose';

import { AddressModel } from '~/modules/address/address.model';
import { CountryModel } from '~/modules/country/country.model';
import { SyncHistoryService } from '~/modules/sync-history/sync-history.service';
import { MyLogger } from '~/processors/logger/logger.service';
import { ThirdPartyConnectorContext } from '~/processors/third-party-connector/strategies/third-party-connector.context';
import { ContactRole, ContactType } from '~/shared/enums/contact.enum';
import { SyncHistoryType } from '~/shared/enums/sync-history.enum';
import { ThirdPartyTypeEnum } from '~/shared/enums/third-party-type.enum';
import { CONTACT_MESSAGE_KEYS } from '~/shared/message-keys/contact.message-key';
import { InjectModel } from '~/transformers/model.transformer';
import { parseObjectId } from '~/utils';
import { maskEmail } from '~/utils/data-masking.util';

import { RegionModel } from '../region/region.model';
import { ContactModel, ContactOrganizationPersonModel } from './contact.model';
import {
  AddressDto,
  GetContactDetailDto,
  GetContactDto,
} from './dtos/contact.dto';
import { OrganizationPersonDto } from './dtos/person-contact.dto';
import { ContactContext } from './strategies/contact-context';

@Injectable()
export class ContactService {
  constructor(
    @InjectModel(ContactModel)
    private readonly contactModel: Model<ContactModel>,
    @InjectModel(RegionModel)
    private readonly regionModel: Model<RegionModel>,
    @InjectModel(ContactOrganizationPersonModel)
    private readonly contactOrganizationPersonModel: Model<ContactOrganizationPersonModel>,
    @InjectModel(CountryModel)
    private readonly countryModel: Model<CountryModel>,
    @InjectModel(AddressModel)
    private readonly addressModel: Model<AddressModel>,

    private readonly contactContext: ContactContext,

    private readonly thirdPartyConnectorContext: ThirdPartyConnectorContext,

    private readonly syncHistoryService: SyncHistoryService,

    private readonly configService: ConfigService,

    private readonly logger: MyLogger,
  ) {}

  findAll(payload: GetContactDto) {
    const { contactRole, ...rest } = payload;
    this.contactContext.setStrategy(contactRole);
    return this.contactContext.findAll({
      ...rest,
      contactRole,
    });
  }

  findOne(payload: GetContactDetailDto) {
    const { contactRole, id } = payload;
    this.contactContext.setStrategy(contactRole);
    return this.contactContext.findOne(id);
  }

  async create(payload: any) {
    const { contactRole, ...body } = payload;
    body.address1 && (await this.validationAddressRegion(body.address1));
    body.address2 && (await this.validationAddressRegion(body.address2));

    this.contactContext.setStrategy(contactRole);
    return this.contactContext.create(body);
  }

  async update(payload: any) {
    const { contactRole, id, ...body } = payload;
    body.address1 && (await this.validationAddressRegion(body.address1));
    body.address2 && (await this.validationAddressRegion(body.address2));

    this.contactContext.setStrategy(contactRole);
    return this.contactContext.update(id, body);
  }

  async getPersonsOrOrganizationsContact(
    contactType: ContactType,
    contactId: string,
  ) {
    let baseAggregates = [
      {
        $match: {
          [contactType]: parseObjectId(contactId),
        },
      },
    ] as any[];

    if (contactType === ContactType.ORGANIZATION) {
      baseAggregates = [
        ...baseAggregates,
        {
          $lookup: {
            from: 'contacts',
            localField: 'person',
            foreignField: '_id',
            as: 'person',
          },
        },
        {
          $unwind: {
            path: '$person',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            _id: 0,
            contactId: '$person._id',
            displayName: '$person.displayName',
            contactRole: '$person.contactRole',
            roleFunction: 1,
          },
        },
      ];
    } else {
      baseAggregates = [
        ...baseAggregates,
        {
          $lookup: {
            from: 'contacts',
            localField: 'organization',
            foreignField: '_id',
            as: 'organization',
          },
        },
        {
          $unwind: {
            path: '$organization',
            preserveNullAndEmptyArrays: true,
          },
        },
        {
          $project: {
            _id: 0,
            contactId: '$organization._id',
            displayName: '$organization.displayName',
            contactRole: '$organization.contactRole',
            roleFunction: 1,
            createdAt: 1,
          },
        },
        {
          $sort: { createdAt: 1 },
        },
      ];
    }

    return this.contactOrganizationPersonModel.aggregate(baseAggregates);
  }

  async validationAddressRegion(address: AddressDto) {
    const region = await this.regionModel.findById(address.region).lean();

    if (!region) {
      throw new BadRequestException(CONTACT_MESSAGE_KEYS.INVALID_REGION);
    }

    if (region.country.toString() !== address.country) {
      throw new BadRequestException(CONTACT_MESSAGE_KEYS.INVALID_REGION);
    }
  }

  async validateDisplayNameForOrganization(
    displayName: any,
    contactRole: ContactRole,
    contactId?: any,
  ) {
    const exitedDisplayName = await this.contactModel.findOne({
      _id: { $ne: contactId },
      contactRole,
      displayName: displayName,
      contactType: ContactType.ORGANIZATION,
    });

    if (exitedDisplayName) {
      throw new BadRequestException(
        CONTACT_MESSAGE_KEYS.EXITED_ORGANIZATION_NAME,
      );
    }
  }

  async validateOrganization(organizations: OrganizationPersonDto[]) {
    return await Promise.all(
      organizations.map(async (item) => {
        const organizationContact = await this.contactModel
          .findById(item.contactId)
          .lean();

        if (!organizationContact) {
          throw new NotFoundException(CONTACT_MESSAGE_KEYS.ORG_NOT_FOUND);
        }

        if (organizationContact.contactType !== ContactType.ORGANIZATION) {
          throw new BadRequestException(CONTACT_MESSAGE_KEYS.INVALID_ORG_TYPE);
        }

        return {
          organization: organizationContact._id,
          name: organizationContact.displayName || organizationContact.name,
          roleFunction: item.roleFunction,
        };
      }),
    );
  }

  async getValidContact(reportContact, contactRole) {
    this.contactContext.setStrategy(contactRole);
    return this.contactContext.getValidContact(reportContact);
  }

  async syncDebtorsFrom3rdParty(payload: {
    [key: string]: any;
    type: ThirdPartyTypeEnum;
  }) {
    if (
      await this.syncHistoryService.isPending(SyncHistoryType.DEBTOR_CONTACT)
    ) {
      throw new BadRequestException(
        'Sync is already in progress by another user',
      );
    }

    this.contactContext.setStrategy(ContactRole.DEBTOR);
    const maskingData = this.configService.get<boolean>('app.maskingData')!;
    const internalContactIdentifier = this.configService.get<string>(
      'app.internalContactIdentifier',
    );

    const findOneOrCreateCountry = async (
      code: string,
      countries: (FlattenMaps<CountryModel> & { _id: Types.ObjectId })[],
    ) => {
      const country = countries.find((c) => c.code === code);

      if (!country) {
        const newCountry = await this.countryModel.create({
          code,
          name: code,
        });
        countries.push(newCountry);
        return newCountry;
      }

      return country;
    };

    const findOneOrCreateRegion = async (
      name: string,
      country: Types.ObjectId,
      regions: (FlattenMaps<RegionModel> & { _id: Types.ObjectId })[],
    ) => {
      const region = regions.find((c) => c.name === name);

      if (!region) {
        const newRegion = await this.regionModel.create({ name, country });
        regions.push(newRegion as any);
        return newRegion;
      }

      return region;
    };

    const session = await mongoose.startSession();

    const syncHistory = await this.syncHistoryService.createNewPending(
      SyncHistoryType.DEBTOR_CONTACT,
      payload.actionType,
    );

    try {
      await session.withTransaction(async () => {
        const syncedAt = dayjs().utc().toDate();
        const countries = await this.countryModel.find().lean();
        const regions = await this.regionModel.find().lean();

        let page = 1;
        let thirdPartyDebtors =
          await this.thirdPartyConnectorContext.getDebtors(payload);

        while (thirdPartyDebtors.length) {
          const updateCommandPromises: Promise<any>[] = [];

          for (const thirdPartyDebtor of thirdPartyDebtors) {
            const migrate = async () => {
              const country = await findOneOrCreateCountry(
                thirdPartyDebtor.country.code,
                countries,
              );
              const region = !thirdPartyDebtor.region
                ? null
                : await findOneOrCreateRegion(
                    thirdPartyDebtor.region.name,
                    country._id,
                    regions as any,
                  );

              const existingDebtor = await this.contactModel.findOne({
                identifier: thirdPartyDebtor.identifier,
              });

              const debtor: any = {
                contactRole: ContactRole.DEBTOR,
                contactType: ContactType.ORGANIZATION,
                name: thirdPartyDebtor.displayName,
                displayName: thirdPartyDebtor.displayName,
                email: maskingData
                  ? maskEmail(thirdPartyDebtor.email)
                  : thirdPartyDebtor.email,
                isActive: true,
                isInternal:
                  internalContactIdentifier === thirdPartyDebtor.identifier,
                phone1: thirdPartyDebtor.phone1,
                syncedAt,
              };

              const address = {
                country: country._id,
                region: region?._id,
                city: thirdPartyDebtor.address1.city,
                number: `${thirdPartyDebtor.address1.houseNumber}`,
                postalCode: thirdPartyDebtor.address1.postCode,
                street: thirdPartyDebtor.address1.street,
              };

              if (existingDebtor) {
                await this.addressModel.updateOne(
                  { _id: existingDebtor.address1._id },
                  { $set: address },
                );
              } else {
                const newAddress = await this.addressModel.create(address);
                debtor.address1 = newAddress._id;
              }

              return {
                updateOne: {
                  filter: { identifier: thirdPartyDebtor.identifier },
                  update: { $set: debtor },
                  upsert: true,
                },
              };
            };

            updateCommandPromises.push(migrate());
          }

          const updateCommands = await Promise.all(updateCommandPromises);
          const upsertResult =
            await this.contactModel.bulkWrite(updateCommands);

          this.logger.log(
            `3rd party syncing - new & updated debtors: ${JSON.stringify(upsertResult)}`,
          );

          if (thirdPartyDebtors.length < 50) {
            break;
          }

          page += 1;
          thirdPartyDebtors = await this.thirdPartyConnectorContext.getDebtors({
            ...payload,
            page,
          });
        }

        const disabledResult = await this.contactModel.updateMany(
          {
            contactRole: ContactRole.DEBTOR,
            isActive: true,
            $or: [
              { syncedAt: { $lt: syncedAt } },
              { syncedAt: { $exists: false } },
            ],
          },
          { $set: { isActive: false } },
        );
        this.logger.log(
          `3rd party syncing - disabled debtors: ${JSON.stringify(disabledResult)}`,
        );
      });
      await this.syncHistoryService.updateToSuccess(syncHistory._id);
    } catch (ex: any) {
      await this.syncHistoryService.updateToFailed(
        syncHistory._id!,
        ex.message,
      );
      throw ex;
    } finally {
      await session.endSession();
    }
  }
}
