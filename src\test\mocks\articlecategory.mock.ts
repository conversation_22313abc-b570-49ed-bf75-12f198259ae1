import { ObjectId } from 'mongodb';
import mongoose from 'mongoose';
import { nanoid } from 'nanoid';

const articleCategoryModel = mongoose.connection.collection('articlecategory');

export const mockArticleCategoryData = {
  _id: new ObjectId(),
  identifier: nanoid(1),
  name: 'Inventaris',
};

export async function initMockArticleCategory(doc?: any) {
  const { _id, ...rest } = { ...mockArticleCategoryData, ...doc };
  await articleCategoryModel.replaceOne({ _id }, rest, { upsert: true });
}
