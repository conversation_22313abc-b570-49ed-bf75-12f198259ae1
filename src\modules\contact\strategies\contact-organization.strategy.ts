import { Injectable } from '@nestjs/common';

import { ContactType } from '~/shared/enums/contact.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { ContactModel } from '../contact.model';
import { OrganizationContactQueryParamDto } from '../dtos/organization-contact.dto';
import { ContactStrategy } from './contact-strategy.interface';

@Injectable()
export class ContactOrganizationStrategy implements ContactStrategy {
  constructor(
    @InjectModel(ContactModel)
    private readonly contactModel: MongooseModel<ContactModel>,
  ) {}

  getValidContact(_id: string): Promise<any> {
    throw new Error('Method not implemented.');
  }

  findAll(params: OrganizationContactQueryParamDto): Promise<any> {
    const { contactRoles, ...restData } = params;
    const newParams = {
      ...restData,
      contactType: ContactType.ORGANIZATION,
    };

    const { query, options } = buildQuery(newParams, [
      'name',
      'displayName',
      'email',
      'phone1',
    ]);

    return this.contactModel.paginate(
      {
        ...query,
        contactRole: { $in: contactRoles },
      },
      {
        ...options,
        select: 'isActive name displayName contactRole',
      },
    );
  }
  async findOne(id: string): Promise<any> {
    return id;
  }
  async create(data: any): Promise<any> {
    return data;
  }
  async update(id: string, data: any): Promise<any> {
    return {
      id,
      ...data,
    };
  }
}
