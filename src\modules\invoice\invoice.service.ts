import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { mongoose } from '@typegoose/typegoose';
import dayjs from 'dayjs';
import _ from 'lodash';
import { Types } from 'mongoose';
import pLimit from 'p-limit';

import { ThirdPartyConnectorContext } from '~/processors/third-party-connector/strategies/third-party-connector.context';
import {
  AgreementLinePeriodType,
  ContractType,
  CostLineStatus,
} from '~/shared/enums/contract.enum';
import { IdentifierType } from '~/shared/enums/identifier.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { INVOICE_MESSAGE_KEYS } from '~/shared/message-keys/invoice.message-key';
import { InjectModel } from '~/transformers/model.transformer';

import { ContractModel } from '../contract/contract.model';
import { CostLineModel } from '../costline/costline.model';
import { IdentifierService } from '../identifier/identifier.service';
import { JobModel } from '../job/job.model';
import {
  ApprovedInvoicesQueryParamsDto,
  ApprovedInvoicesQueryType,
  InvoiceApproveBodyDto,
} from './dtos/invoice-approve.dto';
import {
  ContractInvoiceReviewQueryParamsDto,
  JobInvoiceReviewQueryParamsDto,
} from './dtos/invoice-review.dto';
import {
  addFieldsIsoGroupingPipeline,
  AddFieldVatPipelineStage,
  buildBasicSortStage,
  buildLocationSortStages,
  getPeriodGroupConditionPipeline,
  LookUpCostCenterPipelineStage,
  LookUpLocationPipelineStage,
} from './invoice.helper';
import { InvoiceModel } from './invoice.model';

@Injectable()
export class InvoiceService {
  private readonly logger = new Logger(InvoiceService.name);

  constructor(
    @InjectModel(ContractModel)
    private readonly contractModel: MongooseModel<ContractModel>,
    @InjectModel(CostLineModel)
    private readonly costLineModel: MongooseModel<CostLineModel>,
    @InjectModel(InvoiceModel)
    private readonly invoiceModel: MongooseModel<InvoiceModel>,
    @InjectModel(JobModel)
    private readonly jobModel: MongooseModel<JobModel>,

    private readonly indentifierService: IdentifierService,

    private readonly thirdPartyContext: ThirdPartyConnectorContext,
  ) {}

  async findInvoicesReviewTypeContract(
    params: ContractInvoiceReviewQueryParamsDto,
  ) {
    const locationQuery = params.locations &&
      params.locations.length && {
        $in: Array.isArray(params.locations)
          ? params.locations.map((location) => new Types.ObjectId(location))
          : [new Types.ObjectId(params.locations)],
      };
    const contactQuery = params.contact && new Types.ObjectId(params.contact);
    const startDateQuery = params.startDate && {
      $gte: dayjs(params.startDate).utc().startOf('day').toDate(),
    };
    const endDateQuery = params.endDate && {
      $lte: dayjs(params.endDate).utc().endOf('day').toDate(),
    };

    let aggregation = this.contractModel
      .aggregate()
      .match({
        ...(contactQuery && { contact: new Types.ObjectId(params.contact) }),
        ...(locationQuery && {
          $or: [{ location: locationQuery }, { costCenter: locationQuery }],
        }),
        type: {
          $in: [ContractType.RENTING, ContractType.SERVICE],
        },
        // isActive: true,
        // isDeleted: false,
      })
      .lookup({
        from: 'contacts',
        localField: 'contact',
        foreignField: '_id',
        as: 'contact',
        pipeline: [
          {
            $project: {
              displayName: 1,
            },
          },
        ],
      })
      .append(LookUpLocationPipelineStage)
      .append(LookUpCostCenterPipelineStage)
      .addFields({
        location: {
          $arrayElemAt: ['$location', 0],
        },
        costCenter: {
          $arrayElemAt: ['$costCenter', 0],
        },
      })
      .project({
        contract: '$_id',
        _id: 0,
        agreementLines: 1,
        contact: {
          $first: '$contact',
        },
        location: 1,
        costCenter: 1,
        type: 1,
      })
      .unwind({
        path: '$agreementLines',
        preserveNullAndEmptyArrays: false,
      })
      .lookup({
        from: 'costlines',
        localField: 'agreementLines',
        foreignField: 'agreementLine',
        as: 'costLine',
        let: {
          countryCode: {
            $ifNull: ['$location.countryCode', '$costCenter.countryCode'],
          },
        },
        pipeline: [
          {
            $match: {
              status: CostLineStatus.OPEN,
              isDeleted: false,
              periodType: params.periodType,
              ...(params.periodType === AgreementLinePeriodType.PERIODIC && {
                period: params.period,
              }),
            },
          },
          {
            $project: {
              location: 0,
              costCenter: 0,
            },
          },
          {
            $lookup: {
              from: 'costtypes',
              localField: 'costType',
              foreignField: '_id',
              as: 'costType',
              pipeline: [
                {
                  $project: {
                    itemCode: 1,
                    name: 1,
                  },
                },
              ],
            },
          },
          {
            $addFields: {
              costType: {
                $arrayElemAt: ['$costType', 0],
              },
            },
          },
          {
            $addFields: {
              itemCodeSuffix: {
                $substr: ['$costType.itemCode', 4, 1],
              },
              countryCode: '$$countryCode',
            },
          },
          {
            $unset: ['contact'],
          },
          AddFieldVatPipelineStage,
          ...addFieldsIsoGroupingPipeline(params.period),
        ],
      })
      .unwind({
        path: '$costLine',
        preserveNullAndEmptyArrays: false,
      })
      .replaceRoot({
        $mergeObjects: ['$$ROOT', '$costLine'],
      })
      .project({
        costLine: 0,
        agreementLines: 0,
      })
      .group({
        _id: {
          location: { $ifNull: ['$location._id', '$costCenter._id'] },
          contract: '$contract',
          // agreementLine: '$agreementLine',
          ...(params.periodType === AgreementLinePeriodType.PERIODIC &&
            getPeriodGroupConditionPipeline(params.period!)),
          isCredit: '$isCredit',
        },
        costLines: { $push: '$$ROOT' },
        startDate: { $min: '$startDate' },
        endDate: { $max: '$endDate' },
        net: { $sum: '$totalPrice' },
        vat: { $sum: '$vat' },
        contact: { $first: '$contact' },
        location: { $first: '$location' },
        costCenter: { $first: '$costCenter' },
        updatedAt: { $first: '$updatedAt' },
        type: { $first: '$type' },
      });

    if (startDateQuery) {
      aggregation = aggregation.match({
        startDate: startDateQuery,
      });
    }

    if (endDateQuery) {
      aggregation = aggregation.match({
        endDate: endDateQuery,
      });
    }

    aggregation = aggregation
      .addFields({
        gross: {
          $add: ['$net', '$vat'],
        },
      })
      .project({
        _id: 1,
        startDate: 1,
        endDate: 1,
        net: 1,
        vat: 1,
        gross: 1,
        contact: 1,
        location: 1,
        costCenter: 1,
        type: 1,
        updatedAt: 1,
        invoiceDetails: {
          $map: {
            input: '$costLines',
            as: 'costLine',
            in: {
              _id: '$$costLine._id',
              costType: {
                itemCode: '$$costLine.costType.itemCode',
                name: '$$costLine.costType.name',
              },
              description: '$$costLine.description',
              quantity: '$$costLine.quantity',
              price: '$$costLine.price',
              totalPrice: '$$costLine.totalPrice',
              vat: '$$costLine.vat',
              status: '$$costLine.status',
              startDate: '$$costLine.startDate',
              endDate: '$$costLine.endDate',
            },
          },
        },
        currentIdentifier: 'To be implemented...',
      });

    if (params.sortBy === 'location') {
      aggregation = buildLocationSortStages(aggregation, params.sortDir);
    } else {
      aggregation = buildBasicSortStage(
        aggregation,
        params.sortBy!,
        params.sortDir,
      );
    }

    const lastedInvoice = (
      await this.invoiceModel.aggregate([
        { $match: { isDeleted: false } },
        { $project: { identifier: 1, _id: 0 } },
        { $sort: { identifier: -1 } },
        { $limit: 1 },
      ])
    )[0];

    aggregation.addFields({
      currentIdentifier:
        lastedInvoice?.identifier ??
        (await this.indentifierService.generateDummyIdentifierForInvoice()),
    });

    return await aggregation
      .option({ allowDiskUse: true })
      .collation({ locale: 'en' })
      .exec();
  }

  async findInvoicesReviewTypeJob(params: JobInvoiceReviewQueryParamsDto) {
    const locationQuery = params.locations?.length && {
      $in: Array.isArray(params.locations)
        ? params.locations.map((location) => new Types.ObjectId(location))
        : [new Types.ObjectId(params.locations)],
    };

    const costCenterQuery = params.costCenters?.length && {
      $in: Array.isArray(params.costCenters)
        ? params.costCenters.map((costCenter) => new Types.ObjectId(costCenter))
        : [new Types.ObjectId(params.costCenters)],
    };
    const contactQuery = params.contact && new Types.ObjectId(params.contact);
    const endDateQuery = params.endDate && {
      $lte: dayjs(params.endDate).utc().endOf('day').toDate(),
    };

    let aggregation = this.costLineModel.aggregate(
      [
        {
          $match: {
            $and: [
              ...(contactQuery
                ? [{ contact: new Types.ObjectId(params.contact) }]
                : []),
              { contact: { $exists: true } },
            ],
            type: {
              $in: [ContractType.JOB, ContractType.CUSTOM],
            },
            isDeleted: false,
            invoice: {
              $exists: false,
            },
            ...(endDateQuery && { createdAt: endDateQuery }),
          },
        },
        {
          $lookup: {
            from: 'contacts',
            localField: 'contact',
            foreignField: '_id',
            as: 'contact',
            pipeline: [
              {
                $project: {
                  displayName: 1,
                  collectiveJobInvoice: 1,
                  collectiveCustomInvoice: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            contact: {
              $arrayElemAt: ['$contact', 0],
            },
          },
        },
        {
          $lookup: {
            from: 'locations',
            localField: 'location',
            foreignField: '_id',
            as: 'location',
            pipeline: [
              {
                $lookup: {
                  from: 'addresses',
                  localField: 'address',
                  foreignField: '_id',
                  as: 'address',
                  pipeline: [
                    {
                      $project: {
                        _id: 0,
                        country: 1,
                      },
                    },
                    {
                      $lookup: {
                        from: 'countries',
                        localField: 'country',
                        foreignField: '_id',
                        as: 'country',
                      },
                    },
                    {
                      $set: {
                        country: {
                          $arrayElemAt: ['$country', 0],
                        },
                      },
                    },
                  ],
                },
              },
              {
                $set: {
                  address: {
                    $arrayElemAt: ['$address', 0],
                  },
                },
              },
              {
                $addFields: {
                  country: '$address.country',
                },
              },
              {
                $project: {
                  fullAddress: 1,
                  bvCompany: 1,
                  country: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            location: {
              $arrayElemAt: ['$location', 0],
            },
          },
        },
        {
          $match: {
            ...(locationQuery && { 'location._id': locationQuery }),
          },
        },
        {
          $lookup: {
            from: 'costcenters',
            localField: 'costCenter',
            foreignField: '_id',
            as: 'costCenter',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  name: 1,
                  identifier: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            costCenter: {
              $arrayElemAt: ['$costCenter', 0],
            },
          },
        },
        {
          $match: {
            ...(costCenterQuery && { 'costCenter._id': costCenterQuery }),
          },
        },
        {
          $lookup: {
            from: 'countries',
            localField: 'country',
            foreignField: '_id',
            as: 'country',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  code: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            country: {
              $arrayElemAt: ['$country', 0],
            },
          },
        },
        {
          $project: {
            _id: 1,
            description: 1,
            totalPrice: 1,
            price: 1,
            quantity: 1,
            endDate: 1,
            type: 1,
            costType: 1,
            contact: 1,
            location: 1,
            costCenter: 1,
            country: 1,
            bvCompany: 1,
            job: 1,
            isCredit: 1,
          },
        },
        {
          $lookup: {
            from: 'costtypes',
            localField: 'costType',
            foreignField: '_id',
            as: 'costType',
            pipeline: [
              {
                $project: {
                  itemCode: 1,
                  name: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            costType: {
              $arrayElemAt: ['$costType', 0],
            },
          },
        },
        {
          $addFields: {
            itemCodeSuffix: {
              $substr: ['$costType.itemCode', 4, 1],
            },
            countryCode: {
              $cond: [
                {
                  $ifNull: ['$country', false],
                },
                '$country.code',
                '$location.country.code',
              ],
            },
          },
        },
        {
          $lookup: {
            from: 'jobs',
            localField: 'job',
            foreignField: '_id',
            as: 'job',
            pipeline: [
              {
                $project: {
                  identifier: 1,
                },
              },
            ],
          },
        },
        {
          $unwind: {
            path: '$job',
            preserveNullAndEmptyArrays: true,
          },
        },
        AddFieldVatPipelineStage,
        {
          $group: {
            _id: {
              contactId: '$contact._id',
              location: {
                $cond: [
                  { $eq: ['$type', 'custom'] },
                  {
                    $cond: [
                      {
                        $eq: ['$contact.collectiveCustomInvoice', true],
                      },
                      null,
                      '$location._id',
                    ],
                  },
                  {
                    $cond: [
                      { $eq: ['$contact.collectiveJobInvoice', true] },
                      null,
                      '$location._id',
                    ],
                  },
                ],
              },
              costCenter: {
                $cond: [
                  { $eq: ['$type', 'custom'] },
                  {
                    $cond: [
                      {
                        $eq: ['$contact.collectiveCustomInvoice', true],
                      },
                      null,
                      '$costCenter._id',
                    ],
                  },
                  null,
                ],
              },
              bvCompany: {
                $cond: [
                  { $eq: ['$type', 'custom'] },
                  {
                    $cond: [
                      {
                        $or: [
                          { $eq: ['$bvCompany', null] },
                          { $not: ['$bvCompany'] },
                        ],
                      },
                      '$location.bvCompany',
                      '$bvCompany',
                    ],
                  },
                  '$location.bvCompany',
                ],
              },
              country: {
                $cond: [
                  { $eq: ['$type', 'custom'] },
                  {
                    $cond: [
                      {
                        $or: [
                          { $eq: ['$country', null] },
                          { $not: ['$country'] },
                        ],
                      },
                      '$location.country._id',
                      '$country._id',
                    ],
                  },
                  '$location.country._id',
                ],
              },
              type: '$type',
              isCredit: '$isCredit',
              isUseCostCenter: {
                $cond: [{ $ifNull: ['$costCenter._id', false] }, true, false],
              },
            },
            locations: { $push: '$location.fullAddress' },
            costCenter: {
              $push: {
                $concat: ['$costCenter.identifier', '-', '$costCenter.name'],
              },
            },
            net: { $sum: '$totalPrice' },
            vat: { $sum: '$vat' },
            contact: { $first: '$contact' },
            type: { $first: '$type' },
            costLines: {
              $push: {
                _id: '$_id',
                description: {
                  $cond: {
                    if: {
                      $ifNull: ['$job', false],
                    },
                    then: {
                      $concat: [
                        '(ID: ',
                        '$job.identifier',
                        ') ',
                        '$description',
                      ],
                    },
                    else: '$description',
                  },
                },
                price: '$price',
                quantity: '$quantity',
                endDate: '$endDate',
                type: '$type',
                contact: '$contact',
                location: '$location',
                itemCodeSuffix: '$itemCodeSuffix',
                countryCode: '$countryCode',
                vat: '$vat',
                costType: '$costType',
                totalPrice: '$totalPrice',
              },
            },
          },
        },
        {
          $addFields: {
            gross: {
              $add: ['$net', '$vat'],
            },
          },
        },
        {
          $project: {
            _id: 0,
            contact: 1,
            gross: 1,
            net: 1,
            vat: 1,
            type: 1,
            locations: {
              $cond: [
                {
                  $and: [
                    { $isArray: '$locations' },
                    { $ne: ['$locations', null] },
                    { $gt: [{ $size: '$locations' }, 0] },
                  ],
                },
                {
                  $reduce: {
                    input: { $setUnion: ['$locations', []] },
                    initialValue: '',
                    in: {
                      $concat: [
                        '$$value',
                        {
                          $cond: {
                            if: { $eq: ['$$value', ''] },
                            then: '',
                            else: ', ',
                          },
                        },
                        '$$this',
                      ],
                    },
                  },
                },
                {
                  $reduce: {
                    input: { $setUnion: ['$costCenter', []] },
                    initialValue: '',
                    in: {
                      $concat: [
                        '$$value',
                        {
                          $cond: {
                            if: { $eq: ['$$value', ''] },
                            then: '',
                            else: ', ',
                          },
                        },
                        '$$this',
                      ],
                    },
                  },
                },
              ],
            },
            invoiceDetails: {
              $map: {
                input: '$costLines',
                as: 'costLine',
                in: {
                  _id: '$$costLine._id',
                  costType: {
                    itemCode: '$$costLine.costType.itemCode',
                    name: '$$costLine.costType.name',
                  },
                  description: '$$costLine.description',
                  quantity: '$$costLine.quantity',
                  price: '$$costLine.price',
                  totalPrice: '$$costLine.totalPrice',
                  vat: '$$costLine.vat',
                  status: '$$costLine.status',
                },
              },
            },
            currentIdentifier: 'To be implemented...',
          },
        },
      ],
      {
        collation: { locale: 'en' },
      },
    );

    if (params.sortBy === 'location') {
      aggregation = aggregation.sort({
        locations: params.sortDir === 'desc' ? -1 : 1,
      });
    }

    const lastedInvoice = (
      await this.invoiceModel.aggregate([
        { $match: { isDeleted: false } },
        { $project: { identifier: 1, _id: 0 } },
        { $sort: { identifier: -1 } },
        { $limit: 1 },
      ])
    )[0];

    aggregation.addFields({
      currentIdentifier:
        lastedInvoice?.identifier ??
        (await this.indentifierService.generateDummyIdentifierForInvoice()),
    });

    return await aggregation.option({ allowDiskUse: true }).exec();
  }

  async approveInvoices(payload: InvoiceApproveBodyDto) {
    const limit = pLimit(1);

    try {
      const invoices = payload.invoices.map((invoice) =>
        limit(async () => {
          const approveCostLines = await this.invoiceModel.find({
            costLines: { $in: invoice.costLines },
          });

          if (approveCostLines.length > 0) {
            throw new BadRequestException(
              INVOICE_MESSAGE_KEYS.COST_LINES_HAVE_APPROVED_IN_ANOTHER_INVOICE,
            );
          }

          const costLines = await this.costLineModel
            .aggregate([
              {
                $match: {
                  _id: {
                    $in: invoice.costLines.map(
                      (v) => new mongoose.Types.ObjectId(v),
                    ),
                  },
                  status: CostLineStatus.OPEN,
                  isDeleted: false,
                },
              },
              {
                $lookup: {
                  from: 'costlines',
                  localField: 'parent',
                  foreignField: '_id',
                  as: 'parent',
                  pipeline: [
                    {
                      $lookup: {
                        from: 'invoices',
                        localField: 'invoice',
                        foreignField: '_id',
                        as: 'invoiceReference',
                        pipeline: [{ $project: { identifier: 1 } }],
                      },
                    },
                    {
                      $set: {
                        invoiceReference: {
                          $arrayElemAt: ['$invoiceReference', 0],
                        },
                      },
                    },
                    {
                      $project: {
                        invoiceReference: 1,
                      },
                    },
                  ],
                },
              },
              {
                $set: {
                  parent: {
                    $arrayElemAt: ['$parent', 0],
                  },
                },
              },
              {
                $addFields: {
                  invoiceReference: {
                    $ifNull: ['$parent.invoiceReference', '$invoice'],
                  },
                },
              },
              {
                $unset: ['parent'],
              },
            ])
            .exec();

          if (
            costLines.length === 0 ||
            costLines.length !== invoice.costLines.length
          ) {
            throw new BadRequestException(
              INVOICE_MESSAGE_KEYS.COST_LINES_NOT_FOUND,
            );
          }

          const types = new Set(costLines.map((costLine) => costLine.type));
          if (types.size > 1) {
            throw new BadRequestException(
              INVOICE_MESSAGE_KEYS.COST_LINES_MUST_HAVE_SAME_TYPE,
            );
          }

          const type = types.values().next().value;

          if (type !== invoice.type) {
            throw new BadRequestException(
              INVOICE_MESSAGE_KEYS.COST_LINES_HAVE_TYPE_DIFFERENT_FROM_INVOICE,
            );
          }

          const identifier = await this.indentifierService.generateIdentifier(
            IdentifierType.INVOICE,
          );

          const periodTypes = new Set(
            costLines.map((costLine) => costLine.periodType),
          );

          let isOneTime = false;
          if (
            periodTypes.size === 1 &&
            periodTypes.values().next().value ===
              AgreementLinePeriodType.ONE_TIME
          ) {
            isOneTime = true;
          }
          const minStartDate = !isOneTime
            ? costLines.reduce(
                (min, costLine) =>
                  costLine.startDate < min ? costLine.startDate : min,
                costLines[0].startDate,
              )
            : undefined;
          const maxEndDate = !isOneTime
            ? costLines.reduce(
                (max, costLine) =>
                  costLine.endDate > max ? costLine.endDate : max,
                costLines[0].endDate,
              )
            : undefined;

          const net = costLines.reduce(
            (acc, costLine) => acc + costLine.totalPrice,
            0,
          );

          const costCenters = Array.from(
            new Set(
              costLines
                .map((costLine) =>
                  costLine.costCenter ? costLine.costCenter.toString() : null,
                )
                .filter((v) => v),
            ),
          );
          const locations = Array.from(
            new Set(
              costLines
                .map((costLine) =>
                  costLine.location ? costLine.location?.toString() : null,
                )
                .filter((v) => v),
            ),
          );

          const newInvoice = await this.invoiceModel.create({
            type: invoice.type,
            identifier,
            startDate: minStartDate,
            endDate: maxEndDate,
            net: net,
            contact: invoice.contact,
            costCenters: costCenters,
            locations: locations,
            costLines: invoice.costLines,
            approvedAt: dayjs().utc().toDate(),
          });

          this.updateReferenceAfterApproveInvoices(newInvoice, costLines).then(
            () => {
              this.logger.log('Reference updated');
            },
          );
          return newInvoice;
        }),
      );
      const result = await Promise.all(invoices);

      const pushInvoicesTo3rdPartAsync = (
        thirdParty: any | null,
        type: string,
      ) => {
        for (const invoice of result) {
          this.thirdPartyContext.pushInvoice({ invoice, type, ...thirdParty });
        }
      };
      // TODO Sync to all third parties?
      pushInvoicesTo3rdPartAsync(payload.thirdParties?.afas ?? {}, 'afas');
      return result;
    } catch (ex) {
      throw ex;
    }
  }

  public async findApprovedInvoices(query: ApprovedInvoicesQueryParamsDto) {
    const {
      type,
      contact,
      location,
      costCenter,
      approvedDateFrom,
      approvedDateTo,
      startDate,
      endDate,
    } = query;

    const approvedAtQuery: any = {};
    const typeQuery =
      type === ApprovedInvoicesQueryType.JOB_AND_CUSTOM
        ? { $in: [ContractType.JOB, ContractType.CUSTOM] }
        : { $eq: type };
    const contactQuery = contact && new Types.ObjectId(contact);
    const locationQuery = location && new Types.ObjectId(location);
    const costCenterQuery = costCenter && new Types.ObjectId(costCenter);
    const startDateQuery = startDate && {
      $gte: dayjs(startDate).utc().startOf('day').toDate(),
    };
    const endDateQuery = endDate && {
      $lte: dayjs(endDate).utc().endOf('day').toDate(),
    };
    if (approvedDateFrom) {
      approvedAtQuery.$gte = dayjs(approvedDateFrom)
        .utc()
        .startOf('day')
        .toDate();
    }
    if (approvedDateTo) {
      approvedAtQuery.$lte = dayjs(approvedDateTo).utc().endOf('day').toDate();
    }

    let sortBy: string;
    if (query.sortBy === 'locations') {
      sortBy = 'locations.0.fullAddress';
    } else if (query.sortBy === 'costCenters') {
      sortBy = 'costCenters.0.name';
    } else if (query.sortBy === 'contact') {
      sortBy = 'contact.displayName';
    } else if (query.sortBy === 'invoiceReference') {
      sortBy = 'invoiceReference.identifier';
    } else {
      sortBy = query.sortBy ?? 'identifier';
    }

    const aggregation = this.invoiceModel.aggregate(
      [
        {
          $match: {
            type: typeQuery,
            ...(contactQuery && { contact: contactQuery }),
            ...(locationQuery && { locations: locationQuery }),
            ...(costCenter && { costCenters: costCenterQuery }),
            ...(!_.isEmpty(approvedAtQuery) && { approvedAt: approvedAtQuery }),
            ...(startDateQuery && { startDate: startDateQuery }),
            ...(endDateQuery && { endDate: endDateQuery }),
          },
        },
        {
          $lookup: {
            from: 'contacts',
            localField: 'contact',
            foreignField: '_id',
            as: 'contact',
            pipeline: [
              {
                $project: {
                  displayName: 1,
                },
              },
            ],
          },
        },
        {
          $addFields: {
            contact: {
              $first: '$contact',
            },
          },
        },
        {
          $lookup: {
            from: 'locations',
            localField: 'locations',
            foreignField: '_id',
            as: 'locations',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  fullAddress: 1,
                },
              },
              {
                $sort: {
                  fullAddress: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'costcenters',
            localField: 'costCenters',
            foreignField: '_id',
            as: 'costCenters',
            pipeline: [
              {
                $project: {
                  _id: 1,
                  name: 1,
                  identifier: 1,
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'invoices',
            localField: 'invoiceReference',
            foreignField: '_id',
            as: 'invoiceReference',
            pipeline: [
              {
                $project: {
                  identifier: 1,
                },
              },
            ],
          },
        },
        {
          $project: {
            identifier: 1,
            startDate: 1,
            endDate: 1,
            net: 1,
            contact: 1,
            locations: 1,
            costCenters: 1,
            approvedAt: 1,
            createdAt: 1,
            updatedAt: 1,
            invoiceReference: {
              $first: '$invoiceReference',
            },
          },
        },
      ],
      {
        collation: { locale: 'en' },
      },
    );

    return await this.invoiceModel.aggregatePaginate(aggregation, {
      page: query.pageIndex,
      limit: query.pageSize,
      sort: {
        [sortBy]: query.sortDir === 'desc' ? -1 : 1,
      },
    });
  }

  public async getApprovedInvoiceDetail(invoiceId: string) {
    const invoice = await this.invoiceModel.aggregate([
      {
        $match: {
          _id: new Types.ObjectId(invoiceId),
        },
      },
      {
        $lookup: {
          from: 'contacts',
          localField: 'contact',
          foreignField: '_id',
          as: 'contact',
          pipeline: [
            {
              $project: {
                displayName: 1,
                identifier: 1,
              },
            },
          ],
        },
      },
      {
        $set: {
          contact: {
            $first: '$contact',
          },
        },
      },
      {
        $lookup: {
          from: 'locations',
          localField: 'locations',
          foreignField: '_id',
          as: 'locations',
          pipeline: [{ $project: { fullAddress: 1 } }],
        },
      },
      {
        $lookup: {
          from: 'costcenters',
          localField: 'costCenters',
          foreignField: '_id',
          as: 'costCenters',
          pipeline: [{ $project: { name: 1, identifier: 1 } }],
        },
      },
      {
        $lookup: {
          from: 'invoices',
          localField: 'invoiceReference',
          foreignField: '_id',
          as: 'invoiceReference',
          pipeline: [{ $project: { identifier: 1 } }],
        },
      },
      {
        $lookup: {
          from: 'costlines',
          localField: 'costLines',
          foreignField: '_id',
          as: 'costLines',
          let: { type: '$type' },
          pipeline: [
            {
              $lookup: {
                from: 'costtypes',
                localField: 'costType',
                foreignField: '_id',
                as: 'costType',
                pipeline: [
                  {
                    $project: {
                      itemCode: 1,
                      name: 1,
                    },
                  },
                ],
              },
            },
            {
              $set: {
                costType: {
                  $arrayElemAt: ['$costType', 0],
                },
              },
            },
            {
              $lookup: {
                from: 'jobs',
                localField: 'job',
                foreignField: '_id',
                as: 'job',
              },
            },
            {
              $addFields: {
                job: {
                  $first: '$job',
                },
              },
            },
            {
              $project: {
                _id: 1,
                costType: {
                  itemCode: '$costType.itemCode',
                  name: '$costType.name',
                },
                description: {
                  $cond: {
                    if: {
                      $ifNull: ['$job', false],
                    },
                    then: {
                      $concat: [
                        '(ID: ',
                        '$job.identifier',
                        ') ',
                        '$description',
                      ],
                    },
                    else: '$description',
                  },
                },
                quantity: 1,
                price: 1,
                totalPrice: 1,
                vat: 1,
                status: 1,
                startDate: 1,
                endDate: 1,
                isCredit: 1,
              },
            },
          ],
        },
      },
      {
        $project: {
          identifier: 1,
          type: 1,
          startDate: 1,
          endDate: 1,
          net: 1,
          contact: 1,
          locations: 1,
          costCenters: 1,
          approvedAt: 1,
          invoiceReference: {
            $first: '$invoiceReference',
          },
          costLines: 1,
        },
      },
    ]);

    return invoice[0] ?? null;
  }

  //#region  Private Methods
  private async updateReferenceAfterApproveInvoices(invoice, costLines) {
    const invoiceReferences = costLines
      .map((costLine: any) => costLine.invoiceReference)
      .filter((v) => v);

    await this.costLineModel.updateMany(
      {
        _id: { $in: invoice.costLines },
      },
      {
        invoice: invoice._id,
        status: CostLineStatus.CLOSED,
        approvedAt: dayjs().utc().toDate(),
      },
    );
    if (invoice.type === ContractType.JOB) {
      const jobs = new Set(costLines.map((costLine) => costLine.job));
      const jobIds = Array.from(jobs);
      const closeJobPromises = jobIds.map(async (jobId) => {
        const openCostLineOfJobs = await this.costLineModel
          .find({
            job: jobId,
            status: CostLineStatus.OPEN,
            isDeleted: false,
          })
          .lean();
        if (openCostLineOfJobs.length === 0) {
          await this.jobModel.updateMany(
            {
              _id: jobId,
            },
            {
              status: 'closed',
            },
          );
        }
      });
      await Promise.all(closeJobPromises);
    }

    if (invoiceReferences.length > 0) {
      const updateInvoiceReferencePromises = invoiceReferences.map(
        async (invoiceReference) => {
          const foundInvoice = await this.invoiceModel.findOne({
            _id: invoiceReference._id,
          });

          if (foundInvoice && !foundInvoice.invoiceReference) {
            await this.invoiceModel.updateOne(
              {
                _id: foundInvoice._id,
              },
              {
                invoiceReference: invoice._id,
              },
            );
          }
        },
      );

      await Promise.all(updateInvoiceReferencePromises);
    }
  }
  //#endregion
}
