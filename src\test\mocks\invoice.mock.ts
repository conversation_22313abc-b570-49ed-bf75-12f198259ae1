import { getModelForClass } from '@typegoose/typegoose';
import { Types } from 'mongoose';
import { nanoid } from 'nanoid';

import { InvoiceModel } from '~/modules/invoice/invoice.model';

const invoiceModel = getModelForClass(InvoiceModel);

export const mockInvoiceData = {
  _id: new Types.ObjectId(),
  approvedAt: new Date(),
  costLines: [new Types.ObjectId()],
  createdAt: new Date(),
  identifier: nanoid(),
  net: 7.5,
  type: 'job',
  updatedAt: new Date(),
};

export async function initMockInvoice(doc?: any) {
  const { _id, ...rest } = { ...mockInvoiceData, ...doc };
  await invoiceModel.replaceOne({ _id }, rest, { upsert: true });
}
