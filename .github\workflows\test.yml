name: Unit Test
on:
  push:
    branches:
      - main

jobs:
  unittest:
    name: Unit Test
    runs-on: ubuntu-latest
    steps:

      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install Dependencies
        run: npm install

      - name: Install testmoCLI
        run: npm install -g @testmo/testmo-cli

      - name: Cache node_modules
        uses: actions/cache@v3
        with:
          path: node_modules
          key: ${{ runner.os }}-node-${{ hashFiles('package-lock.json') }}
          restore-keys: |
            ${{ runner.os }}-node-
            ${{ runner.os }}-

      - name: Run Unit Test
        if: always()
        run: 'npx ts-node ./scripts/unitTest/run-test.ts'
        continue-on-error: true

      - name: Send result to Testmo
        if: always()
        run: |
          for file in ./dist/reports/*.xml;
          do
            testmo automation:run:submit --instance "$TESTMO_URL" --project-id 5 --name "$(basename "$file")" --source "UnitTest" --tags UnitTest --results "$file"
          done
        env:
          TESTMO_TOKEN: ${{ secrets.TESTMO_API_KEY }}
          TESTMO_URL: https://testing-rock.testmo.net/

      - name: Send Message to MS Teams
        if: always()
        run: npx ts-node ./scripts/unitTest/send-msteam.ts
        continue-on-error: true
        env:
          GITHUB_ACTOR: ${{ github.actor }}
          GITHUB_REPOSITORY: ${{ github.repository }}
          GITHUB_RUN_ID: ${{ github.run_id }}
