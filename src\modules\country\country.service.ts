import { BadRequestException, Injectable } from '@nestjs/common';

import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { CountryModel } from './country.model';

@Injectable()
export class CountryService {
  constructor(
    @InjectModel(CountryModel)
    private countryModel: MongooseModel<CountryModel>,
  ) {}
  async getList(payload: QueryParamsDto) {
    const { query, options } = buildQuery(payload, ['name', 'code']);
    return await this.countryModel.aggregatePaginate(
      this.countryModel.aggregate([
        { $match: query },
        { $project: { name: 1, code: 1 } },
      ]),
      options,
    );
  }
  async validatePostalCode(address: any, moduleName: string) {
    if (!address) {
      return;
    }
    const country = await this.countryModel.findById(address.country);
    if (!country) {
      throw new BadRequestException(`${moduleName}.form.country_not_found`);
    }

    if (country.code === 'nl') {
      const regex = /^\d{4}\s*[A-Za-z]{2}$/;
      if (!regex.test(address.postalCode)) {
        throw new BadRequestException(
          `${moduleName}.form.wrong_format_postal_code`,
        );
      }
    } else if (country.code === 'de') {
      const regex = /^\d{5}$/;
      if (!regex.test(address.postalCode)) {
        throw new BadRequestException(
          `${moduleName}.form.wrong_format_postal_code`,
        );
      }
    }
  }
}
