import { AbstractCursor } from 'mongodb';
import { Types } from 'mongoose';
import pLimit from 'p-limit';

import { queryForAdvancePaging } from '~/processors/migration/helpers/query.helper';

import { MigrationContext } from '../migration.service';
import MyWritable from './writable';

const migration = async ({
  context,
  sourceCollectionName,
  destinationCollectionName,
  queryDataFunc,
  tranformDataFunc,
}: {
  context: MigrationContext;
  sourceCollectionName: string;
  destinationCollectionName: string;
  queryDataFunc: ({
    skip,
    limit,
    context,
  }: {
    skip: number;
    limit: number;
    context: MigrationContext;
  }) => any;
  tranformDataFunc: ({
    data,
    context,
  }: {
    data: any;
    context: MigrationContext;
  }) => any;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection(sourceCollectionName);

  const destinationCollection = context
    .destinationClient!.db()
    .collection(destinationCollectionName)!;

  const count = await sourceCollection.countDocuments();

  console.log(
    `Total documents in collection: ${sourceCollectionName} is ${count}`,
  );

  const limit = 100;
  let pages = Math.ceil(count / 100);

  while (pages > 0) {
    const skip = (pages - 1) * limit;

    // Create a writable stream
    const writable = new MyWritable({ objectMode: true });

    // Pipe the source collection to the writable stream
    const sourceCursor = queryDataFunc({ skip, limit, context });

    await new Promise((resolve, reject) => {
      sourceCursor
        .stream()
        .pipe(writable)
        .on('finish', resolve)
        .on('error', reject);
    });

    // Insert the data to the destination collection
    if (writable.data.length > 0) {
      // Bulk write the data
      const data = await tranformDataFunc({
        data: writable.data,
        context,
      });

      const limit = pLimit(10);

      const upsertPromises = data.map((doc) =>
        limit(() =>
          destinationCollection
            .findOneAndUpdate(
              { _id: doc._id }, // Assuming _id is the identifier
              { $set: doc },
              { upsert: true, returnDocument: 'after' }, // Use returnDocument: 'after' to get the updated document
            )
            .then(() => {
              console.log(
                `Migrated  document with _id: ${doc._id} into collection ${destinationCollectionName}`,
              );
            })
            .catch((error) => {
              console.error(
                `Error upserting document with _id: ${doc._id}:`,
                error,
              );
            }),
        ),
      );

      await Promise.all(upsertPromises)
        .then(() => {
          console.log(
            `Merged ${data.length} documents from collection ${sourceCollectionName} to collection ${destinationCollectionName}`,
          );
        })
        .catch((error) => {
          console.error('Error during upsert operations:', error);
        });
    }

    pages--;
  }
};

export const migrationV2 = async ({
  context,
  sourceCollectionName,
  destinationCollectionName,
  tranformDataFunc,
  pagingFunc,
  inventoryMode = false,
  limit = 1000,
  count,
  isUpsert = true,
}: {
  context: MigrationContext;
  sourceCollectionName: string;
  destinationCollectionName: string;
  tranformDataFunc: ({
    data,
    context,
  }: {
    data: any;
    context: MigrationContext;
  }) => any;
  pagingFunc?: ({
    nextId,
    limit,
    collection,
  }: {
    nextId: Types.ObjectId | undefined;
    limit: number;
    collection: any;
  }) => AbstractCursor;
  inventoryMode?: boolean;
  useAdvancePaging?: boolean;
  limit?: number;
  count?: number;
  isUpsert?: boolean;
}) => {
  const sourceCollection = (
    inventoryMode ? context.inventorySourceClient! : context.sourceClient!
  )
    .db()
    .collection(sourceCollectionName);

  const destinationCollection = context
    .destinationClient!.db()
    .collection(destinationCollectionName)!;

  const total = count ?? (await sourceCollection.countDocuments());

  console.log(
    `Total documents in collection: ${sourceCollectionName} is ${total}`,
  );

  let totalMigrated = 0;
  let nextId: Types.ObjectId | undefined;
  let writable: MyWritable;

  do {
    // Create a writable stream
    writable = new MyWritable({ objectMode: true });

    // Pipe the source collection to the writable stream
    const sourceCursor = (pagingFunc ?? queryForAdvancePaging)({
      nextId,
      limit,
      collection: sourceCollection!,
    });

    await new Promise((resolve, reject) => {
      sourceCursor
        .stream()
        .pipe(writable)
        .on('finish', resolve)
        .on('error', reject);
    });

    // Insert the data to the destination collection
    if (writable.data.length > 0) {
      // Bulk write the data
      const data = await tranformDataFunc({
        data: writable.data,
        context,
      });

      const limit = pLimit(10);

      const insertOrUpdate = async (doc: { _id: any }) => {
        try {
          await destinationCollection.findOneAndUpdate(
            { _id: doc._id }, // Assuming _id is the identifier
            { $set: doc },
            { upsert: isUpsert, returnDocument: 'after' },
          );
          console.log(
            `Migrated document with _id: ${doc._id} into collection ${destinationCollectionName}`,
          );
        } catch (error) {
          console.error(
            `Error upserting document with _id: ${doc._id}:`,
            error,
          );
        }
      };

      const upsertPromises = data.map(
        (doc: { _id: any; subDocs: { _id: Types.ObjectId }[] }) =>
          limit(async () => {
            nextId = doc._id;
            if (doc.subDocs) {
              for (const subDoc of doc.subDocs) {
                await insertOrUpdate(subDoc);
                totalMigrated++;
              }
            } else {
              await insertOrUpdate(doc);
              totalMigrated++;
            }
          }),
      );

      await Promise.all(upsertPromises)
        .then(() => {
          console.log(
            `Merged ${totalMigrated}/${total} documents from collection ${sourceCollectionName} to collection ${destinationCollectionName}`,
          );
        })
        .catch((error) => {
          console.error('Error during upsert operations:', error);
        });
    }
  } while (writable.data.length > 0);
};

export const migrationDataFromCoreToGeneral = async ({
  context,
  coreSourceCollectionName,
  generalDestinationCollectionName,
  tranformDataFunc,
  pagingFunc,
  limit = 1000,
  count,
}: {
  context: MigrationContext;
  coreSourceCollectionName: string;
  generalDestinationCollectionName: string;
  tranformDataFunc: ({
    data,
    context,
  }: {
    data: any;
    context: MigrationContext;
  }) => any;
  pagingFunc?: ({
    nextId,
    limit,
    collection,
  }: {
    nextId: Types.ObjectId | undefined;
    limit: number;
    collection: any;
  }) => AbstractCursor;
  useAdvancePaging?: boolean;
  limit?: number;
  count?: number;
}) => {
  const coreSourceCollection = context
    .destinationClient!.db()
    .collection(coreSourceCollectionName);

  const generalDestinationCollection = context
    .generalClient!.db()
    .collection(generalDestinationCollectionName)!;

  const total = count ?? (await coreSourceCollection.countDocuments());

  console.log(
    `Total documents in collection: ${coreSourceCollectionName} is ${total}`,
  );

  let totalMigrated = 0;
  let nextId: Types.ObjectId | undefined;
  let writable: MyWritable;

  do {
    // Create a writable stream
    writable = new MyWritable({ objectMode: true });

    // Pipe the source collection to the writable stream
    const sourceCursor = (pagingFunc ?? queryForAdvancePaging)({
      nextId,
      limit,
      collection: coreSourceCollection!,
    });

    await new Promise((resolve, reject) => {
      sourceCursor
        .stream()
        .pipe(writable)
        .on('finish', resolve)
        .on('error', reject);
    });

    // Insert the data to the destination collection
    if (writable.data.length > 0) {
      // Bulk write the data
      const data = await tranformDataFunc({
        data: writable.data,
        context,
      });

      const limit = pLimit(10);

      const insertOrUpdate = async (doc: { _id: any }) => {
        try {
          await generalDestinationCollection.findOneAndUpdate(
            { _id: doc._id }, // Assuming _id is the identifier
            { $set: doc },
            { upsert: true, returnDocument: 'after' },
          );
          console.log(
            `Migrated document with _id: ${doc._id} into collection ${generalDestinationCollectionName}`,
          );
        } catch (error) {
          console.error(
            `Error upserting document with _id: ${doc._id}:`,
            error,
          );
        }
      };

      const upsertPromises = data.map(
        (doc: { _id: any; subDocs: { _id: Types.ObjectId }[] }) =>
          limit(async () => {
            nextId = doc._id;
            if (doc.subDocs) {
              for (const subDoc of doc.subDocs) {
                await insertOrUpdate(subDoc);
                totalMigrated++;
              }
            } else {
              await insertOrUpdate(doc);
              totalMigrated++;
            }
          }),
      );

      await Promise.all(upsertPromises)
        .then(() => {
          console.log(
            `Merged ${totalMigrated}/${total} documents from collection ${coreSourceCollectionName} in core schema to collection ${generalDestinationCollectionName} in general schema`,
          );
        })
        .catch((error) => {
          console.error('Error during upsert operations:', error);
        });
    }
  } while (writable.data.length > 0);
};

export const externalMigration = async ({
  context,
  sourceCollectionName,
  destinationCollectionName,
  tranformDataFunc,
  pagingFunc,
  limit = 1000,
  count,
  isUpsert = true,
}: {
  context: MigrationContext;
  sourceCollectionName: string;
  destinationCollectionName: string;
  tranformDataFunc: ({
    data,
    context,
  }: {
    data: any;
    context: MigrationContext;
  }) => any;
  pagingFunc?: ({
    nextId,
    limit,
    collection,
  }: {
    nextId: Types.ObjectId | undefined;
    limit: number;
    collection: any;
  }) => AbstractCursor;
  useAdvancePaging?: boolean;
  limit?: number;
  count?: number;
  isUpsert?: boolean;
}) => {
  const sourceCollection = context
    .destinationClient!.db()
    .collection(sourceCollectionName);

  const destinationCollection = context
    .destinationClient!.db()
    .collection(destinationCollectionName)!;

  const total = count ?? (await sourceCollection.countDocuments());

  console.log(
    `Total documents in collection: ${sourceCollectionName} is ${total}`,
  );

  let totalMigrated = 0;
  let nextId: Types.ObjectId | undefined;
  let writable: MyWritable;

  do {
    // Create a writable stream
    writable = new MyWritable({ objectMode: true });

    // Pipe the source collection to the writable stream
    const sourceCursor = (pagingFunc ?? queryForAdvancePaging)({
      nextId,
      limit,
      collection: sourceCollection!,
    });

    await new Promise((resolve, reject) => {
      sourceCursor
        .stream()
        .pipe(writable)
        .on('finish', resolve)
        .on('error', reject);
    });

    // Insert the data to the destination collection
    if (writable.data.length > 0) {
      // Bulk write the data
      const data = await tranformDataFunc({
        data: writable.data,
        context,
      });

      const limit = pLimit(10);

      const insertOrUpdate = async (doc: { _id: any }) => {
        try {
          await destinationCollection.findOneAndUpdate(
            { _id: doc._id }, // Assuming _id is the identifier
            { $set: doc },
            { upsert: isUpsert, returnDocument: 'after' },
          );
          console.log(
            `Migrated document with _id: ${doc._id} into collection ${destinationCollectionName}`,
          );
        } catch (error) {
          console.error(
            `Error upserting document with _id: ${doc._id}:`,
            error,
          );
        }
      };

      const upsertPromises = data.map(
        (doc: { _id: any; subDocs: { _id: Types.ObjectId }[] }) =>
          limit(async () => {
            nextId = doc._id;
            if (doc.subDocs) {
              for (const subDoc of doc.subDocs) {
                await insertOrUpdate(subDoc);
                totalMigrated++;
              }
            } else {
              await insertOrUpdate(doc);
              totalMigrated++;
            }
          }),
      );

      await Promise.all(upsertPromises)
        .then(() => {
          console.log(
            `Merged ${totalMigrated}/${total} documents from collection ${sourceCollectionName} to collection ${destinationCollectionName}`,
          );
        })
        .catch((error) => {
          console.error('Error during upsert operations:', error);
        });
    }
  } while (writable.data.length > 0);
};

export default migration;
