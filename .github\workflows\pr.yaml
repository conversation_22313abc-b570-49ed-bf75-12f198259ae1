name: pr

on:
  push:
    branches:
      - hotfix/*

env:
  GCP_PROJECT_ID: ee-acc-v2 # {"$kpt-set":"project"}
  GCP_REPOSITORY: ee-acc-v2
  GCP_APP_NAME: ee-acc-v2-core # {"$kpt-set":"app"} 
  CLOUDRUN_APP_NAME_TENANT1: ee-acc-v2-tenant1
  CLOUDRUN_APP_NAME_TENANT2: ee-acc-v2-tenant2
  CLOUDRUN_REGION: europe-west4
  DOCKER_GCP_REGISTRY: europe-west4-docker.pkg.dev
  PREVIEW_VERSION: "0.0.0-SNAPSHOT-pr-${{github.event.number}}-${{github.run_number}}"

jobs:
  main:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          fetch-depth: '0'

          
      - name: Use Node.js
        uses: actions/setup-node@v1
        with:
          node-version: '18.x'
      - name: Cache Node.js modules
        uses: actions/cache@v4
        with:
          # npm cache files are stored in `~/.npm` on Linux/macOS
          path: ~/.npm
          key: ${{ runner.OS }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.OS }}-node-
            ${{ runner.OS }}-
              
      - id: 'auth'
        uses: 'google-github-actions/auth@v1'
        with:
          token_format: 'access_token'
          credentials_json: '${{ secrets.GOOGLE_CREDENTIALS_V2 }}'  

      - uses: 'docker/login-action@v1'
        with:
          registry: ${{ env.DOCKER_GCP_REGISTRY }}
          username: 'oauth2accesstoken'
          password: '${{ steps.auth.outputs.access_token }}'  

      - name: Set up Docker Context for Buildx
        id: buildx-context
        run: |
          docker context create builders

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v1
        with:
          endpoint: builders
      - name: Build Docker image 
        run: docker build  --build-arg GITHUB_VERSION=${{ github.sha }} -f Dockerfile --tag ${{ env.DOCKER_GCP_REGISTRY }}/${{ env.GCP_PROJECT_ID }}/${{ env.GCP_REPOSITORY }}/${{ env.GCP_APP_NAME }}:${{ env.PREVIEW_VERSION }} .

      - name: Push Docker image
        run: docker push ${{ env.DOCKER_GCP_REGISTRY }}/${{ env.GCP_PROJECT_ID }}/${{ env.GCP_REPOSITORY }}/${{ env.GCP_APP_NAME }}:${{ env.PREVIEW_VERSION }}