import { BadRequestException, Injectable } from '@nestjs/common';
import { isNullOrUndefined } from '@typegoose/typegoose/lib/internal/utils';
import { omit } from 'lodash';
import { ObjectId } from 'mongodb';

import { SyncHistoryService } from '~/modules/sync-history/sync-history.service';
import { ThirdPartyConnectorContext } from '~/processors/third-party-connector/strategies/third-party-connector.context';
import { SyncHistoryType } from '~/shared/enums/sync-history.enum';
import { ThirdPartyTypeEnum } from '~/shared/enums/third-party-type.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { CostCenterQueryDto } from './costcenter.dto';
import { CostCenterModel } from './costcenter.model';

@Injectable()
export class CostCenterService {
  constructor(
    @InjectModel(CostCenterModel)
    private readonly costCenterModel: MongooseModel<CostCenterModel>,

    private readonly thirdPartyContext: ThirdPartyConnectorContext,

    private readonly syncHistoryService: SyncHistoryService,
  ) {}

  async findAll(params: CostCenterQueryDto) {
    const { query, options } = buildQuery(
      omit(params, ['isUsed', 'country', 'bvCompany']),
      ['name', 'identifier', 'isActive'],
    );

    let finalQuery: any = {
      ...query,
    };

    const finalOptions =
      params.pageSize === -1 ? { ...options, pagination: false } : options;

    const orQueries: any = [];

    if (!isNullOrUndefined(params.isUsed)) {
      orQueries.push({ 'locations.0': { $exists: params.isUsed } });
    }

    if (params.bvCompany && params.country) {
      orQueries.push({
        'locationDetails.bvCompany': new ObjectId(params.bvCompany),
        'address.country': new ObjectId(params.country),
      });
    }

    if (orQueries.length) {
      finalQuery = { $and: [finalQuery, { $or: orQueries }] };
    }

    const aggregationPipeline = [
      {
        $lookup: {
          from: 'locations',
          localField: 'locations',
          foreignField: '_id',
          as: 'locationDetails',
        },
      },
      {
        $lookup: {
          from: 'addresses',
          localField: 'locationDetails.address',
          foreignField: '_id',
          as: 'address',
        },
      },
      { $match: finalQuery },
      { $project: { locationDetails: 0, address: 0 } },
    ];

    return this.costCenterModel.aggregatePaginate(
      this.costCenterModel.aggregate(aggregationPipeline),
      finalOptions,
    );
  }

  async findOne(id: string) {
    return (
      await this.costCenterModel.aggregate([
        { $match: { _id: new ObjectId(id) } },
        {
          $lookup: {
            from: 'locations',
            localField: 'locations',
            foreignField: '_id',
            as: 'locations',
            pipeline: [
              {
                $addFields: {
                  fullAddressLower: { $toLower: '$fullAddress' },
                },
              },
              { $sort: { fullAddressLower: 1 } },
            ],
          },
        },
        {
          $addFields: {
            maxOccupants: {
              $sum: '$locations.maxOccupants',
            },
          },
        },
        { $project: { 'locations.fullAddressLower': 0 } },
      ])
    )[0];
  }

  async syncFrom3rdParty(payload: {
    [key: string]: any;
    type: ThirdPartyTypeEnum;
  }) {
    if (await this.syncHistoryService.isPending(SyncHistoryType.COST_CENTER)) {
      throw new BadRequestException(
        'Sync is already in progress by another user',
      );
    }

    const syncHistory = await this.syncHistoryService.createNewPending(
      SyncHistoryType.COST_CENTER,
      payload.actionType,
    );

    try {
      const costCenters = await this.thirdPartyContext.getCostCenters(payload);

      const bulkOperations: any[] = costCenters.map((costCenter) => ({
        updateOne: {
          filter: { identifier: costCenter.identifier },
          update: { $set: costCenter },
          upsert: true,
        },
      }));

      const keptCostCentersIdentifiers = costCenters.map(
        (costCenter) => costCenter.identifier,
      );
      bulkOperations.push({
        updateMany: {
          filter: {
            identifier: { $nin: keptCostCentersIdentifiers },
            isActive: true,
          },
          update: { $set: { isActive: false } },
        },
      });

      const result = await this.costCenterModel.bulkWrite(bulkOperations);
      await this.syncHistoryService.updateToSuccess(syncHistory._id!);

      return result;
    } catch (ex: any) {
      await this.syncHistoryService.updateToFailed(
        syncHistory._id!,
        ex.message,
      );

      throw ex;
    }
  }
}
