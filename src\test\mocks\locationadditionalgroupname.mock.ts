import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { z } from 'zod';

import { LocationAdditionalGroupNameModel } from '~/modules/location-addtional/location-additional-group-name.model';
import { locationAdditionalGroupNameTest } from '~/modules/location-addtional/test/location-additional-group-name.dto.test';
import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';

const locationAdditionalGroupNameModel = getModelForClass(
  LocationAdditionalGroupNameModel,
);

export const mockLocationAdditionalGroupNameData = {
  _id: new ObjectId(),
  identifier: nanoid(),
  description: 'Thermostat',
  dutchDescription: 'Thermostaat',
  isDeleted: false,
  type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
  position: 5,
  key: 'Thermostat',
};

export async function initMockLocationAdditionalGroupName(
  doc?: Partial<z.infer<typeof locationAdditionalGroupNameTest.modelSchema>>,
) {
  const { _id, ...rest } = { ...mockLocationAdditionalGroupNameData, ...doc };
  await locationAdditionalGroupNameModel.replaceOne({ _id }, rest, {
    upsert: true,
  });
}
