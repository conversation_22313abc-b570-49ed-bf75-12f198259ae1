import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import { LocationDocument, LocationModel } from '../location/location.model';

export type UnitDocument = DocumentType<UnitModel>;

@modelOptions({
  options: { customName: 'Unit' },
})
@index({ location: 1 })
@index({ name: 1, location: 1 })
@index({ position: 1, location: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class UnitModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({ default: false })
  isRoot!: boolean;

  @prop({ required: true, trim: true, maxlength: 256 })
  name!: string;

  @prop({ default: 0 })
  maxOccupants!: number;

  @prop({ default: 0 })
  maxArea!: number;

  @prop({ default: 0 })
  position!: number;

  @prop({ ref: () => UnitModel })
  parent?: Ref<UnitDocument>;

  @prop({ ref: () => LocationModel })
  location!: Ref<LocationDocument>;
}
