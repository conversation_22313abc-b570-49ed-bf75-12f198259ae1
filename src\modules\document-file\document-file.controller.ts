import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { DOCUMENT_FILE_MESSAGES } from '~/shared/messages/document-file.message';

import { DocumentFileService } from './document-file.service';
import {
  DocumentFileCreateDto,
  DocumentFileQueryParamDto,
} from './dtos/docment-file.dto';

@Controller()
export class DocumentFileController {
  constructor(private documentFileService: DocumentFileService) {}

  @UsePipes(new ZodValidationPipe(DocumentFileCreateDto))
  @MessagePattern({ cmd: DOCUMENT_FILE_MESSAGES.CREATE_DOCUMENT_FILE })
  async createDocumentFile(@Payload() payload: DocumentFileCreateDto) {
    return await this.documentFileService.createDocumentFile(payload);
  }

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(DocumentFileQueryParamDto))
  @MessagePattern({ cmd: DOCUMENT_FILE_MESSAGES.GET_LIST_DOCUMENT_FILE })
  async getListDocumentFile(@Payload() payload: any) {
    return await this.documentFileService.getListDocumentFile(payload);
  }

  @MessagePattern({ cmd: DOCUMENT_FILE_MESSAGES.DELETE_DOCUMENT_FILE })
  async deleteDocumentFile(@Payload() payload: any) {
    const { id, headers, user } = payload;
    return await this.documentFileService.deleteDocumentFile(id, headers, user);
  }

  @MessagePattern({ cmd: DOCUMENT_FILE_MESSAGES.CHECK_EXISTED_DOCUMENT_FILE })
  async checkExistedDocumentFile(@Payload() payload: any) {
    return await this.documentFileService.checkExistedDocumentFile(payload);
  }
}
