import dayjs from 'dayjs';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { ContractType, OwnerType } from '~/shared/enums/contract.enum';
import { isValidObjectId } from '~/utils';

const contractTypeValues = Object.values(ContractType) as [string, ...string[]];
const ownerTypeValues = Object.values(OwnerType).map((o) =>
  o.toLowerCase(),
) as [string, ...string[]];

const BaseContractQueryParamsSchema = z
  .strictObject({
    type: z.enum(contractTypeValues),
    contact: z
      .string()
      .refine((val) => isValidObjectId(val))
      .optional(),
    location: z
      .string()
      .refine((val) => isValidObjectId(val))
      .optional(),
    startDateFrom: z.dateString().optional(),
    startDateTo: z.dateString().optional(),
    isActive: z.enum(['true', 'false']).optional(),
    isSigned: z.enum(['true', 'false']).optional(),
  })
  .merge(QueryParamsSchema);

export const RentingContractQueryParamsSchema =
  BaseContractQueryParamsSchema.extend({
    location: z
      .string()
      .refine((val) => isValidObjectId(val))
      .optional(),
    owner: z.enum(ownerTypeValues).optional(),
  }).refine((v) => {
    if (v.startDateFrom && v.startDateTo) {
      return dayjs(v.startDateFrom)
        .startOf('day')
        .isSameOrBefore(dayjs(v.startDateTo).startOf('day'));
    }
    return true;
  }, 'EndDate must be equal or greater than StartDate');

export const CreditorContractQueryParamsSchema =
  BaseContractQueryParamsSchema.extend({
    location: z
      .string()
      .refine((val) => isValidObjectId(val))
      .optional(),
  }).refine((v) => {
    if (v.startDateFrom && v.startDateTo) {
      return dayjs(v.startDateFrom)
        .startOf('day')
        .isSameOrBefore(dayjs(v.startDateTo).startOf('day'));
    }
    return true;
  }, 'EndDate must be equal or greater than StartDate');

export const ServiceContractQueryParamsSchema =
  BaseContractQueryParamsSchema.extend({
    costCenter: z
      .string()
      .refine((val) => isValidObjectId(val))
      .optional(),
  }).refine((v) => {
    if (v.startDateFrom && v.startDateTo) {
      return dayjs(v.startDateFrom)
        .startOf('day')
        .isSameOrBefore(dayjs(v.startDateTo).startOf('day'));
    }
    return true;
  }, 'EndDate must be equal or greater than StartDate');

export const SupplierContractQueryParamsSchema =
  BaseContractQueryParamsSchema.extend({
    contractType: z
      .string()
      .refine((val) => isValidObjectId(val))
      .optional(),
    contractTypeName: z.string().optional(),
  }).refine((v) => {
    if (v.startDateFrom && v.startDateTo) {
      return dayjs(v.startDateFrom)
        .startOf('day')
        .isSameOrBefore(dayjs(v.startDateTo).startOf('day'));
    }
    return true;
  }, 'EndDate must be equal or greater than StartDate');
