import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const ChangePositionInPlanningOrderSchema = z.strictObject({
  date: z.string(),
  employee: z.string().refine(isValidObjectId),
  jobs: z.array(z.string().refine(isValidObjectId)).min(1),
});

export class ChangePositionInPlanningOrderSchemaDto extends createZodDto(
  ChangePositionInPlanningOrderSchema,
) {}
