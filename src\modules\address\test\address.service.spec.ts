import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { AddressDto } from '~/modules/contact/dtos/contact.dto';
import { CountryModel } from '~/modules/country/country.model';
import { RegionModel } from '~/modules/region/region.model';
import { ADDRESS_MESSAGE_KEYS } from '~/shared/message-keys/address.message-keys';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockCountry, mockCountryData } from '~/test/mocks/country.mock';
import { initMockRegion, mockRegionData } from '~/test/mocks/region.mock';

import { AddressService } from '../address.service';
import { addressTest } from './address.dto.test';

describe('AddressService', () => {
  let service: AddressService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      providers: [
        AddressService,
        ...testInjectModel([CountryModel, RegionModel]),
      ],
    }).compile();

    service = module.get(AddressService);

    // Init data
    await Promise.all([initMockRegion(), initMockCountry()]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('validationAddressCountryRegion', () => {
    const payload: AddressDto = {
      _id: new ObjectId().toString(),
      country: new ObjectId().toString(),
      region: new ObjectId().toString(),
      street: '123 Main St',
      city: 'City',
      number: '123',
      suffix: 'Apt 1',
      postalCode: '12345',
    };

    it('should throw if country not found', async () => {
      await expect(
        service.validationAddressCountryRegion(payload),
      ).rejects.toThrow(ADDRESS_MESSAGE_KEYS.INVALID_COUNTRY);
    });

    it('should throw if region not found', async () => {
      await expect(
        service.validationAddressCountryRegion({
          ...payload,
          country: mockCountryData._id.toString(),
        }),
      ).rejects.toThrow(ADDRESS_MESSAGE_KEYS.INVALID_REGION);
    });

    it('should throw if region does not belong to country', async () => {
      // Prepare data
      const regionId = new ObjectId();

      await initMockRegion({ _id: regionId, country: new ObjectId() });

      await expect(
        service.validationAddressCountryRegion({
          ...payload,
          country: mockCountryData._id.toString(),
          region: regionId.toString(),
        }),
      ).rejects.toThrow(ADDRESS_MESSAGE_KEYS.INVALID_REGION);
    });

    it('should return country and region if valid', async () => {
      const result = await service.validationAddressCountryRegion({
        ...payload,
        country: mockCountryData._id.toString(),
        region: mockRegionData._id.toString(),
      });

      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        addressTest.validationAddressCountryRegionSchema,
      );
    });
  });
});
