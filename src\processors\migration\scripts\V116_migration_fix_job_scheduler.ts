import path from 'path';

import migration from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

interface OldInspectionScheduler {
  _id: string;
  inspectionSchedule: any;
}
const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('inspection');

  const pipeline = InspectionSchedulePipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const InspectionSchedulePipeLineAggregate = (skip: number, limit: number) => {
  // get all inspectionSchedule
  return [
    {
      $match: {
        type: 'Periodic',
      },
    },
    {
      $lookup: {
        from: 'inspectionschedule',
        localField: 'inspectionSchedule',
        foreignField: '_id',
        as: 'inspectionSchedule',
      },
    },
    {
      $set: {
        inspectionSchedule: {
          $arrayElemAt: ['$inspectionSchedule', 0],
        },
      },
    },
    { $skip: skip },
    { $limit: limit },
  ];
};

const transformData = ({
  data,
  context,
}: {
  data: OldInspectionScheduler[];
  context: any;
}) => {
  console.log(context);
  return Promise.all(
    data.map(async (item: OldInspectionScheduler) => {
      return omitNull({
        _id: item._id,
        fDayInMonth: item.inspectionSchedule.fDayInMonth,
      });
    }),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'inspectionschedule',
      destinationCollectionName: 'jobs',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: transformData,
    });
    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
