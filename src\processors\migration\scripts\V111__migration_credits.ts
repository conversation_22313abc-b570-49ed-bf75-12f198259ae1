import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

interface OldCredit {
  _id: Types.ObjectId;
  amount?: number;
  costCenter?: Types.ObjectId;
  costLine?: {
    _id: Types.ObjectId;
    agreementLine?: Types.ObjectId;
    costLineGeneral?: Types.ObjectId;
  };
  inspectionItem?: { _id: Types.ObjectId; inspection?: Types.ObjectId };
  inspection?: Types.ObjectId;
  costType?: Types.ObjectId;
  creditInvoices?: { _id: Types.ObjectId; createdDate: Date };
  debtorContact?: { _id: Types.ObjectId; grouping?: Types.ObjectId };
  deleted?: boolean;
  description?: string;
  invoiceDate?: Date;
  location?: Types.ObjectId;
  unit?: Types.ObjectId;
  period?: string;
  periodType?: string;
  price?: number;
  quantity?: number;
  status: string;
  type: string;
  startDate?: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection.aggregate([
    {
      $match: {
        _id: { $gt: nextId },
        type: { $in: ['SERVICE', 'JOB', 'CUSTOM', 'RENTING'] },
      },
    },
    { $sort: { _id: 1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'costline',
        localField: 'costLine',
        foreignField: '_id',
        as: 'costLine',
      },
    },
    {
      $unwind: {
        path: '$costLine',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'unit',
        localField: 'costLine.unit',
        foreignField: '_id',
        as: 'unit',
      },
    },
    {
      $lookup: {
        from: 'debtorcontact',
        localField: 'debtorContact',
        foreignField: '_id',
        as: 'debtorContact',
      },
    },
    {
      $lookup: {
        from: 'inspectionitem',
        localField: 'costLine.inspectionItem',
        foreignField: '_id',
        as: 'inspectionItem',
      },
    },
    {
      $lookup: {
        from: 'invoice',
        localField: 'creditInvoices',
        foreignField: '_id',
        as: 'creditInvoices',
      },
    },
    {
      $addFields: {
        unit: { $arrayElemAt: ['$unit', 0] },
        creditInvoices: { $arrayElemAt: ['$creditInvoices', 0] },
        inspectionItem: { $arrayElemAt: ['$inspectionItem', 0] },
        debtorContact: { $arrayElemAt: ['$debtorContact', 0] },
      },
    },
    {
      $lookup: {
        from: 'unit',
        localField: 'inspectionItem.unit',
        foreignField: '_id',
        as: 'inspectionItem.unit',
      },
    },
    {
      $addFields: {
        'inspectionItem.unit': { $arrayElemAt: ['$inspectionItem.unit', 0] },
      },
    },
    {
      $addFields: {
        costCenter: {
          $cond: [{ $eq: ['$type', 'SERVICE'] }, '$costCenter', '$$REMOVE'],
        },
        type: {
          $switch: {
            branches: [
              { case: { $eq: ['$type', 'RENTING'] }, then: 'debtor-renting' },
              { case: { $eq: ['$type', 'SERVICE'] }, then: 'debtor-service' },
            ],
            default: { $toLower: '$type' },
          },
        },
        period: { $toLower: '$period' },
        periodType: {
          $cond: [
            { $in: ['$type', ['RENTING', 'SERVICE']] },
            { $toLower: '$periodType' },
            '$$REMOVE',
          ],
        },
        location: {
          $ifNull: ['$unit.location', '$inspectionItem.unit.location'],
        },
        unit: {
          $cond: ['$unit.parent', '$unit._id', '$$REMOVE'],
        },
        status: { $toLower: '$status' },
      },
    },
  ]);
};

const transformData = ({ data }: { data: OldCredit[] }) => {
  return Promise.all(
    data.map(async (credit: OldCredit) =>
      omitNull({
        _id: credit._id,
        agreementLine: credit.costLine?.agreementLine,
        costLineGeneral: credit.costLine?.costLineGeneral,
        approvedAt: credit.creditInvoices?.createdDate,
        contact: credit.debtorContact?.grouping,
        costCenter: credit.costCenter,
        costType: credit.costType,
        description: credit.description,
        invoice: credit.creditInvoices?._id,
        isCredit: true,
        isCustom: false,
        isDeleted: credit.deleted ?? false,
        job: credit.inspectionItem?.inspection,
        location: credit.location,
        period: credit.period,
        periodType: credit.periodType,
        position: 1,
        price: credit.price ?? 0,
        quantity: credit.quantity ?? 1,
        totalPrice: credit.amount ?? 0,
        status: credit.status,
        type: credit.type,
        unit: credit.unit,
        parent: credit.costLine?._id,
        startDate: credit.startDate ?? credit.invoiceDate,
        endDate: credit.endDate,
        createdAt: credit.createdAt,
        updatedAt: credit.updatedAt,
      }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'credit',
      destinationCollectionName: 'costlines',
      pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
