import {
  DocumentType,
  modelOptions,
  plugin,
  prop,
  Ref,
  Severity,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { JobPointStatusEnum, JobTypeEnum } from '~/shared/enums/job.enum';
import { BaseModel } from '~/shared/models/base.model';

import { CostLineDocument, CostLineModel } from '../costline/costline.model';
import { JobDocument, JobModel } from '../job/job.model';
import { UnitDocument, UnitModel } from '../unit/unit.model';

export type JobPointDocument = DocumentType<JobPointModel>;

export interface JobPointAction {
  isGrouped: boolean;
  description: string;
  type: JobTypeEnum;
  images: string[];
}

@modelOptions({
  options: { customName: 'JobPoint', allowMixed: Severity.ALLOW },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class JobPointModel extends BaseModel {
  @prop({ default: false })
  isGrouped!: boolean;

  @prop({ enum: JobPointStatusEnum, default: JobPointStatusEnum.NONE })
  status!: JobPointStatusEnum;

  @prop({ required: true, trim: true, maxlength: 256 })
  description!: string;

  @prop({ trim: true, maxlength: 2048 })
  notes?: string;

  @prop({ default: 0 })
  position!: number;

  @prop({ default: [] })
  images?: string[];

  @prop({ ref: () => UnitModel })
  unit!: Ref<UnitDocument>;

  @prop({ ref: () => JobModel })
  job!: Ref<JobDocument>;

  @prop()
  actions?: JobPointAction[];

  @prop({ ref: () => CostLineModel, default: [] })
  costLines?: Ref<CostLineDocument>[];
}
