import { isValidObjectId } from 'mongoose';
import { z } from 'nestjs-zod/z';

export interface CreateCostLineGeneralDto {
  description: string;
  price: number;
  position?: number;
  unit?: string;
  startDate: string;
  endDate?: string;
  costType: string;
}

export const CreateRentingCostLineGeneralSchema = z
  .strictObject({
    description: z.string().min(1).max(256),
    price: z.number(),
    position: z.number().int().min(0).optional(),
    unit: z
      .string()
      .refine((val) => isValidObjectId(val))
      .optional(),
    startDate: z.dateString(),
    endDate: z.dateString().nullish(),
    costType: z
      .string()
      .trim()
      .optional()
      .refine((val) => !val || isValidObjectId(val)),
  })
  .refine(
    (schema) =>
      !schema.endDate ||
      Date.parse(schema.startDate) < Date.parse(schema.endDate),
    {
      message: 'End date must be greater than start date',
      path: ['endDate'],
    },
  );

export const CreateServiceCostLineGeneralSchema = z
  .strictObject({
    description: z.string().min(1).max(256),
    price: z.number().optional(),
    position: z.number().int().min(0).optional(),
    startDate: z.dateString(),
    endDate: z.dateString().nullish(),
    costType: z
      .string()
      .trim()
      .optional()
      .refine((val) => !val || isValidObjectId(val)),
  })
  .refine(
    (schema) =>
      !schema.endDate ||
      Date.parse(schema.startDate) < Date.parse(schema.endDate),
    {
      message: 'End date must be greater than start date',
      path: ['endDate'],
    },
  );

export const CreateSupplierCostLineGeneralSchema =
  CreateServiceCostLineGeneralSchema;
