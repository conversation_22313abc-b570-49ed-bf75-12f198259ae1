import { Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(
      async (reservedDetail: { _id: Types.ObjectId; [key: string]: any }) =>
        omitNull({
          _id: reservedDetail._id,
          article: reservedDetail.article,
          reserved: reservedDetail.reserved,
          isDeleted: false,
          amount: reservedDetail.amount,
          pickedUp: reservedDetail.pickedUp,
          used: reservedDetail.used,
          returned: reservedDetail.returned,
          broken: reservedDetail.broken,
          createdAt: reservedDetail.createdAt,
          updatedAt: reservedDetail.updatedAt,
        }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'reserveddetail',
      destinationCollectionName: 'reserveddetail',
      tranformDataFunc: transformData,
      inventoryMode: true,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
