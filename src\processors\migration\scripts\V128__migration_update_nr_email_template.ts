import path from 'path';

import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const baseLayOut = `<!doctype html>
<html xmlns='http://www.w3.org/1999/xhtml' xmlns:v='urn:schemas-microsoft-com:vml'
    xmlns:o='urn:schemas-microsoft-com:office:office'>

<head>
    <title></title>
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
    <meta name='viewport' content='width=device-width,initial-scale=1'>
    <style type='text/css'>
        #outlook a {
            padding: 0
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0;
            mso-table-rspace: 0
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: 0;
            text-decoration: none;
            -ms-interpolation-mode: bicubic
        }

        p {
            display: block;
            margin: 13px 0
        }
    </style>
    
    <link href='https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700' rel='stylesheet' type='text/css'>
    <style type='text/css'>
        @import url(https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700);
    </style>
    <style type='text/css'>
        @media only screen and (min-width:480px) {
            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%
            }

            .mj-column-px-600 {
                width: 600px !important;
                max-width: 600px
            }
        }
    </style>
    <style media='screen and (min-width:480px)'>
        .moz-text-html .mj-column-per-100 {
            width: 100% !important;
            max-width: 100%
        }

        .moz-text-html .mj-column-px-600 {
            width: 600px !important;
            max-width: 600px
        }
    </style>
    <style type='text/css'></style>
    <style type='text/css'>
        .banner {
            background: url(<%= COMPANY_BANNER %>);
            background-size: cover;
            background-repeat: no-repeat;
            background-position: bottom right;
            padding-left: 18px !important
        }

        .text-banner {
            font-family: Arial, sans-serif !important;
            padding-left: 0 !important;
            padding-bottom: 28px !important;
            padding-top: 62px !important;
            text-align: left !important;
            color: #fff !important;
            font-size: 48px !important
        }

        .red-text {
            font-weight: 700 !important;
            color: <%= COMPANY_TYPOGRAPHY_COLORS_SIGN_TEXT %> !important
        }

        .logo-eeac-wrapper img {
            vertical-align: middle !important
        }

        .logo-right img {
            vertical-align: middle !important
        }

        .logo-eeac {
            height: 36px !important;
            width: auto !important
        }

        .logo-linkedin {
            height: 28px !important
        }

        .logo-sfn {
            height: 40px !important
        }

        .first {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.2rem;
            text-align: left;
            color: #343a40;
            word-break: break-word;
            padding-top: 1px;
            padding-bottom: 1px
        }

        .last {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.2rem;
            text-align: left;
            color: #343a40;
            font-weight: 700;
            word-break: break-word;
            padding-top: 1px;
            padding-bottom: 1px;
            padding-left: 0px
        }

        .banner-container {
                padding: 0 0 !important;
                display: <%= DISPLAY_BANNER %>;
            }

        @media only screen and (max-width:478px) {
            .container {
                padding: 0 0 !important
            }

            .banner {
                padding-left: 12px !important
            }

            .text-banner {
                font-size: 35px !important;
                padding-left: 0 !important;
                padding-bottom: 10px !important;
                padding-top: 30px !important
            }

            .section-header {
                width: 100% !important
            }

            .logo-linkedin {
                height: 18px !important
            }

            .logo-sfn {
                height: 30px !important
            }

            .logo-right {
                padding: 0 8px !important;
                height: 100% !important;
                width: max-content !important;
                line-height: 0
            }

            .logo-right .logo-linkedin-wrapper {
                display: inline-block;
                height: 100%;
                margin-right: 8px !important;
                vertical-align: middle !important
            }

            .logo-eeac-wrapper {
                display: inline-block;
                height: 100%;
                vertical-align: middle !important
            }

            .section-footer {
                max-width: 100% !important;
                padding-left: 2px !important
            }
        }
    </style>
</head>

<body style='word-spacing:normal'>
    <div style='padding-bottom: 20px'>
        <div class='banner-container' style='margin:0 auto;max-width:600px'>
            <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation' style='width:100%'>
                <tbody>
                    <tr>
                        <td style='direction:ltr;font-size:0;padding:0;text-align:center'>
                            <div class='mj-column-per-100 mj-outlook-group-fix'
                                style='font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%'>
                                <table border='0' cellpadding='0' cellspacing='0' role='presentation'
                                    style='vertical-align:top' width='100%'>
                                    <tbody>
                                        <tr>
                                            <td align='left' class='banner'
                                                style='font-size:0;padding:10px 25px;word-break:break-word'>
                                                <table cellpadding='0' cellspacing='0' width='100%' border='0'
                                                    style='color:#000;font-family:Ubuntu,Helvetica,Arial,sans-serif;font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none'>
                                                    <tr>
                                                        <td class='text-banner'>
                                                            <div>You're home.</div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class='container' style='margin:0 auto;max-width:600px;padding-top: 50px'>
            <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation' style='width:100%'>
                <tbody>
                    {html}
                    <tr>
                      <td style='padding-top:20px'>
                        Yours sincerely,  
                      </td>
                    </tr>
                    <tr>
                        <td style='padding-top:40px'>
                            <div class='first'><strong class='first red-text text-sign'>
                                    <%=COMPANY_SIGNATURE %>
                                </strong><br><span class='red-text text-sign' style='margin-right:8px'>T</span><%= COMPANY_TELEPHONE %><br><span class='red-text text-sign'
                                        style='margin-right:8px'>E</span><a href='mailto:<%=COMPANY_EMAIL %>'
                                        target='_blank'><%=COMPANY_EMAIL %>
                                    </a><br><span class='red-text text-sign' style='margin-right:12px'>I</span>
                                    <a href='https://<%= COMPANY_WEBSITE %>' target='_blank'><%= COMPANY_WEBSITE %>
                                    </a><br><span class='text-sign'><%= COMPANY_ADDRESS1 %></span>
                                    <br><span class='text-sign'><%= COMPANY_ADDRESS2 %></span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

    </div>
    <!-- Footer -->
    
    <div class='section-footer'
        style='margin: 0px auto; max-width: 100%;'>
        <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation'
            style='width:100%;'>
            <tbody>
                <tr>
                    <td style='direction:ltr;font-size:0px;padding:0;text-align:center;'>
                        <!--[if mso | IE]><table role='presentation' border='0' cellpadding='0' cellspacing='0'><tr><td class='' style='vertical-align:middle;width:600px;' ><![endif]-->
                        <div class='mj-column-px-600 mj-outlook-group-fix'
                            style='font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:middle;width:100%;'>
                            <table border='0' cellpadding='0' cellspacing='0' role='presentation'
                                style='vertical-align:middle;' width='100%'>
                                <tbody>
                                    <tr>
                                        <td align='left' style='font-size:0px;padding:0;word-break:break-word;'>
                                            <table cellpadding='0' cellspacing='0' width='100%' border='0'
                                                style='color:#000000;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none;'>
                                                <tr style="display:<%= DISPLAY_LINKEDIN %>;">
                                                    <td>
                                                        <a href='<%= COMPANY_LINKEDIN_URL %>' target='_blank'><img width='40' style='width:40px' src='<%= COMPANY_LINKEDIN_LOGO %>'/></a>
                                                    </td>
                                                </tr>
                                                <tr>
                                                  <td>
                                                    <a href='https://<%= COMPANY_WEBSITE %>' target='_blank'><img style='width:150px;' src='<%= COMPANY_NR_LOGO %>'/></a>
                                                    <a href='https://<%= COMPANY_WEBSITE %>' target='_blank'><img style='width:70px; padding-left: 5px;' src='<%= COMPANY_SFN_LOGO %>'/></a>
                                                  </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--[if mso | IE]></td></tr></table><![endif]-->
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    <div style='height:20px;line-height:20px;'>&#8202;</div>
</body>

</html>`;

const html1stVerbal = `<tr>
                            <td style='padding-top:20px'>
                                Dear <%= FIRSTNAME %> <%= LASTNAME %>,
                            </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                Please see in the attachment <strong>First Verbal Notification</strong> from location - <strong><%=LOCATION_NAME %></strong>
                            </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                Following warning is for <strong><%=WARNING_CATEGORY %></strong>
                            </td>
                        </tr>
                         <tr>
                            <td style='padding-top:10px'>
                                More specificic: <strong><%=WARNING_DESCRIPTION %></strong>
                            </td>
                        </tr>
                         <tr>
                            <td style='padding-top:10px'>
                                Incident date - <strong><%=DATE_WARNING %></strong>,  Time of incident - <strong><%=TIME_WARNING %></strong>
                            </td>
                        </tr>`;
const html2ndVerbal = `<tr>
                            <td style='padding-top:20px'>
                                Dear <%= FIRSTNAME %> <%= LASTNAME %>,
                            </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                Please see in the attachment <strong>Second Verbal Notification</strong> from location - <strong><%=LOCATION_NAME %></strong>
                            </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                Following warning is for <strong><%=WARNING_CATEGORY %></strong>
                            </td>
                        </tr>
                         <tr>
                            <td style='padding-top:10px'>
                                More specificic: <strong><%=WARNING_DESCRIPTION %></strong>
                            </td>
                        </tr>
                         <tr>
                            <td style='padding-top:10px'>
                                Incident date - <strong><%=DATE_WARNING %></strong>,  Time of incident - <strong><%=TIME_WARNING %></strong>  
                            </td>
                        </tr>`;
const html1stOfficial = `<tr>
                            <td style='padding-top:20px'>
                                Dear <%= FIRSTNAME %> <%= LASTNAME %>,
                            </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                Please see in the attachment <strong>First Official Warning</strong> from location - <strong><%=LOCATION_NAME %></strong>
                            </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                Following warning is for <strong><%=WARNING_CATEGORY %></strong>
                            </td>
                        </tr>
                         <tr>
                            <td style='padding-top:10px'>
                                More specificic: <strong><%=WARNING_DESCRIPTION %></strong>
                            </td>
                        </tr>
                         <tr>
                            <td style='padding-top:10px'>
                                Incident date - <strong><%=DATE_WARNING %></strong>, Time of incident - <strong><%=TIME_WARNING %></strong>
                            </td>
                        </tr>`;
const html2ndOfficial = `<tr>
                            <td style='padding-top:20px'>
                                Dear <%= FIRSTNAME %> <%= LASTNAME %>,
                            </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                Please see in the attachment <strong>Second Official Warning</strong> from location - <strong><%=LOCATION_NAME %></strong>
                            </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                Following warning is for <strong><%=WARNING_CATEGORY %></strong>
                            </td>
                        </tr>
                         <tr>
                            <td style='padding-top:10px'>
                                More specificic: <strong><%=WARNING_DESCRIPTION %></strong>
                            </td>
                        </tr>
                         <tr>
                            <td style='padding-top:10px'>
                                Incident date - <strong><%=DATE_WARNING %></strong>, Time of incident - <strong><%=TIME_WARNING %></strong> 
                            </td>
                        </tr>`;
const htmlRemoval = `<tr>
                            <td style='padding-top:20px'>
                                Dear <%= FIRSTNAME %> <%= LASTNAME %>,
                            </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                Please see in the attachment <strong>REMOVAL LETTER</strong> from location - <strong><%=LOCATION_NAME %></strong>
                            </td>
                        </tr>
                         <tr>
                            <td style='padding-top:10px'>
                                Incident date - <strong><%=DATE_WARNING %></strong>, Time of incident - <strong><%=TIME_WARNING %></strong>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                You have time to leave location till - <strong><%= DAY_TO_LEAVE %></strong>
                            </td>
                        </tr>`;

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const destinationCollectionName = 'emailtemplates';
    const destinationCollection = context
      .destinationClient!.db()
      .collection(destinationCollectionName)!;

    if (!(await destinationCollection.indexExists('name_1'))) {
      destinationCollection.createIndex({ name: 1 }, { unique: true });
    }
    const data = [
      {
        name: 'night_registration_1st_verbal',
        subject: '1st Notification',
        to: [],
        html: baseLayOut.replace('{html}', html1stVerbal),
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'night_registration_2nd_verbal',
        subject: '2nd Notification',
        to: [],
        html: baseLayOut.replace('{html}', html2ndVerbal),
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'night_registration_1st_official',
        subject: '1st Official Warning',
        to: [],
        html: baseLayOut.replace('{html}', html1stOfficial),
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'night_registration_2nd_official',
        subject: '2nd Official Warning',
        to: [],
        html: baseLayOut.replace('{html}', html2ndOfficial),
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'night_registration_removal',
        subject: 'Removal Letter',
        to: [],
        html: baseLayOut.replace('{html}', htmlRemoval),
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    const upsertPromises = data.map((doc) =>
      destinationCollection
        .findOneAndUpdate(
          { name: doc.name },
          { $set: doc },
          { upsert: true, returnDocument: 'after' }, // Use returnDocument: 'after' to get the updated document
        )
        .then(() => {
          console.log(
            `Migrated nr email template with name=${doc.name} into collection ${destinationCollectionName}`,
          );
        })
        .catch((error) => {
          console.error(
            `Error upserting document with name=${doc.name}:`,
            error,
          );
        }),
    );

    await Promise.all(upsertPromises)
      .then(() => {
        console.log(
          `Migrated ${data.length} documents to collection ${destinationCollectionName}`,
        );
      })
      .catch((error) => {
        console.error('Error during upsert operations:', error);
      });
    const after = new Date().getTime();
    console.log(
      `Migration script ${fileName} completed in ${after - before}ms`,
    );
  } catch (error) {
    console.error(`Error in migration script ${fileName}: ${error}`);
  }
};
export default up;
