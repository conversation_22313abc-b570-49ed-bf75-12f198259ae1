import { registerAs } from '@nestjs/config';

export default registerAs('migration', () => ({
  sourceDbUri: `${process.env.SOURCE_DB_URI || ''}`,
  inventorySourceDbUri: `${process.env.INVENTORY_SOURCE_DB_URI || ''}`,
  destinationDbUri: `${process.env.DESTINATION_DB_URI || ''}`,
  generalDbUri: `${process.env.GENERAL_DB_URI || ''}`,
  listMigrationVersions: `${process.env.LIST_MIGRATION_VERSIONS || ''}`,
}));
