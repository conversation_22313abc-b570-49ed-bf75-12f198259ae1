import { HttpStatus, Injectable, Logger } from '@nestjs/common';
import dayjs from 'dayjs';
import { Model } from 'mongoose';

import { LentoIntegrateErrorCodesEnum } from '~/shared/enums/lento-integrate.enum';
import { LentoIntegrateException } from '~/shared/exception/lento-integrate-exception.dto';
import { InjectModel } from '~/transformers/model.transformer';

import { LocationModel } from '../location/location.model';
import { UnitModel } from '../unit/unit.model';
import { CheckOutService } from './check-out/check-out.service';
import { NightRegistrationReservationModel } from './models/night-registration-reservation.model';

@Injectable()
export class TenantMovedService {
  private readonly logger = new Logger(TenantMovedService.name);

  constructor(
    @InjectModel(NightRegistrationReservationModel)
    private readonly reservationModel: Model<NightRegistrationReservationModel>,
    @InjectModel(LocationModel)
    private readonly locationModel: Model<LocationModel>,
    @InjectModel(UnitModel)
    private readonly unitModel: Model<UnitModel>,
    private readonly checkOutService: CheckOutService,
  ) {}

  async processTenantMoved(payload: any) {
    try {
      const { originalBody } = payload;
      const {
        resident: reqResident,
        fromLocation,
        toLocation,
        fromRoom,
        toRoom,
        moveDate,
        remarks,
      } = originalBody;

      const resident = await this.checkOutService.findOneResident(reqResident);
      let errorMessage = '';
      const processName = 'Tenant Moved';

      if (!resident) {
        errorMessage = 'Resident not found';
        throw new LentoIntegrateException(
          errorMessage,
          HttpStatus.NOT_FOUND,
          LentoIntegrateErrorCodesEnum.RESIDENT_NOT_FOUND,
          processName,
        );
      }

      const fromLocationInfo = await this.locationModel
        .findOne({
          fullAddress: fromLocation,
        })
        .populate({
          path: 'units',
          match: { isActive: true },
          populate: 'parent',
          select: 'name isRoot parent maxOccupants position',
        })
        .lean();

      if (!fromLocationInfo) {
        errorMessage = `Location not found for address: ${fromLocation}`;
        throw new LentoIntegrateException(
          errorMessage,
          HttpStatus.NOT_FOUND,
          LentoIntegrateErrorCodesEnum.LOCATION_NOT_FOUND,
          processName,
        );
      }

      const fromTargetUnit = await this.unitModel.findOne({
        name: fromRoom.description.name,
        location: fromLocationInfo._id,
      });

      if (!fromTargetUnit) {
        errorMessage = `Unit not found: ${fromRoom.description.name}`;
        throw new LentoIntegrateException(
          errorMessage,
          HttpStatus.NOT_FOUND,
          LentoIntegrateErrorCodesEnum.UNIT_NOT_FOUND,
          processName,
        );
      }

      const toLocationInfo = await this.locationModel
        .findOne({
          fullAddress: toLocation,
        })
        .lean();

      if (!toLocationInfo) {
        errorMessage = `Target location not found for address: ${toLocation}`;
        throw new LentoIntegrateException(
          errorMessage,
          HttpStatus.NOT_FOUND,
          LentoIntegrateErrorCodesEnum.TARGET_LOCATION_NOT_FOUND,
          processName,
        );
      }

      const toTargetUnit = await this.unitModel.findOne({
        name: toRoom.description.name,
        location: toLocationInfo._id,
      });

      if (!toTargetUnit) {
        errorMessage = `Target unit not found: ${toRoom.description.name}`;
        throw new LentoIntegrateException(
          errorMessage,
          HttpStatus.NOT_FOUND,
          LentoIntegrateErrorCodesEnum.TARGET_UNIT_NOT_FOUND,
          processName,
        );
      }

      const reservation = (
        await this.reservationModel
          .find({
            isDeleted: false,
            resident: resident._id,
            unit: fromTargetUnit._id,
          })
          .sort({ arrivalDate: -1 })
          .limit(1)
          .lean()
      )[0];

      if (!reservation) {
        errorMessage =
          'Reservation not found for the resident in the specified unit';
        throw new LentoIntegrateException(
          errorMessage,
          HttpStatus.NOT_FOUND,
          LentoIntegrateErrorCodesEnum.RESERVATION_NOT_FOUND,
          processName,
        );
      }

      const bed = toRoom.beds.single + toRoom.beds.double;

      const isExisted = await this.checkBedReserved(
        toTargetUnit,
        bed,
        reservation._id,
      );

      if (isExisted) {
        errorMessage = `Bed ${bed} is already reserved in the target unit`;
        throw new LentoIntegrateException(
          errorMessage,
          HttpStatus.NOT_FOUND,
          LentoIntegrateErrorCodesEnum.BED_ALREADY_RESERVED,
          processName,
        );
      }

      this.logger.log('Tenant moved data processed successfully');

      return this.reservationModel.updateOne(
        { _id: reservation._id },
        {
          $set: {
            arrivalDate: moveDate,
            departureDate: null,
            bed,
            isVirtual: false,
            remarks,
            unit: toTargetUnit._id,
            location: toLocationInfo._id,
          },
        },
      );
    } catch (error: any) {
      this.logger.error('Failed to process tenant moved data:', error.message);
      throw error;
    }
  }

  private async checkBedReserved(unit, bed, id) {
    const existedReservation = await this.reservationModel
      .find({
        $or: [
          {
            _id: { $ne: id },
            unit,
            bed,
            $or: [
              {
                departureDate: { $gt: dayjs().utc().toDate() },
              },
              {
                departureDate: null,
              },
            ],
          },
          {
            isVirtual: true,
            unit,
            bed,
          },
        ],
      })
      .lean();

    return existedReservation.length > 0;
  }
}
