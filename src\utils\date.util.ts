import dayjs, { Dayjs } from 'dayjs';

import { DATE_FORMAT_SLASH } from '~/constants/app.constant';
import { Period } from '~/modules/contract/contract.helper';

export type DateLike = string | Date | Dayjs;

export function parseRangeDates(fStartDate?: string, fEndDate?: string) {
  let djsStartDate = null as Dayjs | null;
  let djsEndDate = null as Dayjs | null;

  if (fStartDate) {
    djsStartDate = dayjs(fStartDate).utc().startOf('day');
  }

  if (fEndDate) {
    djsEndDate = dayjs(fEndDate).utc().endOf('day');
  }

  return { djsStartDate, djsEndDate };
}

export function isOddWeekOfYear(date: string, timezone: string = '+00:00') {
  const djsDate = dayjs(date).utcOffset(timezone);
  const weekOfYear = djsDate.isoWeek();
  return weekOfYear % 2 === 1;
}

export function isValidDate(date: string) {
  return dayjs(date).format('YYYY-MM-DD') === date.split('T')[0];
}

export function startAndEndOfIsoWeek(
  isoWeek: number,
  year: number,
  timezone: string = '+00:00',
) {
  const dayjsBase = dayjs()
    .utcOffset(timezone)
    .set('year', year)
    .startOf('year')
    .isoWeek(isoWeek);
  return {
    startDate: dayjsBase.startOf('isoWeek').toDate(),
    endDate: dayjsBase.endOf('isoWeek').toDate(),
  };
}

export function isSameNullSafe(
  date1?: DateLike | null,
  date2?: DateLike | null,
  unit?: dayjs.OpUnitType,
) {
  if (date1) {
    return dayjs(date1).isSame(date2, unit);
  } else if (date2) {
    return dayjs(date2).isSame(date1, unit);
  } else {
    return true;
  }
}

export function getPeriodDiff(comparePeriod: Period, baseDate: Period) {
  const compareStartDate = dayjs(comparePeriod.start);
  const baseStartDate = dayjs(baseDate.start);
  const compareEndDate = dayjs(comparePeriod.end);
  const baseEndDate = dayjs(baseDate.end);

  // negative means compareDate is before baseDate
  const diffStart = compareStartDate.diff(baseStartDate, 'days');
  const diffEnd = compareEndDate.diff(baseEndDate, 'days');

  return { diffStart, diffEnd };
}

export function formatDate(date: DateLike, format: string = DATE_FORMAT_SLASH) {
  return dayjs(date).utc().format(format);
}
