import { getModelForClass } from '@typegoose/typegoose';
import { Decimal128, ObjectId } from 'mongodb';
import { z } from 'zod';

import { TenantPermissionModel } from '~/modules/tenant-permission/tenant-permission.model';
import { tenantPermissionTest } from '~/modules/tenant-permission/test/tenant-permission.dto.test';

const tenantPermissionModel = getModelForClass(TenantPermissionModel);
type tenantPermissionType = z.infer<typeof tenantPermissionTest.modelSchema>;

export const mockTenantPermissionData = {
  _id: new ObjectId(),
  isDeleted: false,
  isActive: true,
  key: 'job_read',
  decimal: Decimal128.fromString('4'),
  createdAt: new Date(),
  updatedAt: new Date(),
};

export async function initMockTenantPermission(
  doc?: Partial<tenantPermissionType>,
) {
  const { _id, ...rest } = { ...mockTenantPermissionData, ...doc };
  await tenantPermissionModel.replaceOne({ _id }, rest, { upsert: true });
}
