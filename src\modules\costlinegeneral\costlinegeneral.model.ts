import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import {
  AgreementLineDocument,
  AgreementLineModel,
} from '../agreementline/agreementline.model';
import { CostTypeDocument, CostTypeModel } from '../costtype/costtype.model';
import { UnitDocument, UnitModel } from '../unit/unit.model';

export type CostLineGeneralDocument = DocumentType<CostLineGeneralModel>;

@modelOptions({
  options: {
    customName: 'CostLineGeneral',
  },
})
@index({ futureGenerationDate: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class CostLineGeneralModel extends BaseModel {
  @prop({ required: true })
  description!: string;

  @prop({ default: 0 })
  position!: number;

  @prop({ default: 0 })
  price!: number;

  @prop({ default: 1 })
  quantity!: number;

  @prop({ required: true })
  startDate!: Date;

  @prop()
  endDate!: Date;

  @prop({ required: false, index: true })
  futureGenerationDate?: Date;

  @prop({ ref: () => UnitModel })
  unit!: Ref<UnitDocument>;

  @prop({ ref: () => AgreementLineModel })
  agreementLine!: Ref<AgreementLineDocument>;

  @prop({ ref: () => CostTypeModel })
  costType!: Ref<CostTypeDocument>;
}
