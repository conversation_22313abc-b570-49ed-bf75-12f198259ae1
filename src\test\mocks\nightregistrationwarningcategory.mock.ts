import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';

import { NightRegistrationWarningCategoryModel } from '~/modules/night-registration/models/night-registration-warning-category';

const nightRegistrationWarningCategoryModel = getModelForClass(
  NightRegistrationWarningCategoryModel,
);

export const mockNightRegistrationWarningCategoryData = {
  _id: new ObjectId(),
  category: 'Causing fire risk',
  identifier: nanoid(9),
  isDeleted: false,
};

export async function initMockNightRegistrationWarningCategory(doc?: any) {
  const { _id, ...rest } = {
    ...mockNightRegistrationWarningCategoryData,
    ...doc,
  };
  await nightRegistrationWarningCategoryModel.replaceOne({ _id }, rest, {
    upsert: true,
  });
}
