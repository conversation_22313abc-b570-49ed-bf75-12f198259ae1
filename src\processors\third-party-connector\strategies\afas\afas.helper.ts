import dayjs from 'dayjs';

import { DATE_FORMAT_HYPHEN } from '~/constants/app.constant';
import { CostCenterModel } from '~/modules/costcenter/costcenter.model';
import { CostTypeModel } from '~/modules/costtype/costtype.model';
import { AFASCostCenter } from '~/processors/third-party-connector/strategies/afas/dtos/afas-cost-center.dto';
import { AFASCostType } from '~/processors/third-party-connector/strategies/afas/dtos/afas-cost-type.dto';
import {
  AFASDebtor,
  TransformedAFASDebtor,
} from '~/processors/third-party-connector/strategies/afas/dtos/afas-debtor.dto';
import {
  AfasDirectInvoice,
  AfasDirectInvoiceLine,
} from '~/processors/third-party-connector/strategies/afas/dtos/afas-invoice.dto';
import { ContactRole, ContactType } from '~/shared/enums/contact.enum';
import {
  AgreementLinePeriodType,
  ContractType,
} from '~/shared/enums/contract.enum';
import { CostTypeType } from '~/shared/enums/cost-type.enum';

export const formatAfasDate = (date?: Date) =>
  dayjs(date || new Date())
    .utc()
    .format('YYYY-MM-DD');

export const transformAfasCostType = (
  afasData: AFASCostType,
): CostTypeModel => {
  return {
    itemCode: afasData.ItemCode,
    name: afasData.Description,
    type: transformTypeOfCostType(afasData.ExtraDescription),
    isActive: true,
    isDeleted: false,
    isSynced: true,
    createdAt: dayjs(afasData.CreateDate).utc().toDate(),
    updatedAt: dayjs(afasData.ModifiedDate).utc().toDate(),
  };
};

export const transformTypeOfCostType = (
  extraDescription?: string,
): CostTypeType => {
  switch (extraDescription) {
    case 'Contract':
      return CostTypeType.CONTRACT;
    case 'Job / Custom':
    default:
      return CostTypeType.JOB_OR_CUSTOM;
  }
};

export const transformAfasCostCenter = (
  afasData: AFASCostCenter,
): CostCenterModel => {
  return {
    identifier: afasData.Code_verbijzondering,
    name: afasData.Omschrijving,
    isActive: true,
    isSynced: true,
  };
};

export const transformAfasDebtor = (
  afasData: AFASDebtor,
): TransformedAFASDebtor => {
  return {
    identifier: afasData.Verkooprelatie,
    name: afasData.Name,
    displayName: afasData.Name,
    contactRole: ContactRole.DEBTOR,
    contactType: ContactType.ORGANIZATION,
    orgNumber: afasData.OrgNumber,
    phone1: afasData.TelWork,
    email: afasData.MailWork,
    // website: afasData.Website, // Website data is not available in AFAS
    isActive: true,
    isDeleted: false,
    isSynced: true,
    address1: {
      street: afasData.Straat,
      houseNumber: afasData.Huisnummer,
      postCode: afasData.Postcode,
      city: afasData.Woonplaats,
    },
    country: {
      code: afasData.Land.toLowerCase(),
      name: afasData.Land_2,
    },
    ...(afasData.Omschrijving && {
      region: {
        name: afasData.Omschrijving,
      },
    }),
  };
};

export const transformAfasDirectInvoice = (
  invoice: any,
  dlad: string = '2889',
): AfasDirectInvoice => {
  const location =
    invoice.type === ContractType.SERVICE
      ? invoice.costCenters[0]?.locations[0]
      : invoice.locations[0];
  const countryCode = getCountryCode(invoice, location);
  const isDE = countryCode === 'de';
  const bvCompanyIdentifier = getCompanyIdentifier(invoice, location);

  return {
    FbDirectInvoice: {
      Element: {
        Fields: {
          DbId: invoice.contact.identifier,
          RfCs: invoice.identifier,
          Unit: bvCompanyIdentifier ? Number(bvCompanyIdentifier) : null,
          ...(isDE && {
            VaDu: '5',
            DlAd: dlad,
          }),
        },
        Objects: [
          {
            FbDirectInvoiceLines: {
              Element: invoice.locationCostLines.flatMap(
                (locationCostLine: { costLines: any[] }) =>
                  locationCostLine.costLines.map(
                    (costLine: any, index: number) =>
                      transformAfasDirectInvoiceLine({
                        costLine,
                        periodType: invoice.periodType,
                        index,
                      }),
                  ),
              ),
            },
          },
        ],
      },
    },
  };
};

export const transformAfasDirectInvoiceLine = ({
  costLine,
  periodType,
  index,
}: {
  costLine: any;
  periodType: AgreementLinePeriodType;
  index: number;
}): AfasDirectInvoiceLine => {
  const totalPrice = costLine.totalPrice.toFixed(2);
  const baseDescription = costLine.description ?? '';
  const dateRange = `${dayjs(costLine.startDate).format(DATE_FORMAT_HYPHEN).toString()} ${
    costLine.endDate
      ? 't/m ' + dayjs(costLine.endDate).format(DATE_FORMAT_HYPHEN).toString()
      : ''
  }`;
  let locationDescription: string = '';
  if (
    costLine.type !== ContractType.SERVICE &&
    index === 0 &&
    costLine.location?.address
  ) {
    const { city, street, number, suffix } = costLine.location.address;
    locationDescription = `${city}, ${street} ${number} ${suffix ?? ''}`.trim();
  }

  const descriptionSegments: string[] = [];

  switch (costLine.type) {
    case ContractType.SERVICE: {
      descriptionSegments.push(baseDescription);
      if (index === 0) {
        descriptionSegments.push(`- ${dateRange}`);
      }
      break;
    }
    case ContractType.RENTING: {
      descriptionSegments.push(baseDescription);
      if (index === 0) {
        descriptionSegments.push(`- ${locationDescription}`);
        descriptionSegments.push(`- ${dateRange}`);
      }
      break;
    }
    default: {
      descriptionSegments.push(baseDescription);
      if (index === 0) {
        descriptionSegments.push(`- ${locationDescription}`);
      }
      if (costLine.job) {
        descriptionSegments.push(`- Jobreferentie: ${costLine.job.identifier}`);
      }
      if (periodType === AgreementLinePeriodType.PERIODIC) {
        descriptionSegments.push(`- ${dateRange}`);
      }

      break;
    }
  }

  return {
    Fields: {
      VaIt: '2',
      ItCd: costLine.costType.itemCode,
      Ds: descriptionSegments.join(' '),
      BiUn: 'STK',
      QuUn: '1',
      Qu: '1',
      Upri: totalPrice,
      CoPr: totalPrice,
      V1Cd:
        costLine.costCenter?.identifier ??
        costLine.location?.costCenter?.identifier ??
        '',
      UE40B6A14A62F450F80EED84ABE2AC52F: formatAfasDate(costLine.startDate),
      U774BDD4BD90F42E7B8DDE7F90C1FC3CC: formatAfasDate(costLine.endDate),
    },
  };
};

const getCountryCode = (invoice: any, location): string => {
  switch (invoice.type) {
    case ContractType.SERVICE:
      return location?.address?.country?.code;
    case ContractType.CUSTOM: {
      if (location) {
        return location?.address?.country?.code;
      }

      return invoice.locationCostLines[0]?.costLines[0]?.country?.code;
    }
    default:
      return location?.address?.country?.code;
  }
};

const getCompanyIdentifier = (invoice: any, location: any) => {
  switch (invoice.type) {
    case ContractType.SERVICE:
      return location.bvCompany?.identifier;
    case ContractType.CUSTOM: {
      if (location) {
        return location.bvCompany?.identifier;
      }

      return invoice.locationCostLines[0]?.costLines[0]?.bvCompany?.identifier;
    }
    default:
      return location.bvCompany?.identifier;
  }
};
