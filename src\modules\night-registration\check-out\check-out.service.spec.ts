import { Test, TestingModule } from '@nestjs/testing';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';

import { LocationModel } from '~/modules/location/location.model';
import { NightRegistrationReservationModel } from '~/modules/night-registration/models/night-registration-reservation.model';
import { NightRegistrationResidentModel } from '~/modules/night-registration/models/night-registration-resident.model';
import { UnitModel } from '~/modules/unit/unit.model';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { initMockNightRegistrationReservation } from '~/test/mocks/nightregistrationreservation.mock';
import {
  initMockNightRegistrationResident,
  mockNightRegistrationResidentData,
} from '~/test/mocks/nightregistrationresident.mock';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';
import { getModelToken } from '~/transformers/model.transformer';

import { CheckOutService } from './check-out.service';

describe('CheckOutService', () => {
  let service: CheckOutService;
  let nightRegistrationReservationModel: MongooseModel<NightRegistrationReservationModel>;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        CheckOutService,
        ...testInjectModel([
          NightRegistrationResidentModel,
          NightRegistrationReservationModel,
          LocationModel,
          UnitModel,
        ]),
      ],
    }).compile();

    service = module.get<CheckOutService>(CheckOutService);
    nightRegistrationReservationModel = module.get(
      getModelToken(NightRegistrationReservationModel.name),
    );

    const subUnitId = new ObjectId();

    await Promise.all([
      initMockUnit({
        parent: null,
        location: mockLocationData._id,
        isRoot: false,
      }),
      initMockUnit({
        _id: subUnitId,
        name: 'A',
        isRoot: false,
        parent: mockUnitData._id,
        maxOccupants: 3,
      }),
      initMockLocation({
        units: [mockUnitData._id, subUnitId],
      }),
      initMockNightRegistrationReservation({
        unit: subUnitId,
        bed: 1,
        arrivalDate: dayjs().utc().subtract(20, 'day').toDate(),
      }),
      initMockNightRegistrationReservation({
        _id: new ObjectId(),
        unit: subUnitId,
        bed: 2,
        arrivalDate: dayjs().utc().subtract(19, 'day').toDate(),
      }),
      initMockNightRegistrationResident({
        unit: subUnitId,
      }),
    ]);
  });

  describe('checkOut', () => {
    it('should check out a resident successfully', async () => {
      const payload = {
        originalBody: {
          resident: {
            firstName: mockNightRegistrationResidentData.firstName,
            lastName: mockNightRegistrationResidentData.lastName,
          },
          location: mockLocationData.fullAddress,
          unit: {
            details: {
              description: {
                name: mockUnitData.name,
              },
            },
          },
          room: {
            description: {
              name: 'A',
            },
          },
        },
      };

      const result = await service.checkOut(payload);

      expect(result).toBeDefined();
      expect(result.acknowledged).toBe(true);
    });

    it('should throw an error if resident not found', async () => {
      const payload = {
        originalBody: {
          resident: {
            firstName: 'Nonexistent',
            lastName: 'Resident',
          },
          location: 'Zwolle, Gein 63',
          unit: {
            details: {
              description: {
                name: mockUnitData.name,
              },
            },
          },
          room: {
            description: {
              name: mockUnitData.name,
            },
          },
        },
      };

      await expect(service.checkOut(payload)).rejects.toThrow(
        `Resident not found`,
      );
    });

    it('should throw an error if location not found', async () => {
      const payload = {
        originalBody: {
          resident: {
            firstName: mockNightRegistrationResidentData.firstName,
            lastName: mockNightRegistrationResidentData.lastName,
          },
          location: 'Nonexistent Location',
          unit: {
            details: {
              description: {
                name: mockUnitData.name,
              },
            },
          },
          room: {
            description: {
              name: mockUnitData.name,
            },
          },
        },
      };

      await expect(service.checkOut(payload)).rejects.toThrow(
        `Location not found for address: ${payload.originalBody.location}`,
      );
    });

    it('should throw an error if unit not found', async () => {
      const payload = {
        originalBody: {
          resident: {
            firstName: mockNightRegistrationResidentData.firstName,
            lastName: mockNightRegistrationResidentData.lastName,
          },
          location: mockLocationData.fullAddress,
          unit: {
            details: {
              description: {
                name: mockUnitData.name,
              },
            },
          },
          room: {
            description: {
              name: 'Nonexistent Unit',
            },
          },
        },
      };

      await expect(service.checkOut(payload)).rejects.toThrow(
        `Sub-unit not found: ${payload.originalBody.room.description.name}`,
      );
    });

    it('should throw an error if no active reservation found', async () => {
      const payload = {
        originalBody: {
          resident: {
            firstName: mockNightRegistrationResidentData.firstName,
            lastName: mockNightRegistrationResidentData.lastName,
          },
          location: mockLocationData.fullAddress,
          unit: {
            details: {
              description: {
                name: mockUnitData.name,
              },
            },
          },
          room: {
            description: {
              name: 'A',
            },
          },
        },
      };

      // Remove all reservations for the resident
      await nightRegistrationReservationModel.deleteMany({});

      await expect(service.checkOut(payload)).rejects.toThrow(
        'Reservation not found for the resident in the specified unit',
      );
    });
  });
});
