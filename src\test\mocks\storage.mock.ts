import { ObjectId } from 'mongodb';
import mongoose from 'mongoose';
import { nanoid } from 'nanoid';

const storageModel = mongoose.connection.collection('storage');

export const mockStorageData = {
  _id: new ObjectId(),
  createdAt: new Date(),
  description: '<PERSON><PERSON><PERSON><PERSON> Vlijmen',
  identifier: nanoid(3),
  isActive: true,
  isDeleted: false,
  type: 'Warehouse',
  updatedAt: new Date(),
};

export async function initMockStorage(doc?: any) {
  const { _id, ...rest } = { ...mockStorageData, ...doc };
  await storageModel.replaceOne({ _id }, rest, { upsert: true });
}
