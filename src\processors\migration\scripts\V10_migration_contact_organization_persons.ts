import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldPerson {
  _id: string;
  active: boolean;
  person: Record<string, any>;
  grouping: Record<string, any>;
  roleFunction: string;
}

const organizationPeoplePipeLineAggregate = (skip: number, limit: number) => {
  return [
    {
      $lookup: {
        from: 'person',
        localField: 'person',
        foreignField: '_id',
        as: 'person',
      },
    },
    {
      $unwind: {
        path: '$person',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $lookup: {
        from: 'grouping',
        localField: 'grouping',
        foreignField: '_id',
        as: 'grouping',
      },
    },
    {
      $unwind: {
        path: '$grouping',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        grouping: { $ne: null },
        person: { $ne: null },
      },
    },
    { $skip: skip },
    {
      $limit: limit,
    },
  ];
};

const tranformDataFunc = ({ data }: { data: OldPerson[]; context: any }) => {
  return Promise.all(
    data.map(async (item) => {
      const tranformed = {
        _id: item._id,
        isActive: true,
        person: item.person._id,
        organization: item.grouping._id,
        roleFunction: item.roleFunction || '',
      };

      return tranformed;
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('persongrouping');

  const pipeline = organizationPeoplePipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'persongrouping',
      destinationCollectionName: 'contactorganizationpeople',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
