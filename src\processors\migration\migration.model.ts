import { DocumentType, index, prop } from '@typegoose/typegoose';
import { modelOptions } from '@typegoose/typegoose';

import { BaseModel } from '~/shared/models/base.model';

export type MigrationDocument = DocumentType<MigrationModel>;

export enum MigrationStatus {
  PENDING = 'PENDING',
  SUCCESS = 'SUCCESS',
  FAILED = 'FAILED',
}

@modelOptions({
  options: { customName: 'Migration' },
})
@index({ version: 1 }, { unique: true })
export class MigrationModel extends BaseModel {
  @prop()
  version!: number;

  @prop()
  script!: string;

  @prop({ default: MigrationStatus.PENDING, enum: MigrationStatus })
  status!: MigrationStatus;
}
