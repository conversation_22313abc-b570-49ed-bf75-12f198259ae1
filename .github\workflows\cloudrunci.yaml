name: cloudrunci
on:
  push:
    branches: 
      - main
env:
  GCP_PROJECT_ID: ee-acc-v2 # {"$kpt-set":"project"}
  GCP_REPOSITORY: ee-acc-v2
  GCP_APP_NAME: ee-acc-v2-core # {"$kpt-set":"app"} 
  CLOUDRUN_APP_NAME_TENANT1: ee-acc-v2-tenant1
  CLOUDRUN_APP_NAME_LOGEJO: ee-acc-v2-tenant-logejo
  CLOUDRUN_APP_NAME_TENANT2: ee-acc-v2-tenant2
  CLOUDRUN_REGION: europe-west4
  DOCKER_GCP_REGISTRY: europe-west4-docker.pkg.dev

jobs:
  build:
    runs-on: ubuntu-latest
    # defaults:
    #   run:
    #     working-directory: src
    steps:
      - uses: actions/checkout@v2
        with: 
          fetch-depth: '0'
          
      - name: Bump version and push tag dry-run
        id: tag_version
        uses: mathieudutour/github-tag-action@v5.1
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          dry_run: true          
    
      - id: 'auth'
        uses: 'google-github-actions/auth@v1'
        with:
          token_format: 'access_token'
          credentials_json: '${{ secrets.GOOGLE_CREDENTIALS_V2 }}'  

      - uses: 'docker/login-action@v1'
        with:
          registry: ${{ env.DOCKER_GCP_REGISTRY }}
          username: 'oauth2accesstoken'
          password: '${{ steps.auth.outputs.access_token }}'  

      - name: Use Node.js
        uses: actions/setup-node@v1
        with:
          node-version: '18.x'

      - name: Cache Node.js modules
        uses: actions/cache@v4
        with:
          # npm cache files are stored in `~/.npm` on Linux/macOS
          path: ~/.npm
          key: ${{ runner.OS }}-node-${{ hashFiles('**/package-lock.json') }}
          restore-keys: |
            ${{ runner.OS }}-node-
            ${{ runner.OS }}- 


      - name: Build Docker image 
        run: docker build  --build-arg GITHUB_VERSION=${{ github.sha }} -f Dockerfile --tag ${{ env.DOCKER_GCP_REGISTRY }}/${{ env.GCP_PROJECT_ID }}/${{ env.GCP_REPOSITORY }}/${{ env.GCP_APP_NAME }}:${{ steps.tag_version.outputs.new_version }} .

      - name: Push Docker image
        run: docker push ${{ env.DOCKER_GCP_REGISTRY }}/${{ env.GCP_PROJECT_ID }}/${{ env.GCP_REPOSITORY }}/${{ env.GCP_APP_NAME }}:${{ steps.tag_version.outputs.new_version }}
            
      - name: Create Release
        uses: actions/create-release@v1
        with:
          tag_name: ${{ steps.tag_version.outputs.new_tag }}
          release_name: ${{ steps.tag_version.outputs.new_tag }}
          body: ${{ steps.tag_version.outputs.changelog }}
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      
    outputs:
      imagetag: ${{ steps.tag_version.outputs.new_version }}
          
  deploy-tenant-homee:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - id: 'auth'
        uses: 'google-github-actions/auth@v1'
        with:
          token_format: 'access_token'
          credentials_json: '${{ secrets.GOOGLE_CREDENTIALS_V2 }}'        
      - name: 'Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          version: '>= 363.0.0'

      - name: 'Deploy latest version to tenant1'
        run: gcloud run deploy $CLOUDRUN_APP_NAME_TENANT1 --region=asia-southeast1 --container=ee-acc-v2-core --image=$DOCKER_GCP_REGISTRY/$GCP_PROJECT_ID/$GCP_REPOSITORY/$GCP_APP_NAME:${{ needs.build.outputs.imagetag }}
            
  deploy-tenant-logejo:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - id: 'auth'
        uses: 'google-github-actions/auth@v1'
        with:
          token_format: 'access_token'
          credentials_json: '${{ secrets.GOOGLE_CREDENTIALS_V2 }}'        
      - name: 'Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          version: '>= 363.0.0'

      - name: 'Deploy latest version to tenant1'
        run: gcloud run deploy $CLOUDRUN_APP_NAME_LOGEJO --region=asia-southeast1 --container=ee-acc-v2-core --image=$DOCKER_GCP_REGISTRY/$GCP_PROJECT_ID/$GCP_REPOSITORY/$GCP_APP_NAME:${{ needs.build.outputs.imagetag }}
            

  deploy-tenant2:
    needs: build
    runs-on: ubuntu-latest
    steps:
      - id: 'auth'
        uses: 'google-github-actions/auth@v1'
        with:
          token_format: 'access_token'
          credentials_json: '${{ secrets.GOOGLE_CREDENTIALS_TENANT_A }}'        
      - name: 'Set up Cloud SDK'
        uses: 'google-github-actions/setup-gcloud@v2'
        with:
          version: '>= 363.0.0'

      - name: 'Deploy latest version to tenant2'
        run: gcloud run deploy $CLOUDRUN_APP_NAME_TENANT2 --region=asia-southeast1  --container=ee-acc-v2-core --image=$DOCKER_GCP_REGISTRY/$GCP_PROJECT_ID/$GCP_REPOSITORY/$GCP_APP_NAME:${{ needs.build.outputs.imagetag }}

  notify-teams:
    needs: [deploy-tenant-homee, deploy-tenant-logejo, deploy-tenant2]
    runs-on: ubuntu-latest
    steps:
      - name: Notify dedicated teams channel
        uses: jdcargile/ms-teams-notification@v1.3
        with:
            github-token: ${{ secrets.GITHUB_TOKEN }}
            ms-teams-webhook-uri: "https://infodation.webhook.office.com/webhookb2/58e27a6e-de2e-4a43-9090-bc9caa6aa7a7@7c11714d-e26d-4173-b8a0-713f2d1b6287/IncomingWebhook/d9c5977c50414c13b31c7b67738d446f/e7ab8f49-024c-46a2-a5ba-db6c2c4b5a0c/V2TEK-3R2CidL3em9kZsTapwrA57FPhECJvYe2_577FTU1"
            notification-summary: "[EEAC][MULTI-TENANT][STAGING][CORE] - Deployment is successful. &#x2705; &#x2705;"
            notification-color: 17a2b8
            timezone: Asia/Ho_Chi_Minh

      
       