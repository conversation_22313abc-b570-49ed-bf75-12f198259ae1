import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import _, { template } from 'lodash';
import { Model } from 'mongoose';
import { createTransport, Transporter } from 'nodemailer';
import SMTPTransport from 'nodemailer/lib/smtp-transport';

import { EmailTemplateService } from '~/modules/email-template/email-template.service';
import { TenantModel } from '~/modules/tenant/tenant.model';
import { InjectModel } from '~/transformers/model.transformer';
import { renderTextTemplate } from '~/utils/render-text-template.util';

import { MyLogger } from '../logger/logger.service';
import { SendEmailDto, SendErrorEmailToLentoDto } from './dtos/send-email.dto';

@Injectable()
export class EmailService {
  private transporter: Transporter;

  constructor(
    @InjectModel(TenantModel)
    private readonly tenantModel: Model<TenantModel>,
    private readonly configService: ConfigService,
    private readonly logger: MyLogger,
    private readonly emailTemplateService: EmailTemplateService,
  ) {
    const options: SMTPTransport.Options = {
      host: this.configService.get<string>('email.host'),
      port: this.configService.get<number>('email.port'),
      secure: this.configService.get<boolean>('email.isSecure'),
      auth: {
        user: this.configService.get<string>('email.user'),
        pass: this.configService.get<string>('email.password'),
      },
      tls: {
        rejectUnauthorized: this.configService.get<boolean>(
          'email.tls.rejectUnauthorized',
        ),
      },
    };

    this.transporter = createTransport(options, {
      from: {
        name: 'No-reply',
        address: this.configService.get<string>('email.from'),
      },
    } as SMTPTransport.Options);

    // verify connection configuration
    this.transporter.verify((error) => {
      if (error) {
        this.logger.error(error.stack);
        return;
      }

      this.logger.log('Server is ready to handle emails');
    });
  }

  async sendEmailWithTemplate(templateName: string, data: object) {
    const template =
      (await this.emailTemplateService.getEmailTemplateByName(templateName))!;

    const escapedData = _.mapValues(data, (value) => _.escape(value));

    return await this.transporter.sendMail({
      ...template,
      subject: renderTextTemplate(data, template.subject),
      text: renderTextTemplate(escapedData, template.text || template.html),
      html: renderTextTemplate(escapedData, template.html),
    });
  }

  async sendEmail(params: SendEmailDto) {
    return await this.transporter.sendMail({ ...params });
  }

  async sendErrorEmailToLento(data: SendErrorEmailToLentoDto) {
    const emailTemplate =
      await this.emailTemplateService.getEmailTemplateByName(
        'error_email_send_to_lento',
      );

    if (!emailTemplate) {
      this.logger.error(`Email template not found: error_email_send_to_lento`);
      return;
    }

    const receiverEmail = this.configService.get<string>(
      'app.lentoErrorReciverEmail',
    );

    if (!receiverEmail) {
      this.logger.error(`Lento error receiver email is not configured`);
      return;
    }

    const tenant = await this.tenantModel.findOne({}).lean();
    if (!tenant) {
      this.logger.error(`Tenant not found`);
      return;
    }

    data.TENANT = tenant.name;

    const html = emailTemplate.html;
    const compiledObject = template(html);
    const htmlTemplate = compiledObject(data);

    await this.sendEmail({
      html: htmlTemplate,
      text: htmlTemplate,
      to: [receiverEmail],
      subject: emailTemplate.subject,
      bcc: emailTemplate.bcc,
      cc: emailTemplate.cc,
    });
  }
}
