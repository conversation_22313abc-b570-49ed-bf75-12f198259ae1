import { spawn } from 'child_process';
import fs from 'fs';
import pLimit from 'p-limit';
import path from 'path';

const modulesDir = 'src/modules';
const reportsDir = 'dist/reports';

fs.mkdirSync(reportsDir, { recursive: true });

const modules = fs
  .readdirSync(modulesDir)
  .filter((name) => fs.existsSync(path.join(modulesDir, name, 'test')));

const limit = pLimit(3);

const runTest = (name: string) =>
  new Promise((res, rej) => {
    const testPath = `${modulesDir}/${name}/test`;
    const output = `${reportsDir}/ut_core_${name}_result.xml`;
    const outputJson = `${reportsDir}/ut_core_${name}_result.json`;

    const p = spawn(
      process.execPath,
      [
        'node_modules/jest/bin/jest.js',
        testPath,
        '--config=jest.config.ts',
        '--passWithNoTests',
      ],
      {
        env: {
          ...process.env,
          JEST_JUNIT_OUTPUT_FILE: output,
          JEST_CTRF_JSON_OUTPUT_FILE: outputJson,
        },
        stdio: 'inherit',
      },
    );

    p.on('close', (code) =>
      code
        ? rej(new Error(`❌ ${name} failed`))
        : (console.log(`✅ ${name}`), res(void 0)),
    );
  });

(async () => {
  try {
    await Promise.all(modules.map((name) => limit(() => runTest(name))));
  } catch (e) {
    console.error('🔥 Error:', e);
  }
})();
