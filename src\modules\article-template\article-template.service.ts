import { Injectable, NotFoundException } from '@nestjs/common';
import mongoose, { Model } from 'mongoose';

import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { ArticleTemplateModel } from './article-template.model';
import {
  CreateArticleTemplateDto,
  UpdateArticleTemplateDto,
} from './dtos/article-template.dto';

@Injectable()
export class ArticleTemplateService {
  constructor(
    @InjectModel(ArticleTemplateModel)
    private readonly articleTemplateModel: Model<ArticleTemplateModel>,
  ) {}

  async create(data: CreateArticleTemplateDto) {
    const convertedData = {
      ...data,
      storage: new mongoose.Types.ObjectId(data.storage), // Convert storage to ObjectId
      articleList: data.articleList.map((article) => ({
        ...article,
        article: new mongoose.Types.ObjectId(article.article), // Convert article to ObjectId
      })),
    };

    const articleTemplate =
      await this.articleTemplateModel.create(convertedData);
    return articleTemplate;
  }

  async findAll(params: QueryParamsDto) {
    const { _id } = params;
    const { query, options } = buildQuery(params, ['name']);

    if (_id) {
      query._id = { $ne: _id };
    }

    let offset = options.offset;
    let limit = options.limit;

    if (limit === -1 || parseInt(limit as any) === -1) {
      offset = 0;
      const count = await this.articleTemplateModel.countDocuments({
        ...query,
      });
      limit = count <= 0 ? 1000 : count;
    }

    const aggregate = await this.articleTemplateModel.aggregate([
      {
        $match: query,
      },
      {
        $skip: offset,
      },
      {
        $limit: limit,
      },
      {
        $sort: options.sort,
      },
      {
        $addFields: {
          storageObjId: { $toObjectId: '$storage' },
          articleIds: {
            $map: {
              input: '$articleList',
              as: 'a',
              in: { $toObjectId: '$$a.article' },
            },
          },
        },
      },
      // Get storage information
      {
        $lookup: {
          from: 'storage',
          localField: 'storageObjId',
          foreignField: '_id',
          as: 'storage',
        },
      },
      {
        $unwind: { path: '$storage', preserveNullAndEmptyArrays: true },
      },
      // Get articles
      {
        $lookup: {
          from: 'article',
          localField: 'articleIds',
          foreignField: '_id',
          as: 'articles',
        },
      },
      // Get category
      {
        $lookup: {
          from: 'articlecategory',
          localField: 'articles.category',
          foreignField: '_id',
          as: 'categories',
        },
      },
      // Get available amount from articlestorage
      {
        $lookup: {
          from: 'articlestorage',
          let: { storageId: '$storage._id', articleIds: '$articleIds' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ['$storage', '$$storageId'] },
                    { $in: ['$article', '$$articleIds'] },
                  ],
                },
              },
            },
          ],
          as: 'articleStorage',
        },
      },
      // rebuild articleList
      {
        $addFields: {
          articleList: {
            $map: {
              input: '$articleList',
              as: 'item',
              in: {
                _id: '$$item._id',
                amount: '$$item.amount',
                position: '$$item.position',
                article: {
                  $let: {
                    vars: {
                      articleId: { $toObjectId: '$$item.article' },
                      article: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: '$articles',
                              as: 'a',
                              cond: {
                                $eq: [
                                  '$$a._id',
                                  { $toObjectId: '$$item.article' },
                                ],
                              },
                            },
                          },
                          0,
                        ],
                      },
                      articleStorage: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: '$articleStorage',
                              as: 'as',
                              cond: {
                                $eq: [
                                  '$$as.article',
                                  { $toObjectId: '$$item.article' },
                                ],
                              },
                            },
                          },
                          0,
                        ],
                      },
                    },
                    in: {
                      _id: '$$article._id',
                      category: {
                        $arrayElemAt: [
                          {
                            $filter: {
                              input: '$categories',
                              as: 'cat',
                              cond: {
                                $eq: ['$$cat._id', '$$article.category'],
                              },
                            },
                          },
                          0,
                        ],
                      },
                      available: '$$articleStorage.availableAmount',
                      batchSize: '$$article.batchSize',
                      description: '$$article.description',
                      expectedDelivery: '$$article.expectedDelivery',
                      identifier: '$$article.identifier',
                      isActive: '$$article.isActive',
                      laborPricePerHour: '$$article.laborPricePerHour',
                      margin: '$$article.margin',
                      minimumStock: '$$article.minimumStock',
                      purchasePrice: '$$article.purchasePrice',
                      removalFee: '$$article.removalFee',
                      salePrice: '$$article.salePrice',
                      workingTime: '$$article.workingTime',
                    },
                  },
                },
              },
            },
          },
        },
      },
      // Sort by articleList.position
      {
        $addFields: {
          articleList: {
            $sortArray: {
              input: '$articleList',
              sortBy: { position: 1 },
            },
          },
        },
      },
      // Return the final result
      {
        $project: {
          name: 1,
          storage: { _id: '$storage._id', description: '$storage.description' },
          articleList: 1,
        },
      },
    ]);

    return aggregate;
  }

  async findOne(id: string) {
    const articleTemplate = await this.articleTemplateModel.findById(id).lean();

    if (!articleTemplate) {
      throw new NotFoundException('Article template not found');
    }

    return articleTemplate;
  }

  async update(data: UpdateArticleTemplateDto) {
    const updated = await this.articleTemplateModel
      .findByIdAndUpdate(data._id, data, { new: true })
      .lean();
    if (!updated) {
      throw new NotFoundException('Article template not found');
    }
    return updated;
  }

  async delete(id: string) {
    const articleTemplate = await this.articleTemplateModel.findById(id);

    if (!articleTemplate) {
      throw new NotFoundException('Article template not found');
    }

    return this.articleTemplateModel.findByIdAndDelete(id);
  }
}
