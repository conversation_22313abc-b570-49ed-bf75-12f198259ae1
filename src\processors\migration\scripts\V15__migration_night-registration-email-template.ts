import path from 'path';

import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const baseLayOut = `<!doctype html>
<html xmlns='http://www.w3.org/1999/xhtml' xmlns:v='urn:schemas-microsoft-com:vml'
    xmlns:o='urn:schemas-microsoft-com:office:office'>

<head>
    <title></title>
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
    <meta name='viewport' content='width=device-width,initial-scale=1'>
    <style type='text/css'>
        #outlook a {
            padding: 0
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0;
            mso-table-rspace: 0
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: 0;
            text-decoration: none;
            -ms-interpolation-mode: bicubic
        }

        p {
            display: block;
            margin: 13px 0
        }
    </style>
    <link href='https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700' rel='stylesheet' type='text/css'>
    <style type='text/css'>
        @import url(https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700);
    </style>
    <style type='text/css'>
        @media only screen and (min-width:480px) {
            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%
            }

            .mj-column-px-600 {
                width: 600px !important;
                max-width: 600px
            }
        }
    </style>
    <style media='screen and (min-width:480px)'>
        .moz-text-html .mj-column-per-100 {
            width: 100% !important;
            max-width: 100%
        }

        .moz-text-html .mj-column-px-600 {
            width: 600px !important;
            max-width: 600px
        }
    </style>
    <style type='text/css'></style>
    <style type='text/css'>
        .banner {
            background: url(<%= COMPANY_BANNER %>);
            background-size: cover;
            background-repeat: no-repeat;
            background-position: bottom right;
            padding-left: 18px !important
        }

        .text-banner {
            font-family: Arial, sans-serif !important;
            padding-left: 0 !important;
            padding-bottom: 28px !important;
            padding-top: 62px !important;
            text-align: left !important;
            color: #fff !important;
            font-size: 48px !important
        }

        .red-text {
            font-weight: 700 !important;
            color: #d2232a !important
        }

        .logo-eeac-wrapper img {
            vertical-align: middle !important
        }

        .logo-right img {
            vertical-align: middle !important
        }

        .logo-eeac {
            height: 36px !important;
            width: auto !important
        }

        .logo-linkedin {
            height: 28px !important
        }

        .logo-sfn {
            height: 40px !important
        }

        .first {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.2rem;
            text-align: left;
            color: #343a40;
            word-break: break-word;
            padding-top: 1px;
            padding-bottom: 1px
        }

        .last {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.2rem;
            text-align: left;
            color: #343a40;
            font-weight: 700;
            word-break: break-word;
            padding-top: 1px;
            padding-bottom: 1px;
            padding-left: 0px
        }

        @media only screen and (max-width:478px) {
            .container {
                padding: 0 0 !important
            }

            .banner {
                padding-left: 12px !important
            }

            .text-banner {
                font-size: 35px !important;
                padding-left: 0 !important;
                padding-bottom: 10px !important;
                padding-top: 30px !important
            }

            .section-header {
                width: 100% !important
            }

            .logo-linkedin {
                height: 18px !important
            }

            .logo-sfn {
                height: 30px !important
            }

            .logo-right {
                padding: 0 8px !important;
                height: 100% !important;
                width: max-content !important;
                line-height: 0
            }

            .logo-right .logo-linkedin-wrapper {
                display: inline-block;
                height: 100%;
                margin-right: 8px !important;
                vertical-align: middle !important
            }

            .logo-eeac-wrapper {
                display: inline-block;
                height: 100%;
                vertical-align: middle !important
            }

            .section-footer {
                max-width: 100% !important;
                padding-left: 2px !important
            }
        }
    </style>
</head>

<body style='word-spacing:normal'>
    <div style='padding-bottom: 100px'>
        <div class='container' style='margin:0 auto;max-width:600px'>
            <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation' style='width:100%'>
                <tbody>
                    <tr>
                        <td style='direction:ltr;font-size:0;padding:0;text-align:center'>
                            <div class='mj-column-per-100 mj-outlook-group-fix'
                                style='font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%'>
                                <table border='0' cellpadding='0' cellspacing='0' role='presentation'
                                    style='vertical-align:top' width='100%'>
                                    <tbody>
                                        <tr>
                                            <td align='left' class='banner'
                                                style='font-size:0;padding:10px 25px;word-break:break-word'>
                                                <table cellpadding='0' cellspacing='0' width='100%' border='0'
                                                    style='color:#000;font-family:Ubuntu,Helvetica,Arial,sans-serif;font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none'>
                                                    <tr>
                                                        <td class='text-banner'>
                                                            <div>You're home.</div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <div class='container' style='margin:0 auto;max-width:600px;padding-top: 50px'>
            <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation' style='width:100%'>
                <tbody>
                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-top:30px;padding-bottom:30px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:20px;font-weight:700;line-height:1;text-align:left;color:#343A40;'>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;font-style:italic;line-height:1.2rem;text-align:left;color:#343A40;'>
                                <%= LOCATION_NAME %><br>
                                    <%= UNIT %>, <%= BED %><br>
                                            <%= STREET %>
                                                <%= STREET_NUMBER %>
                                                    <%= SUFFIX %><br>
                                                        <%= POSTAL_CODE %>, <%= CITY %><br>
                                                                <%= REGION %><br>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343A40;'>
                                Dear resident,</div>
                        </td>
                    </tr>
                    {html}
                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343A40;'>
                                Kind regards,</div>
                        </td>
                    </tr>
                    <tr>
                        <td style='padding-top:20px'>
                            <div class='first'><strong class='first red-text text-sign'>
                                    <%=COMPANY_SIGNATURE %>
                                </strong><br><span class='red-text text-sign' style='margin-right:8px'>T</span><%= COMPANY_TELEPHONE %><br><span class='red-text text-sign'
                                        style='margin-right:8px'>E</span><a href='mailto:<%=COMPANY_EMAIL %>'
                                        target='_blank'><%=COMPANY_EMAIL %>
                                    </a><br><span class='red-text text-sign' style='margin-right:12px'>I</span>
                                    <a href='https://<%= COMPANY_WEBSITE %>' target='_blank'><%= COMPANY_WEBSITE %>
                                    </a><br><span class='text-sign'><%= COMPANY_ADDRESS1 %></span>
                                    <br><span class='text-sign'><%= COMPANY_ADDRESS2 %></span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    <div class='section-footer'
        style='background: <%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>; background-color: <%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>; margin: 0px auto; max-width: 100%;'>
        <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation'
            style='background:<%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>;background-color:<%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>;width:100%;'>
            <tbody>
                <tr>
                    <td style='direction:ltr;font-size:0px;padding:0;text-align:center;'>
                        <div class='mj-column-px-600 mj-outlook-group-fix'
                            style='font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:middle;width:100%;'>
                            <table border='0' cellpadding='0' cellspacing='0' role='presentation'
                                style='vertical-align:middle;' width='100%'>
                                <tbody>
                                    <tr>
                                        <td align='left' style='font-size:0px;padding:0;word-break:break-word;'>
                                            <table cellpadding='0' cellspacing='0' width='100%' border='0'
                                                style='color:#000000;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none;'>
                                                <tr>
                                                    <td> <a class='logo-eeac-wrapper' target='_blank'
                                                            href='https://<%= COMPANY_WEBSITE %>'
                                                            style='display: inline-block; height: 100%; vertical-align: middle;'>
                                                            <img class='logo-eeac' src='<%= COMPANY_LOGO %>' alt='logo'
                                                                width='100%'> </a> </td>
                                                    <td align='right'
                                                        style='width: 1%; white-space: nowrap; height: 100%; background: #fff;'>
                                                        <div class='logo-right'
                                                            style='display: inline-block; vertical-align: middle; background-color: #ffffff; padding: 0 20px; line-height: 0px; height: 100%; width: max-content;'>
                                                            <table cellpadding='0' cellspacing='0' width='100%'
                                                                border='0'>
                                                                <tr>
                                                                    <td> <a class='logo-linkedin-wrapper'
                                                                            target='_blank'
                                                                            href='<%= COMPANY_LINKEDIN_URL %>'
                                                                            style='width: max-content; display: inline-block; height: 100%; margin-right: 20px; vertical-align: middle;'>
                                                                            <img class='logo-linkedin'
                                                                                src='<%= COMPANY_LINKEDIN_LOGO %>'
                                                                                alt='logo'> </a> </td>
                                                                    <td> <img class='logo-sfn'
                                                                            src='<%= COMPANY_SFN_LOGO %>' alt='logo'>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div style='height:20px;line-height:20px;'> </div>
</body>

</html>`;

const htmlArrivalEmail = `                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343A40;'>
                                Welcome to your living location of <%= COMPANY_NAME %>. To complete your registration
                                    please read the statement below carefully.</div>
                        </td>
                    </tr>
                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343A40;'>
                                If you don’t agree with the statement or if you want to report any defects about the
                                accommodation and its inventory assigned to you, you have 24 hours to do so by informing
                                the location manager at this location. <ul style='margin: 0; padding-top: 10px'>
                                    <li> <span> I had a clear introduction by the location manager; </span> </li>
                                    <li> <span> I’m familiar with the living rules at the location and will comply with
                                            them. If I do not I’m aware this could have consequences for my residence at
                                            this location; </span> </li>
                                    <li> <span> I’m familiar with the evacuation regulations and the emergency escape
                                            plans; </span> </li>
                                    <li> <span> I have received the key of the accommodation and will return this key on
                                            departure. If I fail to return the key I will be charged with the costs of
                                            replacement; </span> </li>
                                    <li> <span> I’m aware that I have to announce my departure to the location manager
                                            before leaving; </span> </li>
                                    <li> <span> I’m informed that the accommodation has to be left in the same clean
                                            state in which I obtained it. If I fail to do so, I will be charged with the
                                            costs of replacement, repairs and/or cleaning of it; </span> </li>
                                </ul>
                            </div>
                        </td>
                    </tr>`;
const htmlBeforeDepartureEmail = `                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343A40;'>
                                We received information from your employer that you are scheduled to leave your
                                accommodation. Please take note of the following.</div>
                        </td>
                    </tr>
                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;padding-top: 0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343A40;'>
                                <ul style='margin: 0'>
                                    <li> <span> Inform the location manager about your intended departure time; </span>
                                    </li>
                                    <li> <span> Make an appointment with the location manager about when you will return
                                            the key; </span> </li>
                                    <li> <span> Departing residents are expected to leave the room tidy, i.e. 
                                    <ul  style='list-style-type: disc; margin: 0; padding-left: 25px'>
                                                <li style='list-style-type: circle;'> <span>Stripped of waste and personal belongings</span> </li>
                                                <li style='list-style-type: circle;'> <span>Matresses stripped of bedding <span
                                                            style='color: #D2232A'>(except for the blue
                                                            cover)</span></span> </li>
                                                <li style='list-style-type: circle;'> <span>Refrigerator emptied and cleaned</span> </li>
                                                <li style='list-style-type: circle;'> <span>Broom clean floor space and sanitary facilities</span> </li>
                                            </ul> </span> </li>
                                    <li> <span> After your departure your accommodation is inspected by the location
                                            manager and checked for cleanliness, defects and damage. After this
                                            inspection a report is prepared and sent to your employer; </span> </li>
                                    <li> <span> Return the room in the same state you received it to avoid being charged
                                            costs; </span> </li>
                                </ul>
                            </div>
                        </td>
                    </tr>`;
const htmlDepartureEmail = `                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343A40;'>
                                Today you are scheduled to leave your accommodation. Earlier you received an email in
                                which we asked you to take note of the following.</div>
                        </td>
                    </tr>
                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343A40;'>
                                If you have already responded to this, thank you!</div>
                        </td>
                    </tr>
                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343A40;'>
                                If not, one last reminder to keep in mind the following before you leave the
                                accommodation. <ul style='margin: 0; padding-top: 10px'>
                                    <li> <span> Inform the location manager about your intended departure time; </span>
                                    </li>
                                    <li> <span> Make an appointment with the location manager about when you will return
                                            the key; </span> </li>
                                    <li> <span> Departing residents are expected to leave the room tidy, i.e. 
                                            <ul style='list-style-type: disc; margin: 0;  padding-left: 25px'>
                                                <li style='list-style-type: circle;'> <span>Stripped of waste and personal belongings</span> </li>
                                                <li style='list-style-type: circle;'> <span>Matresses stripped of bedding <span
                                                            style='color: #D2232A'>(except for the blue
                                                            cover)</span></span> </li>
                                                <li style='list-style-type: circle;'> <span>Refrigerator emptied and cleaned</span> </li>
                                                <li style='list-style-type: circle;'> <span>Broom clean floor space and sanitary facilities</span> </li>
                                            </ul> </span> </li>
                                    <li> <span> After your departure your accommodation is inspected by the location
                                            manager and checked for cleanliness, defects and damage. After this
                                            inspection a report is prepared and send to your employer; </span> </li>
                                    <li> <span> Prevent unnecessary charged costs by taking the above into account
                                            before you leave; </span> </li>
                                </ul>
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td align='left'
                            style='font-size:0px;padding:10px 25px;padding-left:0px;word-break:break-word;'>
                            <div
                                style='font-family:Arial, sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343A40;'>
                                Hopefully you enjoyed your stay in our accommodation. If you have any remarks,
                                complaints or compliments, please send these to the email address below.</div>
                        </td>
                    </tr>`;

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const destinationCollectionName = 'emailtemplates';
    const destinationCollection = context
      .destinationClient!.db()
      .collection(destinationCollectionName)!;

    if (!(await destinationCollection.indexExists('name_1'))) {
      destinationCollection.createIndex({ name: 1 }, { unique: true });
    }
    const data = [
      {
        name: 'night_registration_arrival',
        subject: 'Check-in form',
        to: [],
        html: baseLayOut.replace('{html}', htmlArrivalEmail),
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'night_registration_before_departure',
        subject: 'Notification of departure',
        to: [],
        html: baseLayOut.replace('{html}', htmlBeforeDepartureEmail),
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'night_registration_departure',
        subject: 'Reminder, day of departure',
        to: [],
        html: baseLayOut.replace('{html}', htmlDepartureEmail),
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    const upsertPromises = data.map((doc) =>
      destinationCollection
        .findOneAndUpdate(
          { name: doc.name },
          { $set: doc },
          { upsert: true, returnDocument: 'after' }, // Use returnDocument: 'after' to get the updated document
        )
        .then(() => {
          console.log(
            `Migrated location additional group name with identifier=${doc.name} into collection ${destinationCollectionName}`,
          );
        })
        .catch((error) => {
          console.error(
            `Error upserting document with identifier=${doc.name}:`,
            error,
          );
        }),
    );

    await Promise.all(upsertPromises)
      .then(() => {
        console.log(
          `Migrated ${data.length} documents to collection ${destinationCollectionName}`,
        );
      })
      .catch((error) => {
        console.error('Error during upsert operations:', error);
      });
    const after = new Date().getTime();
    console.log(
      `Migration script ${fileName} completed in ${after - before}ms`,
    );
  } catch (error) {
    console.error(`Error in migration script ${fileName}: ${error}`);
  }
};
export default up;
