import { ObjectId } from 'mongodb';
import { z } from 'zod';

import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  AgreementLineType,
} from '~/shared/enums/contract.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    type: z.nativeEnum(AgreementLineType),
    period: z.nativeEnum(AgreementLinePeriod),
    periodType: z.nativeEnum(AgreementLinePeriodType),
    position: z.number(),
    units: z.array(z.instanceof(ObjectId)),
    contract: z.instanceof(ObjectId),
    costLineGenerals: z.array(z.instanceof(ObjectId)),
  })
  .extend(baseModelTestSchema);

export const agreementlineTest = { modelSchema };
