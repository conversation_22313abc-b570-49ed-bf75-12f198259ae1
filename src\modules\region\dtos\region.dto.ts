import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';

const RegionQueryParamSchema = z.strictObject({
  country: z
    .string()
    .refine((value) => isValidObjectId(value))
    .optional(),
});

export class RegionQueryParamDto extends createZodDto(
  RegionQueryParamSchema.merge(QueryParamsSchema),
) {}
