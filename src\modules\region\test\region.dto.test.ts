import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    name: z.string(),
    country: z.instanceof(ObjectId).optional(),
  })
  .extend(baseModelTestSchema);

const getListSchema = z.array(modelSchema.pick({ _id: true, name: true }));

export const regionTest = { modelSchema, getListSchema };
