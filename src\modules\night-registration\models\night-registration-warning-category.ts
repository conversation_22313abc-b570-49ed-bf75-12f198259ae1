import { DocumentType, modelOptions, plugin, prop } from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

export type NightRegistrationWarningCategoryDocument =
  DocumentType<NightRegistrationWarningCategoryModel>;
@modelOptions({
  options: { customName: 'NightRegistrationWarningCategory' },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class NightRegistrationWarningCategoryModel extends BaseModel {
  @prop({ required: true })
  identifier!: string;

  @prop({ required: true })
  category!: string;
}
