import { Global, Module } from '@nestjs/common';
import { EventEmitterModule } from '@nestjs/event-emitter';

import { StatsOccupantModule } from '~/modules/stats-occupant/stats-occupant.module';

import { EventEmitterListener } from './event-emitter.listener';
import { EventEmitterSender } from './event-emitter.sender';

@Module({
  imports: [EventEmitterModule.forRoot(), StatsOccupantModule],
  providers: [EventEmitterSender, EventEmitterListener],
  exports: [EventEmitterSender],
})
@Global()
export class CoreEventEmitterModule {}
