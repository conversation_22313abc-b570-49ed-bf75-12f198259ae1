import { getModelForClass } from '@typegoose/typegoose';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { PlanningOrderModel } from '~/modules/planning/planning-order.model';
import { planningOrderTest } from '~/modules/planning/test/planning.dto.test';
import { PlanningOrderType } from '~/shared/enums/planning-order.enum';

import { mockJobData } from './job.mock';
import { mockTenantData } from './tenant.mock';

const planningOrderModel = getModelForClass(PlanningOrderModel);
type planningOrderType = z.infer<typeof planningOrderTest.modelSchema>;

export const mockPlanningOrderData = {
  _id: new ObjectId(),
  isDeleted: false,
  employee: mockTenantData._id.toString(),
  type: PlanningOrderType.EMPLOYEE,
  date: dayjs().format('YYYY-MM-DD'),
  jobs: [
    {
      _id: mockJobData._id,
      position: 1,
    },
  ],
  createdAt: new Date(),
  updatedAt: new Date(),
};

export async function initMockPlanningOrder(doc?: Partial<planningOrderType>) {
  const { _id, ...rest } = { ...mockPlanningOrderData, ...doc };
  await planningOrderModel.replaceOne({ _id }, rest, {
    upsert: true,
  });
}
