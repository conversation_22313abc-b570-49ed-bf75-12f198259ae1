import dayjs from 'dayjs';
import _ from 'lodash';
import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import {
  MobileUpdateJobPointSchema,
  UpdateJobPointSchema,
} from '~/modules/job-point/dtos/job-point.dto';
import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import {
  JobFDaysEnum,
  JobFRuleEnum,
  JobPeriodTypeEnum,
  JobReportTypeEnum,
  JobStatusEnum,
  JobStatusQueryEnum,
  JobTypeEnum,
} from '~/shared/enums/job.enum';
import { JobCheckInCheckOutEnum } from '~/shared/enums/night-registration.enum';
import { isValidDate } from '~/utils/date.util';

const jobPeriodValues = Object.values(JobPeriodTypeEnum) as [
  JobPeriodTypeEnum,
  ...JobPeriodTypeEnum[],
];

const jobTypeValues = Object.values(JobTypeEnum) as [
  JobTypeEnum,
  ...JobTypeEnum[],
];

const JobReportTypeValues = Object.values(JobReportTypeEnum) as [
  JobReportTypeEnum,
  ...JobReportTypeEnum[],
];

const JobFRuleValues = Object.values(JobFRuleEnum) as [
  JobFRuleEnum,
  ...JobFRuleEnum[],
];

const JobFDaysValues = Object.values(JobFDaysEnum) as [
  JobFDaysEnum,
  ...JobFDaysEnum[],
];

const JobStatusValues = Object.values(JobStatusEnum) as [
  JobStatusEnum,
  ...JobStatusEnum[],
];

const JobStatusQueryValues = Object.values(JobStatusQueryEnum) as [
  JobStatusQueryEnum,
  ...JobStatusQueryEnum[],
];

const equipmentTypeValues = Object.values(EquipmentEnum) as [
  EquipmentEnum,
  ...EquipmentEnum[],
];

// Base schemas
const BaseReservedDetailSchema = z.strictObject({
  article: z.string(),
  amount: z.number().min(1).max(1000),
});

export const CreateReservedDetailSchema = BaseReservedDetailSchema;

export const UpdateReservedDetailSchema = BaseReservedDetailSchema.extend({
  _id: z.string().refine(isValidObjectId).optional(),
  reserved: z.string().optional(),
  isDelete: z.boolean().optional(),
  article: z.string(),
  amount: z.number().min(1).max(1000),
});

const BaseJob = {
  platform: z.string(),
  user: z.string().refine(isValidObjectId),
};

const validatePlannedDate = (val: string) => {
  const plannedDate = dayjs(val).utc().startOf('day');
  const now = dayjs().utc().startOf('day');
  return plannedDate.isSameOrAfter(now);
};

const validateUniqueIds = (val: string[]) => {
  const uniqueIds = new Set(val);
  return uniqueIds.size === val.length;
};

export const JobDetailSchema = z.object({
  id: z.string().refine(isValidObjectId),
  platform: z.string().optional(),
  user: z.string().refine(isValidObjectId).optional(),
});

export const JobExportCsvSchema = JobDetailSchema.extend({
  user: z.string().refine(isValidObjectId),
});

export const JobUpdateStatusSchema = JobDetailSchema.extend({
  user: z.string().refine(isValidObjectId),
  status: z.enum(JobStatusValues),
  platform: z.enum(['portal', 'mb']),
}).passthrough();

const JobQueryParamsSchema = QueryParamsSchema.extend({
  isActive: z.enum(['true', 'false']).optional(),
  location: z.string().refine(isValidObjectId).optional(),
  team: z.string().refine(isValidObjectId).optional(),
  assignee: z.string().refine(isValidObjectId).optional(),
  plannedDate: z.dateString().optional(),
  status: z.enum(JobStatusQueryValues).optional(),
  type: z.enum(jobPeriodValues).optional(),
  startDate: z.dateString().optional(),
  endDate: z.dateString().optional(),
  units: z
    .union([
      z.array(z.string().refine(isValidObjectId)),
      z.string().refine(isValidObjectId),
    ])
    .optional(),
  user: z.string().refine(isValidObjectId),
  jobType: z.enum(jobTypeValues).optional(),
});

const MobileJobQueryParamsSchema = z.strictObject({
  lastSyncedAt: z.dateString().optional(),
  platform: z.string().optional(),
  user: z.string().refine(isValidObjectId),
  isActive: z.enum(['true', 'false']).optional(),
  isDeleted: z.enum(['true', 'false']).optional(),
});

const JobCheckInAndCheckOutReservationsSchema = z.strictObject({
  reservation: z.string().refine(isValidObjectId),
  type: z.nativeEnum(JobCheckInCheckOutEnum),
  checked: z.boolean().default(false),
});

const UnitsJobPointsSchema = z
  .array(
    z.strictObject({
      _id: z.string().refine(isValidObjectId),
      points: z.array(UpdateJobPointSchema).min(1),
      reservations: z
        .array(JobCheckInAndCheckOutReservationsSchema)
        .optional()
        .default([]),
    }),
  )
  .min(1)
  .refine((v) => {
    const ids = v.map((point) => point._id);
    return new Set(ids).size === ids.length;
  }, 'Duplicate units are not allowed')
  .refine((v) => {
    const points = v.map((unit) => unit.points);
    return points.every((point) => point.length > 0);
  }, 'Each unit must have at least one point');

const MobileUnitsJobsSchema = z
  .array(
    z.strictObject({
      _id: z.string().refine(isValidObjectId),
      points: z.array(MobileUpdateJobPointSchema).min(1),
      reservations: z
        .array(JobCheckInAndCheckOutReservationsSchema)
        .optional()
        .default([]),
    }),
  )
  .min(1)
  .refine((v) => {
    const ids = v.map((point) => point._id);
    return new Set(ids).size === ids.length;
  }, 'Duplicate units are not allowed')
  .refine((v) => {
    const points = v.map((unit) => unit.points);
    return points.every((point) => point.length > 0);
  }, 'Each unit must have at least one point');

const CreateJobSchema = z.strictObject({
  ...BaseJob,
  title: z.string().max(256).optional(),
  type: z.enum(jobPeriodValues),
  jobType: z.enum(jobTypeValues),
  instructions: z.string().max(2048).optional().default(''),
  reportType: z.enum(JobReportTypeValues).nullable().optional(),
  reportContact: z.string().refine(isValidObjectId).nullable().optional(),
  plannedDate: z
    .dateString()
    .refine(
      validatePlannedDate,
      'plannedDate must be equal or greater than now',
    ),
  images: z.array(z.string()).max(8).optional(),
  assignee: z.string().refine(isValidObjectId),
  equipments: z
    .array(
      z.strictObject({
        equipment: z.string().refine(isValidObjectId),
      }),
    )
    .optional()
    .default([]),
  location: z.string().refine(isValidObjectId),
  units: z
    .array(
      z.strictObject({
        _id: z.string().refine(isValidObjectId),
        points: z
          .array(
            z.strictObject({
              _id: z.string().optional(),
              description: z.string().max(256),
              position: z.number().optional(),
            }),
          )
          .optional(),
      }),
    )
    .min(1)
    .refine((v) => {
      const ids = v.map((point) => point._id);
      return new Set(ids).size === ids.length;
    }, 'Duplicate units are not allowed'),
  employees: z
    .array(
      z.strictObject({
        employee: z.string().refine(isValidObjectId),
        estimatedHours: z.number().min(1).max(5999),
      }),
    )
    .min(1)
    .refine((val) => {
      const ids = val.map((emp) => emp.employee);
      return new Set(ids).size === ids.length;
    }, 'Duplicate employees are not allowed'),
  reserved: z
    .strictObject({
      storage: z.string(),
      reservedDetails: z.array(CreateReservedDetailSchema).refine(
        (value) => {
          const reservedDetailIds = value.map(
            (reservedDetail) => reservedDetail.article,
          );
          return new Set(reservedDetailIds).size === reservedDetailIds.length;
        },
        {
          message: 'Duplicate Reserved Detail',
        },
      ),
    })
    .optional(),
  tenantId: z.string().refine(isValidObjectId).optional(),
});

const MobileCreateJobSchema = CreateJobSchema.omit({
  jobType: true,
  equipments: true,
  reportType: true,
  reportContact: true,
  employees: true,
  images: true,
}).extend({
  jobType: z.enum([JobTypeEnum.CLEANING, JobTypeEnum.MAINTENANCE]),
  estimatedHours: z.number().min(1).max(5999),
});

const validateMultipleDaysJobSuperRefine = (v: any, ctx: any) => {
  const { plannedDate, plannedEndDate, employees } = v;

  if (plannedDate && plannedEndDate) {
    if (dayjs(plannedEndDate).isBefore(dayjs(plannedDate))) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'plannedEndDate must be greater than plannedDate',
      });
    }

    const dayRanges =
      dayjs(plannedEndDate)
        .utc()
        .startOf('day')
        .diff(dayjs(plannedDate).utc().startOf('day'), 'day') + 1;
    if (dayRanges > 7) {
      ctx.addIssue({
        code: z.ZodIssueCode.custom,
        message: 'Job cannot be scheduled for more than 7 days',
      });
    }

    const groupEmployees = _.groupBy(employees, 'employee');

    Object.entries(groupEmployees).forEach(([employeeId, emp]) => {
      if (emp.length !== dayRanges) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Employee ${employeeId} must have estimatedHours for each day in the range`,
        });
      }

      emp.forEach((e) => {
        if (
          dayjs(e.plannedDate)
            .utc()
            .startOf('day')
            .isBefore(dayjs(plannedDate).utc().startOf('day'))
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `Employee ${employeeId} plannedDate must be equal or greater than plannedDate of the job`,
          });
        }

        if (
          dayjs(e.plannedDate)
            .utc()
            .startOf('day')
            .isAfter(dayjs(plannedEndDate).utc().startOf('day'))
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `Employee ${employeeId} plannedDate must be equal or less than plannedEndDate of the job`,
          });
        }
      });
    });

    const groupEquipments = _.groupBy(v.equipments, 'equipment');
    Object.entries(groupEquipments).forEach(([equipmentId, eq]) => {
      eq.forEach((e) => {
        if (
          dayjs(e.plannedDate)
            .utc()
            .startOf('day')
            .isBefore(dayjs(plannedDate).utc().startOf('day'))
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `Equipment ${equipmentId} plannedDate must be equal or greater than plannedDate of the job`,
          });
        }

        if (
          dayjs(e.plannedDate)
            .utc()
            .startOf('day')
            .isAfter(dayjs(plannedEndDate).utc().startOf('day'))
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `Equipment ${equipmentId} plannedDate must be equal or less than plannedEndDate of the job`,
          });
        }
      });
    });
  }
};

const BaseMultipleDaysJobSchema = CreateJobSchema.omit({
  type: true,
  employees: true,
  equipments: true,
}).extend({
  type: z.enum([JobPeriodTypeEnum.MULTIPLE_DAYS]),
  plannedEndDate: z
    .dateString()
    .refine(
      validatePlannedDate,
      'plannedEndDate must be equal or greater than now',
    ),
  employees: z
    .array(
      z.strictObject({
        employee: z.string().refine(isValidObjectId),
        estimatedHours: z.number().min(0).max(5999),
        plannedDate: z.dateString(),
      }),
    )
    .min(1),
  equipments: z.array(
    z.strictObject({
      equipment: z.string().refine(isValidObjectId),
      plannedDate: z.dateString(),
    }),
  ),
});
const CreateMultipleDaysJobSchema = BaseMultipleDaysJobSchema.superRefine(
  validateMultipleDaysJobSuperRefine,
);

const baseJobSchedule = {
  fRule: z.enum(JobFRuleValues),
  fStartDate: z.dateString(),
  fEndDate: z.dateString(),
};

const jobScheduleDailySchema = z.strictObject({
  ...baseJobSchedule,
});

const jobScheduleWeeklySchema = z.strictObject({
  ...baseJobSchedule,
  fDays: z.enum(JobFDaysValues).array().min(1),
});

const jobScheduleMonthlySchema = z.strictObject({
  ...baseJobSchedule,
  fDays: z.enum(JobFDaysValues).array().min(1).max(1).optional(),
  fDayInMonth: z.number().min(1).max(5).optional(),
});

const jobScheduleYearlySchema = z.strictObject({
  ...baseJobSchedule,
});

const validateJobScheduleSuperRefine = (v: any, ctx: any) => {
  const scheduleValidations = {
    daily: () => {
      const { fRule, fStartDate, fEndDate, fDayInMonth, fDays } = v;
      return jobScheduleDailySchema.safeParse({
        fRule,
        fStartDate,
        fEndDate,
        ...(fDayInMonth && { fDayInMonth }),
        ...(fDays && { fDays }),
      });
    },
    weekly: () => {
      const { fRule, fStartDate, fEndDate, fDayInMonth, fDays } = v;
      const result = jobScheduleWeeklySchema.safeParse({
        fRule,
        fStartDate,
        fEndDate,
        fDays,
        ...(fDayInMonth && { fDayInMonth }),
      });

      const dayInWeek = dayjs(v.fStartDate)
        .format('dd')
        .toUpperCase() as unknown as JobFDaysEnum;

      if (fDays) {
        const setFDays = new Set(fDays);
        if (setFDays.size !== fDays.length) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `fDays must be unique`,
          });
        }
        if (!fDays.includes(dayInWeek)) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            message: `fStartDate must be in fDays`,
          });
        }
      }

      return result;
    },
    monthly: () => {
      const { fRule, fStartDate, fEndDate, fDayInMonth, fDays } = v;
      const result = jobScheduleMonthlySchema.safeParse({
        fRule,
        fStartDate,
        fEndDate,
        fDays,
        fDayInMonth,
      });

      const dayInMonth = dayjs(fStartDate).utc().date();
      if (fDayInMonth && dayInMonth > 28) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Day of fStartDate must be less than 28`,
        });
      }

      if (fDays && !fDayInMonth) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `fDayInMonth is required when fDays is provided`,
        });
      }

      return result;
    },
    yearly: () => {
      const { fRule, fStartDate, fEndDate, fDayInMonth, fDays } = v;

      const result = jobScheduleYearlySchema.safeParse({
        fRule,
        fStartDate,
        fEndDate,
        ...(fDayInMonth && { fDayInMonth }),
        ...(fDays && { fDays }),
      });

      const monthInYear = dayjs(fStartDate).utc().month() + 1;

      const dayInMonth = dayjs(fStartDate).utc().date();

      if (monthInYear === 2 && dayInMonth > 28) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: `Day of fStartDate must be less than 28`,
        });
      }

      return result;
    },
  };

  const validation =
    scheduleValidations[v.fRule as keyof typeof scheduleValidations];

  if (validation) {
    const result = validation();
    if (!result.success) {
      result.error.issues.forEach((issue) => {
        ctx.addIssue({ ...issue });
      });
    }
  }
};

const CreateScheduleJobSchema = CreateJobSchema.omit({
  type: true,
  plannedDate: true,
  reserved: true,
})
  .extend({
    type: z.enum([JobPeriodTypeEnum.PERIODIC]),
    fInterval: z.number().min(1).max(365),
    fRule: z.enum(JobFRuleValues),
    fDays: z.enum(JobFDaysValues).array().optional(),
    fDayInMonth: z.number().optional(),
    fStartDate: z.dateString(),
    fEndDate: z.dateString(),
  })
  .refine((v) => {
    return isValidDate(v.fStartDate) && isValidDate(v.fEndDate);
  }, 'Invalid Date')
  .refine((v) => {
    return dayjs().startOf('day').isSameOrBefore(dayjs(v.fStartDate));
  }, 'Invalid fStartDate')
  .refine((v) => {
    return dayjs(v.fEndDate).isAfter(dayjs(v.fStartDate));
  }, 'fEndDate must be greater than fStartDate')
  .superRefine(validateJobScheduleSuperRefine);

export const UpdateScheduleJobSchema = CreateJobSchema.omit({
  type: true,
  location: true,
  plannedDate: true,
  reserved: true,
  tenantId: true,
  platform: true,
}).extend({
  isOverrideRegular: z.boolean().optional(),
});

export const UpdateScheduleJobSettingsSchema = z
  .strictObject({
    id: z.string().refine(isValidObjectId),
    fInterval: z.number().min(1),
    fRule: z.enum(JobFRuleValues),
    fDays: z.enum(JobFDaysValues).array().optional(),
    fDayInMonth: z.number().optional(),
    fStartDate: z.dateString(),
    fEndDate: z.dateString(),
    isRemoveRegular: z.boolean().optional(),
  })
  .passthrough()
  .refine((v) => {
    return dayjs().startOf('day').isSameOrBefore(dayjs(v.fStartDate));
  }, 'Invalid fStartDate')
  .refine((v) => {
    return dayjs(v.fEndDate).isAfter(dayjs(v.fStartDate));
  }, 'fEndDate must be greater than fStartDate')
  .superRefine(validateJobScheduleSuperRefine);

export const UpdateOpenJobSchema = CreateJobSchema.omit({
  location: true,
  type: true,
  plannedDate: true,
  jobType: true,
  platform: true,
  reserved: true,
  tenantId: true,
}).extend({
  planner: z.string().refine(isValidObjectId),
  plannedDate: z
    .dateString()
    .refine(
      validatePlannedDate,
      'plannedDate must be equal or greater than now',
    )
    .optional(),
  reserved: z
    .strictObject({
      _id: z.string().optional(),
      isDelete: z.boolean().optional(),
      storage: z.string().optional(),
      reservedDetails: z
        .array(UpdateReservedDetailSchema)
        .min(1)
        .refine(
          (value) => {
            const willCreateOrUpdateReservedDetails = _.filter(
              value,
              (value) => !value.isDelete,
            );

            const reservedDetailIds = willCreateOrUpdateReservedDetails.map(
              (reservedDetail) => reservedDetail.article,
            );
            return new Set(reservedDetailIds).size === reservedDetailIds.length;
          },
          {
            message: 'Duplicate Reserved Detail',
          },
        )
        .optional(),
    })
    .optional(),
  isActive: z.boolean().default(true),
});

const multipleDaysJobExtendSchema = {
  plannedEndDate: z
    .dateString()
    .refine(
      validatePlannedDate,
      'plannedEndDate must be equal or greater than now',
    )
    .optional(),
  employees: z
    .array(
      z.strictObject({
        employee: z.string().refine(isValidObjectId),
        estimatedHours: z.number().min(0).max(5999),
        plannedDate: z.dateString(),
      }),
    )
    .min(1),
  equipments: z.array(
    z.strictObject({
      equipment: z.string().refine(isValidObjectId),
      plannedDate: z.dateString(),
    }),
  ),
};

export const UpdateOpenMultipleDaysJobSchema = UpdateOpenJobSchema.omit({
  equipments: true,
  employees: true,
})
  .extend({
    ...multipleDaysJobExtendSchema,
  })
  .superRefine(validateMultipleDaysJobSuperRefine);

export const UpdateInProgressJobSchema = UpdateOpenJobSchema.omit({
  title: true,
  units: true,
  plannedDate: true,
  isActive: true,
}).extend({
  plannedDate: z
    .dateString()
    .refine(
      validatePlannedDate,
      'plannedDate must be equal or greater than now',
    )
    .optional(),
});

export const UpdateInProgressMultipleDaysJobSchema =
  UpdateInProgressJobSchema.omit({})
    .extend({
      ...multipleDaysJobExtendSchema,
    })
    .superRefine(validateMultipleDaysJobSuperRefine);

export const UpdateReviewJobSchema = CreateJobSchema.pick({
  title: true,
  reportType: true,
  reportContact: true,
  user: true,
}).extend({
  employees: z
    .array(
      z.strictObject({
        employee: z.string().refine(isValidObjectId),
        actualHours: z.number().min(1).max(5999),
      }),
    )
    .min(1)
    .refine((val) => {
      const ids = val.map((emp) => emp.employee);
      return new Set(ids).size === ids.length;
    }, 'Duplicate id job employee are not allowed')
    .optional(),
  units: UnitsJobPointsSchema.optional(),
  invoiceContact: z.string().refine(isValidObjectId).optional(),
  isSendRC: z.boolean().optional(),
  isSendRR: z.boolean().optional(),
  rtContacts: z
    .array(z.string().refine(isValidObjectId))
    .refine(validateUniqueIds, 'Duplicate RC contacts are not allowed')
    .optional(),
  rrContacts: z
    .array(z.string().refine(isValidObjectId))
    .refine(validateUniqueIds, 'Duplicate RR contacts are not allowed')
    .optional(),
});

export const UpdateReviewMultipleDaysJobSchema = UpdateReviewJobSchema.omit({
  employees: true,
})
  .extend({
    employees: z
      .array(
        z.strictObject({
          employee: z.string().refine(isValidObjectId),
          actualHours: z.number().min(0).max(5999),
          plannedDate: z.dateString(),
        }),
      )
      .min(1)
      .optional(),
  })
  .superRefine(validateMultipleDaysJobSuperRefine);

export const RejectJobSchema = z.strictObject({
  user: z.string().refine(isValidObjectId),
  instructions: z
    .string({
      required_error: 'Instructions is required',
    })
    .min(1)
    .max(2048),
  rejectedBy: z.string().refine(isValidObjectId),
  rejectedAt: z.dateString(),
});

export const DeleteJobSchema = CreateJobSchema.pick({
  user: true,
  platform: true,
}).extend({
  jobIds: z.array(z.string().refine(isValidObjectId)),
});

const ViewSummaryJobSchema = z.strictObject({
  id: z.string().refine(isValidObjectId),
  user: z.string().refine(isValidObjectId),
  platform: z.string(),
});

const CheckOverlapJobSchema = z.strictObject({
  platform: z.string(),
  jobId: z.string().refine(isValidObjectId).optional(),
  timezone: z
    .string()
    .optional()
    .default('+00:00')
    .refine((value) => /^[+-][0-1][0-9]:[0-5][0-9]$/.test(value), {
      message: 'Invalid timezone format',
    }),
  equipmentType: z.enum(equipmentTypeValues).optional(),
  employees: z
    .array(
      z.strictObject({
        employee: z.string().refine(isValidObjectId),
        plannedDate: z.dateString(),
      }),
    )
    .min(1),
  equipments: z
    .array(
      z.strictObject({
        equipment: z.string().refine(isValidObjectId),
        plannedDate: z.dateString(),
      }),
    )
    .min(1)
    .optional(),
});

const GetPdfReportJobSchema = z
  .strictObject({
    id: z.string().refine(isValidObjectId),
  })
  .passthrough();

export const DeleteScheduleJobSchema = z.strictObject({
  jobId: z.string().refine(isValidObjectId),
  isKeepAllRegularJobs: z.boolean(),
  ...BaseJob,
});

export const UpdatePlanningJobSchema = z.strictObject({
  id: z.string().refine(isValidObjectId),
  plannedDate: z.dateString(),
});

// Mobile Schema
export const MobilePerformJobSchema = z.strictObject({
  user: z.string(),
});

export const MobileUpdateInProgressJobSchema = z.strictObject({
  units: MobileUnitsJobsSchema,
  user: z.string().refine(isValidObjectId),
  actualHours: z
    .array(
      z.strictObject({
        hour: z.number().min(0).max(5999).int(),
        plannedDate: z.dateString(),
      }),
    )
    .optional(),
  feedbacks: z.string().max(2048).optional(),
  fuaDescriptions: z.string().max(2048).optional(),
});

export const MobileCoWorkerSyncDataOfJobSchema = z.strictObject({
  ...BaseJob,
  id: z.string().refine((v) => isValidObjectId(v)),
  actualHours: z
    .array(
      z.strictObject({
        hour: z.number().min(0).max(5999).int(),
        plannedDate: z.dateString(),
      }),
    )
    .optional(),
  feedbacks: z.string().max(2048).optional(),
  fuaDescriptions: z.string().max(2048).optional(),
});

export class JobQuerySchemaDto extends createZodDto(JobQueryParamsSchema) {}
export class MobileJobQuerySchemaDto extends createZodDto(
  MobileJobQueryParamsSchema,
) {}

export class CreateJobDto extends createZodDto(CreateJobSchema) {}

export class CreateScheduleJobDto extends createZodDto(
  CreateScheduleJobSchema,
) {}

export class CreateMultipleDaysJobDto extends createZodDto(
  CreateMultipleDaysJobSchema,
) {}

export class UpdateScheduleJobDto extends createZodDto(
  UpdateScheduleJobSchema,
) {}

export class UpdateScheduleJobSettingsDto extends createZodDto(
  UpdateScheduleJobSettingsSchema,
) {}

export class UpdateOpenJobDto extends createZodDto(UpdateOpenJobSchema) {}
export class UpdateOpenMultipleDaysJobDto extends createZodDto(
  UpdateOpenMultipleDaysJobSchema,
) {}

export class UpdateInProgressJobDto extends createZodDto(
  UpdateInProgressJobSchema,
) {}

export class UpdateReviewJobDto extends createZodDto(UpdateReviewJobSchema) {}

export class RejectJobDto extends createZodDto(RejectJobSchema) {}

export class DeleteJobDto extends createZodDto(DeleteJobSchema) {}

export class MobileCreateJobDto extends createZodDto(MobileCreateJobSchema) {}

export class MobilePerformJobDto extends createZodDto(MobilePerformJobSchema) {}

export class MobileUpdateInProgressJobDto extends createZodDto(
  MobileUpdateInProgressJobSchema,
) {}

export class MobileCoWorkerSyncDataOfJobDto extends createZodDto(
  MobileCoWorkerSyncDataOfJobSchema,
) {}

export class JobDetailDto extends createZodDto(JobDetailSchema) {}

export class JobExportCsvDto extends createZodDto(JobExportCsvSchema) {}

export class JobUpdateStatusDto extends createZodDto(JobUpdateStatusSchema) {}

export class ViewSummaryJobDto extends createZodDto(ViewSummaryJobSchema) {}

export class CheckOverlapJobDto extends createZodDto(CheckOverlapJobSchema) {}

export class GetPdfReportJobDto extends createZodDto(GetPdfReportJobSchema) {}

export class DeleteScheduleJobDto extends createZodDto(
  DeleteScheduleJobSchema,
) {}

export class UpdatePlanningJobDto extends createZodDto(
  UpdatePlanningJobSchema,
) {}
