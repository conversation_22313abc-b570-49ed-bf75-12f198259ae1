import { base, en, Faker } from '@faker-js/faker';
import { createHash } from 'crypto';

const CustomLocale = {
  internet: {
    free_email: ['infodationexample.nl'],
  },
};

/**
 * Masks an email address by generating a deterministic fake email.
 * The fake email is generated using a hash of the original email as a seed.
 *
 * @param {string} email - The original email address to be masked.
 * @returns {string} - The masked email address.
 */
export const maskEmail = (email?: string): string | null => {
  if (email === null || email === undefined) {
    return null;
  }

  const hash = createHash('sha256').update(email, 'utf8').digest('hex');
  const seed = parseInt(hash.slice(0, 16), 16);
  const localFaker = new Faker({
    locale: [CustomLocale, en, base],
  });
  localFaker.seed(seed);

  return localFaker.internet.email();
};
