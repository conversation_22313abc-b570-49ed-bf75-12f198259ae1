import { Modu<PERSON> } from '@nestjs/common';

import { ReportLocationAdditionalController } from './report-location-additional.controller';
import { ReportLocationAdditionalService } from './report-location-additional.service';

@Module({
  controllers: [ReportLocationAdditionalController],
  exports: [ReportLocationAdditionalService],
  providers: [ReportLocationAdditionalService],
})
export class ReportLocationAdditionalModule {}
