import { HttpService } from '@nestjs/axios';
import { Injectable, NotFoundException } from '@nestjs/common';
import AdmZip from 'adm-zip';
import archiver from 'archiver';
import FormData from 'form-data';
import http2 from 'http2-wrapper';
import * as https from 'https';
import mongoose, { Model } from 'mongoose';
import pLimit from 'p-limit';
import { firstValueFrom } from 'rxjs';
import { PassThrough } from 'stream';

import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { LOCATION_MESSAGE_KEYS } from '~/shared/message-keys/location.message-key';
import { LOCATION_FILE_MESSAGE_KEYS } from '~/shared/message-keys/location-file.message-key';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { UploadFileModel } from '../document-file/upload-file.model';
import { LocationService } from '../location/location.service';
import {
  LocationFileCheckExistedParamsDto,
  LocationFileCreateDto,
  LocationUploadFileParamsDto,
} from './dtos/location-file.dto';
import { LocationFileModel } from './location-file.model';

@Injectable()
export class LocationFileService {
  constructor(
    @InjectModel(LocationFileModel)
    private locationFileModel: MongooseModel<LocationFileModel>,

    private readonly locationService: LocationService,
    private httpService: HttpService,

    @InjectModel(UploadFileModel)
    private readonly uploadFileModel: Model<UploadFileModel>,
  ) {}

  async getUploadFilesOfLocation(payload: LocationUploadFileParamsDto) {
    const { id, ...rest } = payload;

    const location = await this.locationService.findOne(id);

    if (!location) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }

    const { query, options } = buildQuery(rest);
    const aggregates = [
      { $match: { ...query, location: location._id } },
      {
        $lookup: {
          from: 'locations',
          localField: 'location',
          foreignField: '_id',
          as: 'location',
          pipeline: [{ $project: { _id: 1, fullAddress: 1 } }],
        },
      },
      // { $unwind: '$location', preserveNullAndEmptyArrays: true },
      {
        $lookup: {
          from: 'uploadfiles',
          localField: 'uploadFile',
          foreignField: '_id',
          as: 'uploadFile',
          pipeline: [
            {
              $project: {
                _id: 1,
                originalFilename: 1,
                extension: 1,
                size: 1,
                publicUrl: 1,
                provider: 1,
              },
            },
          ],
        },
      },
      // { $unwind: { path: '$uploadFile', preserveNullAndEmptyArrays: true } },
      { $sort: options.sort },
      {
        $project: {
          _id: 1,
          description: 1,
          uploadFile: { $first: '$uploadFile' },
          location: { $first: '$location' },
          createdAt: 1,
          updatedAt: 1,
        },
      },
    ];
    const aggregateQuery = this.locationFileModel.aggregate(aggregates);
    return this.locationFileModel.aggregatePaginate(aggregateQuery, {
      ...options,
      pagination: false,
    });
  }

  // upload file
  async uploadFileToLocation(payload: LocationFileCreateDto) {
    const location = await this.locationService.findOne(
      payload.location.toString(),
    );

    if (!location) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }

    const { fileId } = payload;

    const uploadFile = await this.uploadFileModel.findById(fileId);

    if (!uploadFile) {
      throw new NotFoundException('File not found');
    }

    const locationFile = new this.locationFileModel({
      ...payload,
      location: location._id,
      uploadFile: uploadFile._id,
    });

    return locationFile.save();
  }

  // delete uploaded file of location
  async deleteLocationFile(id: string, headers: any) {
    const locationFile = await this.locationFileModel
      .findOne({ _id: id })
      .populate([
        {
          path: 'uploadFile',
          select: '_id fileName extension mimeType size publicUrl provider',
        },
        { path: 'location', select: '_id fullAddress' },
      ]);

    if (!locationFile) {
      throw new NotFoundException(LOCATION_FILE_MESSAGE_KEYS.NOT_FOUND);
    }
    try {
      const {
        host: _,
        connection: __,
        'content-length': ____,
        ...forwardHeaders
      } = headers;
      if (locationFile.uploadFile) {
        const url = `${process.env.UPLOAD_SERVICE_URL}/delete/${locationFile.uploadFile._id}`;
        await firstValueFrom(
          this.httpService.delete(url, {
            httpAgent: new https.Agent({ rejectUnauthorized: false }),
            headers: forwardHeaders,
          }),
        );
      }
    } catch (error: any) {
      console.log(error.message);
      throw error;
    }
    return await this.locationFileModel.deleteOne({ _id: locationFile._id });
  }

  // check existed location file
  async checkExistedLocationFile(payload: LocationFileCheckExistedParamsDto) {
    const { id, fileName } = payload;

    const result = (
      await this.locationFileModel.aggregate([
        {
          $match: {
            location: new mongoose.Types.ObjectId(id),
            isDeleted: false,
          },
        },
        {
          $lookup: {
            from: 'uploadfiles',
            localField: 'uploadFile',
            foreignField: '_id',
            as: 'fileDetails',
          },
        },
        { $unwind: { path: '$fileDetails', preserveNullAndEmptyArrays: true } },
        { $match: { 'fileDetails.originalFilename': fileName } },
      ])
    )[0];

    return { id: result ? result._id : null, isExisted: !!result };
  }

  async downloadFilesOfLocation(id: string) {
    const location = await this.locationService.findOne(id);

    if (!location) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }

    const zipFileName = `location_${location?.address?.city}_${location?.address?.street}_${location?.address?.number}_files.zip`;

    // get all files of location
    const files = await this.locationFileModel
      .find({ location: location._id, isDeleted: false })
      .populate([
        {
          path: 'uploadFile',
          select: '_id originalFilename mimeType publicUrl',
        },
      ]);

    // zip up all files
    const zip = new AdmZip();

    await Promise.all(
      files.map(async (file) => {
        const uploadFile = file.uploadFile as UploadFileModel;
        const { publicUrl, originalFilename } = uploadFile;
        // download file from url
        const fileBuffer = await this.downloadFileFromUrl(publicUrl);
        if (!fileBuffer) {
          return;
        }
        zip.addFile(originalFilename, fileBuffer, '', 0o644);
      }),
    );

    const zipBuffer = zip.toBuffer();

    return { fileName: zipFileName.toLowerCase(), content: zipBuffer };
  }

  private async downloadFileFromUrl(url: string) {
    try {
      const response = await firstValueFrom(
        this.httpService.get(url, {
          httpAgent: new https.Agent({ rejectUnauthorized: false }),
          responseType: 'arraybuffer',
        }),
      );
      const { data, status } = response;
      if (status === 200) {
        return Buffer.from(data, 'binary');
      } else {
        return null;
      }
    } catch (error) {
      return null;
    }
  }

  // download all uploaded files of location
  async downloadAllFilesOfLocation(id: string, headers: any) {
    const location = await this.locationService.findOne(id);

    if (!location) {
      throw new NotFoundException(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    }

    const files = await this.locationFileModel
      .find({ location: location._id, isDeleted: false })
      .populate([
        {
          path: 'uploadFile',
          select: '_id originalFilename mimeType publicUrl size',
        },
      ])
      .lean();

    if (!files.length || files.some((file) => !file.uploadFile)) {
      throw new NotFoundException('No files or invalid file');
    }

    const city = location?.address?.city.replace(/\s+/g, '_');
    const street = location?.address?.street.replace(/\s+/g, '_');
    const number = location?.address?.number ?? '';

    const zipFileName = `location_${city}_${street}_${number ? number + '_' : ''}files.zip`;

    const limit = pLimit(3);
    const archive = archiver('zip', { zlib: { level: 1 } });
    const passThrough = new PassThrough();
    archive.pipe(passThrough);

    await Promise.all(
      files.map(async (file) =>
        limit(async () => {
          const uploadFile = file.uploadFile as UploadFileModel;
          const { publicUrl, originalFilename } = uploadFile;
          try {
            const response = await firstValueFrom(
              this.httpService.get(publicUrl, {
                responseType: 'stream',
                timeout: 300000, // 5 minutes
              }),
            );
            if (response.data) {
              archive.append(response.data, { name: originalFilename });
            }
          } catch (error) {
            console.warn(
              `Skipping file: ${originalFilename} due to download error.`,
            );
          }
        }),
      ),
    );

    archive.finalize();

    try {
      const result = (
        await this.uploadFileStream(zipFileName, passThrough, headers)
      ).data;

      return {
        fileName: zipFileName.toLowerCase(),
        publicUrl: result.publicUrl,
      };
    } catch (err) {
      console.error('Error during zip file upload', err);
      throw err;
    } finally {
      passThrough.destroy();
    }
  }

  private async uploadFileStream(
    fileName: string,
    fileData: any,
    headers: any,
  ): Promise<any> {
    return new Promise((resolve, reject) => {
      const {
        host: _,
        connection: __,
        'content-length': ____,
        'content-type': _____,
        ...forwardHeaders
      } = headers;

      const formData = new FormData();
      formData.append('folderPath', 'locations');
      formData.append('files', fileData, {
        filename: fileName,
        contentType: 'application/zip',
      });

      const formHeaders = formData.getHeaders();
      Object.assign(forwardHeaders, formHeaders);

      const req = http2.request(
        `${process.env.UPLOAD_SERVICE_URL}/stream-upload`,
        { method: 'POST', rejectUnauthorized: false, headers: forwardHeaders },
        (res) => {
          let data = '';
          res.on('data', (chunk) => (data += chunk));
          res.on('end', () => {
            resolve(JSON.parse(data));
          });
        },
      );

      formData.pipe(req);
      req.on('error', (err) => reject(err));
    });
  }
}
