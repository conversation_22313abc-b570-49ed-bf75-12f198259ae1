import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';

const LocationAdditionalQuerySchema = z.object({
  type: z.enum([
    LocationAdditionalType.CERTIFICATE_AND_CONTROL,
    LocationAdditionalType.FEATURE_AND_SUPPLIER,
  ]),
  team: z.string().optional(),
  category: z.string().optional(),
  user: z.string().refine(isValidObjectId).optional(),
  tenantId: z.string().optional(),
});

export class ReportLocationAdditionalQueryDto extends createZodDto(
  LocationAdditionalQuerySchema,
) {}
