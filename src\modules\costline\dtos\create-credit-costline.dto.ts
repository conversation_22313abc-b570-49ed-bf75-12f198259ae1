import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

const CreditCostLineCreateSchema = z.strictObject({
  costLines: z
    .array(
      z.strictObject({
        _id: z.string().refine((v) => isValidObjectId(v)),
        description: z.string().max(256),
        price: z.number(),
      }),
    )
    .min(1),
});

export class CreditCostLineCreateBodyDto extends createZodDto(
  CreditCostLineCreateSchema,
) {}
