import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { addressTest } from '~/modules/address/test/address.dto.test';
import { agreementlineTest } from '~/modules/agreementline/test/agreenmentline.dto.test';
import { contactTest } from '~/modules/contact/test/contact.dto.test';
import { ContractTypeTest } from '~/modules/contract-type/test/contract-type.dto.test';
import { costCenterTest } from '~/modules/costcenter/test/costcenter.dto.test';
import { costLineGeneralTest } from '~/modules/costlinegeneral/test/costlinegeneral.dto.test';
import { costTypeTest } from '~/modules/costtype/test/costtype.dto.test';
import { locationTest } from '~/modules/location/test/location.dto.test';
import { unitTest } from '~/modules/unit/test/unit.dto.test';
import { ContractType } from '~/shared/enums/contract.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isActive: z.boolean(),
    isGenerateCostLine: z.boolean(),
    isSigned: z.boolean(),
    isWholeLocation: z.boolean(),
    identifier: z.string(),
    type: z.nativeEnum(ContractType),
    startDate: z.date(),
    endDate: z.date().nullish(),
    note: z.string(),
    generatePeriod: z.number(),
    noticeDays: z.string(),
    signedAt: z.date(),
    isNew: z.boolean().optional(),
    attachments: z.array(
      z.object({
        originalFilename: z.string(),
        publicUrl: z.string(),
      }),
    ),
    contact: z.instanceof(ObjectId),
    location: z.instanceof(ObjectId),
    agreementLines: z.array(z.instanceof(ObjectId)),
    costCenter: z.instanceof(ObjectId).optional(),
    contractType: z.instanceof(ObjectId).optional(),
  })
  .extend(baseModelTestSchema);

const baseContractSchema = modelSchema
  .pick({
    _id: true,
    identifier: true,
    startDate: true,
    updatedAt: true,
  })
  .extend({
    endDate: z.date().optional(),
    contact: contactTest.modelSchema.pick({
      _id: true,
      displayName: true,
      organizationNames: true,
    }),
  });

const findAllTypeCreditorRentingSchema = z.array(
  baseContractSchema.extend({
    location: locationTest.modelSchema
      .pick({
        _id: true,
        fullAddress: true,
      })
      .nullable()
      .optional(),
    owner: z.string().optional(),
  }),
);

const findAllTypeServiceSchema = z.array(
  baseContractSchema.extend({
    costCenter: z
      .object({
        _id: z.instanceof(ObjectId),
        displayName: z.string(),
      })
      .nullable()
      .optional(),
  }),
);

const findAllTypeSupplierSchema = z.array(
  baseContractSchema.extend({
    contractType: ContractTypeTest.modelSchema
      .pick({
        _id: true,
        name: true,
      })
      .nullable()
      .optional(),
  }),
);

const findAllTypeCustomSchema = z.array(baseContractSchema);

const findDetailOfOneSchema = modelSchema.extend({
  contact: contactTest.modelSchema
    .pick({
      _id: true,
      displayName: true,
      organizationNames: true,
    })
    .optional(),
  contractType: ContractTypeTest.modelSchema
    .pick({
      _id: true,
      name: true,
    })
    .optional(),
  location: locationTest.modelSchema
    .pick({
      _id: true,
      fullAddress: true,
      address: true,
    })
    .extend({
      address: addressTest.modelSchema
        .pick({
          city: true,
          street: true,
          number: true,
          suffix: true,
        })
        .optional(),
    })
    .optional(),
  costCenter: costCenterTest.modelSchema
    .pick({
      _id: true,
      identifier: true,
      name: true,
    })
    .optional(),
  agreementLines: z.array(
    agreementlineTest.modelSchema.extend({
      units: z
        .array(
          unitTest.modelSchema.pick({
            _id: true,
            name: true,
            isRoot: true,
            parent: true,
          }),
        )
        .optional(),
      costLineGenerals: z.array(
        costLineGeneralTest.modelSchema.extend({
          costType: costTypeTest.modelSchema,
          hasApproved: z.boolean(),
        }),
      ),
    }),
  ),
});

const findLocationOfContractSchema = z.object({
  _id: z.instanceof(ObjectId),
  type: z.nativeEnum(ContractType),
  locations: z.array(locationTest.modelSchema),
  maxOccupants: z.number(),
});

const createSchema = modelSchema.extend({
  endDate: z.date().optional(),
  agreementLines: z.array(
    agreementlineTest.modelSchema.extend({
      costLineGenerals: z.array(
        costLineGeneralTest.modelSchema.extend({
          endDate: z.date().optional(),
        }),
      ),
    }),
  ),
});

const updateSchema = findDetailOfOneSchema;

export const contractTest = {
  modelSchema,
  findAllTypeCreditorRentingSchema,
  findAllTypeServiceSchema,
  findAllTypeSupplierSchema,
  findAllTypeCustomSchema,
  findDetailOfOneSchema,
  findLocationOfContractSchema,
  createSchema,
  updateSchema,
};
