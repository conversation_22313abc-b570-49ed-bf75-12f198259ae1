import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';

export const queryForAdvancePaging = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  query = {},
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  query?: any;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  const pagingQuery = {
    _id: { $gt: nextId },
    ...query,
  };

  return collection.find(pagingQuery).sort({ _id: 1 }).limit(limit);
};
