import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { InvoiceType } from '~/shared/enums/contract.enum';

export enum ApprovedInvoicesQueryType {
  DEBTOR_RENTING = 'debtor-renting',
  DEBTOR_SERVICE = 'debtor-service',
  JOB_AND_CUSTOM = 'job-and-custom',
}

const invoiceTypeValues = Object.values(InvoiceType) as [string, ...string[]];

const InvoiceApproveSchema = z.strictObject({
  costLines: z.array(z.string().refine((v) => isValidObjectId(v))).min(1),
  contact: z.string().refine((v) => isValidObjectId(v)),
  type: z.enum(invoiceTypeValues),
});

export const InvoiceApproveBodySchema = z.strictObject({
  invoices: z.array(InvoiceApproveSchema).min(1),
  user: z.string().refine((v) => isValidObjectId(v)),
  thirdParties: z.object({}).passthrough().optional(),
});

const ApprovedInvoicesQueryParamsSchema = QueryParamsSchema.omit({
  _q: true,
})
  .merge(
    z.strictObject({
      type: z.nativeEnum(ApprovedInvoicesQueryType),
      contact: z.string().refine(isValidObjectId).optional(),
      location: z.string().refine(isValidObjectId).optional(),
      costCenter: z.string().refine(isValidObjectId).optional(),
      approvedDateFrom: z.dateString().optional(),
      approvedDateTo: z.dateString().optional(),
      startDate: z.dateString().optional(),
      endDate: z.dateString().optional(),
    }),
  )
  .superRefine((schema, ctx) => {
    switch (schema.type) {
      case ApprovedInvoicesQueryType.DEBTOR_RENTING:
        if (schema.costCenter) {
          ctx.addIssue({
            code: 'custom',
            message: 'Cost query center is not allowed for debtor renting',
          });
        }

        if (
          !schema.contact &&
          !schema.location &&
          !schema.approvedDateFrom &&
          !schema.approvedDateTo &&
          !schema.startDate &&
          !schema.endDate
        ) {
          ctx.addIssue({
            code: 'custom',
            message: 'At least one query parameter is required',
            path: [
              'contact',
              'location',
              'approvedDateFrom',
              'approvedDateTo',
              'startDate',
              'endDate',
            ],
          });
        }
        break;
      case ApprovedInvoicesQueryType.DEBTOR_SERVICE:
        if (schema.location) {
          ctx.addIssue({
            code: 'custom',
            message: 'Location query is not allowed for debtor service',
          });
        }

        if (
          !schema.contact &&
          !schema.costCenter &&
          !schema.approvedDateFrom &&
          !schema.approvedDateTo &&
          !schema.startDate &&
          !schema.endDate
        ) {
          ctx.addIssue({
            code: 'custom',
            message: 'At least one query parameter is required',
            path: [
              'contact',
              'costCenter',
              'approvedDateFrom',
              'approvedDateTo',
              'startDate',
              'endDate',
            ],
          });
        }
        break;
      case ApprovedInvoicesQueryType.JOB_AND_CUSTOM:
        if (schema.startDate || schema.endDate) {
          ctx.addIssue({
            code: 'custom',
            message: 'Start date and end date are not allowed',
            path: ['startDate', 'endDate'],
          });
        }

        if (
          !schema.contact &&
          !schema.location &&
          !schema.approvedDateFrom &&
          !schema.approvedDateTo &&
          !schema.costCenter
        ) {
          ctx.addIssue({
            code: 'custom',
            message: 'At least one query parameter is required',
            path: [
              'contact',
              'location',
              'approvedDateFrom',
              'approvedDateTo',
              'costCenter',
            ],
          });
        }
        break;
    }
  });

export class InvoiceApproveBodyDto extends createZodDto(
  InvoiceApproveBodySchema,
) {}

export class ApprovedInvoicesQueryParamsDto extends createZodDto(
  ApprovedInvoicesQueryParamsSchema,
) {}
