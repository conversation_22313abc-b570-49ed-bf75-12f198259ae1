import {
  DocumentType,
  index,
  mongoose,
  plugin,
  prop,
  Ref,
  Severity,
} from '@typegoose/typegoose';
import { modelOptions } from '@typegoose/typegoose';
import { hashSync } from 'bcrypt';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { GenericDisplayMapping } from '~/modules/tenant-user/dtos/tenant-user.util';
import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { ThisWeekDefaultDisplay } from '~/shared/enums/tenant-user.enum';
import { WorkingDays } from '~/shared/enums/working-days.enum';
import { BaseModel } from '~/shared/models/base.model';

import { TeamDocument, TeamModel } from '../team/team.model';
import { TenantDocument, TenantModel } from '../tenant/tenant.model';
import { TokenDocument, TokenModel } from '../token/token.model';

export type TenantUserDocument = DocumentType<TenantUserModel>;

export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

export enum Language {
  EN = 'en',
  NL = 'nl',
}

class ColumnDisplayConfig {
  @prop({ required: false, default: [] })
  inspectionOverview!: DisplayMapping[];

  @prop({ required: false, default: [] })
  maintenanceOverview!: DisplayMapping[];

  @prop({ required: false, default: [] })
  cleaningOverview!: DisplayMapping[];
}

class DisplayMapping {
  @prop({ required: true })
  field!: string;

  @prop({ default: true })
  isShown!: boolean;

  @prop({ required: true })
  mutable!: boolean;
}

@modelOptions({
  options: { customName: 'TenantUser', allowMixed: Severity.ALLOW },
})
@index({ displayName: 1 })
@index({ username: 1, email: 1 }, { unique: true })
@index({ isActive: 1, isDeleted: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions(['password']))
export class TenantUserModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({ default: false })
  isRoot!: boolean;

  @prop()
  image?: string;

  @prop({ trim: true, maxlength: 128 })
  displayName!: string;

  @prop({ trim: true, maxlength: 64 })
  firstName!: string;

  @prop({ trim: true, maxlength: 64 })
  lastName!: string;

  @prop({ trim: true, required: true, minlength: 5, maxlength: 64 })
  username!: string;

  @prop({
    select: false,
    set: (password: string) => hashSync(password, 10),
    required: true,
    minlength: 8,
  })
  password!: string;

  @prop({ required: true, trim: true, maxlength: 128 })
  email!: string;

  @prop({ enum: Gender })
  gender!: Gender;

  @prop({ enum: Language, default: Language.EN })
  language!: Language;

  @prop({ required: true, trim: true, maxlength: 24 })
  phone1!: string;

  @prop({ trim: true, maxlength: 24 })
  phone2?: string;

  @prop({ required: true })
  roles!: mongoose.Types.Decimal128;

  @prop({ ref: () => TeamModel })
  team!: Ref<TeamDocument>;

  @prop({ default: 0 })
  position!: number;

  @prop({ required: true, default: [] })
  oddWeeks!: WorkingDays[];

  @prop({ required: true, default: [] })
  evenWeeks!: WorkingDays[];

  @prop({
    enum: ThisWeekDefaultDisplay,
    default: ThisWeekDefaultDisplay.SHOPPING_LIST,
  })
  thisWeekDefaultDisplay!: ThisWeekDefaultDisplay;

  @prop({ default: new Date() })
  lastChangePasswordAt?: Date;

  @prop({ ref: () => TokenModel })
  token!: TokenDocument;

  @prop({ ref: () => TenantModel })
  tenant!: Ref<TenantDocument>;

  @prop({
    type: () => ColumnDisplayConfig,
    _id: false,
    default: {
      inspectionOverview: GenericDisplayMapping,
      maintenanceOverview: GenericDisplayMapping,
      cleaningOverview: GenericDisplayMapping,
    },
  })
  columnDisplayConfigs!: ColumnDisplayConfig;

  @prop({ default: true })
  saveToPhotos!: boolean;
}
