export const CONTACT_MESSAGE_KEYS = {
  FIND_CONTACT_NOT_FOUND: 'contact.find.contact_not_found',
  ORG_NOT_FOUND: 'contact.find.org_not_found',
  INVALID_ORG_TYPE: 'contact.find.invalid_org_type',
  INVALID_ROLE: 'contact.form.invalid_role',
  CONTACT_EXIST: 'contact.form.contact_exist',
  INVALID_COUNTRY: 'contact.form.invalid_country',
  INVALID_REGION: 'contact.form.invalid_region',
  EMAIL_EXISTED: 'contact.form.email_existed',
  UPDATE_CONTACT_TYPE_NOT_ALLOWED: 'contact.update.contact_type_not_allowed',
  SUPPLIER_CATEGORY_REQUIRED: 'contact.form.supplier_category_required',
  COUNTRY_NOT_FOUND: 'contact.form.country_not_found',
  WRONG_FORMAT_POSTAL_CODE: 'contact.form.wrong_format_postal_code',
  SUPPLIER_TYPE_RENTAL_MUST_NOT_HAVE_CATEGORY:
    'contact.form.supplier_type_rental_must_not_have_category',
  SUPPLIER_TYPE_REGULAR_MUST_HAVE_CATEGORY:
    'contact.form.supplier_type_regular_must_have_category',
  EXITED_ORGANIZATION_NAME: 'contact.form.existed_organization_name',
  RENTAL_SUPPLIER_CONTRACT_CANNOT_CHANGE:
    'contact.update.rental_supplier_contract_cannot_change',
};
