import {
  DocumentType,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import { ContactDocument, ContactModel } from '../contact/contact.model';
import { CountryDocument, CountryModel } from '../country/country.model';
import { LocationDocument, LocationModel } from '../location/location.model';
import { RegionDocument, RegionModel } from '../region/region.model';

export type AddressDocument = DocumentType<AddressModel>;

@modelOptions({
  options: { customName: 'Address' },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class AddressModel extends BaseModel {
  @prop({ ref: () => RegionModel })
  region!: Ref<RegionDocument>;

  @prop({ ref: () => CountryModel })
  country!: Ref<CountryDocument>;

  @prop({ trim: true })
  city!: string;

  @prop({ trim: true })
  @prop()
  street!: string;

  @prop({ trim: true })
  @prop()
  number!: string;

  @prop({ trim: true })
  @prop()
  suffix!: string;

  @prop({ trim: true })
  @prop()
  postalCode!: string;

  @prop({ ref: () => ContactModel })
  contact?: Ref<ContactDocument>;

  @prop({ ref: () => LocationModel })
  location?: Ref<LocationDocument>;
}
