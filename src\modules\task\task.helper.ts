export const aggregatePeriodsForTaskPlanningOverview = (
  startDate: Date,
  endDate: Date,
  timezone: string,
): any[] => {
  const addDateLimits = {
    $addFields: {
      lowerLimit: { $max: ['$startDate', startDate] },
      upperLimit: { $min: ['$endDate', endDate] },
    },
  };
  const calculateDateRangeFromStartToEndWithinTheProvidedPeriod = {
    $addFields: {
      dateRange: {
        $dateDiff: {
          startDate: '$lowerLimit',
          endDate: '$upperLimit',
          unit: 'day',
          timezone,
        },
      },
    },
  };
  const generateDailyPeriodsBasedOnThePreviousRange = {
    $addFields: {
      dateRange: {
        $map: {
          input: { $range: [0, { $add: ['$dateRange', 1] }] },
          as: 'dayOffset',
          in: {
            $toDate: {
              $dateToString: {
                format: `%Y-%m-%dT00:00:00.000${timezone}`,
                timezone,
                date: {
                  $dateAdd: {
                    startDate: '$lowerLimit',
                    unit: 'day',
                    amount: '$$dayOffset',
                    timezone,
                  },
                },
              },
            },
          },
        },
      },
    },
  };
  const unwindDateRangeFromDailyPeriods = { $unwind: { path: '$dateRange' } };
  const addStartAt = {
    $addFields: {
      startAt: {
        $cond: {
          if: { $lt: ['$dateRange', '$lowerLimit'] },
          then: '$lowerLimit',
          else: '$dateRange',
        },
      },
    },
  };
  const addEndAt = {
    $addFields: {
      endAt: {
        $let: {
          vars: {
            formattedDate: {
              $toDate: {
                $dateToString: {
                  format: `%Y-%m-%dT23:59:59.999${timezone}`,
                  date: '$startAt',
                  timezone,
                },
              },
            },
          },
          in: {
            $cond: {
              if: { $gt: ['$$formattedDate', '$upperLimit'] },
              then: '$upperLimit',
              else: '$$formattedDate',
            },
          },
        },
      },
    },
  };
  const getDayFromStartDate = {
    $addFields: {
      date: {
        $dateToString: { format: '%Y-%m-%d', date: '$startAt', timezone },
      },
    },
  };

  return [
    addDateLimits,
    calculateDateRangeFromStartToEndWithinTheProvidedPeriod,
    generateDailyPeriodsBasedOnThePreviousRange,
    unwindDateRangeFromDailyPeriods,
    addStartAt,
    addEndAt,
    getDayFromStartDate,
  ];
};
