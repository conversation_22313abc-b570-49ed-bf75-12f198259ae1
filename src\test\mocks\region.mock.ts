import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { RegionModel } from '~/modules/region/region.model';

import { mockCountryData } from './country.mock';

const regionModel = getModelForClass(RegionModel);

export const mockRegionData = {
  _id: new ObjectId(),
  country: mockCountryData._id,
  createdAt: new Date(),
  name: 'Friesland',
  updatedAt: new Date(),
};

export async function initMockRegion(doc?: any) {
  const { _id, ...rest } = { ...mockRegionData, ...doc };
  await regionModel.replaceOne({ _id }, rest, { upsert: true });
}
