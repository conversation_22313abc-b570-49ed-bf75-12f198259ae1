import { HttpService } from '@nestjs/axios';
import { Test } from '@nestjs/testing';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { of } from 'rxjs';

import {
  INVENTORY_SERVICE_CLIENT,
  PDF_SERVICE_CLIENT,
} from '~/constants/app.constant';
import { ContactModel } from '~/modules/contact/contact.model';
import { ContactService } from '~/modules/contact/contact.service';
import { CostLineModel } from '~/modules/costline/costline.model';
import { CostTypeModel } from '~/modules/costtype/costtype.model';
import { UploadFileModel } from '~/modules/document-file/upload-file.model';
import { EmailTemplateModel } from '~/modules/email-template/email-template.model';
import { EquipmentModel } from '~/modules/equipment/equipment.model';
import { EquipmentService } from '~/modules/equipment/equipment.service';
import { IdentifierService } from '~/modules/identifier/identifier.service';
import { JobModel } from '~/modules/job/job.model';
import { JobService } from '~/modules/job/job.service';
import { JobEmployeeModel } from '~/modules/job-employee/job-employee.model';
import { JobEquipmentModel } from '~/modules/job-equipment/job-equipment.model';
import { JobPointModel } from '~/modules/job-point/job-point.model';
import { LocationModel } from '~/modules/location/location.model';
import { NightRegistrationService } from '~/modules/night-registration/night-registration.service';
import { TaskService } from '~/modules/task/task.service';
import { TeamModel } from '~/modules/team/team.model';
import { TenantService } from '~/modules/tenant/tenant.service';
import { TenantUserModel } from '~/modules/tenant-user/tenant-user.model';
import { TenantUserService } from '~/modules/tenant-user/tenant-user.service';
import { UnitModel } from '~/modules/unit/unit.model';
import { UnitService } from '~/modules/unit/unit.service';
import { EmailService } from '~/processors/email/email.service';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import {
  JobPeriodTypeEnum,
  JobStatusEnum,
  JobTypeEnum,
} from '~/shared/enums/job.enum';
import { PlanningOrderType } from '~/shared/enums/planning-order.enum';
import { TEAM_MESSAGE_KEYS } from '~/shared/message-keys/team.message-keys';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockJobPlanning } from '~/test/mocks/planning-job.mock';
import { initMockPlanningOrder } from '~/test/mocks/planningorder.mock';
import { initMockTeam, mockTeamData } from '~/test/mocks/team.mock';
import { mockTenantData } from '~/test/mocks/tenant.mock';
import { mockTenantUserData } from '~/test/mocks/tenantuser.mock';

import { ChangePositionInPlanningOrderSchemaDto } from '../dtos/planning-order.dto';
import {
  EquipmentPlanningOverviewQueryParamsDto,
  TeamPlanningOverviewQueryParamsDto,
} from '../dtos/planning-overview-query-params.dto';
import { PlanningService } from '../planning.service';
import { PlanningOrderModel } from '../planning-order.model';
import { planningOrderTest } from './planning.dto.test';

describe('PlanningService', () => {
  let service: PlanningService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        PlanningService,
        JobService,
        ...testInjectProviders([
          TeamModel,
          PlanningOrderModel,
          ContactModel,
          LocationModel,
          TenantUserModel,
          JobModel,
          JobPointModel,
          JobEmployeeModel,
          EquipmentModel,
          UnitModel,
          UploadFileModel,
          EmailTemplateModel,
          CostLineModel,
          CostTypeModel,
          JobEquipmentModel,
          // Service
          TaskService,
          EmailService,
          UnitService,
          IdentifierService,
          TenantUserService,
          ContactService,
          EquipmentService,
          TenantService,
          NightRegistrationService,
          HttpService,
        ]),
        {
          provide: INVENTORY_SERVICE_CLIENT,
          useValue: {
            send: jest.fn().mockReturnValue(of({ data: {} })),
          },
        },
        { provide: PDF_SERVICE_CLIENT, useValue: {} },
      ],
    }).compile();

    service = module.get(PlanningService);

    await Promise.all([
      initMockTeam({ tenantUsers: [mockTenantUserData._id] }),
      initMockPlanningOrder(),
    ]);

    await initMockJobPlanning();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  const baseSchedules = [
    {
      date: new Date().toISOString(),
      items: [
        {
          _id: new ObjectId().toString(),
          title: 'Task/Job/Equipment Title',
          category: 'Task/Job/Equipment Category',
          startAt: new Date().toISOString(),
          endAt: dayjs().add(1, 'day').toISOString(),
        },
      ],
    },
  ];

  const employeeSchedulesData = [
    {
      employee: {
        _id: mockTenantData._id.toString(),
        displayName: mockTenantData.name,
      },
      schedules: baseSchedules,
    },
  ];

  const equipmentSchedulesData = [
    {
      equipment: {
        _id: new ObjectId().toString(),
        name: 'Equipment Name',
      },
      schedules: baseSchedules,
    },
  ];

  describe('teamOverview', () => {
    const basePayload: TeamPlanningOverviewQueryParamsDto = {
      team: mockTeamData._id.toString(),
      isoWeek: 1,
      year: 2023,
      timezone: '+00:00',
    };

    it('should throw error if team not found', async () => {
      const teamId = new ObjectId().toString();

      await expect(
        service.teamOverview({ team: teamId } as any),
      ).rejects.toThrow(TEAM_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw error if employee not found in team', async () => {
      const payload: any = {
        team: mockTeamData._id.toString(),
        employees: [new ObjectId().toString()],
      };

      await expect(service.teamOverview(payload)).rejects.toThrow(
        TEAM_MESSAGE_KEYS.EMPLOYEE_NOT_FOUND,
      );
    });

    it('should return team overview with employees', async () => {
      jest
        .spyOn(service['taskService'], 'getEmployeesSchedules')
        .mockResolvedValue(employeeSchedulesData as any);

      const payload = {
        ...basePayload,
        employees: [mockTenantUserData._id.toString()],
      };

      const result = await service.teamOverview(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(planningOrderTest.teamOverviewSchema);
    });

    it('should return team overview without employees', async () => {
      jest
        .spyOn(service['taskService'], 'getEmployeesSchedules')
        .mockResolvedValue(employeeSchedulesData as any);

      const result = await service.teamOverview(basePayload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(planningOrderTest.teamOverviewSchema);
    });
  });

  describe('equipmentOverview', () => {
    it('should call and return equipment overview', async () => {
      jest
        .spyOn(service['taskService'], 'getEquipmentSchedules')
        .mockResolvedValue(equipmentSchedulesData as any);

      const payload: EquipmentPlanningOverviewQueryParamsDto = {
        equipmentType: EquipmentEnum.DEVICE,
        isoWeek: 1,
        year: 2023,
        timezone: '+00:00',
      };

      const result = await service.equipmentOverview(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(planningOrderTest.equipmentOverviewSchema);
    });
  });

  describe('changePositionInPlanningOrder', () => {
    it('should throw error if planning order not found', async () => {
      const payload: any = {
        date: new Date().toISOString(),
        employee: new ObjectId().toString(),
      };

      await expect(
        service.changePositionInPlanningOrder(payload),
      ).rejects.toThrow('Planning order not found');
    });

    it('should change position in planning order', async () => {
      const payload: ChangePositionInPlanningOrderSchemaDto = {
        date: new Date().toISOString(),
        employee: mockTenantData._id.toString(),
        jobs: [new ObjectId().toString()],
      };

      const result = await service.changePositionInPlanningOrder(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(planningOrderTest.modelSchema);
    });
  });

  describe('mappedJobsByPlanningOrder', () => {
    it('should update planning order when there are new items', async () => {
      const jobsSchedulesData = [
        {
          employee: {
            _id: new ObjectId().toString(),
            displayName: 'Jane Doe',
          },
          schedules: [
            {
              date: '2022-01-05',
              items: [
                {
                  _id: new ObjectId().toString(),
                  title: '',
                  status: 'complete',
                  type: 'regular',
                  jobType: 'inspection',
                  plannedDate: new Date('2022-01-05T00:00:00.000Z'),
                  location: {
                    _id: new ObjectId().toString(),
                    fullAddress: 'Zwolle, Gein 63',
                  },
                  estimatedHours: 60,
                },
              ],
            },
          ],
        },
      ];

      const result = await service['mappedJobsByPlanningOrder'](
        jobsSchedulesData,
        PlanningOrderType.EMPLOYEE,
      );
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        planningOrderTest.mappedJobsByPlanningOrderSchema,
      );
    });

    it('should add new items with correct positions when planning order exists', async () => {
      const employeeId = new ObjectId().toString();
      const existingJobId = new ObjectId().toString();
      const newJobId1 = new ObjectId().toString();
      const newJobId2 = new ObjectId().toString();
      const testDate = '2022-01-06';

      await initMockPlanningOrder({
        employee: employeeId,
        type: PlanningOrderType.EMPLOYEE,
        date: testDate,
        jobs: [
          {
            _id: existingJobId,
            position: 1,
          },
        ],
      });

      const jobsSchedulesData = [
        {
          employee: {
            _id: employeeId,
            displayName: 'John Doe',
          },
          schedules: [
            {
              date: testDate,
              items: [
                {
                  _id: existingJobId,
                  title: 'Existing Job',
                  status: JobStatusEnum.OPEN,
                  type: JobPeriodTypeEnum.REGULAR,
                  jobType: JobTypeEnum.INSPECTION,
                  plannedDate: new Date('2022-01-06T00:00:00.000Z'),
                  location: {
                    _id: new ObjectId().toString(),
                    fullAddress: 'Test Location 1',
                  },
                  estimatedHours: 30,
                },
                {
                  _id: newJobId1,
                  title: 'New Job 1',
                  status: JobStatusEnum.IN_PROGRESS,
                  type: JobPeriodTypeEnum.REGULAR,
                  jobType: JobTypeEnum.MAINTENANCE,
                  plannedDate: new Date('2022-01-06T00:00:00.000Z'),
                  location: {
                    _id: new ObjectId().toString(),
                    fullAddress: 'Test Location 2',
                  },
                  estimatedHours: 45,
                },
                {
                  _id: newJobId2,
                  title: 'New Job 2',
                  status: JobStatusEnum.COMPLETE,
                  type: JobPeriodTypeEnum.REGULAR,
                  jobType: JobTypeEnum.CLEANING,
                  plannedDate: new Date('2022-01-06T00:00:00.000Z'),
                  location: {
                    _id: new ObjectId().toString(),
                    fullAddress: 'Test Location 3',
                  },
                  estimatedHours: 60,
                },
              ],
            },
          ],
        },
      ];

      const result = await service['mappedJobsByPlanningOrder'](
        jobsSchedulesData,
        PlanningOrderType.EMPLOYEE,
      );

      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        planningOrderTest.mappedJobsByPlanningOrderSchema,
      );
      const schedule = result[0].schedules[0];
      expect(schedule.items).toHaveLength(3);
      expect(schedule.items[0]._id).toBe(existingJobId);
      expect(schedule.items[1]._id).toBe(newJobId1);
      expect(schedule.items[2]._id).toBe(newJobId2);
    });

    it('should handle new items when no existing jobs', async () => {
      const employeeId = new ObjectId().toString();
      const newJobId = new ObjectId().toString();
      const testDate = '2022-01-07';

      await initMockPlanningOrder({
        employee: employeeId,
        type: PlanningOrderType.EMPLOYEE,
        date: testDate,
        jobs: [],
      });

      const jobsSchedulesData = [
        {
          employee: {
            _id: employeeId,
            displayName: 'Test Employee',
          },
          schedules: [
            {
              date: testDate,
              items: [
                {
                  _id: newJobId,
                  title: 'First Job',
                  status: JobStatusEnum.OPEN,
                  type: JobPeriodTypeEnum.REGULAR,
                  jobType: JobTypeEnum.INSPECTION,
                  plannedDate: new Date('2022-01-07T00:00:00.000Z'),
                  location: {
                    _id: new ObjectId().toString(),
                    fullAddress: 'Test Location',
                  },
                  estimatedHours: 30,
                },
              ],
            },
          ],
        },
      ];

      const result = await service['mappedJobsByPlanningOrder'](
        jobsSchedulesData,
        PlanningOrderType.EMPLOYEE,
      );

      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        planningOrderTest.mappedJobsByPlanningOrderSchema,
      );
      const schedule = result[0].schedules[0];
      expect(schedule.items).toHaveLength(1);
      expect(schedule.items[0]._id).toBe(newJobId);
    });
  });
});
