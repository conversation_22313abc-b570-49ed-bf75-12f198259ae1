import { ConfigService } from '@nestjs/config';
import { mongoose } from '@typegoose/typegoose';
import consola from 'consola';
import { setPatchHistoryTTL } from 'ts-patch-mongoose';
import { chalk } from 'zx-cjs';

import { DB_CONNECTION_TOKEN } from '~/constants/app.constant';

export const DatabaseProvider = {
  provide: DB_CONNECTION_TOKEN,
  useFactory: async (configService: ConfigService) => {
    const env = configService.get<string>('app.env') || 'development';
    const databaseUri = configService.get<string>('database.uri') || '';

    let reconnectionTask: NodeJS.Timeout | null = null;
    const RECONNECT_INTERVAL = 6000;

    if (env === 'development') {
      mongoose.set('debug', true);
      mongoose.set('debug', { color: true });
    }

    // override mongoose serverSelectionTimeoutMS, tmp increase to 30s for testing
    mongoose.set('bufferTimeoutMS', 30000);

    const connection = () => {
      return mongoose.connect(databaseUri);
    };

    const Badge = ` [${chalk.yellow('MongoDB')}]`;

    const color = (str: TemplateStringsArray) => {
      return str.map((s) => chalk.green(s)).join('');
    };

    mongoose.connection.on('connecting', () => {
      consola.info(Badge, color`connecting...`);
    });

    mongoose.connection.on('open', () => {
      consola.info(Badge, color`ready!`);
      if (reconnectionTask) {
        clearTimeout(reconnectionTask);
        reconnectionTask = null;
      }

      setPatchHistoryTTL('30d')
        .then(() => {
          consola.info(Badge, color`patch history TTL set to 30 days`);
        })
        .catch((error) => {
          consola.error(Badge, color`failed to set patch history TTL`, error);
        });
    });

    mongoose.connection.on('disconnected', () => {
      consola.error(
        Badge,
        chalk.red(
          `disconnected! retry when after ${RECONNECT_INTERVAL / 1000}s`,
        ),
      );
      reconnectionTask = setTimeout(connection, RECONNECT_INTERVAL);
    });

    mongoose.connection.on('error', (error) => {
      consola.error(Badge, 'error!', error);
      mongoose.disconnect();
    });

    return await connection().then((mongoose) => mongoose.connection);
  },
  inject: [ConfigService],
};
