import {
  DocumentType,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import { LocationDocument, LocationModel } from '../location/location.model';

export type CostCenterDocument = DocumentType<CostCenterModel>;

@modelOptions({
  options: { customName: 'CostCenter' },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class CostCenterModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({ trim: true, required: true })
  identifier!: string;

  @prop({ trim: true, required: true })
  name!: string;

  @prop({ ref: () => LocationModel })
  locations?: Ref<LocationDocument>[];

  @prop({ default: false })
  isSynced!: boolean;
}
