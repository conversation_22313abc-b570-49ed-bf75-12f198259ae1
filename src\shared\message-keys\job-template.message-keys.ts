export const JOB_TEMPLATE_MESSAGE_KEY = {
  NOT_FOUND: 'job_template.find.not_found',
  EXISTED_IN_SYSTEM: 'job_template.find.existed_in_system',
  TYPE_GENERAL_DOES_NOT_HAVE_UNIT:
    'job_template.create.type_general_does_not_have_unit',
  TYPE_INSPECTION_MUST_HAVE_UNIT:
    'job_template.create.type_inspection_must_have_unit',
  TYPE_INSPECTION_CANNOT_CUSTOM_NAME:
    'job_template.create.type_inspection_cannot_custom_name',
  UNIT_NOT_FOUND: 'job_template.create.unit_not_found',
  CANNOT_ADD_OR_REMOVE_POINTS:
    'job_template.update.cannot_add_or_remove_points',
  POINT_NOT_FOUND: 'job_template.delete.point_not_found',
  POINTS_NOT_FOUND: 'job_template.update.points_not_found',
  UNITS_NOT_FOUND: 'job_template.copy.units_not_found',
  TYPE_GENERAL_MUST_HAVE_NAME:
    'job_template.create.type_general_must_have_name',
};
