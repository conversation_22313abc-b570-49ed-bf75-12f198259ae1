// strategies/contact-context.ts
import { BadRequestException, Injectable } from '@nestjs/common';

import { ContactRole } from '~/shared/enums/contact.enum';
import { QueryParams } from '~/utils';

import { ContactDebtorStrategy } from './contact-debtor.strategy';
import { ContactOrganizationStrategy } from './contact-organization.strategy';
import { ContactPersonStrategy } from './contact-person.strategy';
import { ContactStrategy } from './contact-strategy.interface';
import { ContactSupplierStrategy } from './contact-supplier.strategy';

@Injectable()
export class ContactContext {
  private strategy!: ContactStrategy;

  constructor(
    private readonly contactOrganizationStrategy: ContactOrganizationStrategy,
    private readonly contactPersonStrategy: ContactPersonStrategy,
    private readonly contactDebtorStrategy: ContactDebtorStrategy,
    private readonly contactSupplierStrategy: ContactSupplierStrategy,
  ) {}

  public setStrategy(type: string) {
    switch (type) {
      case ContactRole.PERSON:
        this.strategy = this.contactPersonStrategy;
        break;
      case ContactRole.ORGANIZATION:
        this.strategy = this.contactOrganizationStrategy;
        break;
      case ContactRole.DEBTOR:
        this.strategy = this.contactDebtorStrategy;
        break;
      case ContactRole.SUPPLIER:
        this.strategy = this.contactSupplierStrategy;
        break;
      default:
        throw new BadRequestException('Invalid contact type');
    }
  }

  findAll(params: QueryParams) {
    return this.strategy.findAll(params);
  }

  findOne(id: string) {
    return this.strategy.findOne(id);
  }

  create(data: any) {
    return this.strategy.create(data);
  }

  update(id: string, data: any) {
    return this.strategy.update(id, data);
  }
  getValidContact(id) {
    return this.strategy.getValidContact(id);
  }
}
