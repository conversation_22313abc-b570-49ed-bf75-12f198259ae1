import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as fs from 'fs';
import { MongoClient } from 'mongodb';
import * as path from 'path';

import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';

import { MigrationModel, MigrationStatus } from './migration.model';

export type MigrationContext = {
  sourceClient: MongoClient | null;
  inventorySourceClient: MongoClient | null;
  destinationClient: MongoClient | null;
  generalClient?: MongoClient | null;
};

@Injectable()
export class MigrationService implements OnModuleInit {
  private readonly logger = new Logger(MigrationService.name);

  private scripts: any[] = [];
  private context: MigrationContext = {
    sourceClient: null,
    inventorySourceClient: null,
    destinationClient: null,
    generalClient: null,
  };

  constructor(
    @InjectModel(MigrationModel)
    private readonly migrationModel: MongooseModel<MigrationModel>,

    private readonly configService: ConfigService,
  ) {}
  async onModuleInit() {
    this.init().then(() => this.up());
  }

  async init() {
    this.logger.log(`Starting Init Migration...`);
    const sourceDbUri =
      this.configService.get<string>('migration.sourceDbUri') || '';
    const inventorySourceDbUri =
      this.configService.get<string>('migration.inventorySourceDbUri') || '';
    const destinationDbUri =
      this.configService.get<string>('migration.destinationDbUri') || '';
    const generalDbUri =
      this.configService.get<string>('migration.generalDbUri') || '';

    if (sourceDbUri) {
      const sourceClient = new MongoClient(sourceDbUri);
      this.context.sourceClient = await sourceClient.connect();
    }

    if (inventorySourceDbUri) {
      const inventorySourceClient = new MongoClient(inventorySourceDbUri);
      this.context.inventorySourceClient =
        await inventorySourceClient.connect();
    }

    if (generalDbUri) {
      const generalClient = new MongoClient(generalDbUri);
      this.context.generalClient = await generalClient.connect();
    }

    const destinationClient = new MongoClient(destinationDbUri);
    this.context.destinationClient = await destinationClient.connect();

    await this.getLatestScript();

    this.logger.log(`Source DB URI: ${sourceDbUri}`);
    this.logger.log(`Destination DB URI: ${destinationDbUri}`);
    this.logger.log(`Inventory Source DB URI: ${inventorySourceDbUri}`);
    this.logger.log(`General DB URI: ${generalDbUri}`);

    return this;
  }

  async getLatestScript() {
    /* will open this when all scripts are ready
    const nextVersion = (await this.getLatestMigrationVersion()) + 1;

    const files = fs.readdirSync(path.join(__dirname, 'scripts'));

    if (!files.length) {
      return [];
    }

    this.scripts = files
      .filter((file) => file.endsWith('.js'))
      .reduce((acc, file) => {
        const version = parseInt(file.split('__')[0].replace('V', ''), 10);
        if (version && version >= nextVersion) {
          acc.push({
            version,
            file,
            filePath: path.join(__dirname, 'scripts', file),
          });
        }

        return acc;
      }, [] as any[])
      .sort((a, b) => a.version - b.version);
    */
    const successVersions = await this.getSuccessVersions();
    this.logger.log(`successVersions: ${successVersions}`);
    // get available versions
    const env = this.configService.get<string>('app.env') || 'development';
    this.logger.log(`env: ${env}`);
    const listMigrationVersions =
      this.configService.get<string>('migration.listMigrationVersions') || '';
    this.logger.log(`listMigrationVersions:  ${listMigrationVersions}`);
    const migrationVersionArrays = listMigrationVersions.split(',').map(Number);

    const availableVersions = this.getAvailableVersions(
      migrationVersionArrays,
      successVersions,
    );

    const files = fs.readdirSync(path.join(__dirname, 'scripts'));

    if (!files.length) {
      return [];
    }
    this.scripts = files
      .filter((file) => file.endsWith('.js'))
      .reduce((acc, file) => {
        const version = parseInt(file.split('__')[0].replace('V', ''), 10);
        if (availableVersions.includes(version)) {
          acc.push({
            version,
            file,
            filePath: path.join(__dirname, 'scripts', file),
          });
        }

        return acc;
      }, [] as any[])
      .sort((a, b) => a.version - b.version);
  }

  async getSuccessVersions() {
    const migrations = await this.migrationModel.find({
      status: MigrationStatus.SUCCESS,
    });

    return migrations.map((m) => m.version);
  }

  getAvailableVersions = (allVersions: number[], successVersions: number[]) => {
    return allVersions.filter((v) => !successVersions.includes(v));
  };

  async getLatestMigrationVersion() {
    const latestMigration = await this.migrationModel
      .findOne()
      .sort({ version: -1 })
      .exec();

    return latestMigration?.version || 0;
  }

  async up() {
    this.logger.log(`Total scripts will run: ${this.scripts.length}`);

    for (const script of this.scripts) {
      const up = await import(script.filePath).then((m) => m.default);

      const migration = await this.migrationModel.findOneAndUpdate(
        {
          version: script.version,
          status: MigrationStatus.PENDING,
        },
        {
          $set: {
            version: script.version,
            script: script.file,
            status: MigrationStatus.PENDING,
          },
        },
        {
          upsert: true,
          new: true,
        },
      );

      try {
        await up(this.context);

        await this.migrationModel.updateOne(
          {
            _id: migration?._id,
          },
          {
            status: MigrationStatus.SUCCESS,
          },
        );
      } catch (ex) {
        console.error(ex);
        await this.migrationModel.updateOne(
          {
            _id: migration?._id,
          },
          {
            status: MigrationStatus.FAILED,
          },
        );
        // stop further migration
        break;
      }
    }
  }
}
