import _ from 'lodash';
import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

type OldAgreementLineType = 'Accommodation' | 'Service' | 'Product';
type OldPeriodType = 'Periodic' | 'One-time';
type OldPeriodUnit = 'Monthly' | 'Four-weekly' | 'Weekly';

interface OldAgreementLine {
  _id: Types.ObjectId;
  type: OldAgreementLineType;
  periodType: OldPeriodType;
  period?: OldPeriodUnit;
  units: Types.ObjectId[];
  position: number;
  rentingContract: Types.ObjectId;
  costLineGenerals: { _id: Types.ObjectId; unit?: Types.ObjectId }[];
  createdAt: Date;
  updatedAt: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection
    .aggregate()
    .match({
      _id: { $gt: nextId },
    })
    .lookup({
      from: 'costlinegeneral',
      as: 'costLineGenerals',
      let: { agreementLineId: '$_id' },
      pipeline: [
        { $match: { $expr: { $eq: ['$agreementLine', '$$agreementLineId'] } } },
        { $project: { _id: 1, unit: 1 } },
      ],
    })
    .sort({ _id: 1 })
    .limit(limit);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const transformData = ({ data }: { data: OldAgreementLine[] }) => {
      return Promise.all(
        data.map(async (contract: OldAgreementLine) => {
          let units = contract.costLineGenerals.map(
            (costLineGeneral) => costLineGeneral.unit,
          );
          units = _.uniqWith(units, (a, b) => a?.toString() === b?.toString());
          units = _.compact(units);

          return omitNull({
            _id: contract._id,
            isDeleted: false,
            type: contract.type.toLowerCase(),
            periodType: contract.periodType.toLowerCase(),
            period: contract.period?.toLowerCase(),
            units: units,
            costLineGenerals: contract.costLineGenerals.map(
              (costLineGeneral) => costLineGeneral._id,
            ),
            position: contract.position,
            contract: contract.rentingContract,
            createdAt: contract.createdAt,
            updatedAt: contract.updatedAt,
          });
        }),
      );
    };

    await migrationV2({
      context,
      sourceCollectionName: 'agreementline',
      destinationCollectionName: 'agreementlines',
      pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
