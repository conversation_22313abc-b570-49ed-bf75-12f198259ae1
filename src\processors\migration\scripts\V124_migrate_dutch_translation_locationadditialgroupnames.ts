import { Collection } from 'mongodb';

import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';

import { MigrationContext } from '../migration.service';

interface ITranslation {
  key: string;
  newNlDesc: string;
  position?: number;
}

const translations: { [k in LocationAdditionalType]: ITranslation[] } = {
  [LocationAdditionalType.FEATURE_AND_SUPPLIER]: [
    { key: 'Afval', newNlDesc: 'Afval' },
    { key: 'Keukenapparatuur', newNlDesc: 'Keukenapparatuur' },
    { key: 'Wasmachine & Droger', newNlDesc: 'Wasmachines en drogers' },
    { key: 'Stoffering', newNlDesc: 'Stoffering' },
    { key: 'Meubilering', newNlDesc: 'Meubilering' },
    { key: 'Tuinen', newNlDesc: 'Tuinen' },
    { key: 'Sloten/card systeem', newNlDesc: 'Sloten/kaartsysteem' },
    { key: 'Sleutelplan', newNlDesc: 'Sluitplan' },
    { key: 'Camerasysteem', newNlDesc: 'Camera systeem' },
    { key: 'Beveiliging', newNlDesc: 'Beveiliging' },
    { key: 'Ongediertebestrijding', newNlDesc: 'Plaagdierbeheersing' },
    { key: 'Overig', newNlDesc: 'Anders, namelijk' },
  ],
  [LocationAdditionalType.CERTIFICATE_AND_CONTROL]: [
    { key: 'HeatingVersion', newNlDesc: 'Warmtevoorziening' },
    { key: 'FireKillResources', newNlDesc: 'Brandblusmiddelen' },
    { key: 'FireInstallation', newNlDesc: 'Brandmeldinstallatie' },
    { key: 'Legionella', newNlDesc: 'Legionella beheer' },
    { key: 'Thermostat', newNlDesc: 'Thermostaat' },
    { key: 'Noodverlichting', newNlDesc: 'Noodverlichting' },
    { key: 'Other', newNlDesc: 'Anders, namelijk' },
    { key: 'Others', newNlDesc: 'Anders, namelijk' },
  ],
  [LocationAdditionalType.GWE_AND_METER_READING]: [],
};

const moveUp = async (
  collection: Collection<any>,
  type: LocationAdditionalType,
  before: string,
  target: string,
) => {
  const updatedPositions: any[] = [];

  const objects = await collection
    .find({ type })
    .sort({ position: 1 })
    .toArray();
  let inc = 0;
  objects.forEach((obj, position) => {
    if (obj.key !== target) {
      updatedPositions.push({
        updateOne: {
          filter: { _id: obj._id },
          update: { $set: { position: position + 1 + inc } },
        },
      });
    } else {
      inc--;
    }

    if (obj.key === before) {
      inc++;
      const targetObj = objects.find((item) => item.key === target)!;

      updatedPositions.push({
        updateOne: {
          filter: { _id: targetObj._id },
          update: { $set: { position: position + 2 } },
        },
      });
    }
  });

  const result = await collection.bulkWrite(updatedPositions);
  console.info(
    `Repositioned ${result.modifiedCount} location additional group names`,
  );
};

const up = async (context: MigrationContext) => {
  try {
    const locationAdditionalGroupNameCollection = context?.destinationClient
      ?.db()
      .collection('locationadditionalgroupnames');

    for (const type in translations) {
      const translationList = translations[type as LocationAdditionalType];

      for (const { key, newNlDesc } of translationList) {
        const locationAdditionalGroupName =
          await locationAdditionalGroupNameCollection?.findOne({ type, key });

        if (!locationAdditionalGroupName) {
          console.warn(
            `Location additional group name not found for type="${type}", key="${key}"`,
          );
          continue;
        }

        const result = await locationAdditionalGroupNameCollection?.updateOne(
          { _id: locationAdditionalGroupName._id },
          {
            $set: {
              dutchDescription: newNlDesc,
              updatedAt: new Date(),
            },
          },
        );

        console.info(
          ` Updated ${result?.modifiedCount} location additional group name: [${key}] "${locationAdditionalGroupName.dutchDescription}" to "${newNlDesc}"`,
        );
      }
    }

    await moveUp(
      locationAdditionalGroupNameCollection!,
      LocationAdditionalType.FEATURE_AND_SUPPLIER,
      'Sloten/card systeem',
      'Sleutelplan',
    );

    await moveUp(
      locationAdditionalGroupNameCollection!,
      LocationAdditionalType.CERTIFICATE_AND_CONTROL,
      'FireInstallation',
      'Noodverlichting',
    );
  } catch (error) {
    console.error('Error updating translations:', error);
  }
};

export default up;
