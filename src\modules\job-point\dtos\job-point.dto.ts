import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { JobPointStatusEnum, JobTypeEnum } from '~/shared/enums/job.enum';

const jobTypeValues = Object.values(JobTypeEnum) as [string, ...string[]];
const jobPointStatusValues = Object.values(JobPointStatusEnum) as [
  string,
  ...string[],
];

const JobPointActionSchema = z.strictObject({
  images: z.array(z.string()).max(8).optional(),
  description: z.string().max(256),
  type: z.enum(jobTypeValues),
  isGrouped: z.boolean().optional().default(false),
});

export const CreateJobPointSchema = z.strictObject({
  _id: z.string().optional(),
  description: z.string().max(256),
  position: z.number().optional(),
  unit: z.string().refine((v) => isValidObjectId(v)),
  job: z.string().refine((v) => isValidObjectId(v)),
});

export const MobileUpdateCostlineOfJobPointSchema = z.strictObject({
  _id: z
    .string()
    .refine((v) => isValidObjectId(v))
    .optional(),
  description: z.string().max(256).optional().default(''),
  price: z.number().min(0).max(1000000).optional().default(0),
  quantity: z.number().min(0).max(100).default(1),
  position: z.number(),
});

export const PortalUpdateCostlineOfJobPointSchema =
  MobileUpdateCostlineOfJobPointSchema.omit({
    description: true,
    price: true,
  }).extend({
    description: z.string().max(256),
    price: z.number().min(0.01).max(1000000),
    costType: z.string().refine((v) => isValidObjectId(v)),
  });

export const UpdateJobPointSchema = z.strictObject({
  _id: z.string().refine((v) => isValidObjectId(v)),
  status: z
    .enum([
      JobPointStatusEnum.GREEN,
      JobPointStatusEnum.YELLOW,
      JobPointStatusEnum.RED,
    ])
    .optional(),
  notes: z.string().max(2048).optional().default(''),
  images: z.array(z.string()).max(8).optional().default([]),
  actions: z.array(JobPointActionSchema).optional().default([]),
  costLines: z
    .array(PortalUpdateCostlineOfJobPointSchema)
    .default([])
    .optional(),
});

export const MobileUpdateJobPointSchema = UpdateJobPointSchema.omit({
  status: true,
  costLines: true,
}).extend({
  status: z.enum(jobPointStatusValues),
  costLines: z
    .array(MobileUpdateCostlineOfJobPointSchema)
    .default([])
    .optional(),
});

export class CreateJobPointDto extends createZodDto(CreateJobPointSchema) {}

export class UpdateJobPointDto extends createZodDto(UpdateJobPointSchema) {}
