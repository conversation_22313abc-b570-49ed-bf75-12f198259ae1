import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { TeamModel } from '~/modules/team/team.model';
import { teamTest } from '~/modules/team/test/team.dto.test';

const teamModel = getModelForClass(TeamModel);
type teamType = z.infer<typeof teamTest.modelSchema>;

export const mockTeamData = {
  _id: new ObjectId(),
  isActive: true,
  name: 'Team 1',
  tenantUsers: [new ObjectId(), new ObjectId()],
  isEquipment: false,
  position: 1,
};

export async function initMockTeam(doc?: Partial<teamType>) {
  const { _id, ...rest } = { ...mockTeamData, ...doc };
  await teamModel.replaceOne({ _id }, rest, { upsert: true });
}
