import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
  Severity,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { EnergyLabel } from '~/shared/enums/location.enum';
import { BaseModel } from '~/shared/models/base.model';

import { AddressDocument, AddressModel } from '../address/address.model';
import {
  BvCompanyDocument,
  BvCompanyModel,
} from '../bvcompany/bvcompany.model';
import {
  CostCenterDocument,
  CostCenterModel,
} from '../costcenter/costcenter.model';
import {
  LocationFileDocument,
  LocationFileModel,
} from '../location-file/location-file.model';
import { TeamModel } from '../team/team.model';
import { UnitDocument, UnitModel } from '../unit/unit.model';

export type LocationDocument = DocumentType<LocationModel>;

export interface LocationOfItem {
  key: string;
  value: string;
  position: number;
}
interface CoordinateDto {
  lng: number;
  lat: number;
}
interface Geo {
  type: string;
  coordinates: CoordinateDto;
}

@modelOptions({
  options: { customName: 'Location', allowMixed: Severity.ALLOW },
})
@index({ isActive: 1, isDeleted: 1 })
@index({ team: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class LocationModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({ default: false })
  isRenting!: boolean;

  @prop({ default: false })
  isService!: boolean;

  @prop({ default: 0 })
  maxOccupants!: number;

  @prop({ default: 0 })
  maxArea!: number;

  @prop({ ref: () => BvCompanyModel })
  bvCompany!: Ref<BvCompanyDocument>;

  @prop({ trim: true, maxlength: 256 })
  fullAddress!: string;

  @prop({ ref: () => AddressModel })
  address!: Ref<AddressDocument>;

  @prop({
    trim: true,
    maxlength: 128,
  })
  email?: string;

  @prop({ default: [] })
  locationOf?: LocationOfItem[];

  @prop({
    default: {
      type: 'Point',
      coordinates: {
        lng: 0,
        lat: 0,
      },
    },
    required: false,
  })
  geo?: Geo;

  @prop({ ref: () => TeamModel })
  team!: Ref<TeamModel>;

  @prop({ ref: () => UnitModel })
  units!: Ref<UnitDocument>[];

  @prop({ ref: () => CostCenterModel })
  costCenter!: Ref<CostCenterDocument>;

  // upload files
  @prop({ ref: () => LocationFileModel })
  uploadFiles?: Ref<LocationFileDocument>[];

  // number of parking spaces
  @prop({ default: 0 })
  parkingSpaces?: number;

  @prop({ enum: EnergyLabel, default: EnergyLabel.G })
  energyLabel!: EnergyLabel;

  @prop({
    required: false,
    min: 1,
    type: Number,
    validate: {
      validator: (v) => Number.isInteger(v) || v === null,
      message: '{VALUE} is not an integer value',
    },
  })
  maximumStayDuration?: number;
}
