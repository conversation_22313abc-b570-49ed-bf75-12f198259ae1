import {
  ArgumentsHost,
  Catch,
  ExceptionFilter,
  HttpException,
  HttpStatus,
  Injectable,
} from '@nestjs/common';
import { RpcException } from '@nestjs/microservices';
import { FastifyReply, FastifyRequest } from 'fastify';
import { throwError } from 'rxjs';

import { SendErrorEmailToLentoDto } from '~/processors/email/dtos/send-email.dto';
import { EmailService } from '~/processors/email/email.service';
import { LentoIntegrateException } from '~/shared/exception/lento-integrate-exception.dto';

type Error = {
  readonly status: number;
  readonly statusCode?: number;
  readonly message?: string;
  readonly _message?: string;
};

@Catch()
@Injectable()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(private readonly emailService: EmailService) {}
  catch(exception: unknown, host: ArgumentsHost) {
    const ctx = host.switchToHttp();

    const response = ctx.getResponse<FastifyReply>();
    const request = ctx.getRequest<FastifyRequest>();

    if (request.method === 'OPTIONS') {
      return response.status(HttpStatus.OK).send();
    }

    if (exception instanceof LentoIntegrateException) {
      const sendEmailToLentoDto: SendErrorEmailToLentoDto = {
        PROCESS_NAME: exception.processName,
        TENANT: '',
        ERROR_CODE: exception.errorCode,
        ERROR_MESSAGE: exception.message,
        ERROR_DETAIL: JSON.stringify(exception.getResponse()),
        REQUEST_BODY: exception.requestBody ?? JSON.stringify(request),
        TIME: new Date().toISOString(),
      };
      this.emailService
        .sendErrorEmailToLento(sendEmailToLentoDto)
        .then(() => {
          console.log('Error email sent to Lento successfully');
        })
        .catch((error) => {
          console.error('Failed to send error email to Lento:', error);
        });
    }
    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        : (exception as any)?.response?.status ||
          (exception as any)?.response?.statusCode ||
          (exception as any)?.status ||
          (exception as any)?.statusCode ||
          HttpStatus.INTERNAL_SERVER_ERROR;

    const message =
      (exception as any)?.response?.message ||
      (exception as Error)?.message ||
      (exception as Error)?._message ||
      'Unknown Error';

    const errors = (exception as any)?.response?.errors || [];

    return throwError(
      () =>
        new RpcException({
          status: status,
          statusCode: status,
          message,
          errors,
        }),
    );
  }
}
