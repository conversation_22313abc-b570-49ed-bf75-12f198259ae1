import { Test, TestingModule } from '@nestjs/testing';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';

import { DATE_FORMAT_HYPHEN } from '~/constants/app.constant';
import { LocationModel } from '~/modules/location/location.model';
import { UnitModel } from '~/modules/unit/unit.model';
import { AgreementLineType, ContractType } from '~/shared/enums/contract.enum';
import {
  // testClearCollections,
  TestDBModule,
} from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockAgreementLine,
  mockAgreementLineData,
} from '~/test/mocks/agreementline.mock';
import { initMockContract } from '~/test/mocks/contract.mock';
import {
  initMockCostlineGeneral,
  mockCostlineGeneralData,
} from '~/test/mocks/costlinegeneral.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { initMockStatsOccupant } from '~/test/mocks/stats-occupant.mock';
import { initMockUnit } from '~/test/mocks/unit.mock';

import { StatsOccupantModel } from '../stats-occupant.model';
import { StatsOccupantService } from '../stats-occupant.service';

const unitId2 = new ObjectId();
const maxOccupantsUnit2 = 10;
const unitId3 = new ObjectId();
const maxOccupantsUnit3 = 10;

const locationMaxOccupants = 30;

const costlineGeneralId2 = new ObjectId();

const agreementLineId2 = new ObjectId();

describe('StatsOccupantService', () => {
  let service: StatsOccupantService;
  const creditorContractId = new ObjectId();
  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        StatsOccupantService,
        ...testInjectModel([LocationModel, StatsOccupantModel, UnitModel]),
      ],
    }).compile();

    service = module.get(StatsOccupantService);

    await Promise.all([
      initMockLocation({
        maxOccupants: locationMaxOccupants,
      }),
      initMockUnit({ isRoot: false, name: 'Unit 1' }),
      initMockUnit({
        _id: unitId2,
        name: 'Unit 2',
        location: mockLocationData._id,
        maxOccupants: maxOccupantsUnit2,
        isRoot: false,
      }),
      initMockUnit({
        _id: unitId3,
        name: 'Unit 3',
        location: mockLocationData._id,
        maxOccupants: maxOccupantsUnit3,
        isRoot: false,
      }),
      initMockCostlineGeneral(),
      initMockCostlineGeneral({
        _id: costlineGeneralId2,
        unit: unitId2,
      }),
      initMockAgreementLine({
        costLineGenerals: [mockCostlineGeneralData._id],
        type: AgreementLineType.ACCOMMODATION,
      }),
      initMockAgreementLine({
        _id: agreementLineId2,
        costLineGenerals: [costlineGeneralId2],
        type: AgreementLineType.SERVICE,
      }),
      initMockContract({
        _id: creditorContractId,
        type: ContractType.CREDITOR,
      }),
      initMockContract({
        agreementLines: [mockAgreementLineData._id, agreementLineId2],
        location: mockLocationData._id,
        type: ContractType.RENTING,
        isWholeLocation: false,
      }),
      initMockStatsOccupant({ creditorContracts: [creditorContractId] }),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('calculateHiredLocations', () => {
    it('should return hired locations', async () => {
      const result = await service.calculateHiredLocations({});
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
    });
  });

  describe('exportListOccupation', () => {
    it('should export occupation list with correct structure', async () => {
      await service.recalculateStatsOccupantsMarkNeedToUpdate({
        locationId: mockLocationData._id,
      });
      const result = await service.exportListOccupation({});
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
      expect(result).toHaveProperty('fileName');
      expect(Array.isArray(result.data)).toBe(true);
      expect(Array.isArray(result.header)).toBe(true);
      expect(typeof result.fileName).toBe('string');
      const currentDate = dayjs()
        .utc()
        .startOf('day')
        .format(DATE_FORMAT_HYPHEN);
      expect(result.fileName).toEqual(`list-occupations-${currentDate}.csv`);
      if (result.data.length > 0) {
        expect(result.data[0]).toHaveProperty('dateOfReport');
        expect(result.data[0]).toHaveProperty('costCenter');
        expect(result.data[0]).toHaveProperty('location');
        expect(result.data[0]).toHaveProperty('team');
        expect(result.data[0]).toHaveProperty('maxCount');
        expect(result.data[0]).toHaveProperty('hiredCount');
        expect(result.data[0]).toHaveProperty('emptyCount');
      }
    });
  });

  describe('getListOccupation', () => {
    it('should return stats summary', async () => {
      await service.recalculateStatsOccupantsMarkNeedToUpdate({
        locationId: mockLocationData._id,
      });
      const result = await service.getListOccupation({});
      expect(Array.isArray(result.items)).toBe(true);
      const firstItem = result.items[0];
      const emptyCount = maxOccupantsUnit2 + maxOccupantsUnit3;
      expect(firstItem.emptyCount).toEqual(emptyCount);
      expect(firstItem.hiredCount).toEqual(locationMaxOccupants - emptyCount);
      expect(firstItem.unHiredUnits).toBeDefined();
      expect(firstItem.unHiredUnits.length).toStrictEqual(2);
      expect(result.totalMaxCount).toBe(locationMaxOccupants);
      expect(result.totalHiredCount).toBe(locationMaxOccupants - emptyCount);
      expect(result.totalEmptyCount).toBe(emptyCount);
    });
  });

  describe('getListVacancies', () => {
    it('should return vacancies', async () => {
      await service.recalculateStatsOccupantsMarkNeedToUpdate({
        locationId: mockLocationData._id,
      });
      const result = await service.getListVacancies({});
      expect(Array.isArray(result)).toBe(true);
      const firstItem = result[0];
      expect(firstItem.emptyCount).toEqual(
        maxOccupantsUnit2 + maxOccupantsUnit3,
      );
    });
  });

  describe('exportListVacancies', () => {
    it('should export vacancies list with correct structure', async () => {
      await service.recalculateStatsOccupantsMarkNeedToUpdate({
        locationId: mockLocationData._id,
      });
      const result = await service.exportListVacancies({});
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
      expect(result).toHaveProperty('fileName');
      expect(Array.isArray(result.data)).toBe(true);
      expect(Array.isArray(result.header)).toBe(true);
      expect(typeof result.fileName).toBe('string');
      if (result.data.length > 0) {
        expect(result.data[0]).toHaveProperty('dateOfReport');
        expect(result.data[0]).toHaveProperty('locationInfo');
        expect(result.data[0]).toHaveProperty('teamInfo');
        expect(result.data[0]).toHaveProperty('unit');
        expect(result.data[0]).toHaveProperty('emptyCount');
      }
    });
  });

  describe('recalculateStatsOccupantsMarkNeedToUpdate', () => {
    it('should mark stats occupants as needing update', async () => {
      const payload = {
        locationId: mockLocationData._id,
      };
      const result =
        await service.recalculateStatsOccupantsMarkNeedToUpdate(payload);
      expect(result).toBeDefined();
    });
    it('should not throw error if locationId is undefined', async () => {
      const payload = {};
      await expect(
        service.recalculateStatsOccupantsMarkNeedToUpdate(payload),
      ).rejects.toThrow('Location ID is required');
    });
    it('should not throw error if location not found', async () => {
      const newLocationId = new ObjectId();
      const payload = { locationId: newLocationId };
      await expect(
        service.recalculateStatsOccupantsMarkNeedToUpdate(payload),
      ).rejects.toThrow(`Location with ID ${newLocationId} not found`);
    });
  });
});
