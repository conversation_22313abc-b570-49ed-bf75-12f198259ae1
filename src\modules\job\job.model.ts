import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
  Severity,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import {
  GeneratePdfStatusEnum,
  JobFDaysEnum,
  JobFRuleEnum,
  JobPeriodTypeEnum,
  JobReportTypeEnum,
  JobStatusEnum,
  JobTypeEnum,
} from '~/shared/enums/job.enum';
import { BaseModel } from '~/shared/models/base.model';

import { ContactDocument, ContactModel } from '../contact/contact.model';
import {
  EquipmentDocument,
  EquipmentModel,
} from '../equipment/equipment.model';
import { LocationModel } from '../location/location.model';
import {
  NightRegistrationResidentDocument,
  NightRegistrationResidentModel,
} from '../night-registration/models/night-registration-resident.model';
import {
  TenantUserDocument,
  TenantUserModel,
} from '../tenant-user/tenant-user.model';
import { UnitDocument, UnitModel } from '../unit/unit.model';

export type JobDocument = DocumentType<JobModel>;

class JobLocationInfo {
  @prop()
  _id!: string;

  @prop()
  fullAddress?: string;
}

class JobTeamInfo {
  @prop()
  _id!: string;

  @prop()
  name?: string;
}

class JobUserInfo {
  @prop()
  _id!: string;

  @prop()
  displayName?: string;

  @prop()
  email?: string;
}

class JobReportContactInfo {
  @prop()
  _id!: string;

  @prop()
  displayName?: string;

  @prop()
  email?: string;
}

@modelOptions({
  options: { customName: 'Job', allowMixed: Severity.ALLOW },
})
@index({
  isActive: 1,
  title: 1,
  identifier: 1,
  team: 1,
  jobType: 1,
})
@index({ location: 1, units: 1 })
@index({ status: 1 })
@index({ type: 1 })
@index({ assignee: 1 })
@index({ plannedDate: 1, isDeleted: 1, type: 1, equipments: 1 })
@index({ fIdentifier: 1 })
@index({ updatedAt: -1 })
@index({ jobType: 1, isActive: 1, isDeleted: 1 })
@index({ plannedStartDate: 1, plannedEndDate: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class JobModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({})
  identifier!: string;

  @prop({ trim: true, maxlength: 256, default: '' })
  title?: string;

  @prop({ enum: JobStatusEnum, default: JobStatusEnum.OPEN })
  status!: JobStatusEnum;

  @prop({ enum: JobPeriodTypeEnum, default: JobPeriodTypeEnum.REGULAR })
  type!: JobPeriodTypeEnum;

  @prop({ enum: JobTypeEnum, default: JobTypeEnum.INSPECTION })
  jobType!: JobTypeEnum;

  @prop({ enum: JobReportTypeEnum })
  reportType!: JobReportTypeEnum;

  @prop({ ref: () => ContactModel })
  reportContact?: Ref<ContactDocument>;

  // Apply for Standard Jobs
  @prop()
  plannedDate!: Date;

  //Apply for Mutiple Scheduling Jobs
  @prop()
  plannedEndDate?: Date;

  @prop({})
  isOverdue?: boolean;

  @prop({ trim: true, maxlength: 2048 })
  instructions?: string;

  @prop({ trim: true, maxlength: 2048 })
  feedbacks?: string;

  @prop({ trim: true, maxlength: 2048 })
  fuaDescriptions?: string;

  @prop({ trim: true, maxlength: 2048 })
  rejectionReason?: string;

  @prop({
    ref: () => JobModel,
  })
  parent?: Ref<JobDocument>;

  @prop({
    ref: () => TenantUserModel,
  })
  assignee!: Ref<TenantUserDocument>;

  @prop({
    ref: () => TenantUserModel,
  })
  planner?: Ref<TenantUserDocument>;

  @prop({
    ref: () => LocationModel,
  })
  location!: Ref<LocationModel>;

  @prop({
    ref: () => UnitModel,
  })
  units!: Ref<UnitDocument>[];

  @prop({
    ref: () => EquipmentModel,
  })
  equipments?: Ref<EquipmentDocument>[];

  @prop()
  images?: string[];

  //
  @prop({ ref: () => TenantUserModel })
  updatedBy?: Ref<TenantUserModel>;

  @prop({ ref: () => TenantUserModel })
  rejectedBy?: Ref<TenantUserModel>;

  @prop({ ref: () => TenantUserModel })
  completedBy?: Ref<TenantUserModel>;

  @prop()
  performedAt?: Date;

  @prop()
  readiedAt?: Date;

  @prop()
  rejectedAt?: Date;

  @prop()
  completedAt?: Date;

  // Periodic Settings
  @prop({ default: 1 })
  fInterval?: number;

  @prop({ enum: JobFRuleEnum })
  fRule?: JobFRuleEnum;

  @prop({ enum: JobFDaysEnum, type: String, default: [] })
  fDays?: JobFDaysEnum[];

  @prop()
  fDayInMonth?: number;

  @prop()
  fStartDate?: Date;

  @prop()
  fEndDate?: Date;

  @prop()
  fIdentifier?: string;

  // Report Settings
  @prop({ default: false })
  isSendRC!: boolean;

  @prop({ default: false })
  isSendRR!: boolean;

  @prop({
    ref: () => ContactModel,
    default: [],
  })
  rtContacts?: Ref<ContactDocument>[];

  @prop({
    ref: () => NightRegistrationResidentModel,
    default: [],
  })
  rrContacts?: Ref<NightRegistrationResidentDocument>[];

  @prop({
    ref: () => ContactModel,
  })
  invoiceContact!: Ref<ContactDocument>;

  @prop({ type: () => JobLocationInfo })
  locationInfo?: JobLocationInfo;

  @prop({ type: () => JobTeamInfo })
  locationTeamInfo?: JobTeamInfo;

  @prop({ type: () => JobUserInfo })
  assigneeInfo?: JobUserInfo;

  @prop({ type: () => JobUserInfo })
  plannerInfo?: JobUserInfo;

  @prop({ type: () => JobReportContactInfo })
  reportContactInfo?: JobReportContactInfo;

  @prop({ default: '' })
  pdfPublicUrl?: string;

  @prop({ default: GeneratePdfStatusEnum.NONE })
  generatePdfStatus?: GeneratePdfStatusEnum;
}
