import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { NightRegistrationReservationModel } from '~/modules/night-registration/models/night-registration-reservation.model';
import { IMockDto } from '~/test/helpers/mock.interface';

import { mockContactData } from './contact.mock';
import { mockNightRegistrationResidentData } from './nightregistrationresident.mock';
import { mockUnitData } from './unit.mock';

const nightRegistrationReservationModel = getModelForClass(
  NightRegistrationReservationModel,
);

export const mockNightRegistrationReservationData = {
  _id: new ObjectId(),
  arrivalDate: new Date(),
  bed: 1,
  contact: mockContactData._id,
  departureDate: null,
  isDeleted: false,
  isVirtual: false,
  job: [],
  remark: '',
  resident: mockNightRegistrationResidentData._id,
  unit: mockUnitData._id,
  remarks: '',
};

export async function initMockNightRegistrationReservation(doc?: IMockDto) {
  const { _id, ...rest } = { ...mockNightRegistrationReservationData, ...doc };
  await nightRegistrationReservationModel.replaceOne({ _id }, rest, {
    upsert: true,
  });
}
