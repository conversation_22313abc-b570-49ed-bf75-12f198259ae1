import {
  DocumentType,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  AgreementLineType,
} from '~/shared/enums/contract.enum';
import { BaseModel } from '~/shared/models/base.model';

import { ContractDocument, ContractModel } from '../contract/contract.model';
import {
  CostLineGeneralDocument,
  CostLineGeneralModel,
} from '../costlinegeneral/costlinegeneral.model';
import { UnitDocument, UnitModel } from '../unit/unit.model';

export type AgreementLineDocument = DocumentType<AgreementLineModel>;

@modelOptions({
  options: {
    customName: 'AgreementLine',
  },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class AgreementLineModel extends BaseModel {
  @prop({ enum: AgreementLineType, default: AgreementLineType.ACCOMMODATION })
  type!: AgreementLineType;

  @prop({ enum: AgreementLinePeriod })
  period!: AgreementLinePeriod;

  @prop({
    enum: AgreementLinePeriodType,
    default: AgreementLinePeriodType.PERIODIC,
  })
  periodType!: AgreementLinePeriodType;

  @prop({ default: 0 })
  position!: number;

  @prop({ ref: () => UnitModel })
  units!: Ref<UnitDocument>[];

  @prop({ ref: () => ContractModel })
  contract!: Ref<ContractDocument>;

  @prop({ ref: () => CostLineGeneralModel })
  costLineGenerals!: Ref<CostLineGeneralDocument>[];
}
