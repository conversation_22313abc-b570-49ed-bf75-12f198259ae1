import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldCompany {
  _id: string;
  identifier: string;
  name: string;
  createdAt: Date;
  updatedAt: Date;
}

const CompanyPipeLineAggregate = (skip: number, limit: number) => {
  return [{ $skip: skip }, { $limit: limit }];
};

const transformDataFunc = ({
  data,
  context,
}: {
  data: OldCompany[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return data;
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('company');

  const pipeline = CompanyPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'company',
      destinationCollectionName: 'bvcompanies',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: transformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
