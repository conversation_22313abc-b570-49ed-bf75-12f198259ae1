import { Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(
      async (additionalArticle: { _id: Types.ObjectId; [key: string]: any }) =>
        omitNull({
          _id: additionalArticle._id,
          order: additionalArticle.order,
          type: additionalArticle.type,
          storage: additionalArticle.storage,
          job: additionalArticle.inspection,
          additionalArticleDetails: additionalArticle.additionalArticleDetails,
          transactions: additionalArticle.transactions,
          isDeleted: additionalArticle.deleted,
          createdAt: additionalArticle.createdAt,
          updatedAt: additionalArticle.updatedAt,
          deletedAt: additionalArticle.deletedAt,
          createdBy: additionalArticle.createdBy, // Reference to tenantusers
          updatedBy: additionalArticle.updatedBy, // Reference to tenantusers
          deletedBy: additionalArticle.deletedBy, // Reference to tenantusers
        }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'additionalarticle',
      destinationCollectionName: 'additionalarticle',
      tranformDataFunc: transformData,
      inventoryMode: true,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
