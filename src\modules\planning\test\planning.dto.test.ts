import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { teamTest } from '~/modules/team/test/team.dto.test';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import {
  JobPeriodTypeEnum,
  JobStatusEnum,
  JobTypeEnum,
} from '~/shared/enums/job.enum';
import { PlanningOrderType } from '~/shared/enums/planning-order.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    employee: z.union([z.string(), z.instanceof(ObjectId)]),
    type: z.nativeEnum(PlanningOrderType),
    date: z.string(),
    jobs: z.array(
      z.object({
        _id: z.union([z.string(), z.instanceof(ObjectId)]),
        position: z.number(),
      }),
    ),
  })
  .extend(baseModelTestSchema);

const baseJobTeamOverviewSchema = z.object({
  employee: z
    .object({
      _id: z.union([z.string(), z.instanceof(ObjectId)]),
      displayName: z.string(),
    })
    .optional(),
  equipment: z
    .object({
      _id: z.union([z.string(), z.instanceof(ObjectId)]),
      name: z.string(),
    })
    .optional(),
  schedules: z.array(
    z.object({
      date: z.string(),
      items: z.array(
        z.object({
          _id: z.union([z.string(), z.instanceof(ObjectId)]),
          title: z.string(),
          type: z.nativeEnum(JobPeriodTypeEnum),
          jobType: z.nativeEnum(JobTypeEnum),
          status: z.nativeEnum(JobStatusEnum),
          plannedDate: z.date(),
          isOverdue: z.boolean().optional(),
          location: z.object({
            _id: z.union([z.string(), z.instanceof(ObjectId)]),
            fullAddress: z.string(),
          }),
          estimatedHours: z.number(),
        }),
      ),
    }),
  ),
});

const baseTaskTeamOverviewSchema = z.object({
  employee: z
    .object({
      _id: z.union([z.string(), z.instanceof(ObjectId)]),
      displayName: z.string(),
    })
    .optional(),
  equipment: z
    .object({
      _id: z.union([z.string(), z.instanceof(ObjectId)]),
      name: z.string(),
    })
    .optional(),
  schedules: z.array(
    z.object({
      date: z.string(),
      items: z.array(
        z.object({
          _id: z.union([z.string(), z.instanceof(ObjectId)]),
          title: z.string(),
          category: z.string(),
          startAt: z.string(),
          endAt: z.string(),
        }),
      ),
    }),
  ),
});

const baseOverviewSchema = z.object({
  isoWeek: z.number(),
  year: z.number(),
  startDate: z.date(),
  endDate: z.date(),
  tasks: z.array(baseTaskTeamOverviewSchema),
  jobs: z.array(baseJobTeamOverviewSchema),
});

const teamOverviewSchema = z
  .object({
    team: teamTest.modelSchema.pick({
      _id: true,
      name: true,
    }),
  })
  .merge(baseOverviewSchema);

const equipmentOverviewSchema = z
  .object({
    equipmentType: z.nativeEnum(EquipmentEnum),
  })
  .merge(baseOverviewSchema);

const mappedJobsByPlanningOrderSchema = z.array(
  baseJobTeamOverviewSchema.omit({
    equipment: true,
  }),
);

export const planningOrderTest = {
  modelSchema,
  teamOverviewSchema,
  equipmentOverviewSchema,
  mappedJobsByPlanningOrderSchema,
};
