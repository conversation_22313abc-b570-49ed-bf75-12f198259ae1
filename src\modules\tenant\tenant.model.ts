import { DocumentType, modelOptions, plugin, prop } from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

export type TenantDocument = DocumentType<TenantModel>;

@modelOptions({
  options: { customName: 'Tenant' },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class TenantModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({ trim: true, required: true, maxlength: 64 })
  name!: string;

  @prop({ default: {}, required: true })
  tenantConfigs!: Record<string, object>;
}
