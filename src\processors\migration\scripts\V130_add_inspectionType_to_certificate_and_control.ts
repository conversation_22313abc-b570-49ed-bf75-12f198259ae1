import {
  CertificateAndControlInspectionType,
  LocationAdditionalType,
} from '~/shared/enums/location-additional.enum';

import { MigrationContext } from '../migration.service';

const up = async (context: MigrationContext) => {
  try {
    const locationAdditionalCollection = context?.destinationClient
      ?.db()
      .collection('locationadditionals');

    const result = await locationAdditionalCollection?.updateMany(
      { type: LocationAdditionalType.CERTIFICATE_AND_CONTROL },
      {
        $set: {
          inspectionType: CertificateAndControlInspectionType.MAINTENANCE,
        },
      },
    );

    console.info(
      `Updated ${result?.modifiedCount} documents to set default inspectionType to 'MAINTENANCE' for CERTIFICATE_AND_CONTROL`,
    );
  } catch (error) {
    console.error(
      'Error updating inspectionType for CERTIFICATE_AND_CONTROL:',
      error,
    );
  }
};

export default up;
