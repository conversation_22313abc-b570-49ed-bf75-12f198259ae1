import { Module } from '@nestjs/common';

import { JobModule } from '~/modules/job/job.module';
import { TaskModule } from '~/modules/task/task.module';

import { PlanningController } from './planning.controller';
import { PlanningService } from './planning.service';

@Module({
  imports: [TaskModule, JobModule],
  controllers: [PlanningController],
  providers: [PlanningService],
})
export class PlanningModule {}
