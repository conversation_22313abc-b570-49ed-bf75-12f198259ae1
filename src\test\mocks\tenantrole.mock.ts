import { getModelForClass } from '@typegoose/typegoose';
import { Decimal128, ObjectId } from 'mongodb';
import { z } from 'zod';

import { TenantRoleModel } from '~/modules/tenant-role/tenant-role.model';
import { tenantRoleTest } from '~/modules/tenant-role/test/tenant-role.dto.test';

import { mockTenantData } from './tenant.mock';

const tenantRoleModel = getModelForClass(TenantRoleModel);
type tenantRoleType = z.infer<typeof tenantRoleTest.modelSchema>;

export const mockTenantRoleData: tenantRoleType = {
  _id: new ObjectId(),
  isDeleted: false,
  isActive: true,
  key: 'cleaner',
  name: 'Cleaner',
  decimal: Decimal128.fromString('2'),
  permissions: Decimal128.fromString('7'),
  tenant: mockTenantData._id,
};

export async function initMockTenantRole(doc?: Partial<tenantRoleType>) {
  const { _id, ...rest } = { ...mockTenantRoleData, ...doc };
  await tenantRoleModel.replaceOne({ _id }, rest, { upsert: true });
}
