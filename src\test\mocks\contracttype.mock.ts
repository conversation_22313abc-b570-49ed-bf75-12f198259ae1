import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { ContractTypeModel } from '~/modules/contract-type/contract-type.model';

const contractTypeModel = getModelForClass(ContractTypeModel);

export const mockContractTypeData = {
  _id: new ObjectId(),
  identifier: '000000010',
  name: 'Camera system',
};

export async function initMockContractType(doc?: any) {
  const { _id, ...rest } = { ...mockContractTypeData, ...doc };
  await contractTypeModel.replaceOne({ _id }, rest, { upsert: true });
}
