import { Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(async (reserved: { _id: Types.ObjectId; [key: string]: any }) =>
      omitNull({
        _id: reserved._id,
        identifier: reserved.identifier,
        isDeleted: reserved.deleted ?? false,
        completed: reserved.completed,
        reservedDetails: reserved.reservedDetails,
        storage: reserved.storage,
        job: reserved.inspection,
        createdAt: reserved.createdAt,
        updatedAt: reserved.updatedAt,
        createdBy: reserved.createdBy, // Reference to tenantusers
        updatedBy: reserved.updatedBy, // Reference to tenantusers
      }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'reserved',
      destinationCollectionName: 'reserved',
      tranformDataFunc: transformData,
      inventoryMode: true,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
