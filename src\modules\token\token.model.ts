import { DocumentType, prop, Severity } from '@typegoose/typegoose';
import { modelOptions } from '@typegoose/typegoose';
import mongoose from 'mongoose';

import { BaseModel } from '~/shared/models/base.model';

export type TokenDocument = DocumentType<TokenModel>;

@modelOptions({
  options: { customName: 'Token', allowMixed: Severity.ALLOW },
})
export class TokenModel extends BaseModel {
  @prop({ required: true })
  accessToken!: string;

  @prop({ required: true })
  refreshToken!: string;

  @prop({ required: true })
  accessTokenSecret!: string;

  @prop({ required: true })
  refreshTokenSecret!: string;

  @prop({ required: true })
  accessTokenExpiresAt!: Date;

  @prop({ required: true })
  refreshTokenExpiresAt!: Date;

  @prop()
  lastUsedAt?: Date;

  @prop()
  tenantUserId!: mongoose.Types.ObjectId;
}
