import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import {
  CreateAgreementLineDto,
  CreateRentingAgreementLineSchema,
  CreateServiceAgreementLineSchema,
  CreateSupplierAgreementLineSchema,
} from '~/modules/agreementline/dtos/create-agreementline.dto';
import { ContractType, NoticeDays } from '~/shared/enums/contract.enum';

export interface CreateContractDto {
  identifier?: string;
  isActive: boolean;
  type: ContractType;
  noticeDays: NoticeDays;
  generatePeriod: number;
  contact: string;
  location: string;
  contractType?: string;
  isWholeLocation: boolean;
  note?: string;
  attachments?: string[];
  isGenerateCostLine: boolean;
  startDate: string;
  endDate?: string;
  signedAt?: string;
  isSigned?: boolean;
  agreementLines: CreateAgreementLineDto[];
  user?: string;
}

export const PdfAttachmentSchema = z.strictObject({
  originalFilename: z
    .string()
    .refine((val) => val.toLowerCase().endsWith('.pdf'), {
      message: 'Only PDF files are allowed',
    }),
  publicUrl: z.string().url(),
});

export const CreateRentingContractSchema = z.strictObject({
  user: z.string().optional(),
  isActive: z.boolean(),
  noticeDays: z.nativeEnum(NoticeDays),
  generatePeriod: z.number().int().min(1).max(12),
  contact: z.string().refine((val) => isValidObjectId(val)),
  note: z.string().optional(),
  attachments: z.array(PdfAttachmentSchema).optional(),
  startDate: z.dateString(),
  endDate: z.dateString().optional(),
  isSigned: z.boolean(),
  signedAt: z.dateString().optional(),
  type: z.literal(ContractType.RENTING),
  location: z.string().refine((val) => isValidObjectId(val)),
  isWholeLocation: z.boolean(),
  isGenerateCostLine: z.boolean().optional().default(true),
  agreementLines: z.array(CreateRentingAgreementLineSchema).min(1),
});

export const CreateServiceContractSchema = z.strictObject({
  user: z.string().optional(),
  isActive: z.boolean(),
  noticeDays: z.nativeEnum(NoticeDays),
  generatePeriod: z.number().int().min(1).max(12),
  contact: z.string().refine((val) => isValidObjectId(val)),
  note: z.string().optional(),
  attachments: z.array(PdfAttachmentSchema).optional(),
  startDate: z.dateString(),
  endDate: z.dateString().optional(),
  isSigned: z.boolean(),
  signedAt: z.dateString().optional(),
  type: z.literal(ContractType.SERVICE),
  costCenter: z.string().refine((val) => isValidObjectId(val)),
  isWholeLocation: z.literal(false).optional().default(false),
  isGenerateCostLine: z.literal(true).optional().default(true),
  agreementLines: z.array(CreateServiceAgreementLineSchema).min(1),
});

export const CreateCreditorContractSchema = z.strictObject({
  user: z.string().optional(),
  isActive: z.boolean(),
  noticeDays: z.nativeEnum(NoticeDays),
  contact: z.string().refine((val) => isValidObjectId(val)),
  note: z.string().optional(),
  attachments: z.array(PdfAttachmentSchema).optional(),
  startDate: z.dateString(),
  endDate: z.dateString().optional(),
  isSigned: z.boolean(),
  signedAt: z.dateString().optional(),
  type: z.literal(ContractType.CREDITOR),
  location: z.string().refine((val) => isValidObjectId(val)),
  isWholeLocation: z.literal(true).optional().default(true),
  isGenerateCostLine: z.literal(false).optional().default(false),
  agreementLines: z.array(CreateRentingAgreementLineSchema).min(1),
});

export const CreateSupplierContractSchema = z.strictObject({
  user: z.string().optional(),
  isActive: z.boolean(),
  noticeDays: z.nativeEnum(NoticeDays),
  contact: z.string().refine((val) => isValidObjectId(val)),
  note: z.string().optional(),
  attachments: z.array(PdfAttachmentSchema).optional(),
  startDate: z.dateString(),
  endDate: z.dateString().optional(),
  isSigned: z.boolean(),
  signedAt: z.dateString().optional(),
  type: z.literal(ContractType.SUPPLIER),
  contractType: z.string().refine((val) => isValidObjectId(val)),
  isWholeLocation: z.literal(false).optional().default(false),
  isGenerateCostLine: z.literal(false).optional().default(false),
  agreementLines: z.array(CreateSupplierAgreementLineSchema).min(1),
});

export const CreateContractSchema = z
  .discriminatedUnion('type', [
    CreateRentingContractSchema,
    CreateServiceContractSchema,
    CreateCreditorContractSchema,
    CreateSupplierContractSchema,
  ])
  .refine((schema) => !schema.isSigned || !!schema.signedAt, {
    message: 'Signed date is required when contract is signed',
    path: ['signedAt'],
  })
  .refine((schema) => schema.isSigned || !schema.signedAt, {
    message: 'Signed date is not allowed when contract is not signed',
    path: ['signedAt'],
  })
  .refine(
    (schema) =>
      !schema.endDate ||
      Date.parse(schema.startDate) < Date.parse(schema.endDate),
    {
      message: 'End date must be greater than start date',
      path: ['endDate'],
    },
  )
  .superRefine((contract, ctx) => {
    contract.agreementLines.forEach((agreementLine, agreementLineIndex) => {
      agreementLine.costLineGenerals.forEach((costLine, costLineIndex) => {
        const costLineStartDate = Date.parse(costLine.startDate);
        const contractStartDate = Date.parse(contract.startDate);

        if (costLineStartDate < contractStartDate) {
          ctx.addIssue({
            code: 'custom',
            message:
              'Cost line general start date must not be before contract start date',
            path: [
              'agreementLines',
              agreementLineIndex,
              'costLineGenerals',
              costLineIndex,
              'startDate',
            ],
          });
        }

        if (!contract.endDate) {
          return;
        }

        if (!contract.endDate) {
          return;
        }

        const costLineEndDate = Date.parse(costLine.endDate);
        const contractEndDate = Date.parse(contract.endDate);

        if (costLineEndDate > contractEndDate) {
          ctx.addIssue({
            code: 'custom',
            message:
              'Cost line general end date must not be after contract end date',
            path: [
              'agreementLines',
              agreementLineIndex,
              'costLineGenerals',
              costLineIndex,
              'endDate',
            ],
          });
        }

        if (costLineStartDate > contractEndDate) {
          ctx.addIssue({
            code: 'custom',
            message:
              'Cost line general start date must not be after contract end date',
            path: [
              'agreementLines',
              agreementLineIndex,
              'costLineGenerals',
              costLineIndex,
              'startDate',
            ],
          });
        }
      });
    });
  });

export const CreateContractZodDto = createZodDto(CreateContractSchema);
