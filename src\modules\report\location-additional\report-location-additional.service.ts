import { BadRequestException, Injectable } from '@nestjs/common';
import dayjs from 'dayjs';

import { DATE_FORMAT_HYPHEN } from '~/constants/app.constant';
import { LocationAdditionalModel } from '~/modules/location-addtional/location-addtional.model';
import { getGroupTypeDisplay } from '~/modules/report/report.helper';
import { TeamModel } from '~/modules/team/team.model';
import { TenantModel } from '~/modules/tenant/tenant.model';
import {
  Language,
  TenantUserModel,
} from '~/modules/tenant-user/tenant-user.model';
import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { parseObjectId } from '~/utils';

import { ReportLocationAdditionalQueryDto } from './dtos/report-location-additional.dto';

@Injectable()
export class ReportLocationAdditionalService {
  constructor(
    @InjectModel(TeamModel)
    private readonly teamModel: MongooseModel<TeamModel>,
    @InjectModel(LocationAdditionalModel)
    private readonly locationAdditionalModel: MongooseModel<LocationAdditionalModel>,
    @InjectModel(TenantUserModel)
    private readonly tenantUserModel: MongooseModel<TenantUserModel>,
    @InjectModel(TenantModel)
    private readonly tenantModel: MongooseModel<TenantModel>,
  ) {}

  async getAdditionalLocations(payload: any) {
    const { team, category, type, tenantId } = payload;

    if (!team && !category) {
      throw new BadRequestException('Team or category is required');
    }

    const tenant = await this.tenantModel.findById(tenantId).lean();

    return this.teamModel.aggregate([
      {
        $match: {
          ...(team ? { _id: parseObjectId(team) } : {}),
          isActive: true,
        },
      },
      {
        $lookup: {
          from: 'locations',
          localField: '_id',
          foreignField: 'team',
          as: 'locations',
          pipeline: [
            { $match: { isActive: true, isDeleted: { $in: [false, null] } } },
            {
              $lookup: {
                from: 'locationadditionals',
                localField: '_id',
                foreignField: 'location',
                as: 'additionals',
                pipeline: [
                  {
                    $match: {
                      isDeleted: false,
                      type: type,
                      ...(category
                        ? { groupName: parseObjectId(category) }
                        : {}),
                    },
                  },
                  {
                    $lookup: {
                      from: 'contacts',
                      localField: 'contact',
                      foreignField: '_id',
                      as: 'contact',
                    },
                  },
                  {
                    $unwind: {
                      path: '$contact',
                      preserveNullAndEmptyArrays: true,
                    },
                  },
                  {
                    $lookup: {
                      from: 'locationadditionalgroupnames',
                      localField: 'groupName',
                      foreignField: '_id',
                      as: 'groupName',
                    },
                  },
                  { $unwind: '$groupName' },
                  {
                    $addFields: {
                      groupType: {
                        $switch: {
                          branches: [
                            {
                              case: { $eq: ['$groupType', 'owner'] },
                              then: 'Owner',
                            },
                            {
                              case: { $eq: ['$groupType', 'eeac'] },
                              then: getGroupTypeDisplay(tenant?.name),
                            },
                            {
                              case: { $eq: ['$groupType', 'customer'] },
                              then: 'Customer',
                            },
                            {
                              case: { $eq: ['$groupType', 'none'] },
                              then: 'N/A',
                            },
                          ],
                          default: '$groupType',
                        },
                      },
                    },
                  },
                  {
                    $group: {
                      _id: '$groupName._id',
                      description: { $first: '$groupName.description' },
                      dutchDescription: {
                        $first: '$groupName.dutchDescription',
                      },
                      items: {
                        $push: {
                          _id: '$_id',
                          type: '$type',
                          brandType: '$brandType',
                          yearInstallation: '$yearInstallation',
                          inspectionType: '$inspectionType',
                          dateCheck: '$dateCheck',
                          name: '$name',
                          groupType: '$groupType',
                          contact: '$contact',
                          code: '$code',
                        },
                      },
                    },
                  },
                  { $sort: { _id: 1 } },
                ],
              },
            },
            { $match: { 'additionals.0': { $exists: true } } },
          ],
        },
      },
      {
        $project: {
          name: 1,
          locations: {
            _id: 1,
            fullAddress: 1,
            additionals: {
              _id: 1,
              description: 1,
              dutchDescription: 1,
              items: {
                _id: 1,
                brandType: 1,
                yearInstallation: 1,
                inspectionType: 1,
                dateCheck: 1,
                name: 1,
                groupType: 1,
                contact: { _id: 1, displayName: 1 },
                code: 1,
              },
            },
          },
        },
      },
    ]);
  }

  async getAdditionalLocationsNew(payload: ReportLocationAdditionalQueryDto) {
    const { team, category, type, tenantId } = payload;

    if (!team && !category) {
      throw new BadRequestException('Team or category is required');
    }

    const tenant = await this.tenantModel.findById(tenantId).lean();

    return this.locationAdditionalModel.aggregate([
      { $match: { isDeleted: false, type: type } },
      {
        $lookup: {
          from: 'locations',
          localField: 'location',
          foreignField: '_id',
          as: 'location',
          pipeline: [
            { $match: { ...(team ? { team: parseObjectId(team) } : {}) } },
            { $project: { _id: 1, team: 1, fullAddress: 1 } },
          ],
        },
      },
      { $unwind: { path: '$location', preserveNullAndEmptyArrays: false } },
      {
        $match: {
          ...(category ? { groupName: parseObjectId(category) } : {}),
        },
      },
      {
        $lookup: {
          from: 'teams',
          localField: 'location.team',
          foreignField: '_id',
          as: 'team',
          pipeline: [{ $project: { _id: 1, name: 1 } }],
        },
      },
      { $unwind: { path: '$team', preserveNullAndEmptyArrays: false } },
      {
        $lookup: {
          from: 'locationadditionalgroupnames',
          localField: 'groupName',
          foreignField: '_id',
          as: 'groupName',
          pipeline: [{ $match: { isDeleted: false } }],
        },
      },
      { $unwind: { path: '$groupName', preserveNullAndEmptyArrays: false } },
      {
        $lookup: {
          from: 'contacts',
          localField: 'contact',
          foreignField: '_id',
          as: 'contact',
          pipeline: [
            { $match: { isDeleted: false } },
            { $project: { _id: 1, displayName: 1 } },
          ],
        },
      },
      { $unwind: { path: '$contact', preserveNullAndEmptyArrays: true } },
      // group by groupName to make items
      {
        $group: {
          _id: {
            teamId: '$team._id',
            locationId: '$location._id',
            groupNameId: '$groupName._id',
          },
          teamName: { $first: '$team.name' },
          locationFullAddress: { $first: '$location.fullAddress' },
          groupDescription: { $first: '$groupName.description' },
          groupDutchDescription: { $first: '$groupName.dutchDescription' },
          items: {
            $push: {
              _id: '$_id',
              brandType: '$brandType',
              yearInstallation: '$yearInstallation',
              dateCheck: '$dateCheck',
              name: '$name',
              groupType: {
                $switch: {
                  branches: [
                    {
                      case: { $eq: ['$groupType', 'owner'] },
                      then: 'Owner',
                    },
                    {
                      case: { $eq: ['$groupType', 'eeac'] },
                      then: getGroupTypeDisplay(tenant?.name),
                    },
                    {
                      case: { $eq: ['$groupType', 'customer'] },
                      then: 'Customer',
                    },
                    {
                      case: { $eq: ['$groupType', 'contact'] },
                      then: 'Contact',
                    },
                    {
                      case: { $eq: ['$groupType', 'none'] },
                      then: 'N/A',
                    },
                  ],
                  default: '$groupType',
                },
              },
              contact: '$contact',
              code: '$code',
            },
          },
        },
      },
      // sort default
      {
        $project: {
          _id: 1,
          teamName: 1,
          locationFullAddress: 1,
          groupDescription: 1,
          groupDutchDescription: 1,
          items: {
            $sortArray: { input: '$items', sortBy: { _id: 1 } },
          },
        },
      },
      // group by location to make additionals
      {
        $group: {
          _id: {
            teamId: '$_id.teamId',
            locationId: '$_id.locationId',
          },
          teamName: { $first: '$teamName' },
          locationFullAddress: { $first: '$locationFullAddress' },
          additionals: {
            $push: {
              _id: '$_id.groupNameId',
              description: '$groupDescription',
              dutchDescription: '$groupDutchDescription',
              items: '$items',
            },
          },
        },
      },
      // sort default
      {
        $project: {
          _id: 1,
          teamName: 1,
          locationFullAddress: 1,
          additionals: {
            $sortArray: { input: '$additionals', sortBy: { _id: 1 } },
          },
        },
      },
      // group by team to make locations
      {
        $group: {
          _id: '$_id.teamId',
          name: { $first: '$teamName' },
          locations: {
            $push: {
              _id: '$_id.locationId',
              fullAddress: '$locationFullAddress',
              additionals: '$additionals',
            },
          },
        },
      },
      // sort default
      {
        $project: {
          _id: 1,
          name: 1,
          locations: {
            $sortArray: { input: '$locations', sortBy: { _id: 1 } },
          },
        },
      },
      { $sort: { _id: 1 } },
    ]);
  }

  async exportAdditionalLocations(payload: any) {
    const { type, user, tenantId } = payload;

    const foundUser = await this.tenantUserModel.findById(user);
    const tenant = await this.tenantModel.findById(tenantId).lean();
    const language = foundUser!.language;

    let header: any = [];
    let fileName = '';

    if (type === LocationAdditionalType.FEATURE_AND_SUPPLIER) {
      fileName = `feature-and-supplier-report-${dayjs().format(DATE_FORMAT_HYPHEN)}.csv`;
      header = [
        { field: 'team', title: 'Team' },
        { field: 'location', title: 'Location' },
        { field: 'category', title: 'Item Category' },
        { field: 'responsible', title: 'Responsible' },
        { field: 'supplier', title: 'Supplier' },
        { field: 'code', title: 'Code' },
      ];
    } else if (type === LocationAdditionalType.CERTIFICATE_AND_CONTROL) {
      fileName = `certificate-control-report-${dayjs().format(DATE_FORMAT_HYPHEN)}.csv`;
      header = [
        { field: 'team', title: 'Team' },
        { field: 'location', title: 'Location' },
        { field: 'category', title: 'Item Category' },
        { field: 'inspectionType', title: 'Inspection Type' },
        { field: 'brandType', title: 'Brand & Type' },
        { field: 'yearInstallation', title: 'Year Of Installation' },
        { field: 'itemName', title: 'Item' },
        { field: 'responsible', title: 'Responsible' },
        { field: 'supplier', title: 'Supplier' },
        { field: 'dateCheck', title: 'Last Inspection' },
      ];
    }

    const responsibleDutch = {
      Owner: 'Eigenaar',
      EEAC: getGroupTypeDisplay(tenant?.name),
      Customer: 'Klant',
      Contact: 'Contact',
      'N/A': 'N.v.t',
    };

    const rawData = await this.getAdditionalLocations(payload);

    const extractItems = (
      team: any,
      location: any,
      additional: any,
      language: string,
    ) => {
      return additional.items.map((item: any) => ({
        team: team.name,
        location: location.fullAddress,
        category:
          language === Language.EN
            ? additional.description
            : additional.dutchDescription,
        responsible:
          language === Language.EN
            ? item.groupType
            : responsibleDutch[item.groupType],
        supplier: item.contact?.displayName || '',
        code: item.code || '',
        brandType: item.brandType || '',
        inspectionType: item.inspectionType || '',
        yearInstallation: parseYearInstallation(item.yearInstallation),
        itemName: item.name || '',
        dateCheck: formatDateCheck(item.dateCheck),
      }));
    };

    const parseYearInstallation = (yearInstallation: any) => {
      if (!yearInstallation) return '';
      if (typeof yearInstallation === 'number') return yearInstallation;
      return dayjs(yearInstallation).isValid()
        ? dayjs(yearInstallation).utc().year()
        : '';
    };

    const formatDateCheck = (dateCheck: any) => {
      return dateCheck ? dayjs(dateCheck).format(DATE_FORMAT_HYPHEN) : '';
    };

    const data = rawData.flatMap((team: any) =>
      team.locations.flatMap((location: any) =>
        location.additionals.flatMap((additional: any) =>
          extractItems(team, location, additional, language),
        ),
      ),
    );

    return { data, header, fileName };
  }
}
