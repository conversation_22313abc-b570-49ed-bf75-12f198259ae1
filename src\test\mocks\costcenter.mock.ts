import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { z } from 'zod';

import { CostCenterModel } from '~/modules/costcenter/costcenter.model';
import { costCenterTest } from '~/modules/costcenter/test/costcenter.dto.test';

const costCenterModel = getModelForClass(CostCenterModel);
type costCenterType = z.infer<typeof costCenterTest.modelSchema>;

export const mockCostCenterData = {
  _id: new ObjectId(),
  identifier: nanoid(10),
  isActive: true,
  isDeleted: true,
  locations: [],
  name: 'CostCenter 1',
};

export async function initMockCostCenter(doc?: Partial<costCenterType>) {
  const { _id, ...rest } = { ...mockCostCenterData, ...doc };
  await costCenterModel.replaceOne({ _id }, rest, { upsert: true });
}
