import dayjs from 'dayjs';
import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
} from '~/shared/enums/contract.enum';

enum InvoiceQueryType {
  CONTRACT = 'contract',
  JOB = 'job',
}

const JobInvoiceReviewQueryParams = z.strictObject({
  type: z.literal(InvoiceQueryType.JOB),
  endDate: z.dateString().optional(),
  contact: z
    .string()
    .refine((v) => isValidObjectId(v))
    .optional(),
  locations: z
    .union([
      z.array(z.string().refine(isValidObjectId)),
      z.string().refine(isValidObjectId),
    ])
    .optional(),
  costCenters: z
    .union([
      z.array(z.string().refine(isValidObjectId)),
      z.string().refine(isValidObjectId),
    ])
    .optional(),
  sortBy: z.string().default('startDate').optional(),
  sortDir: z.enum(['asc', 'desc']).default('desc').optional(),
});

const ContractInvoiceReviewQueryParams = z.strictObject({
  type: z.literal(InvoiceQueryType.CONTRACT),
  contact: z
    .string()
    .refine((v) => isValidObjectId(v))
    .optional(),
  periodType: z.enum([
    AgreementLinePeriodType.ONE_TIME,
    AgreementLinePeriodType.PERIODIC,
  ]),
  period: z.nativeEnum(AgreementLinePeriod).optional(),
  startDate: z.dateString().optional(),
  endDate: z.dateString().optional(),
  locations: z
    .union([
      z.array(z.string().refine(isValidObjectId)),
      z.string().refine(isValidObjectId),
    ])
    .optional(),
  sortBy: z.string().default('startDate').optional(),
  sortDir: z.enum(['asc', 'desc']).default('desc').optional(),
});

export class JobInvoiceReviewQueryParamsDto extends createZodDto(
  JobInvoiceReviewQueryParams,
) {}

export class ContractInvoiceReviewQueryParamsDto extends createZodDto(
  ContractInvoiceReviewQueryParams,
) {}

export const InvoiceReviewQueryParamsDto = createZodDto(
  z
    .discriminatedUnion('type', [
      JobInvoiceReviewQueryParams,
      ContractInvoiceReviewQueryParams,
    ])
    .superRefine((v, ctx) => {
      if (v.type !== InvoiceQueryType.CONTRACT) {
        return;
      }

      if (
        v.periodType !== AgreementLinePeriodType.ONE_TIME &&
        !v.contact &&
        !v.locations?.length &&
        !v.startDate &&
        !v.endDate
      ) {
        ctx.addIssue({
          code: 'custom',
          message:
            'One of contact, locations, startDate and endDate is required',
          path: ['contact', 'locations', 'startDate', 'endDate'],
        });
      }

      if (v.periodType === AgreementLinePeriodType.PERIODIC && !v.period) {
        ctx.addIssue({
          code: 'custom',
          message: 'Period is required',
          path: ['period'],
        });
      }

      if (v.startDate && v.endDate && dayjs(v.startDate).isAfter(v.endDate)) {
        ctx.addIssue({
          code: 'invalid_date',
          message: 'Start date cannot be after end date',
          path: ['startDate', 'endDate'],
        });
      }
    })
    .transform((v) => {
      if (v.type !== InvoiceQueryType.CONTRACT) {
        return v;
      }

      if (v.periodType === AgreementLinePeriodType.ONE_TIME) {
        delete v.period;
        delete v.startDate;
        delete v.endDate;
      }

      return v;
    }),
);
