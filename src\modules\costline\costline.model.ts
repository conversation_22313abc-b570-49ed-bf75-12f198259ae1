import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  ContractType,
  CostLineStatus,
} from '~/shared/enums/contract.enum';
import { BaseModel } from '~/shared/models/base.model';

import {
  AgreementLineDocument,
  AgreementLineModel,
} from '../agreementline/agreementline.model';
import {
  BvCompanyDocument,
  BvCompanyModel,
} from '../bvcompany/bvcompany.model';
import { ContactDocument, ContactModel } from '../contact/contact.model';
import { CostCenterModel } from '../costcenter/costcenter.model';
import {
  CostLineGeneralDocument,
  CostLineGeneralModel,
} from '../costlinegeneral/costlinegeneral.model';
import { CostTypeDocument, CostTypeModel } from '../costtype/costtype.model';
import { CountryDocument, CountryModel } from '../country/country.model';
import { InvoiceDocument, InvoiceModel } from '../invoice/invoice.model';
import { JobDocument, JobModel } from '../job/job.model';
import { LocationModel } from '../location/location.model';
import { UnitDocument, UnitModel } from '../unit/unit.model';

export type CostLineDocument = DocumentType<CostLineModel>;

@modelOptions({
  options: {
    customName: 'CostLine',
  },
})
@index({ costLineGeneral: 1 })
@index({
  agreementLine: 1,
  periodType: 1,
  approvedAt: 1,
  costType: 1,
  startDate: 1,
  endDate: 1,
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class CostLineModel extends BaseModel {
  @prop({ default: false })
  isCustom!: boolean;

  @prop({ default: false })
  isCredit!: boolean;

  @prop({ enum: CostLineStatus, default: CostLineStatus.OPEN })
  status!: CostLineStatus;

  @prop({ default: '' })
  description!: string;

  @prop({ default: 0 })
  position!: number;

  @prop({ default: 0 })
  totalPrice!: number;

  @prop({ default: 0 })
  price!: number;

  @prop({ default: 1 })
  quantity!: number;

  @prop()
  startDate!: Date;

  @prop()
  endDate!: Date;

  @prop({ enum: ContractType, default: ContractType.RENTING })
  type!: ContractType;

  @prop({ enum: AgreementLinePeriod })
  period!: AgreementLinePeriod;

  @prop({ enum: AgreementLinePeriodType })
  periodType!: AgreementLinePeriodType;

  @prop()
  canceledAt!: Date;

  @prop()
  approvedAt!: Date;

  @prop({ ref: () => UnitModel })
  unit!: Ref<UnitDocument>;

  @prop({ ref: () => CostLineGeneralModel })
  costLineGeneral!: Ref<CostLineGeneralDocument>;

  @prop({ ref: () => AgreementLineModel })
  agreementLine!: Ref<AgreementLineDocument>;

  @prop({ ref: () => CostTypeModel })
  costType!: Ref<CostTypeDocument>;

  @prop({ ref: () => InvoiceModel, required: false })
  invoice?: Ref<InvoiceDocument>;

  @prop({ ref: () => ContactModel })
  contact?: Ref<ContactDocument>;

  @prop({ ref: () => LocationModel })
  location?: Ref<LocationModel>;

  @prop({ ref: () => CostCenterModel })
  costCenter?: Ref<CostCenterModel>;

  @prop({ ref: () => CountryModel })
  country?: Ref<CountryDocument>;

  @prop({ ref: () => BvCompanyModel })
  bvCompany?: Ref<BvCompanyDocument>;

  @prop({ ref: () => JobModel })
  job?: Ref<JobDocument>;

  @prop({ ref: () => CostLineModel })
  parent?: Ref<CostLineDocument>;
}
