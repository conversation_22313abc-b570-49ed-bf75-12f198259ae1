import { ForbiddenException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import dayjs from 'dayjs';
import { omit } from 'lodash';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';

import { AgreementLineModel } from '~/modules/agreementline/agreementline.model';
import { ContactModel } from '~/modules/contact/contact.model';
import { ContractTypeModel } from '~/modules/contract-type/contract-type.model';
import { CostlineService } from '~/modules/costline/costline.service';
import { CostLineGeneralModel } from '~/modules/costlinegeneral/costlinegeneral.model';
import { IdentifierService } from '~/modules/identifier/identifier.service';
import { LocationModel } from '~/modules/location/location.model';
import { TenantUserService } from '~/modules/tenant-user/tenant-user.service';
import { UnitModel } from '~/modules/unit/unit.model';
import { EventEmitterSender } from '~/processors/event-emitter/event-emitter.sender';
import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  AgreementLineType,
  ContractType,
  NoticeDays,
} from '~/shared/enums/contract.enum';
import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';
import { CONTACT_MESSAGE_KEYS } from '~/shared/message-keys/contact.message-key';
import { CONTRACT_MESSAGE_KEYS } from '~/shared/message-keys/contract.message-keys';
import { CONTRACT_TYPE_MESSAGE_KEYS } from '~/shared/message-keys/contract-type.message-key';
import { LOCATION_MESSAGE_KEYS } from '~/shared/message-keys/location.message-key';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import {
  initMockAgreementLine,
  mockAgreementLineData,
} from '~/test/mocks/agreementline.mock';
import { initMockContact, mockContactData } from '~/test/mocks/contact.mock';
import { initMockContract, mockContractData } from '~/test/mocks/contract.mock';
import {
  initMockContractType,
  mockContractTypeData,
} from '~/test/mocks/contracttype.mock';
import {
  initMockCostlineGeneral,
  mockCostlineGeneralData,
} from '~/test/mocks/costlinegeneral.mock';
import { initMockCostType, mockCostTypeData } from '~/test/mocks/costtype.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { initMockLocationAdditional } from '~/test/mocks/locationadditional.mock';
import { initMockLocationAdditionalGroupName } from '~/test/mocks/locationadditionalgroupname.mock';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';

import { ContractModel } from '../contract.model';
import { ContractService } from '../contract.service';
import { CreateContractDto } from '../dtos/create-contract.dto';
import { contractTest } from './contract.dto.test';

describe('ContractService', () => {
  let service: ContractService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        ContractService,
        ...testInjectProviders([
          ContractModel,
          AgreementLineModel,
          CostLineGeneralModel,
          ContactModel,
          LocationModel,
          UnitModel,
          ContractTypeModel,
          IdentifierService,
          CostlineService,
          TenantUserService,
          EventEmitterSender,
        ]),
      ],
    }).compile();

    service = module.get(ContractService);
  });

  beforeAll(async () => {
    // Init data
    await Promise.all([
      initMockContract({ type: ContractType.SUPPLIER }),
      initMockUnit(),
      initMockLocation(),
      initMockLocationAdditional({
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
      }),
      initMockLocationAdditionalGroupName(),
      initMockContact(),
      initMockContractType(),
      initMockAgreementLine({
        type: AgreementLineType.ACCOMMODATION,
        contract: mockContractData._id,
        costLineGenerals: [mockCostlineGeneralData._id],
      }),
      initMockCostlineGeneral(),
      initMockCostType(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should throw error if the user does not have permission', async () => {
      const payload = {
        user: new ObjectId(),
        type: ContractType.RENTING,
      };
      await expect(service.findAll(payload)).rejects.toThrow();
    });

    it('should call fn with type renting or creditor in payload and return list data', async () => {
      // Prepare data
      await initMockContract({ type: ContractType.RENTING });

      // Mock private method
      jest
        .spyOn(service as any, 'validateReadPermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validatePayloadWhenFindAllContract')
        .mockResolvedValue(null);

      const payload = {
        user: new ObjectId(),
        type: ContractType.RENTING,
      };

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(
        contractTest.findAllTypeCreditorRentingSchema,
      );
    });

    it('should call fn with type service in payload and return list data', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateReadPermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validatePayloadWhenFindAllContract')
        .mockResolvedValue(null);

      const payload = {
        user: new ObjectId(),
        type: ContractType.SUPPLIER,
      };

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(contractTest.findAllTypeServiceSchema);
    });

    it('should call fn with type supplier in payload and return list data', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateReadPermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validatePayloadWhenFindAllContract')
        .mockResolvedValue(null);

      const payload = {
        user: new ObjectId(),
        type: ContractType.SUPPLIER,
      };

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(contractTest.findAllTypeSupplierSchema);
    });

    it('should call fn with type custom in payload and return list data', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateReadPermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validatePayloadWhenFindAllContract')
        .mockResolvedValue(null);

      const payload = {
        user: new ObjectId(),
        type: ContractType.CUSTOM,
      };

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(contractTest.findAllTypeCustomSchema);
    });
  });

  describe('findDetailOfOne', () => {
    it('should call fn with id and return data', async () => {
      const id = mockContractData._id.toString();

      const result = await service.findDetailOfOne(id);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContractData._id);
      expect(result).toMatchSchema(contractTest.findDetailOfOneSchema);
    });

    it('should return null if contract not found', async () => {
      const id = new ObjectId().toString();

      const result = await service.findDetailOfOne(id);
      expect(result).toBeNull();
    });
  });

  describe('findLocationOfContract', () => {
    it('should call fn with id and return data', async () => {
      // Prepare data
      await initMockContract({ type: ContractType.SUPPLIER });

      const id = mockContractData._id.toString();

      const result = await service.findLocationOfContract(id);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContractData._id);
      expect(result).toMatchSchema(contractTest.findLocationOfContractSchema);
    });

    it('should return null if contract not found', async () => {
      const id = new ObjectId().toString();

      const result = await service.findLocationOfContract(id);
      expect(result).toBeUndefined();
    });
  });

  describe('create', () => {
    const payload: CreateContractDto = {
      identifier: nanoid(),
      isActive: true,
      type: ContractType.CREDITOR,
      noticeDays: NoticeDays.ONE_MONTH,
      generatePeriod: 12,
      contact: mockContactData._id.toString(),
      location: mockLocationData._id.toString(),
      contractType: mockContractTypeData._id.toString(),
      isWholeLocation: true,
      note: 'Test note',
      attachments: [],
      isGenerateCostLine: true,
      startDate: new Date().toISOString(),
      signedAt: new Date().toISOString(),
      isSigned: false,
      agreementLines: [
        {
          period: AgreementLinePeriod.MONTHLY,
          periodType: AgreementLinePeriodType.PERIODIC,
          type: AgreementLineType.ACCOMMODATION,
          units: [mockUnitData._id.toString()],
          costLineGenerals: [
            {
              costType: mockCostTypeData._id.toString(),
              description: 'Beheer',
              position: 1,
              price: 2.3,
              startDate: new Date().toISOString(),
              unit: mockUnitData._id.toString(),
            },
          ],
        },
      ],
      user: new ObjectId().toString(),
    };

    it('should throw error if user not found in payload', async () => {
      await expect(service.create(omit(payload, 'user'))).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw error if user does not have permission', async () => {
      await expect(service.create(payload)).rejects.toThrow(ForbiddenException);
    });

    it('should throw error if contact not found', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);

      const wrongPayload = {
        ...payload,
        contact: new ObjectId().toString(),
      };

      await expect(service.create(wrongPayload)).rejects.toThrow(
        CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND,
      );
    });

    it('should throw error if location not found', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest.spyOn(service as any, 'validateContactType').mockResolvedValue(null);

      const wrongPayload = {
        ...payload,
        location: new ObjectId().toString(),
      };

      await expect(service.create(wrongPayload)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if contract type not found', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest.spyOn(service as any, 'validateContactType').mockResolvedValue(null);

      const wrongPayload = {
        ...payload,
        contractType: new ObjectId().toString(),
      };

      await expect(service.create(wrongPayload)).rejects.toThrow(
        CONTRACT_TYPE_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if unit is root when isWholeLocation is false', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest.spyOn(service as any, 'validateContactType').mockResolvedValue(null);

      // Prepare data
      await initMockUnit({ parent: null });

      const wrongPayload = {
        ...payload,
        isWholeLocation: false,
      };

      await expect(service.create(wrongPayload)).rejects.toThrow(
        CONTRACT_MESSAGE_KEYS.ROOT_UNIT_NOT_ALLOWED,
      );
    });

    it('should throw error if unit not exist in location when isWholeLocation is false', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest.spyOn(service as any, 'validateContactType').mockResolvedValue(null);

      // Prepare data
      await initMockUnit({ isRoot: false, parent: null, location: null });

      const wrongPayload = {
        ...payload,
        isWholeLocation: false,
      };

      await expect(service.create(wrongPayload)).rejects.toThrow(
        CONTRACT_MESSAGE_KEYS.UNITS_ARE_NOT_PART_OF_LOCATION,
      );
    });

    it('should throw error if root unit not found when isWholeLocation is true', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest.spyOn(service as any, 'validateContactType').mockResolvedValue(null);

      // Prepare data
      await initMockUnit({ isRoot: true });

      await expect(service.create(payload)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.ROOT_UNIT_NOT_FOUND,
      );
    });

    it('should not create cost lines if isGenerateCostLine is false', async () => {
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest.spyOn(service as any, 'validateContactType').mockResolvedValue(null);
      jest.spyOn(service as any, 'overwriteRootUnit').mockResolvedValue(null);
      jest
        .spyOn(service['identifierService'], 'generateIdentifier')
        .mockResolvedValue(nanoid());
      const costLineSpy = jest
        .spyOn(service['costLineService'], 'createCostLines')
        .mockResolvedValue([]);

      await service.create({ ...payload, isGenerateCostLine: false });
      expect(costLineSpy).not.toHaveBeenCalled();
    });

    it('should set isActive to false if endDate is in the past', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest.spyOn(service as any, 'validateContactType').mockResolvedValue(null);
      jest.spyOn(service as any, 'overwriteRootUnit').mockResolvedValue(null);
      jest
        .spyOn(service['identifierService'], 'generateIdentifier')
        .mockResolvedValue(nanoid());

      const pastDate = dayjs().subtract(2, 'days').toISOString();
      const result = await service.create({
        ...payload,
        endDate: pastDate,
      });
      expect(result).toBeDefined();
      expect(result).toHaveProperty('isActive', false);
      expect(result).toMatchSchema(contractTest.createSchema);
    });

    it('should call fn with payload and create data', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest.spyOn(service as any, 'validateContactType').mockResolvedValue(null);
      jest.spyOn(service as any, 'overwriteRootUnit').mockResolvedValue(null);
      jest
        .spyOn(service['identifierService'], 'generateIdentifier')
        .mockResolvedValue(nanoid());

      const result = await service.create(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(contractTest.createSchema);
    });
  });

  describe('update', () => {
    const basePayload = {
      id: mockContractData._id.toString(),
      user: new ObjectId().toString(),
      type: ContractType.RENTING,
      isWholeLocation: true,
    };

    it('should throw error if user not found in payload', async () => {
      await expect(service.update(omit(basePayload, 'user'))).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw error if user does not have permission', async () => {
      await expect(service.update(basePayload)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw error if contract not found', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);

      await expect(
        service.update({ ...basePayload, id: new ObjectId().toString() }),
      ).rejects.toThrow(CONTRACT_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw error if agreementLines not found', async () => {
      const payload = {
        ...basePayload,
        agreementLines: [{ _id: new ObjectId() }],
      };

      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validateDataWhenUpdateContract')
        .mockReturnValue(payload);

      await expect(service.update(payload)).rejects.toThrow(
        CONTRACT_MESSAGE_KEYS.AGREEMENT_NOT_FOUND,
      );
    });

    it('should throw error if costLineGenerals not found', async () => {
      const payload = {
        ...basePayload,
        agreementLines: [
          {
            _id: mockAgreementLineData._id.toString(),
            costLineGenerals: [{ _id: new ObjectId().toString() }],
          },
        ],
      };

      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validateDataWhenUpdateContract')
        .mockReturnValue(payload);

      await expect(service.update(payload)).rejects.toThrow(
        CONTRACT_MESSAGE_KEYS.SOME_COSTLINE_GENERAL_ARE_NOT_IN_CONTRACT,
      );
    });

    it('should throw error if payload is invalid', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);

      await expect(
        service.update({ isActive: true, type: ContractType.RENTING }),
      ).rejects.toThrow();
    });

    it('should throw error if start date is too early', async () => {
      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validateDataWhenUpdateContract')
        .mockReturnValue({
          ...basePayload,
          startDate: dayjs().subtract(62, 'days').toISOString(),
        });

      await expect(
        service.update({
          ...basePayload,
          startDate: dayjs().subtract(62, 'days').toISOString(),
        }),
      ).rejects.toThrow(
        CONTRACT_MESSAGE_KEYS.START_DATE_CANNOT_BE_CHANGED_BEFORE_60_DAYS,
      );
    });

    it('should throw error if agreementLines has more than one ACCOMMODATION when isWholeLocation is true', async () => {
      const payload = {
        ...basePayload,
        agreementLines: [
          { type: AgreementLineType.ACCOMMODATION },
          { type: AgreementLineType.ACCOMMODATION },
        ],
      };

      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validateDataWhenUpdateContract')
        .mockReturnValue(payload);

      await expect(service.update(payload)).rejects.toThrow(
        CONTRACT_MESSAGE_KEYS.CONTRACT_ONLY_HAVE_ONE_ACCOMMODATION_AGREEMENTLINE,
      );
    });

    it('should throw if agreementLines has ACCOMMODATION and DB already has one when isWholeLocation is true', async () => {
      const payload = {
        ...basePayload,
        agreementLines: [
          {
            type: AgreementLineType.ACCOMMODATION,
            costLineGenerals: [
              {
                startDate: new Date().toISOString(),
              },
            ],
          },
        ],
      };

      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validateDataWhenUpdateContract')
        .mockReturnValue(payload);

      await expect(service.update(payload)).rejects.toThrow(
        CONTRACT_MESSAGE_KEYS.CONTRACT_ONLY_HAVE_ONE_ACCOMMODATION_AGREEMENTLINE,
      );
    });

    it('should set isActive to false if endDate is in the past', async () => {
      const pastDate = dayjs().subtract(2, 'days').toISOString();
      const payload = {
        ...basePayload,
        endDate: pastDate,
      };

      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validateDataWhenUpdateContract')
        .mockReturnValue(payload);

      const result = await service.update(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('isActive', false);
      expect(result).toHaveProperty('_id', mockContractData._id);
      expect(result).toMatchSchema(contractTest.updateSchema);
    });

    it('should call fn with payload and update data', async () => {
      const payload = {
        ...basePayload,
        generatePeriod: 15,
      };

      // Mock private method
      jest
        .spyOn(service as any, 'validateManagePermissionByContractType')
        .mockResolvedValue(null);
      jest
        .spyOn(service as any, 'validateDataWhenUpdateContract')
        .mockReturnValue(payload);

      const result = await service.update(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('isActive', false);
      expect(result).toHaveProperty('_id', mockContractData._id);
      expect(result).toMatchSchema(contractTest.updateSchema);
    });
  });
});
