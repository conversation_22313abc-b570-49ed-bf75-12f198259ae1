import { HttpException } from '@nestjs/common';

export class LentoIntegrateException extends HttpException {
  constructor(
    message: string,
    statusCode: number,
    public errorCode: string,
    public processName: string,
    public readonly errorDetail?: string,
    public readonly requestBody?: string,
  ) {
    super(
      {
        statusCode,
        message,
        errorCode,
        processName,
        errorDetail,
        requestBody,
      },
      statusCode,
    );
  }
}
