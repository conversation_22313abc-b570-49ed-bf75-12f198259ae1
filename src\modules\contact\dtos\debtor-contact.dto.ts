import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { ContactType } from '~/shared/enums/contact.enum';

import {
  BaseContactSchema,
  ContactIdSchema,
  ContactRoleSchema,
} from './contact.dto';
import { CreateOrganizationContactSchema } from './organization-contact.dto';
import { CreatePersonContactSchema } from './person-contact.dto';

const contactTypeValues = Object.values(ContactType) as [string, ...string[]];

export const CreateDebtorSchema = z
  .strictObject({
    contactType: z.enum(contactTypeValues),
    paymentTermRentInvoice: z.number().min(0).optional(),
    paymentTermJobInvoice: z.number().min(0).optional(),
    invoiceEmail: z
      .string()
      .email()
      .optional()
      .nullable()
      .transform((val) => {
        if (val === null) return '';
        else return val;
      }),
    invoiceReference: z
      .string()
      .max(128)
      .trim()
      .optional()
      .nullable()
      .transform((val) => {
        if (val === null) return '';
        else return val;
      }),
    collectiveJobInvoice: z.boolean().optional(),
    collectiveCustomInvoice: z.boolean().optional(),
  })
  .merge(BaseContactSchema)
  .merge(ContactRoleSchema);

export const UpdateDebtorSchema = CreateDebtorSchema.merge(ContactIdSchema);

export class CreateDebtorPersonDto extends createZodDto(
  CreateDebtorSchema.merge(CreatePersonContactSchema),
) {}

export class CreateDebtorOrganizationDto extends createZodDto(
  CreateDebtorSchema.merge(CreateOrganizationContactSchema).merge(
    z.strictObject({
      snf: z
        .string()
        .max(64)
        .trim()
        .optional()
        .nullable()
        .transform((val) => {
          if (val === null) return '';
          else return val;
        }),
    }),
  ),
) {}

export class UpdateDebtorPersonDto extends createZodDto(
  UpdateDebtorSchema.merge(CreatePersonContactSchema),
) {}

export class UpdateDebtorOrganizationDto extends createZodDto(
  UpdateDebtorSchema.merge(CreateOrganizationContactSchema).merge(
    z.strictObject({
      snf: z
        .string()
        .max(64)
        .trim()
        .optional()
        .nullable()
        .transform((val) => {
          if (val === null) return '';
          else return val;
        }),
      // add identifier
      identifier: z.string(),
    }),
  ),
) {}
