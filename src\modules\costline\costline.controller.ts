import { <PERSON>, Post, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { COSTLINE_MESSAGES } from '~/shared/messages/costline.message';

import { CostlineService } from './costline.service';
import { CreditCostLineCreateBodyDto } from './dtos/create-credit-costline.dto';
import {
  CreateCustomCostLinesDto,
  CreateCustomCostLinesZodDto,
  DeleteCostlineDto,
} from './dtos/create-custom-costline.dto';

@Controller('costline')
export class CostlineController {
  constructor(private readonly costlineService: CostlineService) {}

  @MessagePattern({ cmd: COSTLINE_MESSAGES.CREATE_CUSTOM })
  @UsePipes(new ZodValidationPipe(CreateCustomCostLinesZodDto))
  @Post('custom')
  async createCustomCostLine(@Payload() data: CreateCustomCostLinesDto) {
    return this.costlineService.createCustomCostLine(data);
  }

  @MessagePattern({ cmd: COSTLINE_MESSAGES.DELETE })
  @UsePipes(new ZodValidationPipe(DeleteCostlineDto))
  async delete(@Payload() payload: DeleteCostlineDto) {
    return this.costlineService.deleteCostLine(payload);
  }

  @MessagePattern({ cmd: COSTLINE_MESSAGES.CREATE_CREDIT })
  @UsePipes(new ZodValidationPipe(CreditCostLineCreateBodyDto))
  async createCreditCostLines(@Payload() data: any) {
    return this.costlineService.createCreditCostLines(data);
  }
}
