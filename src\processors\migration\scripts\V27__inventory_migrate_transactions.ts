import { Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(async (transaction: { _id: Types.ObjectId; [key: string]: any }) =>
      omitNull({
        _id: transaction._id,
        identifier: transaction.identifier,
        transactionType: transaction.transactionType,
        transactionDate: transaction.transactionDate,
        isDeleted: transaction.deleted,
        completed: transaction.completed,
        from: transaction.from, // Reference to transactionreference
        to: transaction.to, // Reference to transactionreference
        transactionDetails: transaction.transactionDetails, // Reference to transactiondetail
        job: transaction.inspection, // Reference to job
        createdBy: transaction.createdBy, // Reference to tenantusers
        updatedBy: transaction.updatedBy, // Reference to tenantusers
        deletedBy: transaction.deletedBy, // Reference to tenantusers
        createdAt: transaction.createdAt,
        updatedAt: transaction.updatedAt,
        deletedAt: transaction.deletedAt,
      }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'transaction',
      destinationCollectionName: 'transaction',
      tranformDataFunc: transformData,
      inventoryMode: true,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
