import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { ContactRole } from '~/shared/enums/contact.enum';
import { isValidObjectId } from '~/utils';

import { BaseContactSchema, GetContactSchema } from './contact.dto';

const contactRoleValues = Object.values(ContactRole) as [string, ...string[]];

export const CreateOrganizationContactSchema = z
  .strictObject({
    name: z.string().min(1).max(128).trim(),
    kvk: z
      .string()
      .min(1)
      .max(64)
      .trim()
      .optional()
      .nullable()
      .transform((val) => {
        if (val === null) return '';
        else return val;
      }),
    vatCode: z
      .string()
      .min(1)
      .max(64)
      .trim()
      .optional()
      .nullable()
      .transform((val) => {
        if (val === null) return '';
        else return val;
      }),
    parentOrganization: z
      .string()
      .refine((val) => isValidObjectId(val))
      .optional()
      .nullable(),
  })
  .merge(BaseContactSchema)
  .merge(GetContactSchema);

const OrganizationContactQuerySchema = z
  .strictObject({
    contactRoles: z.array(z.enum(contactRoleValues)),
  })
  .merge(QueryParamsSchema);
export class CreateOrganizationContactDto extends createZodDto(
  CreateOrganizationContactSchema,
) {}

export class OrganizationContactQueryParamDto extends createZodDto(
  OrganizationContactQuerySchema,
) {}
