import path from 'path';

import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const assigneeJobHtml = `<p style='font-family:Arial,Helvetica,sans-serif;font-size:14px;line-height:1.2em'>Beste <%= ASSIGNEE%>,</p><p style='font-family:Arial,Helvetica,sans-serif;font-size:14px;line-height:1.2em'>Er staat een nieuwe inspectie voor je klaar <%= JOB_ID%>.</p><p style='font-family:Arial,Helvetica,sans-serif;font-size:14px;line-height:1.2em'>Locaties: <%= FULLADDRESS%></p><p style='font-family:Arial,Helvetica,sans-serif;font-size:14px;line-height:1.2em'>Deze inspectie zal uitgevoerd moeten worden op <%= PLANNED_DATE%></p><p style='font-family:Arial,Helvetica,sans-serif;font-size:14px;line-height:1.2em'>Instructies: <%= INSTRUCTIONS %></p>`;

const completeJobHtml = `<!doctype html>
<html xmlns='http://www.w3.org/1999/xhtml' xmlns:v='urn:schemas-microsoft-com:vml'
    xmlns:o='urn:schemas-microsoft-com:office:office'>

<head>
    <title></title><!--[if !mso]><!-->
    <meta http-equiv='X-UA-Compatible' content='IE=edge'><!--<![endif]-->
    <meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
    <meta name='viewport' content='width=device-width,initial-scale=1'>
    <style type='text/css'>
        #outlook a {
            padding: 0
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0;
            mso-table-rspace: 0
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: 0;
            text-decoration: none;
            -ms-interpolation-mode: bicubic
        }

        p {
            display: block;
            margin: 13px 0
        }
    </style>
    <link href='https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700' rel='stylesheet' type='text/css'>
    <style type='text/css'>
        @import url(https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700);
    </style>
    <style type='text/css'>
        @media only screen and (min-width:480px) {
            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%
            }

            .mj-column-px-600 {
                width: 600px !important;
                max-width: 600px
            }
        }
    </style>
    <style media='screen and (min-width:480px)'>
        .moz-text-html .mj-column-per-100 {
            width: 100% !important;
            max-width: 100%
        }

        .moz-text-html .mj-column-px-600 {
            width: 600px !important;
            max-width: 600px
        }
    </style>
    <style type='text/css'></style>
    <style type='text/css'>
        .banner {
            background: url(<%= COMPANY_BANNER %>);
            background-size: cover;
            background-repeat: no-repeat;
            background-position: bottom right;
            padding-left: 18px !important
        }

        .text-banner {
            font-family: Arial, sans-serif !important;
            padding-left: 0 !important;
            padding-bottom: 28px !important;
            padding-top: 62px !important;
            text-align: left !important;
            color: #fff !important;
            font-size: 48px !important
        }

        .primary-text {
            font-weight: 700 !important;
            color: <%=COMPANY_TYPOGRAPHY_COLORS_PRIMARY %> !important
        }

        .logo-eeac-wrapper {
            width: max-content;
            height: max-content
        }

        .logo-eeac-wrapper img {
            vertical-align: middle !important
        }

        .logo-right img {
            vertical-align: middle !important
        }

        .logo-eeac {
            height: 36px !important;
            width: auto !important
        }

        .logo-linkedin {
            height: 28px !important
        }

        .logo-sfn {
            height: 40px !important
        }

        @media only screen and (max-width:478px) {
            .container {
                padding: 0 0 !important
            }

            .banner {
                padding-left: 12px !important
            }

            .text-banner {
                font-size: 35px !important;
                padding-left: 0 !important;
                padding-bottom: 10px !important;
                padding-top: 30px !important
            }

            .section-header {
                width: 100% !important
            }

            .logo-linkedin {
                height: 18px !important
            }

            .logo-sfn {
                height: 30px !important
            }

            .logo-right {
                padding: 0 8px !important;
                height: 100% !important;
                width: max-content !important;
                line-height: 0
            }

            .logo-right .logo-linkedin-wrapper {
                display: inline-block;
                height: 100%;
                margin-right: 8px !important;
                vertical-align: middle !important
            }

            .logo-eeac-wrapper {
                display: inline-block;
                height: 100%;
                vertical-align: middle !important
            }

            .section-footer {
                max-width: 100% !important;
                padding-left: 2px !important
            }
        }
    </style>
</head>

<body style='word-spacing:normal'>
    <div style>
        <!--[if mso | IE]><table align='center' border='0' cellpadding='0' cellspacing='0' class='container-outlook' style='width:600px' width='600'><tr><td style='line-height:0;font-size:0;mso-line-height-rule:exactly'><![endif]-->
        <div class='container' style='margin:0 auto;max-width:600px'>
            <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation' style='width:100%'>
                <tbody>
                    <tr>
                        <td style='direction:ltr;font-size:0;padding:0;text-align:center'>
                            <!--[if mso | IE]><table role='presentation' border='0' cellpadding='0' cellspacing='0'><tr><td class='' style='vertical-align:top;width:600px'><![endif]-->
                            <div class='mj-column-per-100 mj-outlook-group-fix'
                                style='font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%'>
                                <table border='0' cellpadding='0' cellspacing='0' role='presentation'
                                    style='vertical-align:top' width='100%'>
                                    <tbody>
                                        <tr>
                                            <td align='left'
                                                style='font-size:0;padding:10px 25px;padding-left:0;word-break:break-word'>
                                                <div
                                                    style='font-family:Arial,sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343a40'>
                                                    Geachte heer / mevrouw,</div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align='left'
                                                style='font-size:0;padding:10px 25px;padding-left:0;word-break:break-word'>
                                                <div
                                                    style='font-family:Arial,sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343a40'>
                                                    Wij hebben locatie <%= FULLADDRESS %> geïnspecteerd. Dit betreft de
                                                        inspectie met nummer <%=JOB_ID %>.</div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align='left'
                                                style='font-size:0;padding:10px 25px;padding-left:0;word-break:break-word'>
                                                <div
                                                    style='font-family:Arial,sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343a40'>
                                                    Als u nadere informatie hierover wenst dan kunt u contact met ons
                                                    opnemen.</div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align='left'
                                                style='font-size:0;padding:10px 25px;padding-left:0;word-break:break-word'>
                                                <div
                                                    style='font-family:Arial,sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343a40'>
                                                    Het onderhoudsrapport vindt u <a href='<%= LINK %>'
                                                        target='_blank'>hier</a></div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align='left'
                                                style='font-size:0;padding:10px 25px;padding-left:0;word-break:break-word'>
                                                <div
                                                    style='font-family:Arial,sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343a40'>
                                                    Met vriendelijke groeten,</div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td align='left'
                                                style='font-size:0;padding:10px 25px;padding-left:0;word-break:break-word'>
                                                <div
                                                    style='font-family:Arial,sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343a40'>
                                                    <span class='primary-text text-sign' style='margin-right:8px'>
                                                        <%= COMPANY_SIGN %>
                                                    </span><br><span class='primary-text text-sign'
                                                        style='margin-right:8px'>T</span>
                                                    <%= COMPANY_TELEPHONE %> <br><span class='primary-text text-sign'
                                                            style='margin-right:8px'>E</span><a
                                                            href='mailto:<%= SIGNATURE_EMAIL %>' target='_blank'>
                                                            <%= SIGNATURE_EMAIL %>
                                                        </a><br><span class='primary-text text-sign'
                                                            style='margin-right:12px'>I</span><a
                                                            href='https://<%= COMPANY_WEBSITE %>' target='_blank'>
                                                            <%= COMPANY_WEBSITE %>
                                                        </a><br><span class='text-sign'>
                                                            <%= COMPANY_ADDRESS1 %>
                                                        </span><br><span class='text-sign'>
                                                            <%= COMPANY_ADDRESS2 %>
                                                        </span>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style='font-size:0;word-break:break-word'>
                                                <div style='height:32px;line-height:32px'>&#8202;</div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td style='font-size:0;word-break:break-word'>
                                                <div style='height:32px;line-height:32px'>&#8202;</div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div><!--[if mso | IE]><![endif]-->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div><!--[if mso | IE]><![endif]-->
        <div class='section-footer'
            style='background: <%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>;background-color: <%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>;margin:0 auto;max-width:100%'>
            <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation'
                style='background:<%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>;background-color:<%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>;width:100%'>
                <tbody>
                    <tr>
                        <td style='direction:ltr;font-size:0;padding:0;text-align:center'>
                            <!--[if mso | IE]><table role='presentation' border='0' cellpadding='0' cellspacing='0'><tr><td class='' style='vertical-align:middle;width:600px'><![endif]-->
                            <div class='mj-column-px-600 mj-outlook-group-fix'
                                style='font-size:0;text-align:left;direction:ltr;display:inline-block;vertical-align:middle;width:100%'>
                                <table border='0' cellpadding='0' cellspacing='0' role='presentation'
                                    style='vertical-align:middle' width='100%'>
                                    <tbody>
                                        <tr>
                                            <td align='left' style='font-size:0;padding:0;word-break:break-word'>
                                                <table cellpadding='0' cellspacing='0' width='100%' border='0'
                                                    style='color:#000;font-family:Ubuntu,Helvetica,Arial,sans-serif;font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none'>
                                                    <tr>
                                                        <td><a class='logo-eeac-wrapper' target='_blank'
                                                                href='https://<%= COMPANY_WEBSITE %>'
                                                                style='display:inline-block;height:100%;vertical-align:middle'><img
                                                                    class='logo-eeac' src='<%= COMPANY_LOGO %>'
                                                                    alt='logo' width='100%'></a></td>
                                                        <td align='right'
                                                            style='width:1%;white-space:nowrap;height:100%;background:#fff'>
                                                            <div class='logo-right'
                                                                style='display:inline-block;vertical-align:middle;background-color:#fff;padding:0 20px;line-height:0;height:100%;width:max-content'>
                                                                <table cellpadding='0' cellspacing='0' width='100%'
                                                                    border='0'>
                                                                    <tr>
                                                                        <td><a class='logo-linkedin-wrapper'
                                                                                target='_blank'
                                                                                href='<%= COMPANY_LINKEDIN_URL %>'
                                                                                style='width:max-content;display:inline-block;height:100%;margin-right:20px;vertical-align:middle'><img
                                                                                    class='logo-linkedin'
                                                                                    src='<%= COMPANY_LINKEDIN_LOGO %>'
                                                                                    alt='logo'></a></td>
                                                                        <td><img class='logo-sfn'
                                                                                src='<%= COMPANY_SFN_LOGO %>'
                                                                                alt='logo'></td>
                                                                    </tr>
                                                                </table>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                </table>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div><!--[if mso | IE]><![endif]-->
                        </td>
                    </tr>
                </tbody>
            </table>
        </div><!--[if mso | IE]><![endif]-->
        <div style='height:20px;line-height:20px'>&#8202;</div>
    </div>
</body>

</html>`;

const rejectJobHtml = `<p style='font-family:Arial,Helvetica,sans-serif'>Beste <%= ASSIGNEE %>,</p>
<p style='font-family:Arial,Helvetica,sans-serif'>Onderhoud <%= JOB_ID %> voor locatie <%= FULLADDRESS %> is afgewezen.
</p>
<p style='font-family:Arial,Helvetica,sans-serif'>De reden hiervoor is: <%= REASON %>
</p>`;

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const destinationCollectionName = 'emailtemplates';
    const destinationCollection = context
      .destinationClient!.db()
      .collection(destinationCollectionName)!;

    if (!(await destinationCollection.indexExists('name_1'))) {
      destinationCollection.createIndex({ name: 1 }, { unique: true });
    }
    const data = [
      {
        name: 'job_assigne',
        subject: 'Assigne job send email',
        to: [],
        html: assigneeJobHtml,
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'job_complete',
        subject: 'Completed job send Report',
        to: [],
        html: completeJobHtml,
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'job_reject',
        subject: 'Reject job send email',
        to: [],
        html: rejectJobHtml,
        bcc: [],
        cc: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    const upsertPromises = data.map((doc) =>
      destinationCollection
        .findOneAndUpdate(
          { name: doc.name },
          { $set: doc },
          { upsert: true, returnDocument: 'after' }, // Use returnDocument: 'after' to get the updated document
        )
        .then(() => {
          console.log(
            `Migrated location additional group name with identifier=${doc.name} into collection ${destinationCollectionName}`,
          );
        })
        .catch((error) => {
          console.error(
            `Error upserting document with identifier=${doc.name}:`,
            error,
          );
        }),
    );

    await Promise.all(upsertPromises)
      .then(() => {
        console.log(
          `Migrated ${data.length} documents to collection ${destinationCollectionName}`,
        );
      })
      .catch((error) => {
        console.error('Error during upsert operations:', error);
      });
    const after = new Date().getTime();
    console.log(
      `Migration script ${fileName} completed in ${after - before}ms`,
    );
  } catch (error) {
    console.error(`Error in migration script ${fileName}: ${error}`);
  }
};
export default up;
