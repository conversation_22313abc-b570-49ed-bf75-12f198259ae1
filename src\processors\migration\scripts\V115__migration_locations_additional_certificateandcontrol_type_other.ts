import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);
const defaultType = 'certificate_and_control';

interface OldLocationAdditional {
  _id: string;
  description: string;
  position: number;
  group_name: string;
  name: string;
  date_check: Date;
  location: string;
  type: 'owner' | 'eeac' | 'customer';
  grouping_id: string;
  contact: string;
  brandType: string;
  yearInstallation: string;
  createdAt: Date;
  updatedAt: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection.aggregate([
    {
      $match: {
        $and: [
          {
            _id: { $gt: nextId },
          },
          {
            group_name: 'Other',
          },
        ],
      },
    },
    { $sort: { _id: 1 } },
    { $limit: limit },
  ]);
};

const transformData = ({
  data,
  context,
}: {
  data: OldLocationAdditional[];
  context: any;
}) => {
  return Promise.all(
    data.map(async (item: OldLocationAdditional) => {
      const { group_name, grouping_id, contact } = item;

      const transformedItem = {
        _id: item._id,
        type: defaultType,
        position: item.position,
        name: item.name,
        description: item.description,
        groupType: item.type,
        brandType: item.brandType,
        yearInstallation: item.yearInstallation,
        contact: contact ? contact : grouping_id,
        dateCheck: item.date_check,
        location: item.location,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
        isDeleted: false,
      };

      if (group_name) {
        const foundGroupName = await context
          .destinationClient!.db()
          .collection('locationadditionalgroupnames')
          .findOne({ key: group_name, type: defaultType });

        if (foundGroupName) {
          return omitNull({
            ...transformedItem,
            groupName: foundGroupName._id,
          });
        }
      } else {
        return omitNull(transformedItem);
      }
    }),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();
    await migrationV2({
      context,
      sourceCollectionName: 'location_additional_certificateandcontrol',
      destinationCollectionName: 'locationadditionals',
      pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
      // isUpsert: false,
    });
    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
