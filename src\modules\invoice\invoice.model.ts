import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { InvoiceType } from '~/shared/enums/contract.enum';
import { BaseModel } from '~/shared/models/base.model';

import { ContactDocument, ContactModel } from '../contact/contact.model';
import {
  CostCenterDocument,
  CostCenterModel,
} from '../costcenter/costcenter.model';
import { CostLineModel } from '../costline/costline.model';
import { LocationDocument, LocationModel } from '../location/location.model';

export type InvoiceDocument = DocumentType<InvoiceModel>;

@modelOptions({
  options: {
    customName: 'Invoice',
  },
})
@index({ identifier: 1 })
@index({ costLines: 1 })
@index({
  type: 1,
  locations: 1,
  contact: 1,
  identifier: 1,
  net: 1,
  invoiceReference: 1,
  approvedAt: 1,
  startDate: 1,
  endDate: 1,
})
@index({
  type: 1,
  costCenters: 1,
  contact: 1,
  identifier: 1,
  net: 1,
  invoiceReference: 1,
  approvedAt: 1,
  startDate: 1,
  endDate: 1,
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class InvoiceModel extends BaseModel {
  @prop({ enum: InvoiceType })
  type!: InvoiceType;

  @prop({ required: true, trim: true })
  identifier!: string;

  @prop()
  startDate?: Date;

  @prop()
  endDate?: Date;

  @prop({ required: true })
  net!: number;

  @prop()
  approvedAt!: Date;

  @prop({ ref: () => ContactModel })
  contact!: Ref<ContactDocument>;

  @prop({ ref: () => LocationModel })
  locations!: Ref<LocationDocument>[];

  @prop({ ref: () => CostCenterModel })
  costCenters!: Ref<CostCenterDocument[]>[];

  @prop({ ref: () => CostLineModel })
  costLines!: Ref<CostLineModel>[];

  @prop({ ref: () => InvoiceModel })
  invoiceReference?: Ref<InvoiceDocument>;
}
