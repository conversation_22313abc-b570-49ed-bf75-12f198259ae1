import { Test, TestingModule } from '@nestjs/testing';

import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockTenantRole } from '~/test/mocks/tenantrole.mock';

import { TenantRoleModel } from '../tenant-role.model';
import { TenantRoleService } from '../tenant-role.service';
import { tenantRoleTest } from './tenant-role.dto.test';

describe('TenantRoleService', () => {
  let service: TenantRoleService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [TenantRoleService, ...testInjectModel([TenantRoleModel])],
    }).compile();

    service = module.get(TenantRoleService);

    // Init data
    await initMockTenantRole();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should call fn with payload and return list data', async () => {
      const result = await service.findAll({});
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(tenantRoleTest.findAllSchema);
    });

    it('should call fn and return empty list when data not exist', async () => {
      const result = await service.findAll({ pageIndex: 999 });
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });
});
