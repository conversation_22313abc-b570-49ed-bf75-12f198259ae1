import { fakerNL } from '@faker-js/faker';
import { ObjectId } from 'mongodb';

import { JobPeriodTypeEnum } from '~/shared/enums/job.enum';
import { Gender, Language } from '~/shared/enums/tenant-user.enum';
import { initMockEquipment } from '~/test/mocks/equipment.mock';
import { initMockJob } from '~/test/mocks/job.mock';
import { initMockJobEmployee } from '~/test/mocks/jobemployee.mock';
import { initMockJobEquipment } from '~/test/mocks/jobequipment.mock';
import { initMockLocation } from '~/test/mocks/location.mock';
import { initMockTeam } from '~/test/mocks/team.mock';
import {
  initMockTenantUser,
  mockTenantUserData,
} from '~/test/mocks/tenantuser.mock';
import { initMockUnit } from '~/test/mocks/unit.mock';

export async function initMockJobPlanning() {
  const tenantUser2 = new ObjectId();
  const plannedDate = new Date('2022-01-05T00:00:00.000Z');

  await Promise.all([
    initMockLocation(),
    initMockUnit(),
    initMockEquipment(),
    initMockTeam({
      tenantUsers: [mockTenantUserData._id, tenantUser2],
    }),
    initMockTenantUser(),
    initMockTenantUser({
      _id: tenantUser2,
      displayName: 'Jane Doe',
      firstName: 'Jane',
      lastName: 'Doe',
      username: 'jane_doe',
      password: fakerNL.internet.password(),
      email: '<EMAIL>',
      gender: Gender.FEMALE,
      language: Language.EN,
      phone1: '123456789',
    }),
    initMockJob({
      type: JobPeriodTypeEnum.REGULAR,
      assignee: mockTenantUserData._id,
      assigneeInfo: {
        _id: mockTenantUserData._id,
        displayName: mockTenantUserData.displayName,
        email: mockTenantUserData.email,
      },
      plannedDate,
    }),
    initMockJobEmployee({
      actualHours: 120,
      plannedDate,
    }),
    initMockJobEmployee({
      employee: tenantUser2,
      actualHours: 120,
      plannedDate,
    }),
    initMockJobEquipment({
      plannedDate,
    }),
  ]);
}
