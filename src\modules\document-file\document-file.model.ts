import {
  DocumentType,
  modelOptions,
  mongoose,
  plugin,
  prop,
  Ref,
  Severity,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { DocumentFileTypeEnum } from '~/shared/enums/document-file-type.enum';
import { BaseModel } from '~/shared/models/base.model';

import { LocationDocument, LocationModel } from '../location/location.model';
import { UploadFileDocument, UploadFileModel } from './upload-file.model';

export type DocumentFileDocument = DocumentType<DocumentFileModel>;

@modelOptions({
  options: { customName: 'DocumentFile', allowMixed: Severity.ALLOW },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class DocumentFileModel extends BaseModel {
  @prop({ enum: DocumentFileTypeEnum })
  type!: DocumentFileTypeEnum;

  @prop({ required: true, trim: true })
  fileName!: string;

  @prop({ required: true, trim: true })
  uploaderName!: string;

  @prop({ required: true })
  createdBy!: mongoose.Types.ObjectId;

  @prop()
  updatedBy?: mongoose.Types.ObjectId;

  @prop({ ref: () => LocationModel })
  location?: Ref<LocationDocument>;

  @prop({ ref: () => UploadFileModel })
  uploadFile!: Ref<UploadFileDocument>;

  @prop()
  bvCompany?: string;

  @prop()
  migratedAt?: Date;
}
