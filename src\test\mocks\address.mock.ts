import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { AddressModel } from '~/modules/address/address.model';

import { mockContactData } from './contact.mock';
import { mockCountryData } from './country.mock';
import { mockRegionData } from './region.mock';

const addressModel = getModelForClass(AddressModel);

export const mockAddressData = {
  _id: new ObjectId(),
  city: 'Moerdijk',
  contact: mockContactData._id,
  country: mockCountryData._id,
  isActive: true,
  number: '1',
  postalCode: '4782 SL',
  region: mockRegionData._id,
  suffix: 'A',
  street: 'Plaza',
};

export async function initMockAddress(doc?: any) {
  const { _id, ...rest } = { ...mockAddressData, ...doc };
  await addressModel.replaceOne({ _id }, rest, { upsert: true });
}
