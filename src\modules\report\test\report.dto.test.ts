import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { bvCompanyTest } from '~/modules/bvcompany/test/bvcompany.dto.test';
import { contactTest } from '~/modules/contact/test/contact.dto.test';
import { costCenterTest } from '~/modules/costcenter/test/costcenter.dto.test';
import { costTypeTest } from '~/modules/costtype/test/costtype.dto.test';
import { locationTest } from '~/modules/location/test/location.dto.test';
import { teamTest } from '~/modules/team/test/team.dto.test';

const getRevenueHiredLocationsSchema = z.object({
  items: z.array(
    z.object({
      location: locationTest.modelSchema
        .pick({
          _id: true,
          fullAddress: true,
        })
        .nullish(),
      costCenter: costCenterTest.modelSchema
        .pick({
          _id: true,
          name: true,
          identifier: true,
        })
        .nullish(),
      bvCompany: bvCompanyTest.modelSchema
        .pick({
          _id: true,
          name: true,
        })
        .nullish(),
      contact: contactTest.modelSchema
        .pick({
          _id: true,
          displayName: true,
          name: true,
        })
        .nullish(),
      revenues: z.array(
        z.object({
          costType: costTypeTest.modelSchema
            .pick({
              _id: true,
              itemCode: true,
              name: true,
            })
            .nullish(),
          totalPricePerCostType: z.number(),
        }),
      ),
      totalPricePerLocation: z.number(),
    }),
  ),
  totalPrice: z.number(),
});

const getListEmployeesSchema = z.array(
  teamTest.modelSchema
    .pick({
      _id: true,
      name: true,
    })
    .extend({
      employees: z.array(
        z.object({
          _id: z.instanceof(ObjectId),
          displayName: z.string(),
          jobs: z.array(
            z
              .object({
                _id: z.instanceof(ObjectId),
                jobType: z.string(),
                plannedDate: z.date(),
                jobIdentifier: z.string(),
                estimatedHours: z.number(),
                actualHours: z.number(),
                diffHours: z.number(),
              })
              .extend({
                location: locationTest.modelSchema
                  .pick({
                    _id: true,
                    fullAddress: true,
                  })
                  .nullish(),
                costCenter: costCenterTest.modelSchema
                  .pick({
                    _id: true,
                    identifier: true,
                  })
                  .nullish(),
              }),
          ),
        }),
      ),
    }),
);

export const reportTest = {
  getRevenueHiredLocationsSchema,
  getListEmployeesSchema,
};
