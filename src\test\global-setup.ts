import { MongoMemoryReplSet } from 'mongodb-memory-server';
import mongoose from 'mongoose';

export = async function globalSetup() {
  const instance = await MongoMemoryReplSet.create({
    replSet: { count: 1 },
  });
  const uri = instance.getUri();

  await instance.waitUntilRunning();

  (global as any).__MONGOINSTANCE = instance;
  process.env.JEST_MONGO_URI = uri;

  // Suppress warnings during tests
  process.emitWarning = () => {};

  // Ensure the database is clean before tests
  const connection = await mongoose.connect(uri);
  await connection.connection.db?.dropDatabase();
  await mongoose.disconnect();
};
