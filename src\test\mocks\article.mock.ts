import { ObjectId } from 'mongodb';
import mongoose from 'mongoose';
import { nanoid } from 'nanoid';

import { mockArticleCategoryData } from './articlecategory.mock';
import { mockContactData } from './contact.mock';

const articleModel = mongoose.connection.collection('article');

export const mockArticleData = {
  _id: new ObjectId(),
  batchSize: 14,
  category: mockArticleCategoryData._id,
  createdAt: new Date(),
  description: 'Afvalbak plastic 50 ltr',
  expectedDelivery: 6,
  identifier: nanoid(5),
  isActive: true,
  isDeleted: false,
  laborPricePerHour: 50,
  margin: 35.26,
  minimumStock: 6,
  purchasePrice: 11.09,
  removalFee: 2,
  salePrice: 15,
  supplier: mockContactData._id,
  updatedAt: new Date(),
  workingTime: 8,
};

export async function initMockArticle(doc?: any) {
  const { _id, ...rest } = { ...mockArticleData, ...doc };
  await articleModel.replaceOne({ _id }, rest, { upsert: true });
}
