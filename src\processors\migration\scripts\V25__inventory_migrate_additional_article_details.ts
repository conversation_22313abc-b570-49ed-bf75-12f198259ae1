import { Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(
      async (additionalArticleDetail: {
        _id: Types.ObjectId;
        [key: string]: any;
      }) =>
        omitNull({
          _id: additionalArticleDetail._id,
          article: additionalArticleDetail.article,
          additionalArticle: additionalArticleDetail.additionalArticle,
          reason: additionalArticleDetail.reason,
          order: additionalArticleDetail.order,
          amount: additionalArticleDetail.amount,
          isDeleted: additionalArticleDetail.deleted ?? false,
          createdAt: additionalArticleDetail.createdAt,
          updatedAt: additionalArticleDetail.updatedAt,
          createdBy: additionalArticleDetail.createdBy, // Reference to tenantusers
          updatedBy: additionalArticleDetail.updatedBy, // Reference to tenantusers
        }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'additionalarticledetail',
      destinationCollectionName: 'additionalarticledetail',
      tranformDataFunc: transformData,
      inventoryMode: true,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
