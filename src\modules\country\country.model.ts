import { DocumentType, modelOptions, plugin, prop } from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

export type CountryDocument = DocumentType<CountryModel>;

@modelOptions({
  options: { customName: 'Country' },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class CountryModel extends BaseModel {
  @prop({ required: true, trim: true })
  name!: string;

  @prop({ required: true, trim: true })
  code!: string;
}
