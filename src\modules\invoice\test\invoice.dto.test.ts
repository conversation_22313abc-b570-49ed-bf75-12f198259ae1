import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { costlineTest } from '~/modules/costline/test/costline.dto.test';
import { costTypeTest } from '~/modules/costtype/test/costtype.dto.test';
import { ContractType, InvoiceType } from '~/shared/enums/contract.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    type: z.nativeEnum(InvoiceType),
    identifier: z.string(),
    startDate: z.date().optional(),
    endDate: z.date().optional(),
    net: z.number(),
    approvedAt: z.date(),
    contact: z.instanceof(ObjectId),
    locations: z.array(z.instanceof(ObjectId)),
    costCenters: z.array(z.instanceof(ObjectId)),
    costLines: z.array(z.instanceof(ObjectId)),
    invoiceReference: z.instanceof(ObjectId).optional(),
  })
  .extend(baseModelTestSchema);

const baseInvoiceDetailsSchema = costlineTest.modelSchema
  .pick({
    _id: true,
    description: true,
    quantity: true,
    price: true,
    totalPrice: true,
    status: true,
    startDate: true,
    endDate: true,
  })
  .extend({
    vat: z.number(),
    costType: costTypeTest.modelSchema.pick({
      name: true,
      itemCode: true,
    }),
  });

const baseInvoiceReviewSchema = z.object({
  _id: z.object({
    location: z.instanceof(ObjectId).optional(),
    contract: z.instanceof(ObjectId),
    isCredit: z.boolean(),
  }),
  startDate: z.date().nullable().optional(),
  endDate: z.date().nullable().optional(),
  net: z.number(),
  vat: z.number(),
  gross: z.number(),
  contact: z.object({
    _id: z.instanceof(ObjectId),
    displayName: z.string(),
  }),
  location: z
    .object({
      _id: z.instanceof(ObjectId),
      fullAddress: z.string(),
      countryCode: z.string().nullable().optional(),
    })
    .nullable()
    .optional(),
  costCenter: z
    .object({
      _id: z.instanceof(ObjectId),
      name: z.string(),
      identifier: z.string(),
      countryCode: z.string(),
    })
    .nullable()
    .optional(),
  type: z.nativeEnum(ContractType),
  updatedAt: z.date(),
  invoiceDetails: z.array(baseInvoiceDetailsSchema),
  currentIdentifier: z.string(),
});

const findInvoicesReviewTypeContractSchema = z.array(baseInvoiceReviewSchema);

const findInvoicesReviewTypeJobSchema = z.array(
  baseInvoiceReviewSchema
    .omit({
      _id: true,
      startDate: true,
      updatedAt: true,
    })
    .extend({
      contact: z.object({
        _id: z.instanceof(ObjectId),
        displayName: z.string(),
        collectiveJobInvoice: z.boolean(),
        collectiveCustomInvoice: z.boolean(),
      }),
      location: z.string().optional(),
      invoiceDetails: z.array(
        baseInvoiceDetailsSchema.omit({
          startDate: true,
          endDate: true,
          status: true,
        }),
      ),
    }),
);

const approveInvoicesSchema = z.array(modelSchema);

const getApprovedInvoiceDetailSchema = modelSchema.omit({ type: true }).extend({
  contact: z
    .object({
      _id: z.instanceof(ObjectId),
      displayName: z.string(),
    })
    .nullable()
    .optional(),
  locations: z
    .array(
      z.object({
        _id: z.instanceof(ObjectId),
        fullAddress: z.string(),
      }),
    )
    .nullable()
    .optional(),
  costCenters: z
    .array(
      z.object({
        _id: z.instanceof(ObjectId),
        name: z.string(),
        identifier: z.string(),
      }),
    )
    .nullable()
    .optional(),
  invoiceReference: z
    .object({
      _id: z.instanceof(ObjectId),
      identifier: z.string(),
    })
    .nullable()
    .optional(),
  costLines: z.array(
    baseInvoiceDetailsSchema.omit({
      startDate: true,
      endDate: true,
    }),
  ),
});

const findApprovedInvoicesSchema = z.array(
  getApprovedInvoiceDetailSchema.omit({
    costLines: true,
  }),
);

export const invoiceTest = {
  modelSchema,
  findInvoicesReviewTypeContractSchema,
  findInvoicesReviewTypeJobSchema,
  approveInvoicesSchema,
  findApprovedInvoicesSchema,
  getApprovedInvoiceDetailSchema,
};
