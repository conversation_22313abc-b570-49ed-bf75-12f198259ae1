import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { USER_MESSAGES } from '~/shared/messages/user.message';

import {
  ChangeTenantUserPasswordDto,
  CreateTenantUserDto,
  EmployeeQueryParamsDto,
  GetTeamManagementDto,
  ResetTenantUserPasswordDto,
  UpdateDisplayConfigDto,
  UpdateTeamManagementDto,
  UpdateTenantUserDto,
  UpdateTenantUserMeDto,
  UpdateTenantUserPasswordDto,
} from './dtos/tenant-user.dto';
import { TenantUserService } from './tenant-user.service';

@Controller('users')
export class TenantUserController {
  constructor(private readonly tenantUserService: TenantUserService) {}

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(EmployeeQueryParamsDto))
  @MessagePattern({ cmd: USER_MESSAGES.GET_USERS })
  public async findAll(@Payload() data: EmployeeQueryParamsDto) {
    return this.tenantUserService.findAll(data);
  }

  @MessagePattern({ cmd: USER_MESSAGES.GET_USER_DETAIL })
  public async findOne(@Payload() data: { id: string }) {
    return this.tenantUserService.findOne(data.id);
  }

  @UsePipes(new ZodValidationPipe(CreateTenantUserDto))
  @MessagePattern({ cmd: USER_MESSAGES.CREATE_USER })
  public async create(@Payload() data: CreateTenantUserDto) {
    return this.tenantUserService.create(data);
  }

  @UsePipes(new ZodValidationPipe(UpdateTenantUserDto))
  @MessagePattern({ cmd: USER_MESSAGES.UPDATE_USER })
  public async update(@Payload() data: UpdateTenantUserDto) {
    return this.tenantUserService.update(data, false);
  }

  @UsePipes(new ZodValidationPipe(UpdateTenantUserMeDto))
  @MessagePattern({ cmd: USER_MESSAGES.UPDATE_USER_ME })
  public async updateMe(@Payload() data: UpdateTenantUserMeDto) {
    return this.tenantUserService.update(data, true);
  }

  @UsePipes(new ZodValidationPipe(UpdateTenantUserPasswordDto))
  @MessagePattern({ cmd: USER_MESSAGES.UPDATE_USER_PASSWORD_ME })
  public async updatePasswordMe(@Payload() data: UpdateTenantUserPasswordDto) {
    return this.tenantUserService.updatePassword(data, false);
  }

  @UsePipes(new ZodValidationPipe(ChangeTenantUserPasswordDto))
  @MessagePattern({ cmd: USER_MESSAGES.CHANGE_USER_PASSWORD_ME })
  public async changePasswordMe(@Payload() data: ChangeTenantUserPasswordDto) {
    return this.tenantUserService.updatePassword(data, true);
  }

  @UsePipes(new ZodValidationPipe(ResetTenantUserPasswordDto))
  @MessagePattern({ cmd: USER_MESSAGES.RESET_USER_PASSWORD })
  public async resetPassword(@Payload() data: ResetTenantUserPasswordDto) {
    return this.tenantUserService.resetPassword(data);
  }

  @UsePipes(new ZodValidationPipe(GetTeamManagementDto))
  @MessagePattern({ cmd: USER_MESSAGES.GET_TEAM_MANAGEMENT })
  public async getTeamManagement(@Payload() data: { team: string }) {
    return this.tenantUserService.getTeamManagement(data.team);
  }

  @UsePipes(new ZodValidationPipe(UpdateTeamManagementDto))
  @MessagePattern({ cmd: USER_MESSAGES.UPDATE_TEAM_MANAGEMENT })
  public async updateTeamManagement(@Payload() data: UpdateTeamManagementDto) {
    return this.tenantUserService.updateTeamManagement(data);
  }

  @MessagePattern({ cmd: USER_MESSAGES.EXPORT_USERS })
  public async exportEmployees() {
    return this.tenantUserService.exportEmployees();
  }

  @MessagePattern({ cmd: USER_MESSAGES.SEND_EMAIL_INVITE_TO_NEW_SYSTEM })
  public async sendEmailInviteToNewSystem() {
    return this.tenantUserService.sendEmailInviteToNewSystem();
  }

  @UsePipes(new ZodValidationPipe(UpdateDisplayConfigDto))
  @MessagePattern({ cmd: USER_MESSAGES.UPDATE_DISPLAY_CONFIG })
  public async updateDisplayConfig(@Payload() data: UpdateDisplayConfigDto) {
    const { id, page, displayConfig } = data;
    return this.tenantUserService.updateDisplayConfig(id, page, displayConfig);
  }
}
