import { index, modelOptions, prop } from '@typegoose/typegoose';

import { TenantRoleModel } from '~/modules/tenant-role/tenant-role.model';
import { BaseModel } from '~/shared/models/base.model';

@index({ name: 1 }, { unique: true })
@index({ description: 1 })
@modelOptions({
  schemaOptions: { timestamps: true },
  options: { customName: 'RoleGroup' },
})
export class RoleGroupModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({ required: true })
  name!: string;

  @prop()
  description?: string;

  @prop({ ref: () => TenantRoleModel })
  roles?: TenantRoleModel[];
}
