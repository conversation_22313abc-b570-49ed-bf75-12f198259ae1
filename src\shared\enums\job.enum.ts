export enum JobStatusEnum {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  READY = 'ready',
  COMPLETE = 'complete',
  CLOSE = 'close',
  REJECT = 'reject',
}

export enum JobStatusQueryEnum {
  OPEN = 'open',
  IN_PROGRESS = 'in_progress',
  READY = 'ready',
  COMPLETED = 'completed',
  CLOSED = 'closed',
  REJECT = 'reject',
}

export enum JobPeriodTypeEnum {
  NONE = 'none',
  BEGIN = 'begin',
  END = 'end',
  REGULAR = 'regular',
  PERIODIC = 'periodic',
  MULTIPLE_DAYS = 'multiple_days',
}

export enum JobTypeEnum {
  INSPECTION = 'inspection',
  MAINTENANCE = 'maintenance',
  CLEANING = 'cleaning',
}

export enum JobReportTypeEnum {
  CUSTOMER = 'customer',
  SUPPLIER = 'supplier',
  INTERNAL = 'internal',
}

export enum JobFRuleEnum {
  DAILY = 'daily',
  WEEKLY = 'weekly',
  MONTHLY = 'monthly',
  YEARLY = 'yearly',
}

export enum JobFDaysEnum {
  MONDAY = 'MO',
  TUESDAY = 'TU',
  WEDNESDAY = 'WE',
  THURSDAY = 'TH',
  FRIDAY = 'FR',
  SATURDAY = 'SA',
  SUNDAY = 'SU',
}

export enum JobPointStatusEnum {
  NONE = 'none',
  GREEN = 'green',
  YELLOW = 'yellow',
  RED = 'red',
}

export enum GeneratePdfStatusEnum {
  NONE = 'none',
  PENDING = 'pending',
  DONE = 'done',
  FAILED = 'failed',
}
