import {
  BadRequestException,
  forwardRef,
  Inject,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { mongoose } from '@typegoose/typegoose';
import { compareSync } from 'bcrypt';
import { randomInt } from 'crypto';
import { stringify } from 'csv-stringify';
import dayjs from 'dayjs';
import _, { template } from 'lodash';
import omit from 'lodash/omit';
import { Model, Types } from 'mongoose';

import { DATE_FORMAT_HYPHEN } from '~/constants/app.constant';
import { EmailService } from '~/processors/email/email.service';
import { EmailTemplateEnum } from '~/shared/enums/email-template.enum';
import { TenantRoleEnum } from '~/shared/enums/tenant-role.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { TENANT_USER_MESSAGE_KEYS } from '~/shared/message-keys/tenant-user.message-key';
import { InjectModel } from '~/transformers/model.transformer';
import {
  buildQuery,
  decimalToBitPosition,
  parseBigInttoDecimal,
  parseDecimalToBigInt,
  splitDecimalSum,
  sumDecimal,
} from '~/utils';
import { escapeHtml } from '~/utils/html.util';

import { EmailTemplateService } from '../email-template/email-template.service';
import { TeamModel } from '../team/team.model';
import { TenantModel } from '../tenant/tenant.model';
import { TenantService } from '../tenant/tenant.service';
import { TenantPermissionModel } from '../tenant-permission/tenant-permission.model';
import { TenantRoleModel } from '../tenant-role/tenant-role.model';
import { TokenModel } from '../token/token.model';
import {
  CreateTenantUserDto,
  EmployeeQueryParamsDto,
  ResetTenantUserPasswordDto,
  UpdateTeamManagementDto,
  UpdateTenantUserDto,
  UpdateTenantUserMeDto,
} from './dtos/tenant-user.dto';
import { TenantUserModel } from './tenant-user.model';

@Injectable()
export class TenantUserService {
  private readonly logger: Logger = new Logger(TenantUserService.name);
  constructor(
    @InjectModel(TeamModel)
    private readonly teamModel: Model<TeamModel>,
    @InjectModel(TenantUserModel)
    private readonly tenantUserModel: MongooseModel<TenantUserModel>,
    @InjectModel(TenantRoleModel)
    private readonly tenantRoleModel: Model<TenantRoleModel>,
    @InjectModel(TokenModel)
    private readonly tokenModel: Model<TokenModel>,
    @InjectModel(TenantPermissionModel)
    private readonly tenantPermissionModel: Model<TenantPermissionModel>,
    @InjectModel(TenantModel)
    private readonly tenantModel: Model<TenantModel>,

    @Inject(forwardRef(() => TenantService))
    private readonly tenantService: TenantService,
    private readonly emailService: EmailService,
    private readonly emailTemplateService: EmailTemplateService,
  ) {}
  async findAll(payload: EmployeeQueryParamsDto) {
    const queryOptions = buildQuery(payload, [
      'displayName',
      'firstName',
      'lastName',
    ]);

    const { options } = queryOptions;
    let { query } = queryOptions;

    if (payload.roles) {
      const rolePayloads = payload.roles.map((val) => val.toString());
      const tenantRoles = await this.tenantRoleModel.find({
        key: {
          $in: rolePayloads,
        },
      });
      if (!tenantRoles || tenantRoles.length !== payload.roles.length) {
        throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.INVALID_ROLE);
      }

      const $or: any[] = [];

      for (const role of tenantRoles) {
        const requiredRoleDecimal = role.decimal;
        const bitPosition = decimalToBitPosition(requiredRoleDecimal);
        const divisor = Math.pow(2, bitPosition);

        $or.push({
          $eq: [
            {
              $mod: [{ $floor: { $divide: ['$roles', divisor] } }, 2],
            },
            1,
          ],
        });
      }

      query.$expr = { $or: $or };

      query = _.omit(query, 'roles');
    }

    return this.tenantUserModel.paginate(
      {
        ...query,
        isRoot: false,
      },
      {
        ...options,
        populate: [
          {
            path: 'team',
            select: '_id name',
          },
        ],
        select:
          'image isActive username email lastName firstName displayName oddWeeks evenWeeks',
      },
    );
  }

  async findOne(id: string, populate = []) {
    const user = await this.tenantUserModel
      .findById(id)
      .populate(populate.join(' '))
      .populate({
        path: 'team',
        select: '_id name',
      })
      .select('-__v -createdAt -updatedAt -tenant -token -isRoot')
      .lean();

    if (!user) {
      throw new NotFoundException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
    }

    const roles = await this.getTenantUserRoles(user.roles);

    return {
      ...user,
      roles: roles.map((role) => ({
        key: role.key,
        name: role.name,
      })),
    };
  }

  async create(data: CreateTenantUserDto) {
    const user = await this.tenantUserModel.findOne({
      $or: [{ email: data.email }, { username: data.username }],
    });

    if (user) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.USER_EXISTS);
    }

    const team = await this.teamModel.findById(data.team);

    if (!team) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.INVALID_TEAM);
    }

    const roleDecimalSum = await this.handleRoles(data, false);

    const isGeneratePassword = data.isGeneratePassword;

    if (isGeneratePassword) {
      data.password = this.generateRandomPassword(8);
    } else {
      if (!data.password) {
        return;
      }
    }

    const userCreated = await this.tenantUserModel.create({
      ...data,
      roles: roleDecimalSum,
      displayName: `${data.firstName} ${data.lastName}`,
      lastChangePasswordAt: isGeneratePassword
        ? dayjs().subtract(7, 'month').toDate()
        : new Date(),
    });

    // Add user into team
    await this.teamModel.findByIdAndUpdate(data.team, {
      $addToSet: { tenantUsers: userCreated._id },
    });

    this.sendEmailWhenCreateOrResetPasswordUser(
      EmailTemplateEnum.CREATE_USER,
      userCreated,
      data.password,
    ).then(() => {
      this.logger.log('Create user email sent successfully');
    });

    return this.findOne(userCreated._id.toString());
  }

  async update(
    data: UpdateTenantUserDto | UpdateTenantUserMeDto,
    isMe: boolean,
  ) {
    const { id, ...body } = data;

    const user = await this.validateUser(id, isMe);

    let roleDecimalSum = mongoose.Types.Decimal128.fromString('0');
    if ('roles' in data) {
      roleDecimalSum = await this.handleRoles(data, user.isRoot);
    }

    if ('email' in data) {
      await this.validateEmail(data, id);
    }

    if ('team' in data) {
      await this.handleTeam(id, user.team?.toString(), data);
    }

    const omitFields = ['username', 'password'];

    await this.tenantUserModel.findByIdAndUpdate(
      id,
      omit(
        {
          ...body,
          ...(!isMe && { roles: roleDecimalSum }),
          displayName: `${body.firstName} ${body.lastName}`,
        },
        omitFields,
      ),
    );

    return this.findOne(id);
  }

  async updatePassword(data: any, isChange: boolean) {
    const { id, username, oldPassword, password } = data;

    const user = await this.tenantUserModel
      .findOne({
        $or: [{ _id: id }, { username }],
      })
      .select('password lastChangePasswordAt')
      .lean();

    if (!user) {
      throw new NotFoundException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
    }

    if (isChange) {
      const currentDayJs = dayjs.utc();
      const lastChangePasswordAtDayJs = dayjs.utc(user.lastChangePasswordAt);

      if (currentDayJs.diff(lastChangePasswordAtDayJs, 'month') < 6) {
        throw new BadRequestException(
          TENANT_USER_MESSAGE_KEYS.CHANGE_PASSWORD_BEFORE_SIX_MONTHS,
        );
      }
    }

    if (!compareSync(oldPassword, user.password)) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.WRONG_PASSWORD);
    }

    if (compareSync(password, user.password)) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.OLD_PASSWORD);
    }

    const tenantId = user._id.toString();

    await this.tenantUserModel.findByIdAndUpdate(tenantId, {
      $set: {
        password,
        lastChangePasswordAt: new Date(),
        // tmp hide function reset token after change password
        // token: null,
      },
    });

    // tmp hide function reset token after change password
    // await this.tokenModel.deleteMany({ tenantUserId: tenantId });

    return this.findOne(tenantId);
  }

  async resetPassword(data: ResetTenantUserPasswordDto) {
    const { id, password } = data;

    const user = await this.tenantUserModel
      .findById(id)
      .select('username email password')
      .lean();

    if (!user) {
      throw new NotFoundException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
    }

    if (compareSync(password, user.password)) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.OLD_PASSWORD);
    }

    await this.tenantUserModel.findByIdAndUpdate(id, {
      $set: {
        password,
        lastChangePasswordAt: new Date(),
        token: null,
      },
    });

    await this.tokenModel.deleteMany({ tenantUserId: id });

    this.sendEmailWhenCreateOrResetPasswordUser(
      EmailTemplateEnum.RESET_PASSWORD_USER,
      user,
      password,
    ).then(() => {
      this.logger.log('Reset password email sent successfully');
    });

    return this.findOne(id);
  }

  async getTeamManagement(team: string) {
    const foundTeam = await this.teamModel.findById(team);

    if (!foundTeam) {
      throw new NotFoundException(TENANT_USER_MESSAGE_KEYS.INVALID_TEAM);
    }

    const users = await this.tenantUserModel
      .find({ team, isActive: true })
      .select(
        'displayName lastName firstName roles position evenWeeks oddWeeks',
      )
      .sort({
        position: 1,
      })
      .lean();

    return Promise.all(
      users.map(async (user) => {
        // Get roles from user
        const roles = await this.getTenantUserRoles(user.roles);

        return {
          ...user,
          roles: roles.map((role) => ({
            key: role.key,
            name: role.name,
          })),
        };
      }),
    );
  }

  async updateTeamManagement(data: UpdateTeamManagementDto) {
    const { team: teamId, employees } = data;

    const team = await this.teamModel.findById(teamId);

    if (!team) {
      throw new NotFoundException(TENANT_USER_MESSAGE_KEYS.INVALID_TEAM);
    }

    // Check tenant user is exist in team
    await Promise.all(
      employees.map(async (item) => {
        const user = await this.tenantUserModel.findById(item._id).lean();

        if (!user) {
          throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
        }

        if (user.team.toString() !== teamId) {
          throw new BadRequestException(
            TENANT_USER_MESSAGE_KEYS.USER_NOT_EXIST_IN_TEAM,
          );
        }
      }),
    );

    await Promise.all(
      employees.map((item) =>
        this.tenantUserModel.findByIdAndUpdate(item._id, {
          $set: {
            position: item.position,
          },
        }),
      ),
    );

    return this.getTeamManagement(teamId);
  }

  async exportEmployees() {
    const [users, roles] = await Promise.all([
      this.tenantUserModel
        .find({ isDeleted: false, isActive: true })
        .select('username roles')
        .lean(),
      this.tenantRoleModel.find().select('name decimal').lean(),
    ]);

    const roleMap = roles.reduce(
      (map, role) => {
        map[role.decimal.toString()] = role.name;
        return map;
      },
      {} as Record<string, string>,
    );

    const extractRoles = (
      rolesDecimal: mongoose.Types.Decimal128,
    ): string[] => {
      const roleDecimals = splitDecimalSum(parseDecimalToBigInt(rolesDecimal));
      return roleDecimals
        .map((decimal) => roleMap[decimal.toString()])
        .filter(Boolean);
    };

    const userWithRole = users.map((user) => ({
      username: user.username,
      roles: extractRoles(user.roles).join(', '),
    }));

    const fileName = `employees-${dayjs().utc().format(DATE_FORMAT_HYPHEN)}.csv`;

    const content = await new Promise<string>((resolve, reject) => {
      stringify(
        userWithRole,
        { header: true, columns: ['username', 'roles'], delimiter: ';' },
        (err, output) => {
          if (err) reject(err);
          else resolve(output);
        },
      );
    });

    return { fileName, content };
  }

  async getTenantUserRoles(rolesDecimal: mongoose.Types.Decimal128) {
    const roleDecimals = splitDecimalSum(parseDecimalToBigInt(rolesDecimal));
    return await this.tenantRoleModel
      .find({
        decimal: {
          $in: roleDecimals.map((role) => parseBigInttoDecimal(role)),
        },
      })
      .select('key name')
      .lean();
  }

  async sendEmailInviteToNewSystem() {
    const tenant = await this.tenantModel.findOne().lean();
    if (!tenant) {
      return;
    }
    const companyInfomation = await this.tenantService.getCompanyInfomation(
      tenant._id,
    );
    const html = `<!doctype html>
<html xmlns='http://www.w3.org/1999/xhtml' xmlns:v='urn:schemas-microsoft-com:vml'
    xmlns:o='urn:schemas-microsoft-com:office:office'>

<head>
    <title></title>
    <meta http-equiv='X-UA-Compatible' content='IE=edge'>
    <meta http-equiv='Content-Type' content='text/html; charset=UTF-8'>
    <meta name='viewport' content='width=device-width,initial-scale=1'>
    <style type='text/css'>
        #outlook a {
            padding: 0
        }

        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%
        }

        table,
        td {
            border-collapse: collapse;
            mso-table-lspace: 0;
            mso-table-rspace: 0
        }

        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: 0;
            text-decoration: none;
            -ms-interpolation-mode: bicubic
        }

        p {
            display: block;
            margin: 13px 0
        }
    </style>
    
    <link href='https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700' rel='stylesheet' type='text/css'>
    <style type='text/css'>
        @import url(https://fonts.googleapis.com/css?family=Ubuntu:300,400,500,700);
    </style>
    <style type='text/css'>
        @media only screen and (min-width:480px) {
            .mj-column-per-100 {
                width: 100% !important;
                max-width: 100%
            }

            .mj-column-px-600 {
                width: 600px !important;
                max-width: 600px
            }
        }
    </style>
    <style media='screen and (min-width:480px)'>
        .moz-text-html .mj-column-per-100 {
            width: 100% !important;
            max-width: 100%
        }

        .moz-text-html .mj-column-px-600 {
            width: 600px !important;
            max-width: 600px
        }
    </style>
    <style type='text/css'></style>
    <style type='text/css'>
        .banner {
            background: url(<%= COMPANY_BANNER %>);
            background-size: cover;
            background-repeat: no-repeat;
            background-position: bottom right;
            padding-left: 18px !important
        }

        .text-banner {
            font-family: Arial, sans-serif !important;
            padding-left: 0 !important;
            padding-bottom: 28px !important;
            padding-top: 62px !important;
            text-align: left !important;
            color: #fff !important;
            font-size: 48px !important
        }

        .red-text {
            font-weight: 700 !important;
            color: #d2232a !important
        }

        .logo-eeac-wrapper img {
            vertical-align: middle !important
        }

        .logo-right img {
            vertical-align: middle !important
        }

        .logo-eeac {
            height: 98% !important;
            width: auto !important
        }

        .logo-linkedin {
            height: 28px !important
        }

        .logo-sfn {
            height: 40px !important
        }

        .first {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.2rem;
            text-align: left;
            color: #343a40;
            word-break: break-word;
            padding-top: 1px;
            padding-bottom: 1px
        }

        .last {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.2rem;
            text-align: left;
            color: #343a40;
            font-weight: 700;
            word-break: break-word;
            padding-top: 1px;
            padding-bottom: 1px;
            padding-left: 0px
        }

        @media only screen and (max-width:478px) {
            .container {
                padding: 0 0 !important
            }

            .banner {
                padding-left: 12px !important
            }

            .text-banner {
                font-size: 35px !important;
                padding-left: 0 !important;
                padding-bottom: 10px !important;
                padding-top: 30px !important
            }

            .section-header {
                width: 100% !important
            }

            .logo-linkedin {
                height: 18px !important
            }

            .logo-sfn {
                height: 30px !important
            }

            .logo-right {
                padding: 0 8px !important;
                height: 100% !important;
                width: max-content !important;
                line-height: 0
            }

            .logo-right .logo-linkedin-wrapper {
                display: inline-block;
                height: 100%;
                margin-right: 8px !important;
                vertical-align: middle !important
            }

            .logo-eeac-wrapper {
                display: inline-block;
                height: 100%;
                vertical-align: middle !important
            }

            .section-footer {
                max-width: 100% !important;
                padding-left: 2px !important
            }
        }
    </style>
</head>

<body style='word-spacing:normal'>
    <div style='padding-bottom: 100px'>
        <div class='container' style='margin:0 auto;max-width:600px;padding-top: 50px'>
            <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation' style='width:100%'>
                <tbody>
                  
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>DUTCH:</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 20px'>
                            <div class='first'>Beste <%=USERNAME %></div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 15px'>
                            <div class='first'>We zijn verheugd om aan te kondigen dat de migratie naar ons nieuwe platform succesvol is voltooid! Vanaf nu kun je gebruik maken van het nieuwe platform en de bijbehorende HomEE app.</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>Hieronder vind je belangrijke informatie om toegang te krijgen:</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>Je kunt inloggen op het nieuwe platform via de volgende link: <a href="https://homee.nu/">homee.nu</a> </div>
                          </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                <div
                                    style='font-family:Arial,sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343a40'>
                                   Daarnaast kun je de HomEE-app downloaden via:
                                   <ul style='margin:0; padding-top: 5px'>
                                        <li><span><strong>Apple Store: </strong><a href="https://apps.apple.com/app/id6503337195">https://apps.apple.com/app/id6503337195</a></span></li>
                                        <li><span><strong>Google Play Store: </strong><a href="https://play.google.com/store/apps/details?id=com.infodation.eeac">https://play.google.com/store/apps/details?id=com.infodation.eeac</a></span> </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>Je kunt inloggen met je huidige gebruikersnaam en het volgende tijdelijke wachtwoord:</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 5px'>
                            <div class='first'><strong>Tijdelijk wachtwoord:</strong> <%= USER_PASSWORD %> </div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>Bij het eerste inloggen wordt je gevraagd om direct een nieuw wachtwoord te kiezen. Dit is verplicht; zonder het wijzigen van je wachtwoord krijg je geen toegang tot het systeem.</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>Na het instellen van je nieuwe wachtwoord kun je direct aan de slag!</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>Bedankt voor je geduld en medewerking tijdens deze overgang. We kijken ernaar uit om samen verder te werken in het vernieuwde systeem!</div>
                          </td>
                        </tr>
                        
                        <tr>
                          <td style='padding-top: 20px'>
                            <div class='first'>ENGLISH:</div>
                          </td>
                        </tr>
                        
                        <tr>
                          <td style='padding-top: 20px'>
                            <div class='first'>Dear <%=USERNAME %></div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 15px'>
                            <div class='first'>We are excited to announce that the migration to our new platform has been successfully completed! From now on, you can use the new platform and the HomEE app.</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>Below is important information to gain access:</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>You can log in to the new platform via the following link: <a href="https://homee.nu/">homee.nu</a> </div>
                          </td>
                        </tr>
                        <tr>
                            <td style='padding-top:10px'>
                                <div
                                    style='font-family:Arial,sans-serif;font-size:14px;line-height:1.2rem;text-align:left;color:#343a40'>
                                   Additionally, you can download the HomEE app via:
                                   <ul style='margin:0; padding-top: 5px'>
                                        <li><span><strong>Apple Store: </strong><a href="https://apps.apple.com/app/id6503337195">https://apps.apple.com/app/id6503337195</a></span></li>
                                        <li><span><strong>Google Play Store: </strong><a href="https://play.google.com/store/apps/details?id=com.infodation.eeac">https://play.google.com/store/apps/details?id=com.infodation.eeac</a></span> </li>
                                    </ul>
                                </div>
                            </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>You can log in using your current username and the following temporary password:</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 5px'>
                            <div class='first'><strong>Temporary password:</strong> <%= USER_PASSWORD %> </div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>When logging in for the first time, you will be required to immediately choose a new password. This step is mandatory; without changing your password, you will not have access to the system.</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>After setting your new password, you can get started right away!</div>
                          </td>
                        </tr>
                        <tr>
                          <td style='padding-top: 10px'>
                            <div class='first'>Thank you for your patience and cooperation during this transition. We look forward to continuing our work together on the updated system!</div>
                          </td>
                        </tr>
                    <tr>
                        <td style='padding-top:40px'>
                            <div class='first'><strong class='first red-text text-sign'>
                                    <%=COMPANY_SIGNATURE %>
                                </strong><br><span class='red-text text-sign' style='margin-right:8px'>T</span><%= COMPANY_TELEPHONE %><br><span class='red-text text-sign'
                                        style='margin-right:8px'>E</span><a href='mailto:<%=COMPANY_EMAIL %>'
                                        target='_blank'><%=COMPANY_EMAIL %>
                                    </a><br><span class='red-text text-sign' style='margin-right:12px'>I</span>
                                    <a href='https://<%= COMPANY_WEBSITE %>' target='_blank'><%= COMPANY_WEBSITE %>
                                    </a><br><span class='text-sign'><%= COMPANY_ADDRESS1 %></span>
                                    <br><span class='text-sign'><%= COMPANY_ADDRESS2 %></span>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

    </div>
    <!-- Footer -->
    
    <div class='section-footer'
        style='background: <%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>; background-color: <%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>; margin: 0px auto; max-width: 100%;'>
        <table align='center' border='0' cellpadding='0' cellspacing='0' role='presentation'
            style='background:<%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>;background-color:<%= COMPANY_TYPOGRAPHY_COLORS_PRIMARY %>;width:100%;'>
            <tbody>
                <tr>
                    <td style='direction:ltr;font-size:0px;padding:0;text-align:center;'>
                        <!--[if mso | IE]><table role='presentation' border='0' cellpadding='0' cellspacing='0'><tr><td class='' style='vertical-align:middle;width:600px;' ><![endif]-->
                        <div class='mj-column-px-600 mj-outlook-group-fix'
                            style='font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:middle;width:100%;'>
                            <table border='0' cellpadding='0' cellspacing='0' role='presentation'
                                style='vertical-align:middle;' width='100%'>
                                <tbody>
                                    <tr>
                                        <td align='left' style='font-size:0px;padding:0;word-break:break-word;'>
                                            <table cellpadding='0' cellspacing='0' width='100%' border='0'
                                                style='color:#000000;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:13px;line-height:22px;table-layout:auto;width:100%;border:none;'>
                                                <tr>
                                                    <td>
                                                        <a class='logo-eeac-wrapper' target='_blank'
                                                            href='https://<%= COMPANY_WEBSITE %>'
                                                            style='display: inline-block; height: 100%; vertical-align: middle;'>
                                                            <img class='logo-eeac' src='<%= COMPANY_LOGO %>' alt='logo'
                                                                width='100%'>
                                                        </a>
                                                    </td>
                                                    <td align='right'
                                                        style='width: 1%; white-space: nowrap; height: 100%; background: #fff;'>
                                                        <div class='logo-right'
                                                            style='display: inline-block; vertical-align: middle; background-color: #ffffff; padding: 0 20px; line-height: 0px; height: 100%; width: max-content;'>
                                                            <table cellpadding='0' cellspacing='0' width='100%'
                                                                border='0'>
                                                                <tr>
                                                                    <td>

                                                                        <a class='logo-linkedin-wrapper' target='_blank'
                                                                            href='<%= COMPANY_LINKEDIN_URL %>'
                                                                            style='width: max-content; display: inline-block; height: 100%; margin-right: 20px; vertical-align: middle;'>
                                                                            <img class='logo-linkedin'
                                                                                src='<%= COMPANY_LINKEDIN_LOGO %>' alt='logo'>
                                                                        </a>

                                                                    </td>
                                                                    <td>
                                                                        <img class='logo-sfn' src='<%= COMPANY_SFN_LOGO %>'
                                                                            alt='logo'>
                                                                    </td>
                                                                </tr>
                                                            </table>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </table>
                                        </td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--[if mso | IE]></td></tr></table><![endif]-->
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <!--[if mso | IE]></td></tr></table><![endif]-->
    <div style='height:20px;line-height:20px;'>&#8202;</div>
</body>

</html>`;
    const users = await this.tenantUserModel
      .find({
        isActive: true,
        isDeleted: false,
      })
      .lean();
    const userDatas: any[] = [];
    const updatePasswordPromises = users.map(async (user) => {
      const password = this.generateRandomPassword();
      userDatas.push({
        username: user.username,
        password: password,
        email: user.email,
        tenantId: user.tenant,
      });
      await this.tenantUserModel.findByIdAndUpdate(user._id, {
        password,
        lastChangePasswordAt: dayjs().utc().add(-1, 'year').toDate(),
      });
    });

    await Promise.all(updatePasswordPromises);

    for (const user of userDatas) {
      if (!user.email) {
        return;
      }

      const compiledObject = template(html);
      const emailData = {
        USERNAME: user.username,
        USER_PASSWORD: user.password,
        COMPANY_EMAIL: companyInfomation['email'],
        COMPANY_SIGNATURE: companyInfomation['sign'],
        COMPANY_BANNER: companyInfomation['banner'],
        COMPANY_TYPOGRAPHY_COLORS_PRIMARY:
          companyInfomation['typography']['colors']['primary'],
        COMPANY_TELEPHONE: companyInfomation['telephone'],
        COMPANY_ADDRESS1: companyInfomation['address1'],
        COMPANY_ADDRESS2: companyInfomation['address2'],
        COMPANY_WEBSITE: companyInfomation['website'],
        COMPANY_LOGO: companyInfomation['logo'],
        COMPANY_LINKEDIN_URL: companyInfomation['linkedinUrl'],
        COMPANY_LINKEDIN_LOGO: companyInfomation['linkedinLogo'],
        COMPANY_SFN_LOGO: companyInfomation['sfnLogo'],
      };

      const htmlTemplate = compiledObject(emailData);
      const listEmail: string[] = [user.email];

      try {
        await this.emailService.sendEmail({
          html: htmlTemplate,
          text: htmlTemplate,
          to: listEmail,
          subject:
            'Migratie succesvol voltooid - Belangrijke informatie over toegang tot het nieuwe HomEE platform',
          bcc: [],
          cc: [],
        });
        console.log(
          'Send email reset password change to new system successfully for user: ',
          user.username,
        );
      } catch (error) {
        console.error(error);
      }
      setTimeout(() => {}, 5000);
    }
    return {};
  }

  private async sendEmailWhenCreateOrResetPasswordUser(
    action: EmailTemplateEnum,
    user: any,
    password: string,
  ) {
    const tenant = await this.tenantModel.findOne().lean();
    if (!tenant) {
      return;
    }

    const companyInfomation = await this.tenantService.getCompanyInfomation(
      tenant._id,
    );

    const emailTemplate =
      await this.emailTemplateService.getEmailTemplateByName(action.toString());
    if (!emailTemplate) {
      this.logger.log('Email template create user not found');
      return;
    }

    const html = emailTemplate.html;

    const compiledObject = template(html);

    const escapePassword = escapeHtml(password);

    const emailData =
      action === EmailTemplateEnum.CREATE_USER
        ? {
            FIRSTNAME: user.firstName,
            LASTNAME: user.lastName,
            USERNAME: user.username,
            PASSWORD: escapePassword,
            COMPANY_TELEPHONE: companyInfomation['telephone'],
          }
        : {
            USERNAME: user.username,
            PASSWORD: escapePassword,
            COMPANY_TELEPHONE: companyInfomation['telephone'],
          };
    const htmlTemplate = compiledObject(emailData);
    const listEmail: string[] = [user.email];

    try {
      await this.emailService.sendEmail({
        html: htmlTemplate,
        text: htmlTemplate,
        to: listEmail,
        subject: emailTemplate.subject,
        bcc: [],
        cc: [],
      });
      this.logger.log('Send email to user successfully: ', user.username);
    } catch (error) {
      this.logger.error(error);
    }
  }

  private generateRandomPassword(length: number = 8) {
    const upperCase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    const lowerCase = 'abcdefghijklmnopqrstuvwxyz';
    const numbers = '0123456789';
    const specialChars = "!@#$%^&*()_+[]{}|;:',.<>?/";
    const allChars = upperCase + lowerCase + numbers + specialChars;

    let password = '';
    password += upperCase[randomInt(upperCase.length)];
    password += lowerCase[randomInt(lowerCase.length)];
    password += numbers[randomInt(numbers.length)];
    password += specialChars[randomInt(specialChars.length)];

    for (let i = 4; i < length; i++) {
      password += allChars[randomInt(allChars.length)];
    }

    password = password
      .split('')
      .sort(() => randomInt(2) - 1)
      .join('');

    return password;
  }

  async validateUser(id: string, isMe: boolean) {
    const user = await this.tenantUserModel.findById(id);

    if (!user) {
      throw new NotFoundException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
    }

    if (!isMe && user.isRoot) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
    }

    return user;
  }

  async validateRole(roleDecimals: any[], isRootUser?: boolean) {
    await Promise.all(
      roleDecimals.map(async (decimal) => {
        const role = await this.tenantRoleModel.findOne({ decimal });

        if (!role) {
          throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.INVALID_ROLE);
        }

        if (role.key === 'administrator' && isRootUser) {
          throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.INVALID_ROLE);
        }
      }),
    );
  }

  async validateEmail(
    data: UpdateTenantUserDto | UpdateTenantUserMeDto,
    id: string,
  ) {
    const user = await this.tenantUserModel.findOne({
      email: data.email,
      _id: { $ne: id },
    });

    if (user) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.EMAIL_EXISTS);
    }
  }

  async handleRoles(
    data: CreateTenantUserDto | UpdateTenantUserDto | UpdateTenantUserMeDto,
    isRootUser?: boolean,
  ) {
    if (!('roles' in data)) {
      return mongoose.Types.Decimal128.fromString('0');
    }

    const roles = await this.tenantRoleModel.find({
      key: { $in: data.roles },
    });

    if (!roles.length) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.ROLES_REQUIRED);
    }

    if (roles.length !== data.roles.length) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.INVALID_ROLE);
    }

    const roleDecimals = roles.map((role) => role.decimal);
    const roleDecimalSum = sumDecimal(roleDecimals);

    await this.validateRole(roleDecimals, isRootUser);

    return roleDecimalSum;
  }

  async handleTeam(
    id: string,
    oldTeam: string,
    data: UpdateTenantUserDto | UpdateTenantUserMeDto,
  ) {
    if (!('team' in data)) {
      return;
    }

    const team = await this.teamModel.findById(data.team);

    if (!team) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.INVALID_TEAM);
    }

    if (oldTeam !== data.team) {
      // Remove user from old team
      await this.teamModel.findByIdAndUpdate(oldTeam, {
        $pull: { tenantUsers: id },
      });

      // Add user to new team
      await this.teamModel.findByIdAndUpdate(data.team, {
        $addToSet: { tenantUsers: id },
      });
    }
  }

  async verifyRBAC(tenantUserId: string, permissions: number[]) {
    const foundTenantUser = await this.tenantUserModel
      .findById(tenantUserId)
      .lean();

    if (!foundTenantUser) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
    }

    const permissionsBigInt = permissions.map(
      (b: number) => BigInt(1) << BigInt(b),
    );

    const rolesDecimalSum = foundTenantUser.roles;
    const rolseDecimal = splitDecimalSum(parseDecimalToBigInt(rolesDecimalSum));

    const roles = await this.tenantRoleModel
      .find({
        decimal: {
          $in: rolseDecimal.map((role) => parseBigInttoDecimal(role)),
        },
      })
      .lean();

    const permissionsDecimalRoles = roles.map((role) =>
      splitDecimalSum(parseDecimalToBigInt(role.permissions)),
    );

    const uniquePermissionsDecimal = new Set(permissionsDecimalRoles.flat());

    const tenantUserPermissions = await this.tenantPermissionModel
      .find({
        decimal: {
          $in: Array.from(uniquePermissionsDecimal).map((permission) =>
            parseBigInttoDecimal(permission),
          ),
        },
      })
      .lean();

    const tenantUserPermissionsKey = tenantUserPermissions.map((permission) =>
      parseDecimalToBigInt(permission.decimal),
    );

    const canAccess = tenantUserPermissionsKey.some((permission) =>
      permissionsBigInt.includes(permission),
    );

    return canAccess;
  }

  async verifyRoles(tenantUserId: string, roles: TenantRoleEnum[]) {
    const foundTenantUser = await this.tenantUserModel
      .findById(tenantUserId)
      .lean();

    if (!foundTenantUser) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
    }

    const userRoles = await this.getTenantUserRoles(foundTenantUser.roles);
    const roleKeys = userRoles.map((role) => role.key);
    if (roleKeys.includes('administrator')) {
      return true;
    }
    return roleKeys.some((val) => roles.includes(val as TenantRoleEnum));
  }

  async existsOrThrow(userIds: string[]) {
    const users = await this.tenantUserModel.find(
      { _id: { $in: userIds.map((id) => new Types.ObjectId(id)) } },
      { team: 1 },
    );

    if (userIds.length !== users.length) {
      throw new BadRequestException(TENANT_USER_MESSAGE_KEYS.NOT_FOUND);
    }
  }

  async getDisplayConfig(tenantUserId: string, page: string) {
    const displayConfig = (await this.findOne(tenantUserId))
      .columnDisplayConfigs[page];

    if (!displayConfig) {
      throw new NotFoundException(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND_DISPLAY_CONFIG,
      );
    }

    return displayConfig;
  }

  async updateDisplayConfig(
    tenantUserId: string,
    page: string,
    updatedDisplayConfig: any,
  ) {
    const displayConfig: any[] = await this.getDisplayConfig(
      tenantUserId,
      page,
    );

    const finalDisplayConfig = displayConfig.map((dc) => {
      if (updatedDisplayConfig.field !== dc.field) {
        return dc;
      }

      if (!dc.mutable) {
        throw new BadRequestException(
          TENANT_USER_MESSAGE_KEYS.DISPLAY_CONFIG_NOT_MUTABLE,
        );
      }

      return {
        ...dc,
        isShown: updatedDisplayConfig.isShown,
      };
    });

    await this.tenantUserModel.findByIdAndUpdate(tenantUserId, {
      $set: { [`columnDisplayConfigs.${page}`]: finalDisplayConfig },
    });

    return finalDisplayConfig;
  }
}
