import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { LOCATION_MESSAGES } from '~/shared/messages/location.message';

import {
  CreateLocationDto,
  GetLocationNearByQueryDto,
  GetUnitsOfLocationDto,
  LocationIdDto,
  LocationQueryParamDto,
  UpdateLocationDto,
  UpdateUnitsOfLocationDto,
} from './dtos/location.dto';
import { LocationService } from './location.service';

@Controller('location')
export class LocationController {
  constructor(private readonly locationService: LocationService) {}

  @HTTPDecorators.Paginator
  @MessagePattern({ cmd: LOCATION_MESSAGES.GET_LOCATIONS })
  @UsePipes(new ZodValidationPipe(LocationQueryParamDto))
  public async findAll(@Payload() data: LocationQueryParamDto) {
    return this.locationService.findAll(data);
  }

  @UsePipes(new ZodValidationPipe(LocationIdDto))
  @MessagePattern({ cmd: LOCATION_MESSAGES.GET_LOCATION_DETAIL })
  public async findOne(@Payload() data: { id: string }) {
    return this.locationService.findOne(data.id);
  }

  @UsePipes(new ZodValidationPipe(CreateLocationDto))
  @MessagePattern({ cmd: LOCATION_MESSAGES.CREATE_LOCATION })
  public async create(@Payload() data: CreateLocationDto) {
    return this.locationService.create(data);
  }

  @UsePipes(new ZodValidationPipe(UpdateLocationDto))
  @MessagePattern({ cmd: LOCATION_MESSAGES.UPDATE_LOCATION })
  public async update(@Payload() data: UpdateLocationDto) {
    return this.locationService.update(data);
  }

  @UsePipes(new ZodValidationPipe(LocationIdDto))
  @MessagePattern({ cmd: LOCATION_MESSAGES.EXPORT_LOCATION })
  public async export(@Payload() data: { id: string }) {
    return this.locationService.export(data.id);
  }

  @UsePipes(new ZodValidationPipe(GetUnitsOfLocationDto))
  @MessagePattern({ cmd: LOCATION_MESSAGES.GET_UNITS_OF_LOCATION })
  public async getUnitsOfLocation(payload: GetUnitsOfLocationDto) {
    return this.locationService.getUnitsOfLocation(payload);
  }

  @UsePipes(new ZodValidationPipe(UpdateUnitsOfLocationDto))
  @MessagePattern({ cmd: LOCATION_MESSAGES.UPDATE_UNITS_OF_LOCATION })
  public async updateUnitsOfLocation(payload: UpdateUnitsOfLocationDto) {
    return this.locationService.updateUnitsOfLocation(payload);
  }

  @UsePipes(new ZodValidationPipe(GetLocationNearByQueryDto))
  @MessagePattern({ cmd: LOCATION_MESSAGES.GET_LOCATION_NEARBY })
  public async getLocationNearBy(payload: GetLocationNearByQueryDto) {
    return this.locationService.getLocationNearBy(payload);
  }

  @MessagePattern({ cmd: LOCATION_MESSAGES.GET_TOTAL_STATS_OCCUPANTS })
  public async getTotalStatsOccupantOfAllActiveLocations() {
    return this.locationService.getTotalStatsOccupantOfAllActiveLocations();
  }
}
