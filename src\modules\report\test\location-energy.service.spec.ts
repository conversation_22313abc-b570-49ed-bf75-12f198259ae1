import { Test } from '@nestjs/testing';

import { LocationModel } from '~/modules/location/location.model';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockLocation } from '~/test/mocks/location.mock';

import { LocationEnergyService } from '../location-energy.service';

describe('LocationEnergyService', () => {
  let service: LocationEnergyService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [LocationEnergyService, ...testInjectModel([LocationModel])],
    }).compile();

    service = module.get(LocationEnergyService);

    await initMockLocation();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getEnergyReport', () => {
    it('should return energy report data', async () => {
      const result = await service.getEnergyReport({});
      expect(result).toBeDefined();
      expect(Array.isArray(result)).toBe(true);
    });
  });

  describe('exportEnergyReport', () => {
    it('should export energy report data', async () => {
      const result = await service.exportEnergyReport({});
      expect(result).toBeDefined();
      expect(result.fileName).toMatch(
        /location-energy-report-\d{2}-\d{2}-\d{4}\.csv/,
      );
      expect(Array.isArray(result.data)).toBe(true);
      expect(result.header).toEqual([
        { field: 'fullAddress', title: 'Locatie' },
        { field: 'energyLabel', title: 'Energielabel' },
      ]);
    });
  });
});
