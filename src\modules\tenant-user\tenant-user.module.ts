import { Modu<PERSON> } from '@nestjs/common';

import { EmailModule } from '~/processors/email/email.module';

import { EmailTemplateModule } from '../email-template/email-template.module';
import { TenantModule } from '../tenant/tenant.module';
import { TenantUserController } from './tenant-user.controller';
import { TenantUserService } from './tenant-user.service';

@Module({
  imports: [TenantModule, EmailModule, EmailTemplateModule],
  controllers: [TenantUserController],
  providers: [TenantUserService],
  exports: [TenantUserService],
})
export class TenantUserModule {}
