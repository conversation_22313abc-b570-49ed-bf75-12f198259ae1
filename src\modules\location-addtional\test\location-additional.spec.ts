import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { GetLocationGroupNamesDto } from '~/modules/location/dtos/location.dto';
import { LocationService } from '~/modules/location/location.service';
import {
  CertificateAndControlInspectionType,
  LocationAdditionalGroupType,
  LocationAdditionalType,
} from '~/shared/enums/location-additional.enum';
import { LOCATION_MESSAGE_KEYS } from '~/shared/message-keys/location.message-key';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockContact } from '~/test/mocks/contact.mock';
import { initMockContract } from '~/test/mocks/contract.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import {
  initMockLocationAdditional,
  mockLocationAdditionalData,
} from '~/test/mocks/locationadditional.mock';
import {
  initMockLocationAdditionalGroupName,
  mockLocationAdditionalGroupNameData,
} from '~/test/mocks/locationadditionalgroupname.mock';

import { LocationAdditionalGroupNameModel } from '../location-additional-group-name.model';
import { LocationAdditionalService } from '../location-additonal.service';
import { LocationAdditionalModel } from '../location-addtional.model';
import { locationAdditionalTest } from './location-additional.dto.test';

describe('LocationAdditionalService', () => {
  let service: LocationAdditionalService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        LocationAdditionalService,
        ...testInjectProviders([
          LocationAdditionalModel,
          LocationAdditionalGroupNameModel,
          LocationService,
        ]),
      ],
    }).compile();

    service = module.get(LocationAdditionalService);

    // Init data
    await Promise.all([
      initMockLocation(),
      initMockLocationAdditional(),
      initMockLocationAdditionalGroupName(),
      initMockContact(),
      initMockContract(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should call fn and return all location additional', async () => {
      const result = await service.findAll();
      expect(result).toBeDefined();
      expect(result).toMatchSchema(locationAdditionalTest.findAllSchema);
    });

    it('should call fn and return empty array', async () => {
      jest.spyOn(service, 'findAll').mockResolvedValue([]);

      const result = await service.findAll();
      expect(result).toEqual([]);
    });
  });

  describe('create', () => {
    it('should call fn and create a new location additional', async () => {
      const payload = {
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        inspectionType: CertificateAndControlInspectionType.CERTIFICATION,
        groupName: mockLocationAdditionalGroupNameData._id,
        groupType: LocationAdditionalGroupType.EEAC,
        position: 1,
      };

      const result = await service.create(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(locationAdditionalTest.modelSchema);
    });
  });

  describe('update', () => {
    const payload = {
      _id: mockLocationAdditionalData._id,
      position: 2,
    };

    it('should call fn and update a location additional', async () => {
      const result = await service.update(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('modifiedCount', 1);
    });

    it('should call fn and return empty object for non-existing id', async () => {
      const result = await service.update({
        _id: new ObjectId().toString(),
        position: 2,
      });
      expect(result).toBeDefined();
      expect(result).toHaveProperty('modifiedCount', 0);
    });
  });

  describe('delete', () => {
    it('should call fn and delete a location additional', async () => {
      // Prepare data
      const id = new ObjectId();
      await initMockLocationAdditional({ _id: id });

      const result = await service.delete(id.toString());
      expect(result).toBeDefined();
      expect(result.deletedCount).toBe(1);
    });

    it('should call fn and return 0 deleted count for non-existing id', async () => {
      const result = await service.delete(new ObjectId().toString());
      expect(result).toBeDefined();
      expect(result.deletedCount).toBe(0);
    });
  });

  describe('getAdditionalGroupNames', () => {
    it('should call fn with payload and return list data', async () => {
      const payload: GetLocationGroupNamesDto = {
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
      };

      const result = await service.getAdditionalGroupNames(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        locationAdditionalTest.getAdditionalGroupNamesSchema,
      );
    });

    it('should call fn and return list empty if data not found', async () => {
      const payload: GetLocationGroupNamesDto = {
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
      };

      const result = await service.getAdditionalGroupNames(payload);
      expect(result).toBeDefined();
      expect(result).toEqual([]);
    });
  });

  describe('getAdditionalByLocation', () => {
    it('should throw error if location not found', async () => {
      const payload = {
        id: new ObjectId().toString(),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
      };

      await expect(service.getAdditionalByLocation(payload)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should call fn and return list data with type CERTIFICATE_AND_CONTROL', async () => {
      // Prepare data
      jest
        .spyOn(service['locationService'], 'findOne')
        .mockResolvedValue(mockLocationData);

      const payload = {
        id: mockLocationData._id.toString(),
        type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
      };

      const result = await service.getAdditionalByLocation(payload);
      expect(result).toBeDefined();
      expect(result[0]).toHaveProperty('_id', mockLocationAdditionalData._id);
      expect(result).toMatchSchema(
        locationAdditionalTest.getAdditionalByLocationTypeCertificateSchema,
      );
    });

    it('should call fn and return list data with type GWE_AND_METER_READING', async () => {
      // Prepare data
      await initMockLocationAdditional({
        type: LocationAdditionalType.GWE_AND_METER_READING,
      });

      jest
        .spyOn(service['locationService'], 'findOne')
        .mockResolvedValue(mockLocationData);

      const payload = {
        id: mockLocationData._id.toString(),
        type: LocationAdditionalType.GWE_AND_METER_READING,
      };

      const result = await service.getAdditionalByLocation(payload);
      expect(result).toBeDefined();
      expect(result[0]).toHaveProperty('_id', mockLocationAdditionalData._id);
      expect(result).toMatchSchema(
        locationAdditionalTest.getAdditionalByLocationTypeGweSchema,
      );
    });

    it('should call fn and return list data with type FEATURE_AND_SUPPLIER', async () => {
      // Prepare data
      await initMockLocationAdditional({
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
      });

      jest
        .spyOn(service['locationService'], 'findOne')
        .mockResolvedValue(mockLocationData);

      const payload = {
        id: mockLocationData._id.toString(),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
      };

      const result = await service.getAdditionalByLocation(payload);
      expect(result).toBeDefined();
      expect(result[0]).toHaveProperty('_id', mockLocationAdditionalData._id);
      expect(result).toMatchSchema(
        locationAdditionalTest.getAdditionalByLocationTypeFeatureSchema,
      );
    });

    it('should call fn and return empty array if no additional found', async () => {
      // Prepare data
      const locationId = new ObjectId();
      await initMockLocation({ _id: locationId });

      jest
        .spyOn(service['locationService'], 'findOne')
        .mockResolvedValue({ ...mockLocationData, _id: locationId });

      const payload = {
        id: locationId.toString(),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
      };

      const result = await service.getAdditionalByLocation(payload);
      expect(result).toBeDefined();
      expect(result).toEqual([]);
    });
  });

  describe('createOrUpdateAdditionalByLocation', () => {
    it('should throw error if location not found', async () => {
      // Mock method
      jest.spyOn(service['locationService'], 'findOne').mockResolvedValue(null);

      const payload = {
        id: new ObjectId().toString(),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        items: [],
      };
      await expect(
        service.createOrUpdateAdditionalByLocation(payload),
      ).rejects.toThrow(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should create new additional items for location', async () => {
      // Prepare data
      const locationId = new ObjectId();
      await initMockLocation({ _id: locationId });
      jest
        .spyOn(service['locationService'], 'findOne')
        .mockResolvedValue({ ...mockLocationData, _id: locationId });

      const payload = {
        id: locationId.toString(),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        items: [
          {
            type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
            groupName: mockLocationAdditionalGroupNameData._id,
            groupType: LocationAdditionalGroupType.EEAC,
            position: 1,
          },
        ],
      };

      const result = await service.createOrUpdateAdditionalByLocation(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        locationAdditionalTest.getAdditionalByLocationTypeFeatureSchema,
      );
    });

    it('should update existing additional items for location', async () => {
      // Prepare data
      const locationId = new ObjectId();
      const locationAdditionalId = new ObjectId();

      await Promise.all([
        initMockLocation({ _id: locationId }),
        initMockLocationAdditional({
          _id: locationAdditionalId,
          type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
          groupName: mockLocationAdditionalGroupNameData._id,
          groupType: LocationAdditionalGroupType.EEAC,
          position: 1,
          location: locationId,
        }),
      ]);

      jest
        .spyOn(service['locationService'], 'findOne')
        .mockResolvedValue({ ...mockLocationData, _id: locationId });

      const payload = {
        id: locationId.toString(),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        items: [
          {
            _id: locationAdditionalId,
            type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
            groupName: mockLocationAdditionalGroupNameData._id,
            groupType: LocationAdditionalGroupType.EEAC,
            position: 2,
          },
        ],
      };

      const result = await service.createOrUpdateAdditionalByLocation(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        locationAdditionalTest.getAdditionalByLocationTypeFeatureSchema,
      );
    });

    it('should handle empty items array gracefully', async () => {
      const locationId = new ObjectId();
      await initMockLocation({ _id: locationId });
      jest
        .spyOn(service['locationService'], 'findOne')
        .mockResolvedValue({ ...mockLocationData, _id: locationId });

      const payload = {
        id: locationId.toString(),
        type: LocationAdditionalType.FEATURE_AND_SUPPLIER,
        items: [],
      };

      const result = await service.createOrUpdateAdditionalByLocation(payload);
      expect(result).toBeDefined();
      expect(result).toEqual([]);
    });
  });
});
