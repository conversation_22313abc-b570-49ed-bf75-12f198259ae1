import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { LocationAdditionalModel } from '~/modules/location-addtional/location-addtional.model';

import { locationAdditionalTest } from './../../modules/location-addtional/test/location-additional.dto.test';
import {
  LocationAdditionalGroupType,
  LocationAdditionalType,
} from './../../shared/enums/location-additional.enum';
import { mockContactData } from './contact.mock';
import { mockContractData } from './contract.mock';
import { mockLocationData } from './location.mock';
import { mockLocationAdditionalGroupNameData } from './locationadditionalgroupname.mock';

const locationAdditionalModel = getModelForClass(LocationAdditionalModel);

export const mockLocationAdditionalData = {
  _id: new ObjectId(),
  brandType: 'Vesta',
  contact: mockContactData._id,
  dateCheck: new Date(),
  description: 'this is description',
  groupName: mockLocationAdditionalGroupNameData._id,
  groupType: LocationAdditionalGroupType.EEAC,
  isDeleted: false,
  location: mockLocationData._id,
  name: 'brandblusser',
  position: 2,
  type: LocationAdditionalType.CERTIFICATE_AND_CONTROL,
  yearInstallation: 2000,
  contract: mockContractData._id,
};

export async function initMockLocationAdditional(
  doc?: Partial<z.infer<typeof locationAdditionalTest.modelSchema>>,
) {
  const { _id, ...rest } = { ...mockLocationAdditionalData, ...doc };
  await locationAdditionalModel.replaceOne({ _id }, rest, { upsert: true });
}
