import { DocumentType, modelOptions, plugin, prop } from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

export type BvCompanyDocument = DocumentType<BvCompanyModel>;

@modelOptions({
  options: {
    customName: 'BvCompany',
  },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class BvCompanyModel extends BaseModel {
  @prop({ required: true, trim: true })
  identifier!: string;
  @prop({ required: true, trim: true })
  name!: string;
}
