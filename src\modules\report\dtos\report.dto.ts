import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { ContractType, OwnerType } from '~/shared/enums/contract.enum';
import { JobTypeEnum } from '~/shared/enums/job.enum';
import { EmployeeReportSortField } from '~/shared/enums/report.enum';

const ownerTypeValues = Object.values(OwnerType).map((o) =>
  o.toLowerCase(),
) as [string, ...string[]];

const BaseRevenueHiredLocationQuerySchema = z.strictObject({
  year: z.string(),
  month: z.string().optional(),
  costType: z
    .union([
      z.array(z.string().refine(isValidObjectId)),
      z.string().refine(isValidObjectId),
    ])
    .optional(),
  bvCompany: z
    .string()
    .refine((v) => isValidObjectId(v))
    .optional(),
  contact: z
    .string()
    .refine((v) => isValidObjectId(v))
    .optional(),
  sortBy: z.string().min(1).optional().default('updatedAt'),
  sortDir: z.enum(['asc', 'desc']).optional().default('desc'),
});

const DebtorRentingRevenueHiredLocationQuerySchema = z
  .strictObject({
    contractType: z.literal(ContractType.RENTING),
    location: z
      .union([
        z.array(z.string().refine(isValidObjectId)),
        z.string().refine(isValidObjectId),
      ])
      .optional(),
    costCenter: z.undefined().optional(),
  })
  .merge(BaseRevenueHiredLocationQuerySchema);

const DebtorServiceRevenueHiredLocationQuerySchema = z
  .strictObject({
    contractType: z.literal(ContractType.SERVICE),
    costCenter: z
      .union([
        z.array(z.string().refine(isValidObjectId)),
        z.string().refine(isValidObjectId),
      ])
      .optional(),
    location: z.undefined().optional(),
  })
  .merge(BaseRevenueHiredLocationQuerySchema);

export const RevenueHiredLocationQueryZodDto = createZodDto(
  z.discriminatedUnion('contractType', [
    DebtorRentingRevenueHiredLocationQuerySchema,
    DebtorServiceRevenueHiredLocationQuerySchema,
  ]),
);

export class DebtorRentingRevenueHiredLocationQueryDto extends createZodDto(
  DebtorRentingRevenueHiredLocationQuerySchema,
) {}

export class DebtorServiceRevenueHiredLocationQueryDto extends createZodDto(
  DebtorServiceRevenueHiredLocationQuerySchema,
) {}

export type RevenueHiredLocationQueryDto =
  | DebtorRentingRevenueHiredLocationQueryDto
  | DebtorServiceRevenueHiredLocationQueryDto;

const ReportEmployeeQueryParamSchema = z.object({
  sortBy: z.nativeEnum(EmployeeReportSortField).optional(),
  sortDir: z.enum(['asc', 'desc']).default('desc').optional(),
  team: z.string().refine(isValidObjectId).optional(),
  employees: z
    .union([
      z.array(z.string().refine(isValidObjectId)),
      z.string().refine(isValidObjectId),
    ])
    .optional(),
  jobType: z.nativeEnum(JobTypeEnum).optional(),
  location: z.string().refine(isValidObjectId).optional(),
  month: z.coerce.number().int().min(1).max(12),
  year: z.coerce.number().int().min(1000).max(9999),
  user: z.string().refine(isValidObjectId).optional(),
});

export class ReportEmployeeQueryParamsDto extends createZodDto(
  ReportEmployeeQueryParamSchema,
) {}

const DebtorAndCreditorReportQuerySchema = z.strictObject({
  year: z.string(),
  month: z.string().optional(),
  week: z.string().optional(),
  team: z.string().refine(isValidObjectId).optional(),
  location: z.string().refine(isValidObjectId).optional(),
  customer: z.string().refine(isValidObjectId).optional(),
  creditor: z.string().refine(isValidObjectId).optional(),
  bvCompany: z.string().refine(isValidObjectId).optional(),
  owner: z
    .enum(ownerTypeValues)
    .optional()
    .transform((v) => {
      if (!v) {
        return v;
      } else if (v === 'homee') {
        return OwnerType.HOMEE;
      } else {
        return OwnerType.LENTO;
      }
    }),
});

export class DebtorAndCreditorReportQueryDto extends createZodDto(
  DebtorAndCreditorReportQuerySchema,
) {}
