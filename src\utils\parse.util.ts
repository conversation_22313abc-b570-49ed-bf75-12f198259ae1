import mongoose from 'mongoose';

export const isValidObjectId = (id: string): boolean => {
  return mongoose.Types.ObjectId.isValid(id);
};

export const validateUniqueIds = (val: string[]) => {
  const uniqueIds = new Set(val);
  return uniqueIds.size === val.length;
};

export const parseObjectId = (id: string): mongoose.Types.ObjectId => {
  if (!isValidObjectId(id)) {
    throw new Error('Invalid ObjectId');
  }

  return new mongoose.Types.ObjectId(id);
};

export const parseDecimal128ToNumber = (
  value: mongoose.Types.Decimal128,
): number => {
  // Checking if the value is null or undefined
  if (!value) {
    return 0;
  }

  return parseFloat(value.toString());
};

// Parser 10000.00 => 10.000,00
export const formatNumberNL = (value: number) => {
  const fractionDigits = value.toString().split('.')[1]?.length || 0;

  return value.toLocaleString('nl-NL', {
    minimumFractionDigits: fractionDigits,
    maximumFractionDigits: fractionDigits,
  });
};

export const kebabCaseToCamelCase = (str: string): string => {
  return str.replace(/-./g, (g) => g[1].toUpperCase());
};
