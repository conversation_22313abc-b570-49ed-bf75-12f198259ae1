import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { TaskModel } from '~/modules/task/task.model';
import { taskTest } from '~/modules/task/test/task.dto.test';
import { TaskCategory, TaskType } from '~/shared/enums/task.enum';

import { mockEquipmentData } from './equipment.mock';
import { mockTenantUserData } from './tenantuser.mock';

const taskModel = getModelForClass(TaskModel);
type taskType = Partial<z.infer<typeof taskTest.modelSchema>>;

export const mockTaskData: taskType = {
  _id: new ObjectId(),
  cars: [mockEquipmentData._id],
  category: TaskCategory.PUBLIC_HOLIDAY,
  createdAt: new Date(),
  description: '',
  destination: '',
  devices: [],
  employees: [mockTenantUserData._id],
  isDeleted: false,
  startDate: new Date(),
  title: '2e Pinksterdag',
  type: TaskType.PLANNING,
  updatedAt: new Date(),
};

export async function initMockTask(doc?: taskType) {
  const { _id, ...rest } = { ...mockTaskData, ...doc };
  await taskModel.replaceOne({ _id }, rest, { upsert: true });
}
