import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

export const UnitSchema = z.strictObject({
  isActive: z.boolean(),
  name: z.string().min(1).max(256),
  maxArea: z.number().min(0),
  maxOccupants: z.number().min(0),
  position: z.number().min(0),
  parent: z
    .string()
    .refine((value) => isValidObjectId(value))
    .optional(),
});

export const UpdateUnitSchema = UnitSchema.extend({
  _id: z
    .string()
    .refine((value) => isValidObjectId(value))
    .optional(),
}).partial();

export const UpdateLocationSubUnitSchema = UnitSchema.extend({
  _id: z
    .string()
    .refine((value) => isValidObjectId(value))
    .optional(),
  children: z
    .array(
      UnitSchema.extend({
        _id: z
          .string()
          .refine((value) => isValidObjectId(value))
          .optional(),
      }),
    )
    .optional(),
});

export const UpdateLocationUnitSchema = UnitSchema.extend({
  _id: z
    .string()
    .refine((value) => isValidObjectId(value))
    .optional(),
  children: z.array(UpdateLocationSubUnitSchema).optional(),
});

export class UpdateLocationUnitDto extends createZodDto(
  UpdateLocationUnitSchema,
) {}

export class UpdateUnitDto extends createZodDto(UpdateUnitSchema) {}
