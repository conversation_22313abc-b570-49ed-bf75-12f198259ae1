import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { JobEmployeeModel } from '~/modules/job-employee/job-employee.model';

import { mockJobData } from './job.mock';
import { mockTenantUserData } from './tenantuser.mock';

const jobEmployeeModel = getModelForClass(JobEmployeeModel);
type jobEmployeeType = InstanceType<typeof JobEmployeeModel>;

export const mockJobEmployeeData = {
  _id: new ObjectId(),
  actualHours: 0,
  createdAt: new Date(),
  employee: mockTenantUserData._id,
  estimatedHours: 60,
  isDeleted: false,
  job: mockJobData._id,
  plannedDate: mockJobData.plannedDate,
  updatedAt: new Date(),
};

export async function initMockJobEmployee(
  doc?: Partial<jobEmployeeType & { _id: ObjectId }>,
) {
  const data = { ...mockJobEmployeeData, ...doc };
  await jobEmployeeModel.replaceOne({ _id: data._id }, data, { upsert: true });
}
