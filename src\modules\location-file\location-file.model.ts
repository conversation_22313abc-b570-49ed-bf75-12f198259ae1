import {
  DocumentType,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import {
  UploadFileDocument,
  UploadFileModel,
} from '../document-file/upload-file.model';
import { LocationDocument, LocationModel } from '../location/location.model';

export type LocationFileDocument = DocumentType<LocationFileModel>;

@modelOptions({
  options: { customName: 'locationFiles' },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class LocationFileModel extends BaseModel {
  @prop({ required: true })
  description!: string;

  @prop({ ref: () => LocationModel })
  location?: Ref<LocationDocument>;

  @prop({ ref: () => UploadFileModel })
  uploadFile!: Ref<UploadFileDocument>;
}
