import { Injectable } from '@nestjs/common';

import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { TenantRoleModel } from './tenant-role.model';

@Injectable()
export class TenantRoleService {
  constructor(
    @InjectModel(TenantRoleModel)
    private readonly tenantRoleModel: MongooseModel<TenantRoleModel>,
  ) {}

  async findAll(payload: any) {
    const { query, options } = buildQuery(payload, ['name', 'key']);
    return this.tenantRoleModel.paginate(
      {
        ...query,
        key: { $ne: 'administrator' },
        isActive: true,
      },
      {
        limit: options.limit,
        offset: options.offset,
        sort: options.sort,
        select: 'key name',
      },
    );
  }
}
