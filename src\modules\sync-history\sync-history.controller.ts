import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { SyncHistoryService } from '~/modules/sync-history/sync-history.service';
import { SYNC_HISTORY_MESSAGES } from '~/shared/messages/sync-history.message';

@Controller('sync-history')
export class SyncHistoryController {
  constructor(private readonly syncHistoryService: SyncHistoryService) {}

  @MessagePattern({ cmd: SYNC_HISTORY_MESSAGES.FIND_LATEST_SYNCED_DATE })
  async findLatestSyncHistory(@Payload() payload: any) {
    const { type } = payload;
    const a =
      await this.syncHistoryService.findLatestSuccessfulSyncedDate(type);
    return a;
  }
}
