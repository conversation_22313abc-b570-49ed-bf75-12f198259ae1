import dayjs from 'dayjs';
import { z, ZodRawShape } from 'nestjs-zod/z';

import {
  CreateRentingAgreementLineSchema,
  CreateServiceAgreementLineSchema,
  CreateSupplierAgreementLineSchema,
} from '~/modules/agreementline/dtos/create-agreementline.dto';
import {
  UpdateRentingAgreementLineSchema,
  UpdateServiceAgreementLineSchema,
  UpdateSupplierAgreementLineSchema,
} from '~/modules/agreementline/dtos/update-agreementline.dto';
import { NoticeDays } from '~/shared/enums/contract.enum';
import { isValidObjectId } from '~/utils';

import { PdfAttachmentSchema } from './create-contract.dto';

const BaseUpdateContractSchema = z.strictObject({
  id: z.string().refine((v) => isValidObjectId(v)),
  isActive: z.boolean().optional(),
  startDate: z.dateString().optional(),
  endDate: z.dateString().nullish(),
  noticeDays: z.nativeEnum(NoticeDays).optional(),
  isSigned: z.boolean().optional(),
  signedAt: z.dateString().optional(),
  note: z.string().optional(),
  attachments: z.array(PdfAttachmentSchema).optional(),
  user: z.string().optional(),
});

function addRefineFromBaseUpdateContractSchema(schema: ZodRawShape) {
  return BaseUpdateContractSchema.extend(schema).refine((v) => {
    if (v.startDate && v.endDate) {
      return dayjs(v.endDate).utc().isSameOrAfter(dayjs(v.startDate).utc());
    }
    return true;
  }, 'EndDate must be equal or greater than StartDate');
}

const oneOfUpdateRentingAgreementLineSchema = z.union([
  UpdateRentingAgreementLineSchema,
  CreateRentingAgreementLineSchema,
]);

const oneOfUpdateServiceAgreementLineSchema = z.union([
  UpdateServiceAgreementLineSchema,
  CreateServiceAgreementLineSchema,
]);

const oneOfUpdateSupplierAgreementLineSchema = z.union([
  UpdateSupplierAgreementLineSchema,
  CreateSupplierAgreementLineSchema,
]);

export const UpdateDebtorRentingContractSchema =
  addRefineFromBaseUpdateContractSchema({
    generatePeriod: z.number().int().min(1).max(12).optional(),
    agreementLines: z
      .array(oneOfUpdateRentingAgreementLineSchema)
      .min(1)
      .optional(),
  });

export const UpdateCreditorRentingContractSchema =
  addRefineFromBaseUpdateContractSchema({
    agreementLines: z
      .array(oneOfUpdateRentingAgreementLineSchema)
      .min(1)
      .optional(),
  });

export const UpdateDebtorServiceContractSchema =
  addRefineFromBaseUpdateContractSchema({
    agreementLines: z
      .array(oneOfUpdateServiceAgreementLineSchema)
      .min(1)
      .optional(),
  });

export const UpdateSupplierContractSchema =
  addRefineFromBaseUpdateContractSchema({
    agreementLines: z
      .array(oneOfUpdateSupplierAgreementLineSchema)
      .min(1)
      .optional(),
  });
