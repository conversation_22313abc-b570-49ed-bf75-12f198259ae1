import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

interface OldAggregatedTask {
  _id: Types.ObjectId;
  title: string;
  description: string;
  destination: string;
  startDate: Date;
  endDate: Date;
  category: string;
  cars: Types.ObjectId[];
  devices: Types.ObjectId[];
  employees: Types.ObjectId[];
  createdAt: Date;
  updatedAt: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection.aggregate([
    {
      $match: {
        _id: { $gt: nextId },
      },
    },
    { $sort: { _id: 1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'planning_plan',
        localField: 'category',
        foreignField: '_id',
        as: 'category',
      },
    },
    {
      $lookup: {
        from: 'planning_equipment',
        as: 'cars',
        let: { taskId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $in: ['$$taskId', '$tasks'] },
                  { $eq: ['$type', 'CAR'] },
                ],
              },
            },
          },
        ],
      },
    },
    {
      $lookup: {
        from: 'planning_equipment',
        as: 'devices',
        let: { taskId: '$_id' },
        pipeline: [
          {
            $match: {
              $expr: {
                $and: [
                  { $in: ['$$taskId', '$tasks'] },
                  { $eq: ['$type', 'DEVICE'] },
                ],
              },
            },
          },
        ],
      },
    },
    {
      $addFields: {
        cars: '$cars._id',
        devices: '$devices._id',
        category: {
          $arrayElemAt: ['$category', 0],
        },
      },
    },
    {
      $addFields: {
        category: {
          $switch: {
            branches: [
              {
                case: { $eq: ['$category.type', 'publicholiday'] },
                then: 'public-holiday',
              },
              {
                case: { $eq: ['$category.type', 'sickleave'] },
                then: 'sick-leave',
              },
            ],
            default: { $ifNull: ['$category.type', 'others'] },
          },
        },
      },
    },
  ]);
};

const transformData = ({ data }: { data: OldAggregatedTask[] }) => {
  return Promise.all(
    data.map(async (task: OldAggregatedTask) =>
      omitNull({
        _id: task._id,
        title: task.title,
        description: task.description,
        destination: task.destination,
        startDate: task.startDate,
        endDate: task.endDate,
        category: task.category,
        type: 'planning',
        cars: task.cars,
        devices: task.devices,
        employees: task.employees,
        isDeleted: false,
        createdAt: task.createdAt,
        updatedAt: task.updatedAt,
      }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'planning_task',
      destinationCollectionName: 'tasks',
      pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
