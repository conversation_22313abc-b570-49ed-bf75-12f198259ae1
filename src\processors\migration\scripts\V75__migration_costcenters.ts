import { Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

interface OldCostCenter {
  _id: Types.ObjectId;
  active: boolean;
  costIdentifier: string;
  name: string;
  isSynced: boolean;
  createdDate?: Date;
  updatedDate?: Date;
}

const findLocationsWithCostCenter = async (
  context: MigrationContext,
  costCenterId: Types.ObjectId,
) => {
  const collection = context.sourceClient!.db().collection('location');
  const locations = await collection
    .aggregate()
    .match({ costcenter: costCenterId })
    .group({ _id: '$costcenter', locations: { $push: '$_id' } })
    .toArray();

  return locations[0]?.locations ?? [];
};

const transformData = ({
  data,
  context,
}: {
  data: OldCostCenter[];
  context: MigrationContext;
}) => {
  return Promise.all(
    data.map(async (costCenter: OldCostCenter) =>
      omitNull({
        _id: costCenter._id,
        identifier: costCenter.costIdentifier,
        name: costCenter.name,
        isActive: costCenter.active,
        isDeleted: !costCenter.active,
        isSynced: costCenter.isSynced,
        locations:
          (await findLocationsWithCostCenter(context, costCenter._id)) ?? [],
        createdAt: costCenter.createdDate,
        updatedAt: costCenter.updatedDate,
      }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'costcenter',
      destinationCollectionName: 'costcenters',
      tranformDataFunc: transformData,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
