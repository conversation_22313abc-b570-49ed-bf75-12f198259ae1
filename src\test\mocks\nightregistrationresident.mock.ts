import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { NightRegistrationResidentModel } from '~/modules/night-registration/models/night-registration-resident.model';
import { NightRegistrationGender } from '~/shared/enums/night-registration.enum';

import { mockContactData } from './contact.mock';
import { mockNightRegistrationNationalityData } from './nightregistrationnationality.mock';

const nightRegistrationResidentModel = getModelForClass(
  NightRegistrationResidentModel,
);

export const mockNightRegistrationResidentData = {
  _id: new ObjectId(),
  clientId: '9999999999',
  dateOfBirth: new Date(),
  displayName: 'Hubei Choro',
  email: '<EMAIL>',
  firstName: 'Hubert',
  gender: NightRegistrationGender.MALE,
  isDeleted: false,
  lastName: 'Choro',
  phoneNumber: '09999999999',
  nationality: mockNightRegistrationNationalityData._id,
  contact: mockContactData._id,
  isBRP: true,
};

export async function initMockNightRegistrationResident(doc?: any) {
  const { _id, ...rest } = { ...mockNightRegistrationResidentData, ...doc };
  await nightRegistrationResidentModel.replaceOne({ _id }, rest, {
    upsert: true,
  });
}
