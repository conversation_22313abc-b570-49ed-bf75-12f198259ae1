import { Injectable, NotFoundException } from '@nestjs/common';
import dayjs from 'dayjs';
// import * as fs from 'fs';
import { differenceBy } from 'lodash';
import { Types } from 'mongoose';

import { EquipmentService } from '~/modules/equipment/equipment.service';
import {
  CheckOverLapTaskDto,
  CreateTaskDto,
  UpdateTaskDto,
} from '~/modules/task/dtos/create-task.dto';
import { aggregatePeriodsForTaskPlanningOverview } from '~/modules/task/task.helper';
import { TaskModel } from '~/modules/task/task.model';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { TASK_MESSAGE_KEYS } from '~/shared/message-keys/task.message-key';
import { InjectModel } from '~/transformers/model.transformer';
import { isOddWeekOfYear } from '~/utils/date.util';

import { EquipmentModel } from '../equipment/equipment.model';
import { TenantUserModel } from '../tenant-user/tenant-user.model';
import { TenantUserService } from '../tenant-user/tenant-user.service';

@Injectable()
export class TaskService {
  constructor(
    @InjectModel(TaskModel)
    private readonly taskModel: MongooseModel<TaskModel>,
    @InjectModel(TenantUserModel)
    private readonly tenantUserModel: MongooseModel<TenantUserModel>,
    @InjectModel(EquipmentModel)
    private readonly equipmentModel: MongooseModel<EquipmentModel>,

    private readonly tenantUserService: TenantUserService,
    private readonly equipmentService: EquipmentService,
  ) {}

  async create(task: CreateTaskDto) {
    if (task.cars && task.cars.length) {
      await this.equipmentService.existsActiveOrThrow(
        task.cars,
        EquipmentEnum.CAR,
      );
    }

    if (task.devices && task.devices.length) {
      await this.equipmentService.existsActiveOrThrow(
        task.devices,
        EquipmentEnum.DEVICE,
      );
    }

    await this.tenantUserService.existsOrThrow(task.employees);

    return this.taskModel.create(task);
  }

  async findOne(id: string) {
    return (
      await this.taskModel.aggregate([
        {
          $match: {
            _id: new Types.ObjectId(id),
            isDeleted: false,
          },
        },
        {
          $lookup: {
            from: 'equipment',
            localField: 'cars',
            foreignField: '_id',
            as: 'cars',
          },
        },
        {
          $lookup: {
            from: 'equipment',
            localField: 'devices',
            foreignField: '_id',
            as: 'devices',
          },
        },
        {
          $lookup: {
            from: 'tenantusers',
            localField: 'employees',
            foreignField: '_id',
            pipeline: [
              {
                $lookup: {
                  from: 'teams',
                  localField: 'team',
                  foreignField: '_id',
                  as: 'team',
                },
              },
              { $unwind: { path: '$team', preserveNullAndEmptyArrays: true } },
            ],
            as: 'employees',
          },
        },
        {
          $project: {
            _id: 1,
            title: 1,
            description: 1,
            destination: 1,
            type: 1,
            category: 1,
            startDate: 1,
            endDate: 1,
            cars: {
              _id: 1,
              description: 1,
              type: 1,
            },
            devices: {
              _id: 1,
              description: 1,
              type: 1,
            },
            employees: {
              _id: 1,
              displayName: 1,
              email: 1,
              team: {
                _id: 1,
                name: 1,
                isActive: true,
                position: 1,
              },
            },
          },
        },
      ])
    )[0];
  }

  async update(task: UpdateTaskDto) {
    const taskFound = await this.taskModel.findById(task.id);

    if (!taskFound) {
      throw new NotFoundException(TASK_MESSAGE_KEYS.NOT_FOUND);
    }

    if (task.cars && task.cars.length) {
      await this.equipmentService.existsActiveOrThrow(
        task.cars,
        EquipmentEnum.CAR,
      );
    }

    if (task.devices && task.devices.length) {
      await this.equipmentService.existsActiveOrThrow(
        task.devices,
        EquipmentEnum.DEVICE,
      );
    }

    await this.tenantUserService.existsOrThrow(task.employees);

    return this.taskModel.findByIdAndUpdate(task.id, task, { new: true });
  }

  async delete(id: string) {
    const task = await this.taskModel.findById(id);

    if (!task) {
      throw new NotFoundException(TASK_MESSAGE_KEYS.NOT_FOUND);
    }

    return this.taskModel.findByIdAndDelete(id);
  }

  async checkOverlapTask(payload: CheckOverLapTaskDto) {
    const {
      employees,
      equipments = [],
      startDate,
      endDate,
      timezone = '+00:00',
      equipmentType,
    } = payload;

    const employeesDbs = await this.tenantUserModel
      .find({ _id: { $in: employees } })
      .select('oddWeeks evenWeeks displayName')
      .lean();

    const equipmentsDbs = await this.equipmentModel
      .find({
        _id: { $in: equipments },
        type: equipmentType ? equipmentType : { $exists: true },
      })
      .select('type description isActive')
      .lean();

    const dateRange: any = [];
    let currentDate = dayjs(startDate).utcOffset(timezone);
    while (
      currentDate.isBefore(dayjs(endDate).utcOffset(timezone), 'day') ||
      currentDate.isSame(dayjs(endDate).utcOffset(timezone), 'day')
    ) {
      dateRange.push(currentDate.toISOString());
      currentDate = currentDate.add(1, 'day');
    }

    const overlapEmployees = employeesDbs.filter((employee) => {
      const oddWeeks = employee.oddWeeks.map((day) => day.toLowerCase());
      const evenWeeks = employee.evenWeeks.map((day) => day.toLowerCase());

      return dateRange.some((date: any) => {
        const isOddWeek = isOddWeekOfYear(date, timezone);
        const workingDays = isOddWeek ? oddWeeks : evenWeeks;
        const dayName = dayjs(date)
          .utcOffset(timezone)
          .format('ddd')
          .toLowerCase();
        return !workingDays.includes(dayName);
      });
    });

    const availableEquipments =
      await this.equipmentService.getAvailableEquipments(payload);

    const overlapEquipments = availableEquipments.length
      ? differenceBy(equipmentsDbs, availableEquipments, (e) =>
          e._id.toString(),
        )
      : availableEquipments;

    return {
      startDate,
      endDate,
      employees: overlapEmployees,
      equipments: overlapEquipments,
    };
  }

  async getEmployeesSchedules(
    period: {
      startDate: Date;
      endDate: Date;
      timezone: string;
    },
    employees: Types.ObjectId[],
  ) {
    const { startDate, endDate, timezone } = period;
    const matchEmployeesWithinPeriod = {
      $match: {
        employees: { $in: employees },
        startDate: { $lte: endDate },
        endDate: { $gte: startDate },
      },
    };
    const removeEmployeesNotInProvidedList = {
      $addFields: {
        employees: {
          $filter: {
            input: '$employees',
            as: 'employee',
            cond: { $in: ['$$employee', employees] },
          },
        },
      },
    };
    const lookupEmployeeDetails = {
      $lookup: {
        from: 'tenantusers',
        localField: 'employees',
        foreignField: '_id',
        as: 'employee',
        pipeline: [{ $match: { isActive: true, isDeleted: false } }],
      },
    };
    const unwindEmployees = {
      $unwind: { path: '$employee', preserveNullAndEmptyArrays: false },
    };
    const generateDailyPeriodsPipeLines =
      aggregatePeriodsForTaskPlanningOverview(startDate, endDate, timezone);
    const groupByDateAndEmployee = {
      $group: {
        _id: { date: '$date', employee: '$employee._id' },
        date: { $first: '$date' },
        employee: { $first: '$employee' },
        items: { $push: '$$ROOT' },
      },
    };
    const sortByDateAsc = { $sort: { date: 1 } };
    const groupByEmployee = {
      $group: {
        _id: { employee: '$employee._id' },
        employee: { $first: '$employee' },
        schedules: { $push: '$$ROOT' },
      },
    };
    const projectNeededFieldsOnly = {
      $project: {
        _id: 0,
        employee: {
          _id: 1,
          displayName: 1,
        },
        schedules: {
          date: 1,
          items: {
            _id: 1,
            title: 1,
            category: 1,
            startAt: 1,
            endAt: 1,
          },
        },
      },
    };
    const sortByEmployeeNameAsc = { $sort: { 'employee.displayName': 1 } };

    return this.taskModel.aggregate(
      [
        matchEmployeesWithinPeriod,
        removeEmployeesNotInProvidedList,
        lookupEmployeeDetails,
        unwindEmployees,
        ...generateDailyPeriodsPipeLines,
        groupByDateAndEmployee,
        sortByDateAsc,
        groupByEmployee,
        projectNeededFieldsOnly,
        sortByEmployeeNameAsc,
      ] as any[],
      { allowDiskUse: true },
    );
  }

  async getEquipmentSchedules(
    period: {
      startDate: Date;
      endDate: Date;
      timezone: string;
    },
    equipmentType: EquipmentEnum,
  ) {
    const { startDate, endDate, timezone } = period;
    const equipmentField =
      equipmentType === EquipmentEnum.CAR ? 'cars' : 'devices';
    const matchEquipmentWithinPeriod = {
      $match: {
        startDate: { $lte: endDate },
        endDate: { $gte: startDate },
        [`${equipmentField}.0`]: { $exists: true },
      },
    };
    const lookupEquipmentDetails = {
      $lookup: {
        from: 'equipment',
        localField: equipmentField,
        foreignField: '_id',
        as: 'equipment',
      },
    };
    const unwindEquipment = {
      $unwind: { path: '$equipment', preserveNullAndEmptyArrays: false },
    };
    const generateDailyPeriodsPipeLines =
      aggregatePeriodsForTaskPlanningOverview(startDate, endDate, timezone);
    const groupByDateAndEquipment = {
      $group: {
        _id: { date: '$date', equipment: '$equipment._id' },
        date: { $first: '$date' },
        equipment: { $first: '$equipment' },
        items: { $push: '$$ROOT' },
      },
    };
    const sortByDateAsc = { $sort: { date: 1 } };
    const groupByEquipment = {
      $group: {
        _id: { equipment: '$equipment._id' },
        equipment: { $first: '$equipment' },
        schedules: { $push: '$$ROOT' },
      },
    };
    const projectNeededFieldsOnly = {
      $project: {
        _id: 0,
        equipment: {
          _id: 1,
          description: 1,
        },
        schedules: {
          date: 1,
          items: {
            _id: 1,
            title: 1,
            category: 1,
            startAt: 1,
            endAt: 1,
          },
        },
      },
    };
    const sortByEquipmentNameAsc = { $sort: { 'equipment.description': 1 } };

    return this.taskModel.aggregate(
      [
        matchEquipmentWithinPeriod,
        lookupEquipmentDetails,
        unwindEquipment,
        ...generateDailyPeriodsPipeLines,
        groupByDateAndEquipment,
        sortByDateAsc,
        groupByEquipment,
        projectNeededFieldsOnly,
        sortByEquipmentNameAsc,
      ] as any[],
      { allowDiskUse: true },
    );
  }
}
