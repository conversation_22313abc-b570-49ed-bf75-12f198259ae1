import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockCountry, mockCountryData } from '~/test/mocks/country.mock';

import { CountryModel } from '../country.model';
import { CountryService } from '../country.service';
import { countryTest } from './country.dto.test';

describe('CountryService', () => {
  let service: CountryService;

  const countryId2 = new ObjectId();

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [CountryService, ...testInjectModel([CountryModel])],
    }).compile();

    service = module.get(CountryService);

    // Init data
    await Promise.all([
      initMockCountry(),
      initMockCountry({ _id: countryId2, code: 'de' }),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getList', () => {
    it('should call fn and return list data', async () => {
      const result = await service.getList({});
      expect(result.docs).toMatchSchema(countryTest.getListSchema);
    });

    it('should return list empty if data not exist', async () => {
      const result = await service.getList({ pageIndex: 99 });
      expect(result.docs).toEqual([]);
    });
  });

  describe('validatePostalCode', () => {
    const moduleName = 'location';

    it('should return empty when address not exist', async () => {
      expect(
        await service.validatePostalCode(null, moduleName),
      ).toBeUndefined();
    });

    it('should throw error when country not found', async () => {
      await expect(
        service.validatePostalCode({ country: new ObjectId() }, moduleName),
      ).rejects.toThrow(`${moduleName}.form.country_not_found`);
    });

    it('should throw error with wrong Dutch postal code format', async () => {
      const wrongNlAddress = {
        country: mockCountryData._id.toString(),
        postalCode: '1234',
      };

      await expect(
        service.validatePostalCode(wrongNlAddress, moduleName),
      ).rejects.toThrow(`${moduleName}.form.wrong_format_postal_code`);
    });

    it('should pass with correct Dutch postal code format', async () => {
      const correctNlAddress = {
        country: mockCountryData._id.toString(),
        postalCode: '1234 AB',
      };

      await expect(
        service.validatePostalCode(correctNlAddress, moduleName),
      ).resolves.toBeUndefined();
    });

    it('should throw error with wrong German postal code format', async () => {
      const wrongDeAddress = { country: countryId2, postalCode: '123' };

      await expect(
        service.validatePostalCode(wrongDeAddress, moduleName),
      ).rejects.toThrow(`${moduleName}.form.wrong_format_postal_code`);
    });

    it('should pass with correct German postal code format', async () => {
      const correctDeAddress = { country: countryId2, postalCode: '12345' };

      await expect(
        service.validatePostalCode(correctDeAddress, moduleName),
      ).resolves.toBeUndefined();
    });
  });
});
