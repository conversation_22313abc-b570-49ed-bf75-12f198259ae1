import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
} from '@typegoose/typegoose';
import AggregatePaginate from 'mongoose-aggregate-paginate-v2';
import Paginate from 'mongoose-paginate-v2';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';

export type ContractTypeDocument = DocumentType<ContractTypeModel>;

@plugin(Paginate)
@plugin(AggregatePaginate)
@modelOptions({
  options: { customName: 'ContractType' },
  schemaOptions: {
    id: false,
    toJSON: { virtuals: true },
    toObject: { virtuals: true },
  },
})
@index({ identifier: 1 }, { unique: true })
@index({ name: 1 }, { unique: true })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class ContractTypeModel {
  @prop({ trim: true, required: true })
  identifier!: string;

  @prop({ trim: true, required: true })
  name!: string;
}
