import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import { nanoid } from 'nanoid';
import * as path from 'path';

import { migrationV2 } from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldGeneralTemplate {
  _id: string;
  name: string;
  points: any[];
  createdAt: Date;
  updatedAt: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection
    .aggregate()
    .match({
      _id: { $gt: nextId },
    })
    .sort({ _id: 1 })
    .limit(limit);
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldGeneralTemplate[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (item) => {
      return {
        _id: item._id,
        type: 'general',
        name: item.name,
        points: item.points.map((point) => {
          return {
            _id: nanoid(),
            description: point.description,
            position: point.position,
          };
        }),
        isDeleted: false,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      };
    }),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'generaltemplate',
      destinationCollectionName: 'jobtemplates',
      pagingFunc,
      tranformDataFunc: tranformDataFunc,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
