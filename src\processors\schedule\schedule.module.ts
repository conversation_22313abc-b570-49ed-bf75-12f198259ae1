import { Module } from '@nestjs/common';

import { ContractModule } from '~/modules/contract/contract.module';
import { CostlineModule } from '~/modules/costline/costline.module';
import { JobModule } from '~/modules/job/job.module';
import { NightRegistrationModule } from '~/modules/night-registration/night-registration.module';
import { StatsOccupantModule } from '~/modules/stats-occupant/stats-occupant.module';

import { EmailModule } from '../email/email.module';
import { ScheduleController } from './schedule.controller';
import { ScheduleService } from './schedule.service';

@Module({
  imports: [
    JobModule,
    ContractModule,
    CostlineModule,
    EmailModule,
    NightRegistrationModule,
    StatsOccupantModule,
  ],
  controllers: [ScheduleController],
  providers: [ScheduleService],
})
export class ScheduleModule {}
