import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { REPORT_MESSAGES } from '~/shared/messages/report.message';

import { ReportLocationAdditionalQueryDto } from './dtos/report-location-additional.dto';
import { ReportLocationAdditionalService } from './report-location-additional.service';

@Controller('reports/location-additional')
export class ReportLocationAdditionalController {
  constructor(
    private readonly reportLocationAdditionalService: ReportLocationAdditionalService,
  ) {}

  @UsePipes(new ZodValidationPipe(ReportLocationAdditionalQueryDto))
  @MessagePattern({ cmd: REPORT_MESSAGES.GET_LOCATION_ADDITIONAL })
  async getLocationAdditionalReport(@Payload() payload: any) {
    return this.reportLocationAdditionalService.getAdditionalLocations(payload);
  }

  @UsePipes(new ZodValidationPipe(ReportLocationAdditionalQueryDto))
  @MessagePattern({ cmd: REPORT_MESSAGES.GET_LOCATION_ADDITIONAL_NEW })
  async getLocationAdditionalReportNew(
    @Payload() payload: ReportLocationAdditionalQueryDto,
  ) {
    return this.reportLocationAdditionalService.getAdditionalLocationsNew(
      payload,
    );
  }

  @UsePipes(new ZodValidationPipe(ReportLocationAdditionalQueryDto))
  @MessagePattern({ cmd: REPORT_MESSAGES.EXPORT_LOCATION_ADDITIONAL })
  async exportLocationAdditionalReport(@Payload() payload: any) {
    return this.reportLocationAdditionalService.exportAdditionalLocations(
      payload,
    );
  }
}
