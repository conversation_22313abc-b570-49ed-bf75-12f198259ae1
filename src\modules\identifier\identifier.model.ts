import { DocumentType, index, modelOptions, prop } from '@typegoose/typegoose';

import { IdentifierType } from '~/shared/enums/identifier.enum';
import { BaseModel } from '~/shared/models/base.model';

export type IdentifierDocument = DocumentType<IdentifierModel>;

@modelOptions({
  options: {
    customName: 'Identifiers',
  },
})
@index({ maxIdentifier: 1, isUsed: 1, type: 1 })
export class IdentifierModel extends BaseModel {
  @prop({ required: true })
  type!: IdentifierType;

  @prop({ required: true })
  maxIdentifier!: string;

  @prop({ default: false })
  isUsed!: boolean;

  @prop({ default: new Date().getFullYear() })
  year!: number;
}
