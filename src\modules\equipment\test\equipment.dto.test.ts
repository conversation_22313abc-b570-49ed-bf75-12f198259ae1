import { z } from 'zod';

import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    description: z.string(),
    type: z.nativeEnum(EquipmentEnum),
    isActive: z.boolean(),
  })
  .extend(baseModelTestSchema);

const getListEquipmentSchema = z.array(modelSchema);

export const equipmentTest = {
  modelSchema,
  getListEquipmentSchema,
};
