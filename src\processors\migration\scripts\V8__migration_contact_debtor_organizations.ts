import * as path from 'path';

import { ContactRole, ContactType } from '~/shared/enums/contact.enum';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldOrganization {
  _id: string;
  active: boolean;
  name: string;
  email: string;
  phoneNumber1: string;
  phoneNumber2: string;
  address1: string;
  address2: string;
  website: string;
  warningEmail: string;
  remark: string;
  parentId: string;
  debtorcontact: Record<string, any>;
}

const organizationPipeLineAggregate = (skip: number, limit: number) => {
  return [
    {
      $match: {
        debtorcontact: { $ne: null },
        _replace: { $exists: false },
      },
    },
    {
      $lookup: {
        from: 'debtorcontact',
        localField: 'debtorcontact',
        foreignField: '_id',
        as: 'debtorcontact',
      },
    },
    {
      $unwind: {
        path: '$debtorcontact',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        debtorcontact: { $ne: null },
      },
    },
    {
      $sort: {
        _id: 1,
      },
    },
    { $skip: skip },
    {
      $limit: limit,
    },
  ];
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldOrganization[];
  context: any;
}) => {
  return Promise.all(
    data.map(async (item) => {
      const tranformed: any = {
        _id: item._id,
        isActive: item.active,
        displayName: item.name,
        name: item.name,
        contactType: ContactType.ORGANIZATION,
        contactRole: ContactRole.DEBTOR,
        email: item.email || '',
        warningEmail: item.warningEmail || '',
        phone1: item.phoneNumber1 || '',
        phone2: item.phoneNumber2 || '',
        address1: item.address1,
        ...(item.address2 && { address2: item.address2 }),
        remark: item.remark || '',
        contactRefId: item.debtorcontact._id,
        identifier: item.debtorcontact?.accountView,
        parentOrganization: item.parentId,
        kvk: item.debtorcontact?.KVK || '',
        snf: item.debtorcontact?.SNF || '',
        vatCode: item.debtorcontact?.VATCode || '',
        invoiceEmail: item.debtorcontact?.invoiceEmail || '',
        invoiceReference: item.debtorcontact?.reference || '',
        collectiveJobInvoice: item.debtorcontact?.collectiveJobInvoice || false,
        collectiveCustomInvoice:
          item.debtorcontact?.collectiveCustomInvoice || false,
        paymentTermJobInvoice: item.debtorcontact?.paymentTermJob || 14,
        paymentTermRentInvoice: item.debtorcontact?.paymentTerm || 30,
        isInternal:
          item.debtorcontact?.accountView === 'CON-11' ||
          item.debtorcontact?.isEEAC === true
            ? true
            : false,
        isDeleted: false,
      };

      if (item.address1) {
        const address = await context
          .sourceClient!.db()
          .collection('address')
          .findOne({ _id: item.address1 });

        if (address) {
          await context
            .destinationClient!.db()
            .collection('addresses')
            .findOneAndUpdate(
              { _id: address._id },
              {
                $set: {
                  _id: address._id,
                  isActive: address.active,
                  street: address.street,
                  city: address.city,
                  postalCode: address.postalCode,
                  number: address.number,
                  country: address.country,
                  region: address.region,
                  contact: tranformed._id,
                },
              },
              {
                upsert: true,
              },
            );
        }
      }

      if (item.address2) {
        const address = await context
          .sourceClient!.db()
          .collection('address')
          .findOne({ _id: item.address2 });

        if (address?.country) {
          await context
            .destinationClient!.db()
            .collection('addresses')
            .findOneAndUpdate(
              { _id: address._id },
              {
                $set: {
                  _id: address._id,
                  isActive: address.active,
                  street: address.street,
                  city: address.city,
                  postalCode: address.postalCode,
                  number: address.number,
                  country: address.country,
                  region: address.region,
                  contact: tranformed._id,
                },
              },
              {
                upsert: true,
              },
            );
        } else {
          tranformed.address2 = null;
        }
      }

      return tranformed;
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('grouping');

  const pipeline = organizationPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'grouping',
      destinationCollectionName: 'contacts',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
