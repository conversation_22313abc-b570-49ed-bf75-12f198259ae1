import * as path from 'path';

import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const destinationCollectionName = 'tenantusers';
    const destinationCollection = context
      .destinationClient!.db()
      .collection(destinationCollectionName)!;

    const cursor = destinationCollection.find({ saveToPhotos: null });
    while (await cursor.hasNext()) {
      const tenantUser = await cursor.next();

      await destinationCollection
        .findOneAndUpdate(
          {
            _id: tenantUser!._id,
          },
          { $set: { saveToPhotos: true } },
        )
        .then(() =>
          console.log(
            `Migrated saveToPhotos=true for tenant user=${tenantUser!._id} & name=${tenantUser!.displayName} into collection ${destinationCollectionName}`,
          ),
        )
        .catch((error) => {
          console.error(
            `Error updating tenant user ${tenantUser!._id} & name=${tenantUser!.displayName}:`,
            error,
          );
        });
    }

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
