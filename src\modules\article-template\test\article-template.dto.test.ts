import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    name: z.string(),
    storage: z.union([z.string(), z.instanceof(ObjectId)]),
    articleList: z.array(
      z.object({
        _id: z.union([z.string(), z.instanceof(ObjectId)]),
        article: z.union([z.string(), z.instanceof(ObjectId)]),
        amount: z.number(),
        position: z.number(),
      }),
    ),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(
  z.object({
    _id: z.instanceof(ObjectId),
    name: z.string(),
    storage: z
      .object({
        _id: z.instanceof(ObjectId),
        description: z.string(),
      })
      .nullish()
      .optional(),
    articleList: z
      .array(
        z.object({
          _id: z.union([z.instanceof(ObjectId), z.string()]),
          amount: z.number(),
          position: z.number(),
          article: z.object({
            _id: z.instanceof(ObjectId),
            category: z
              .object({
                _id: z.instanceof(ObjectId),
                identifier: z.union([z.string(), z.number()]),
                name: z.string(),
              })
              .nullish()
              .optional(),
            batchSize: z.number(),
            description: z.string(),
            expectedDelivery: z.number(),
            identifier: z.union([z.string(), z.number()]),
            isActive: z.boolean(),
            laborPricePerHour: z.number(),
            margin: z.number(),
            minimumStock: z.number(),
            purchasePrice: z.number(),
            removalFee: z.number(),
            salePrice: z.number(),
            workingTime: z.number(),
          }),
        }),
      )
      .nullish()
      .optional(),
  }),
);

export const articleTemplateTest = {
  modelSchema,
  findAllSchema,
};
