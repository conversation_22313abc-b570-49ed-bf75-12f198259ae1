import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { z } from 'zod';

import { ArticleTemplateModel } from '~/modules/article-template/article-template.model';
import { articleTemplateTest } from '~/modules/article-template/test/article-template.dto.test';

import { mockArticleData } from './article.mock';
import { mockStorageData } from './storage.mock';

const articleTemplateModel = getModelForClass(ArticleTemplateModel);
type articleTemplateType = z.infer<typeof articleTemplateTest.modelSchema>;

export const mockArticleTemplateData = {
  _id: new ObjectId(),
  isDeleted: false,
  name: 'ndt 4',
  storage: mockStorageData._id.toString(),
  articleList: [
    {
      _id: nanoid(),
      article: mockArticleData._id.toString(),
      amount: 2,
      position: 1,
    },
  ],
  createdAt: new Date(),
  updatedAt: new Date(),
  __v: 0,
};

export async function initMockArticleTemplate(
  doc?: Partial<articleTemplateType>,
) {
  const { _id, ...rest } = { ...mockArticleTemplateData, ...doc };
  await articleTemplateModel.replaceOne({ _id }, rest, { upsert: true });
}
