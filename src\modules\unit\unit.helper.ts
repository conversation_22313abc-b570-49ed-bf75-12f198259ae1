import { mongoose } from '@typegoose/typegoose';
import _ from 'lodash';

import { UpdateLocationUnitDto } from './dtos/unit.dto';

export function flattenUnits(
  units: UpdateLocationUnitDto[],
  rootUnitId?: string,
  parentId?: string,
  parentIsActive: boolean = true,
): UpdateLocationUnitDto[] {
  return units.reduce(
    (acc: UpdateLocationUnitDto[], unit: UpdateLocationUnitDto) => {
      const _id = unit._id || new mongoose.Types.ObjectId().toString();
      const parent = parentId || rootUnitId;

      const isActive = parentIsActive && (unit.isActive ?? true);

      let flattenedUnit: UpdateLocationUnitDto;

      if (unit.children && unit.children.length) {
        const childrenFlattened = flattenUnits(
          unit.children,
          rootUnitId,
          _id,
          isActive,
        );

        const { maxOccupants, maxArea } = childrenFlattened.reduce(
          (sum, child) => {
            if (!child.isActive) {
              return sum;
            }

            return {
              maxOccupants: sum.maxOccupants + child.maxOccupants,
              maxArea: sum.maxArea + child.maxArea,
            };
          },
          { maxOccupants: 0, maxArea: 0 },
        );

        flattenedUnit = {
          _id,
          ..._.omit(unit, 'children'),
          parent,
          maxOccupants,
          maxArea,
          isActive,
        };

        return [...acc, flattenedUnit, ...childrenFlattened];
      } else {
        flattenedUnit = {
          _id,
          ...unit,
          parent,
          isActive,
        };

        return [...acc, flattenedUnit];
      }
    },
    [],
  );
}
