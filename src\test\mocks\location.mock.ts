import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { LocationModel } from '~/modules/location/location.model';
import { locationTest } from '~/modules/location/test/location.dto.test';

import { mockAddressData } from './address.mock';
import { mockBvCompanyData } from './bvcompany.mock';
import { mockCostCenterData } from './costcenter.mock';
import { mockTeamData } from './team.mock';

const locationModel = getModelForClass(LocationModel);

export const mockLocationData = {
  _id: new ObjectId(),
  address: mockAddressData._id,
  bvCompany: mockBvCompanyData._id,
  costCenter: mockCostCenterData._id,
  email: '',
  fullAddress: 'Zwolle, Gein 63',
  geo: {
    type: 'Point',
    coordinates: {
      lng: 6.1110875,
      lat: 52.5264975,
    },
  },
  isActive: true,
  isRenting: true,
  isService: false,
  locationOf: [],
  maxArea: 197.2,
  maxOccupants: 2,
  maximumStayDuration: null,
  team: mockTeamData._id,
};

export async function initMockLocation(
  doc?: Partial<z.infer<typeof locationTest.modelSchema>>,
) {
  const { _id, ...rest } = { ...mockLocationData, ...doc };
  await locationModel.replaceOne({ _id }, rest, { upsert: true });
}
