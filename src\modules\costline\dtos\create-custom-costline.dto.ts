import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

export interface CreateCustomCostLinesDto {
  contact: string;
  location?: string;
  costCenter?: string;
  country?: string;
  bvCompany?: string;
  costLines: {
    costType: string;
    description: string;
    price: number;
    quantity: number;
  }[];
}

export const CreateCustomCostLinesSchema = z.strictObject({
  contact: z.string().refine(isValidObjectId), // Debtor only - contractRole = debtor
  location: z.string().refine(isValidObjectId).optional(), // Location
  costCenter: z.string().optional(), // CostCenter
  country: z.string().refine(isValidObjectId).optional(), // Country
  bvCompany: z.string().refine(isValidObjectId).optional(), // BVCompany
  costLines: z
    .array(
      z.strictObject({
        costType: z.string().refine(isValidObjectId), // CostType with with=job_or_custom only
        description: z.string().max(256),
        price: z.number().max(1_000_000_000),
        quantity: z.number().min(0.1).max(999_999),
      }),
    )
    .min(1),
});

const DeleteCostLineSchema = z.strictObject({
  id: z.string().refine((v) => isValidObjectId(v)),
});

export const CreateCustomCostLinesZodDto = createZodDto(
  CreateCustomCostLinesSchema,
);

export class DeleteCostlineDto extends createZodDto(DeleteCostLineSchema) {}
