import { Module } from '@nestjs/common';

import { EquipmentModule } from '~/modules/equipment/equipment.module';
import { TenantUserModule } from '~/modules/tenant-user/tenant-user.module';

import { TaskController } from './task.controller';
import { TaskService } from './task.service';

@Module({
  imports: [TenantUserModule, EquipmentModule],
  providers: [TaskService],
  controllers: [TaskController],
  exports: [TaskService],
})
export class TaskModule {}
