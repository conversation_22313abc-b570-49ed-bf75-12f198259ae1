import {
  DocumentType,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

import { CountryDocument, CountryModel } from '../country/country.model';

export type RegionDocument = DocumentType<RegionModel>;

@modelOptions({
  options: { customName: 'Region' },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class RegionModel extends BaseModel {
  @prop({ required: true, trim: true })
  name!: string;

  @prop({ ref: () => CountryModel })
  country!: Ref<CountryDocument>;
}
