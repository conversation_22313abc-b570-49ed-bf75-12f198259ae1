import { Injectable, NotFoundException } from '@nestjs/common';
import dayjs from 'dayjs';
import _ from 'lodash';
import { ObjectId } from 'mongodb';
import { Types } from 'mongoose';

import { JobService } from '~/modules/job/job.service';
import {
  EquipmentPlanningOverviewQueryParamsDto,
  TeamPlanningOverviewQueryParamsDto,
} from '~/modules/planning/dtos/planning-overview-query-params.dto';
import { TaskService } from '~/modules/task/task.service';
import { TeamModel } from '~/modules/team/team.model';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { TEAM_MESSAGE_KEYS } from '~/shared/message-keys/team.message-keys';
import { InjectModel } from '~/transformers/model.transformer';
import { startAndEndOfIsoWeek } from '~/utils/date.util';

import { PlanningOrderType } from '../../shared/enums/planning-order.enum';
import { ChangePositionInPlanningOrderSchemaDto } from './dtos/planning-order.dto';
import { PlanningOrderModel } from './planning-order.model';

@Injectable()
export class PlanningService {
  constructor(
    @InjectModel(TeamModel)
    private readonly teamModel: MongooseModel<TeamModel>,
    @InjectModel(PlanningOrderModel)
    private readonly planningOrderModel: MongooseModel<PlanningOrderModel>,

    private readonly taskService: TaskService,
    private readonly jobService: JobService,
  ) {}

  async teamOverview(query: TeamPlanningOverviewQueryParamsDto) {
    const {
      team: teamId,
      isoWeek,
      year,
      location,
      employees,
      timezone = '+00:00',
    } = query;

    const team = await this.teamModel
      .findById(teamId)
      .select('name tenantUsers')
      .lean();

    if (!team) {
      throw new NotFoundException(TEAM_MESSAGE_KEYS.NOT_FOUND);
    }

    let tenantUserIds: Types.ObjectId[];
    if (!employees?.length) {
      tenantUserIds = team.tenantUsers.map(({ _id }) => _id);
    } else if (
      team.tenantUsers.findIndex(({ _id }) =>
        employees.includes(_id.toString()),
      ) === -1
    ) {
      throw new NotFoundException(TEAM_MESSAGE_KEYS.EMPLOYEE_NOT_FOUND);
    } else {
      tenantUserIds = employees.map((v) => new Types.ObjectId(v));
    }

    const { startDate, endDate } = startAndEndOfIsoWeek(
      isoWeek,
      year,
      timezone,
    );

    const promises = Promise.all([
      location
        ? []
        : this.taskService.getEmployeesSchedules(
            { startDate, endDate, timezone },
            tenantUserIds,
          ),
      this.jobService.getEmployeesSchedules(
        { startDate, endDate, timezone },
        tenantUserIds,
        location,
      ),
    ]);

    const [tasks, jobs] = await promises;

    const mappedJobs = await this.mappedJobsByPlanningOrder(
      jobs,
      PlanningOrderType.EMPLOYEE,
    );

    return {
      team: {
        id: teamId,
        name: team.name,
      },
      isoWeek,
      year,
      startDate,
      endDate,
      tasks,
      jobs: mappedJobs,
    };
  }

  async equipmentOverview(query: EquipmentPlanningOverviewQueryParamsDto) {
    const {
      equipmentType,
      isoWeek,
      year,
      timezone = '+00:00',
      location,
    } = query;

    const { startDate, endDate } = startAndEndOfIsoWeek(
      isoWeek,
      year,
      timezone,
    );

    const promises = Promise.all([
      location
        ? []
        : this.taskService.getEquipmentSchedules(
            { startDate, endDate, timezone },
            equipmentType,
          ),
      this.jobService.getEquipmentSchedules(
        { startDate, endDate, timezone },
        equipmentType,
        location,
      ),
    ]);

    const [tasks, jobs] = await promises;

    const mappedJobs = await this.mappedJobsByPlanningOrder(
      jobs,
      PlanningOrderType.EQUIPMENT,
    );

    return {
      equipmentType,
      isoWeek,
      year,
      startDate,
      endDate,
      tasks,
      jobs: mappedJobs,
    };
  }

  async changePositionInPlanningOrder(
    payload: ChangePositionInPlanningOrderSchemaDto,
  ) {
    const foundPlanningOrder = await this.planningOrderModel
      .findOne({
        date: dayjs(payload.date).format('YYYY-MM-DD'),
        employee: new ObjectId(payload.employee),
      })
      .lean();
    if (!foundPlanningOrder) {
      throw new NotFoundException('Planning order not found');
    }
    return this.planningOrderModel
      .findOneAndUpdate(
        {
          _id: foundPlanningOrder._id,
        },
        {
          $set: {
            jobs: payload.jobs.map((job, index) => {
              return {
                _id: new ObjectId(job),
                position: index + 1,
              };
            }),
          },
        },
        {
          new: true,
        },
      )
      .exec();
  }

  private async mappedJobsByPlanningOrder(
    jobs: any[],
    type: PlanningOrderType,
  ) {
    const mappedJobPromises = jobs.map(async (job) => {
      const employeeId =
        type === PlanningOrderType.EMPLOYEE
          ? job.employee._id
          : job.equipment._id;
      const jobByDatesPromise = job.schedules.map(async (jobByDate) => {
        const date = jobByDate.date;
        const items = jobByDate.items;
        const itemIds = items.map((item) => item._id.toString());
        const employeePlanningOrder = await this.planningOrderModel
          .findOne({
            date: date,
            employee: employeeId,
            type: type,
          })
          .lean();
        if (employeePlanningOrder) {
          const employeePlanningOrderJobIds = employeePlanningOrder.jobs.map(
            (job) => job._id.toString(),
          );
          const sameItems = employeePlanningOrderJobIds.filter((job) =>
            itemIds.includes(job),
          );
          const newItems = itemIds.filter(
            (item) => !employeePlanningOrderJobIds.includes(item),
          );
          let jobs: any[] = sameItems.map((item, index) => {
            return {
              _id: new ObjectId(item),
              position: index + 1,
            };
          });
          if (newItems.length > 0) {
            const lastPosition = _.maxBy(jobs, 'position')?.position || 0;

            jobs = [
              ...jobs,
              ...newItems.map((item, index) => {
                return {
                  _id: new ObjectId(item),
                  position: lastPosition + index + 1,
                };
              }),
            ];
          }
          if (
            newItems.length > 0 ||
            sameItems.length !== employeePlanningOrderJobIds.length
          ) {
            await this.planningOrderModel.updateOne(
              {
                _id: employeePlanningOrder._id,
              },
              {
                $set: {
                  jobs: jobs,
                },
              },
            );
          }
          return {
            ...jobByDate,
            items: jobs.map((job) => {
              return items.find(
                (item) => item._id.toString() === job._id.toString(),
              );
            }),
          };
        } else {
          await this.planningOrderModel.create({
            date: date,
            type: type,
            employee: employeeId,
            jobs: items.map((item, index) => {
              return {
                _id: new ObjectId(item._id),
                position: index + 1,
              };
            }),
          });
          return {
            ...jobByDate,
            items: items,
          };
        }
      });
      const jobByDates = await Promise.all(jobByDatesPromise);
      return {
        ...job,
        schedules: jobByDates,
      };
    });

    const mappedJobs = await Promise.all(mappedJobPromises);
    return mappedJobs;
  }
}
