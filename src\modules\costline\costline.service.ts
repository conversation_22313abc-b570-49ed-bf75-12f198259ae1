import { BadRequestException, Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import _, { omit } from 'lodash';
import mongoose, { Types } from 'mongoose';
import pLimit from 'p-limit';

import {
  addFieldsIsoGroupingPipeline,
  ISO_WEEK_END_MONGO_VARIABLE,
} from '~/modules/invoice/invoice.helper';
import { ContactRole } from '~/shared/enums/contact.enum';
import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  ContractType,
  CostLineStatus,
} from '~/shared/enums/contract.enum';
import { CostTypeType } from '~/shared/enums/cost-type.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { CONTACT_MESSAGE_KEYS } from '~/shared/message-keys/contact.message-key';
import { COSTLINE_MESSAGE_KEYS } from '~/shared/message-keys/costline.message-keys';
import { COSTTYPE_MESSAGE_KEYS } from '~/shared/message-keys/costtype.message-keys';
import { InjectModel } from '~/transformers/model.transformer';
import { DateLike } from '~/utils/date.util';

import { BvCompanyModel } from '../bvcompany/bvcompany.model';
import { ContactModel } from '../contact/contact.model';
import {
  calculateNextFutureGenerationDate,
  calculateTotalPrice,
  convertCostLineToCreditLine,
  convertCostLineToCreditLines,
  CostLineGeneralUpdateField,
  generatePeriods,
  isSamePeriod,
  isSameTotalPrice,
} from '../contract/contract.helper';
import { ContractModel } from '../contract/contract.model';
import { CostCenterModel } from '../costcenter/costcenter.model';
import { CostLineGeneralModel } from '../costlinegeneral/costlinegeneral.model';
import { CostTypeModel } from '../costtype/costtype.model';
import { CountryModel } from '../country/country.model';
import { LocationModel } from '../location/location.model';
import { CostLineModel } from './costline.model';
import { CreditCostLineCreateBodyDto } from './dtos/create-credit-costline.dto';
import {
  CreateCustomCostLinesDto,
  DeleteCostlineDto,
} from './dtos/create-custom-costline.dto';

@Injectable()
export class CostlineService {
  constructor(
    @InjectModel(CostLineModel)
    private readonly costlineModel: MongooseModel<CostLineModel>,
    @InjectModel(ContactModel)
    private readonly contactModel: MongooseModel<ContactModel>,
    @InjectModel(CostTypeModel)
    private readonly costTypeModel: MongooseModel<CostTypeModel>,
    @InjectModel(LocationModel)
    private readonly locationModel: MongooseModel<LocationModel>,
    @InjectModel(CostCenterModel)
    private readonly costCenterModel: MongooseModel<CostCenterModel>,
    @InjectModel(CostLineGeneralModel)
    private readonly costLineGeneralModel: MongooseModel<CostLineGeneralModel>,
    @InjectModel(ContractModel)
    private readonly contractModel: MongooseModel<ContractModel>,
    @InjectModel(CountryModel)
    private readonly countryModel: MongooseModel<CountryModel>,
    @InjectModel(BvCompanyModel)
    private readonly bvCompanyModel: MongooseModel<BvCompanyModel>,
  ) {}

  async createCustomCostLine(data: CreateCustomCostLinesDto) {
    const [_costTypeExists, _isDebtor] = await Promise.all([
      this.validateAllCostTypesExist(data.costLines),
      this.validateDebtorContact(data.contact),
      this.validateThenGetLocationOrCostCenterType(
        data.location,
        data.costCenter,
      ),
      this.validateCountry(data.country),
      this.validateBvCompany(data.bvCompany),
    ]);

    const costLines = data.costLines.map((item) => ({
      contact: data.contact,
      description: item.description,
      price: item.price,
      quantity: item.quantity,
      totalPrice: item.price * item.quantity,
      costType: item.costType,
      isCustom: true,
      isCredit: false,
      ...(data.location ? { location: data.location } : {}),
      ...(data.costCenter ? { costCenter: data.costCenter } : {}),
      ...(data.country ? { country: data.country } : {}),
      ...(data.bvCompany ? { bvCompany: data.bvCompany } : {}),
      type: ContractType.CUSTOM,
    }));

    return await this.costlineModel.insertMany(costLines);
  }

  async deleteCostLine(payload: DeleteCostlineDto) {
    const { id } = payload;
    const result = await this.costlineModel.findOneAndUpdate(
      { _id: id, isDeleted: false, canceledAt: { $exists: false } },
      { isDeleted: true, canceledAt: dayjs().utc().toDate() },
    );
    if (!result) {
      throw new BadRequestException(COSTLINE_MESSAGE_KEYS.NOT_FOUND);
    }
    return result;
  }

  async createCreditCostLines(payload: CreditCostLineCreateBodyDto) {
    const costLineIds = payload.costLines.map(({ _id }) => _id);
    const costLines = await this.costlineModel
      .find({
        _id: { $in: costLineIds },
        isDeleted: false,
        invoice: { $exists: true },
        approvedAt: { $exists: true },
        status: CostLineStatus.CLOSED,
        type: { $in: [ContractType.JOB, ContractType.CUSTOM] },
      })
      .lean();

    if (costLines.length !== payload.costLines.length) {
      throw new BadRequestException(COSTLINE_MESSAGE_KEYS.NOT_FOUND);
    }

    const types = _.uniq(costLines.map((costLine) => costLine.type));
    if (types.length !== 1) {
      throw new BadRequestException(
        COSTLINE_MESSAGE_KEYS.COST_LINES_MUST_HAVE_SAME_TYPE,
      );
    }

    const creditCostLines = costLines.map((costLine) => {
      const { _id, price, description } = payload.costLines.filter(
        (item) => item._id === costLine._id.toString(),
      )[0];
      return {
        ..._.pick(costLine, [
          'costType',
          'quantity',
          'startDate',
          'endDate',
          'type',
          'period',
          'periodType',
          'contact',
          'location',
          'costCenter',
          'country',
          'bvCompany',
        ]),
        isCredit: true,
        status: CostLineStatus.OPEN,
        price,
        totalPrice: costLine.quantity * price,
        description,
        parent: _id,
      };
    });

    return await this.costlineModel.insertMany(creditCostLines);
  }

  async generateCostLineByPeriodByCronJob(data: any) {
    const { fakeCurrentDate, fakeContractIds } = data;
    const currentDate = fakeCurrentDate
      ? dayjs(fakeCurrentDate).utc().startOf('day')
      : dayjs().utc().startOf('day');

    const contracts = await this.contractModel
      .find({
        isActive: true,
        isGenerateCostLine: true,
        $or: [
          { endDate: { $gte: currentDate.toDate() } },
          { endDate: { $exists: false } },
          { endDate: null },
        ],
      })
      .lean();
    const contractIds =
      fakeContractIds && fakeContractIds.length > 0
        ? fakeContractIds.map(
            (fakeContractId: string) => new Types.ObjectId(fakeContractId),
          )
        : contracts.map(({ _id }) => _id);
    const costLineGenerals = await this.costLineGeneralModel
      .aggregate([
        {
          $match: {
            futureGenerationDate: {
              $lte: currentDate.toDate(),
            },
          },
        },
        {
          $lookup: {
            from: 'agreementlines',
            localField: 'agreementLine',
            foreignField: '_id',
            as: 'agreementLine',
            pipeline: [
              {
                $lookup: {
                  from: 'contracts',
                  localField: 'contract',
                  foreignField: '_id',
                  as: 'contract',
                  pipeline: [
                    {
                      $match: {
                        ...(fakeContractIds?.length > 0 && {
                          isActive: true,
                        }),
                        _id: { $in: contractIds },
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          $set: {
            agreementLine: {
              $arrayElemAt: ['$agreementLine', 0],
            },
          },
        },
        {
          $unwind: {
            path: '$agreementLine.contract',
            preserveNullAndEmptyArrays: false,
          },
        },
        {
          $lookup: {
            from: 'costlines',
            localField: '_id',
            foreignField: 'costLineGeneral',
            as: 'costLines',
          },
        },
      ])
      .exec();

    if (costLineGenerals.length === 0) {
      return;
    }

    const limit = pLimit(1);
    const costLineCreatePromises = costLineGenerals.map((costLineGeneral) =>
      limit(async () => {
        if (costLineGeneral.costLines.length === 0) {
          const result = await this.createCostLines({
            costLineGeneral,
            agreementLine: costLineGeneral.agreementLine,
            contract: costLineGeneral.agreementLine.contract,
          });

          const lastCostLine = [...result].sort(
            (a: any, b: any) => b.endDate - a.endDate,
          )[0];

          const futureGenerationDate = calculateNextFutureGenerationDate({
            period: costLineGeneral.agreementLine.period,
            generatePeriod:
              costLineGeneral.agreementLine.contract.generatePeriod,
            startDate: dayjs(lastCostLine.endDate),
            now: currentDate,
          });

          await this.costLineGeneralModel.updateOne(
            { _id: costLineGeneral._id },
            { futureGenerationDate },
          );
          return result;
        } else {
          const costLines = costLineGeneral.costLines;
          const lastCostLine = costLines.sort(
            (a, b) => b.endDate - a.endDate,
          )[0];

          if (!lastCostLine) {
            throw new BadRequestException(COSTLINE_MESSAGE_KEYS.NOT_FOUND);
          }

          const { endDate, price, costType } = costLineGeneral;
          const { period, periodType } = costLineGeneral.agreementLine;
          const { generatePeriod, location, costCenter } =
            costLineGeneral.agreementLine.contract;
          const periods = generatePeriods({
            startDate: lastCostLine.endDate,
            endDate,
            periodType,
            period,
            generatePeriod,
            now: currentDate,
          });

          let index = lastCostLine.position;

          const createCostLines = periods
            .filter((timeline) => {
              return dayjs(timeline.end).isAfter(dayjs(lastCostLine.endDate));
            })
            .map((timeline) => {
              index += 1;
              return {
                contact: costLineGeneral.agreementLine.contract.contact,
                agreementLine: costLineGeneral.agreementLine._id,
                costLineGeneral: costLineGeneral._id,
                startDate: timeline.start,
                endDate: timeline.end,
                description: costLineGeneral.description,
                price,
                period: costLineGeneral.agreementLine.period,
                periodType: costLineGeneral.agreementLine.periodType,
                totalPrice: calculateTotalPrice(
                  price,
                  period,
                  timeline,
                  costLineGeneral.agreementLine.contract.isNew,
                ),
                costType,
                isCustom: false,
                isCredit: false,
                generatedAt: new Date(),
                type: costLineGeneral.agreementLine.contract.type,
                version: 1,
                position: index,
                location,
                costCenter,
              };
            });

          const result = await this.costlineModel.create(createCostLines);
          if (result.length > 0) {
            const futureGenerationDate = calculateNextFutureGenerationDate({
              period,
              generatePeriod: generatePeriod,
              startDate: dayjs(lastCostLine.endDate),
              now: currentDate,
            });

            await this.costLineGeneralModel.updateOne(
              { _id: costLineGeneral._id },
              { futureGenerationDate },
            );
          }

          return result;
        }
      }),
    );

    await Promise.all(costLineCreatePromises);
  }

  async updateCostCenterByLocation(
    location: string | Types.ObjectId,
    prevCostCenter: string | Types.ObjectId,
    newCostCenter: string | Types.ObjectId,
  ) {
    await this.costlineModel.updateMany(
      {
        location: new Types.ObjectId(location),
        costCenter: new Types.ObjectId(prevCostCenter),
        isDeleted: false,
        status: CostLineStatus.OPEN,
      },
      {
        $set: { costCenter: newCostCenter },
      },
    );
  }

  //#region Private methods

  private async validateBvCompany(bvCompanyId: string | undefined) {
    if (bvCompanyId) {
      const foundBvCompany = await this.bvCompanyModel.findOne(
        {
          _id: bvCompanyId,
        },
        { name: 1 },
      );

      if (!foundBvCompany) {
        throw new BadRequestException(
          COSTLINE_MESSAGE_KEYS.BV_COMPANY_NOT_FOUND,
        );
      }
    }
  }

  private async validateCountry(countryId: string | undefined) {
    if (countryId) {
      const foundCountry = await this.countryModel.findOne(
        {
          _id: countryId,
        },
        { countryCode: 1 },
      );

      if (!foundCountry) {
        throw new BadRequestException(COSTLINE_MESSAGE_KEYS.COUNTRY_NOT_FOUND);
      }
    }
  }

  async validateAllCostTypesExist(costTypes: { costType: string }[]) {
    const uniqueCostTypeIds = _.uniqBy(costTypes, 'costType').map(
      ({ costType }) => costType,
    );
    const foundCostTypes = await this.costTypeModel.find(
      {
        _id: { $in: uniqueCostTypeIds },
      },
      { type: 1 },
    );

    if (foundCostTypes.length !== uniqueCostTypeIds.length) {
      throw new BadRequestException(COSTTYPE_MESSAGE_KEYS.NOT_FOUND);
    } else if (
      foundCostTypes.some(({ type }) => type !== CostTypeType.JOB_OR_CUSTOM)
    ) {
      throw new BadRequestException(COSTTYPE_MESSAGE_KEYS.NOT_JOB_OR_CUSTOM);
    }
  }

  async validateDebtorContact(contactId: string) {
    const foundContact = await this.contactModel.findOne(
      {
        _id: contactId,
      },
      { contactRole: 1 },
    );

    if (!foundContact) {
      throw new BadRequestException(
        CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND,
      );
    } else if (foundContact.contactRole !== ContactRole.DEBTOR) {
      throw new BadRequestException(
        COSTLINE_MESSAGE_KEYS.CUSTOM_COSTLINE_INVALID_DEBTOR,
      );
    }
  }

  async validateThenGetLocationOrCostCenterType(
    location: string | undefined,
    costCenter: string | undefined,
  ) {
    let isLocationExists = false;
    let isCostCenterExists = false;
    if (location) {
      isLocationExists = !!(await this.locationModel.exists({
        _id: location,
        isActive: true,
      }));
    }

    if (costCenter) {
      isCostCenterExists = !!(await this.costCenterModel.exists({
        _id: costCenter,
        'locations.0': { $exists: false },
        isActive: true,
      }));
    }

    if (isLocationExists || isCostCenterExists) {
      return;
    }

    throw new BadRequestException(
      COSTLINE_MESSAGE_KEYS.CUSTOM_COSTLINE_LOCATION_OR_COSTCENTER_NOT_FOUND,
    );
  }

  private async generateCostLines({
    costLineGeneral,
    agreementLine,
    contract,
  }: {
    costLineGeneral: any;
    agreementLine: any;
    contract: any;
  }) {
    const { startDate, endDate, price, costType } = costLineGeneral;
    const { period, periodType } = agreementLine;
    const { generatePeriod, location, costCenter } = contract;

    const periods = generatePeriods({
      startDate,
      endDate,
      periodType,
      period,
      generatePeriod,
    });

    return periods.map((timeline, index) => ({
      contact: contract.contact._id,
      agreementLine: agreementLine._id,
      costLineGeneral: costLineGeneral._id,
      startDate: timeline.start,
      endDate: timeline.end,
      description: costLineGeneral.description,
      price,
      period: agreementLine.period,
      periodType: agreementLine.periodType,
      totalPrice: calculateTotalPrice(price, period, timeline, contract.isNew),
      costType,
      isCustom: false,
      isCredit: false,
      generatedAt: new Date(),
      type: contract.type,
      version: 1,
      position: index + 1,
      location,
      costCenter,
    }));
  }

  public async createCostLines({
    costLineGeneral,
    agreementLine,
    contract,
    session,
  }: {
    costLineGeneral: any;
    agreementLine: any;
    contract: any;
    session?: any;
  }) {
    const newCostLines = await this.generateCostLines({
      costLineGeneral,
      agreementLine,
      contract,
    });

    return await this.costlineModel.create(newCostLines, {
      session,
      ordered: true,
    });
  }

  /**
   * <h3>Update cost lines when one of 'endDate', 'startDate', 'generatePeriod' is changed</h3>
   * <hr/>
   *
   * @param costLineGeneral
   * @param agreementLine
   * @param updatedContract
   * @param session
   * @param dayJsCurrentDate
   * @param futureGenerationDate
   */
  public async updateCostLinesViaPeriods({
    updatedCostLineGeneral,
    agreementLine,
    updatedContract,
    session,
    dayJsCurrentDate,
    futureGenerationDate,
  }: {
    existedCostLineGeneral: {
      _id: string | mongoose.Types.ObjectId;
      [_key: string]: any;
    };
    updatedCostLineGeneral: {
      _id: string | mongoose.Types.ObjectId;
      [_key: string]: any;
    };
    agreementLine: {
      _id: string | mongoose.Types.ObjectId;
      [_key: string]: any;
    };
    updatedContract: any;
    session?: any;
    dayJsCurrentDate: dayjs.Dayjs;
    futureGenerationDate?: Date;
  }) {
    let newCostLines: any[] = [];
    const toBeRemovedCostLines: any[] = [];

    const approvedCostLines = await this.findAndGroupApprovedCostLines({
      agreementLineId: agreementLine._id,
      costLineGeneralId: updatedCostLineGeneral._id,
    });

    const generatedCostLines = await this.generateCostLines({
      costLineGeneral: updatedCostLineGeneral,
      agreementLine,
      contract: updatedContract,
    });

    for (const approvedCostLine of approvedCostLines) {
      const costLine = approvedCostLine.costLine;
      // Work around because old data somehow has startDate at 23:59:59, Lol
      costLine.startDate = dayjs(costLine.startDate)
        .utc()
        .startOf('day')
        .toDate();
      costLine.endDate = dayjs(costLine.endDate).utc().endOf('day').toDate();

      if (agreementLine.periodType === AgreementLinePeriodType.ONE_TIME) {
        const newCostLine = generatedCostLines[0];

        if (!newCostLine) {
          continue;
        }

        generatedCostLines.splice(0, 1);

        if (!isSameTotalPrice(costLine, newCostLine)) {
          newCostLines.push({
            ...omit(costLine, ['_id', 'approvedAt', 'createdAt', 'updatedAt']),
            isCredit: true,
            isDeleted: false,
            status: CostLineStatus.OPEN,
            price: newCostLine.price,
            totalPrice: newCostLine.totalPrice - costLine.totalPrice,
          });
        }

        continue;
      }

      const newCostLineIndex = generatedCostLines.findIndex((newCostLine) =>
        isSamePeriod(
          newCostLine.startDate,
          costLine.startDate,
          agreementLine.period,
        ),
      );
      const newCostLine = generatedCostLines[newCostLineIndex];

      if (!newCostLine) {
        newCostLines.push(convertCostLineToCreditLine(costLine));
        continue;
      } else if (isSameTotalPrice(costLine, newCostLine)) {
        generatedCostLines.splice(newCostLineIndex, 1);
        continue;
      }

      if (newCostLineIndex === -1) {
        newCostLines.push(convertCostLineToCreditLine(costLine));
        continue;
      }

      if (
        !costLine.isCredit &&
        dayjs(newCostLine.endDate).isAfter(costLine.endDate, 'day')
      ) {
        newCostLine.startDate = dayjs(costLine.endDate)
          .add(1, 'day')
          .utc()
          .startOf('day')
          .toDate();

        newCostLine.totalPrice = calculateTotalPrice(
          newCostLine.price,
          agreementLine.period,
          { start: newCostLine.startDate, end: newCostLine.endDate },
          updatedContract.isNew,
        );

        generatedCostLines.splice(newCostLineIndex, 1, newCostLine);
      } else if (
        !costLine.isCredit &&
        dayjs(newCostLine.startDate).isBefore(costLine.startDate)
      ) {
        newCostLine.endDate = dayjs(costLine.startDate)
          .subtract(1, 'day')
          .utc()
          .endOf('day')
          .toDate();

        newCostLine.totalPrice = calculateTotalPrice(
          newCostLine.price,
          agreementLine.period,
          { start: newCostLine.startDate, end: newCostLine.endDate },
          updatedContract.isNew,
        );

        generatedCostLines.splice(newCostLineIndex, 1, newCostLine);
      } else {
        // Create credit in case startDate is pushed to the future or endDate is pulled to the past
        newCostLines.push(
          ...convertCostLineToCreditLines(
            updatedContract,
            agreementLine,
            costLine,
            newCostLine,
          ),
        );
        generatedCostLines.splice(newCostLineIndex, 1);

        // if (!generatedCostLines[newCostLineIndex].isCredit) {
        //   const startDateBound = dayjs(costLine.startDateBound);
        //   const endDateBound = dayjs(costLine.endDateBound);
        //
        //   if (
        //     costLine.price !== newCostLine.price &&
        //     endDateBound.diff(startDateBound, 'day') > 0
        //   ) {
        //     const newStartDate = startDateBound
        //       .utc()
        //       .add(1, 'day')
        //       .startOf('day');
        //     const newEndDate = endDateBound
        //       .utc()
        //       .subtract(1, 'day')
        //       .endOf('day');
        //     const generatedCostLine = generatedCostLines[newCostLineIndex];
        //
        //     generatedCostLine.isCredit = true;
        //     generatedCostLine.startDate = newStartDate.isSameOrBefore(
        //       newCostLine.startDate,
        //     )
        //       ? newStartDate.toDate()
        //       : newCostLine.startDate;
        //     generatedCostLine.endDate = newEndDate.isSameOrAfter(
        //       newCostLine.endDate,
        //     )
        //       ? newEndDate.toDate()
        //       : newCostLine.endDate;
        //     generatedCostLine.totalPrice = calculateCreditPrice(
        //       costLine.price,
        //       newCostLine.price,
        //       costLine.totalPrice,
        //       agreementLine.period,
        //       {
        //         start: generatedCostLine.startDate,
        //         end: generatedCostLine.endDate,
        //       },
        //       updatedContract.isNew,
        //     );
        //     generatedCostLines.splice(newCostLineIndex, 1, generatedCostLine);
        //     // newCostLines.push(generatedCostLine);
        //   } else {
        //     generatedCostLines.splice(newCostLineIndex, 1);
        //   }
        // }
      }
    }

    const openCostLines = await this.findAndGroupOpenCostLines({
      agreementLineId: agreementLine._id,
      costLineGeneralId: updatedCostLineGeneral._id,
    });

    for (const { costLine } of openCostLines) {
      // if (costLine.isCredit) {
      //   const newCredits = newCostLines.filter(
      //     (newCostLine) =>
      //       newCostLine.isCredit &&
      //       isSamePeriod(
      //         newCostLine.startDate,
      //         costLine.startDate,
      //         agreementLine.period,
      //       ),
      //   );
      //
      //   if (newCredits.length) {
      //     toBeRemovedCostLines.push(costLine._id);
      //   }
      //
      //   continue;
      // }

      // In case there's already an open cost line created for this period,
      // we should remove the old open cost line to avoid duplication.
      // const ignoredCostLineIndex = findIndexCostLineInPeriod({
      //   costLines: generatedCostLines,
      //   comparableCostLine: costLine,
      //   customFilter: (c) =>
      //     isSamePeriod(c.startDate, costLine.startDate, agreementLine.period),
      // });
      //
      // if (ignoredCostLineIndex !== -1) {
      //   toBeRemovedCostLines.push(costLine._id);
      // }

      toBeRemovedCostLines.push(costLine._id);
    }

    if (generatedCostLines.length !== 0) {
      // Always keep credit lines
      if (
        futureGenerationDate &&
        dayJsCurrentDate.isBefore(futureGenerationDate)
      ) {
        newCostLines.push(
          ...generatedCostLines.filter((costLine) => costLine.isCredit),
        );
      } else {
        newCostLines.push(...generatedCostLines);
      }
    }

    if (toBeRemovedCostLines.length !== 0) {
      await this.costlineModel.updateMany(
        { _id: { $in: toBeRemovedCostLines } },
        {
          isDeleted: true,
          status: CostLineStatus.CANCELED,
          canceledAt: new Date(),
        },
        { session },
      );
    }

    newCostLines = newCostLines.filter((costLine) => costLine.totalPrice != 0);
    return newCostLines.length
      ? this.costlineModel.create(newCostLines, { session, ordered: true })
      : [];
  }

  public async updateCostLines({
    existedCostLineGeneral,
    updatedCostLineGeneral,
    agreementLine,
    updatedContract,
    session,
    dayJsCurrentDate,
    futureGenerationDate,
    costLineGeneralUpdatedFields,
  }: {
    existedCostLineGeneral: {
      _id: string | mongoose.Types.ObjectId;
      startDate: DateLike;
      endDate?: DateLike;
      [_key: string]: any;
    };
    updatedCostLineGeneral: {
      _id: string | mongoose.Types.ObjectId;
      startDate: DateLike;
      endDate?: DateLike;
      [_key: string]: any;
    };
    agreementLine: {
      _id: string | mongoose.Types.ObjectId;
      [_key: string]: any;
    };
    updatedContract: any;
    session?: any;
    dayJsCurrentDate: dayjs.Dayjs;
    futureGenerationDate?: Date;
    costLineGeneralUpdatedFields: CostLineGeneralUpdateField[];
  }) {
    if (
      costLineGeneralUpdatedFields.every((field) =>
        ['endDate', 'generatePeriod', 'startDate', 'price'].includes(field),
      )
    ) {
      return this.updateCostLinesViaPeriods({
        existedCostLineGeneral: existedCostLineGeneral,
        updatedCostLineGeneral: updatedCostLineGeneral,
        agreementLine,
        updatedContract,
        session,
        dayJsCurrentDate,
        futureGenerationDate,
      });
    }
  }

  private async findAndGroupApprovedCostLines({
    agreementLineId,
    costLineGeneralId,
  }: {
    agreementLineId: string | mongoose.Types.ObjectId;
    costLineGeneralId: string | mongoose.Types.ObjectId;
  }) {
    const defaultGrouping = {
      // price: { $abs: '$price' },
      costType: '$costType',
    };

    return this.costlineModel
      .aggregate()
      .match({
        agreementLine: new mongoose.Types.ObjectId(agreementLineId),
        costLineGeneral: new mongoose.Types.ObjectId(costLineGeneralId),
        approvedAt: { $exists: true },
      })
      .sort({ _id: -1 })
      .append(...addFieldsIsoGroupingPipeline(AgreementLinePeriod.FOUR_WEEKLY)) // This is a hack to add isoWeekEnd field
      .group({
        _id: {
          $switch: {
            branches: [
              {
                case: { $eq: ['$period', AgreementLinePeriod.WEEKLY] },
                then: {
                  ...defaultGrouping,
                  isoWeekYear: '$isoWeekYear',
                  isoWeek: '$isoWeek',
                },
              },
              {
                case: { $eq: ['$period', AgreementLinePeriod.FOUR_WEEKLY] },
                then: {
                  ...defaultGrouping,
                  isoWeekYear: '$isoWeekYear',
                  [ISO_WEEK_END_MONGO_VARIABLE]: `$${ISO_WEEK_END_MONGO_VARIABLE}`,
                },
              },
              {
                case: { $eq: ['$period', AgreementLinePeriod.MONTHLY] },
                then: {
                  ...defaultGrouping,
                  isoWeekYear: '$isoWeekYear',
                  month: '$month',
                },
              },
            ],
            default: defaultGrouping,
          },
        },
        priceSum: { $sum: '$totalPrice' },
        costLines: { $push: '$$ROOT' },
        startDateBound: { $maxN: { input: '$endDate', n: 2 } },
        endDateBound: { $minN: { input: '$startDate', n: 2 } },
        startDateMin: { $min: '$startDate' },
        endDateMax: { $max: '$endDate' },
      })
      .addFields({ costLine: { $first: '$costLines' } })
      .addFields({
        'costLine.totalPrice': '$priceSum',
        'costLine.startDateBound': {
          $arrayElemAt: ['$startDateBound', 1],
        },
        'costLine.endDateBound': {
          $arrayElemAt: ['$endDateBound', 1],
        },
        'costLine.startDate': '$startDateMin',
        'costLine.endDate': '$endDateMax',
      })
      .addFields({
        'costLine.startDateBound': {
          $cond: {
            if: { $eq: ['$costLine.startDateBound', '$endDateMax'] },
            then: '$startDateMin',
            else: '$costLine.startDateBound',
          },
        },
        'costLine.endDateBound': {
          $cond: {
            if: { $eq: ['$costLine.endDateBound', '$startDateMin'] },
            then: '$endDateMax',
            else: '$costLine.endDateBound',
          },
        },
      });
  }

  private async findAndGroupOpenCostLines({
    agreementLineId,
    costLineGeneralId,
  }: {
    agreementLineId: string | mongoose.Types.ObjectId;
    costLineGeneralId: string | mongoose.Types.ObjectId;
  }) {
    const defaultGrouping = {
      price: { $abs: '$price' },
      costType: '$costType',
    };

    return this.costlineModel
      .aggregate()
      .match({
        agreementLine: new mongoose.Types.ObjectId(agreementLineId),
        costLineGeneral: new mongoose.Types.ObjectId(costLineGeneralId),
        status: CostLineStatus.OPEN,
        isDeleted: false,
      })
      .append(...addFieldsIsoGroupingPipeline(AgreementLinePeriod.FOUR_WEEKLY)) // This is a hack to add isoWeekEnd field
      .group({
        _id: {
          $switch: {
            branches: [
              {
                case: { $eq: ['$period', AgreementLinePeriod.WEEKLY] },
                then: {
                  ...defaultGrouping,
                  isoWeekYear: '$isoWeekYear',
                  isoWeek: '$isoWeek',
                },
              },
              {
                case: { $eq: ['$period', AgreementLinePeriod.FOUR_WEEKLY] },
                then: {
                  ...defaultGrouping,
                  isoWeekYear: '$isoWeekYear',
                  [ISO_WEEK_END_MONGO_VARIABLE]: `$${ISO_WEEK_END_MONGO_VARIABLE}`,
                },
              },
              {
                case: { $eq: ['$period', AgreementLinePeriod.MONTHLY] },
                then: {
                  ...defaultGrouping,
                  isoWeekYear: '$isoWeekYear',
                  month: '$month',
                },
              },
            ],
            default: defaultGrouping,
          },
        },
        priceSum: { $sum: '$price' },
        costLine: { $push: '$$ROOT' },
      })
      .match({ priceSum: { $ne: 0 } })
      .unwind({ path: '$costLine', preserveNullAndEmptyArrays: false });
  }

  //#endregion
}
