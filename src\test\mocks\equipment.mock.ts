import { getModelForClass } from '@typegoose/typegoose';
import { Types } from 'mongoose';
import { z } from 'zod';

import { EquipmentModel } from '~/modules/equipment/equipment.model';
import { equipmentTest } from '~/modules/equipment/test/equipment.dto.test';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';

const equipmentModel = getModelForClass(EquipmentModel);
type equipmentType = z.infer<typeof equipmentTest.modelSchema>;

export const mockEquipmentData = {
  _id: new Types.ObjectId(),
  description: 'Car',
  type: EquipmentEnum.CAR,
  isActive: true,
  createdDate: new Date(),
  updatedDate: new Date(),
};

export async function initMockEquipment(doc?: Partial<equipmentType>) {
  const { _id, ...rest } = { ...mockEquipmentData, ...doc };
  await equipmentModel.replaceOne({ _id }, rest, { upsert: true });
}
