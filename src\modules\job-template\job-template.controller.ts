import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { JOB_TEMPLATE_MESSAGES } from '~/shared/messages/job-template.message';

import {
  CopyJobTemplateDto,
  CreateJobTemplateDto,
  DeleteJobTemplateDto,
  DeletePointJobTemplateDto,
  GetJobTemplateByUnitsDto,
  JobTemplateIdDto,
  UpdateJobTemplateDto,
} from './dtos/job-template.dto';
import { JobTemplateService } from './job-template.service';

@Controller('job-templates')
export class JobTemplateController {
  constructor(private readonly jobTemplateService: JobTemplateService) {}

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(QueryParamsDto))
  @MessagePattern({ cmd: JOB_TEMPLATE_MESSAGES.GET_JOB_TEMPLATES })
  public async findAll(@Payload() payload: QueryParamsDto) {
    return this.jobTemplateService.findAll(payload);
  }

  @UsePipes(new ZodValidationPipe(JobTemplateIdDto))
  @MessagePattern({ cmd: JOB_TEMPLATE_MESSAGES.DETAIL_JOB_TEMPLATE })
  public async findOne(@Payload() payload: JobTemplateIdDto) {
    return this.jobTemplateService.findOne(payload.id);
  }

  @UsePipes(new ZodValidationPipe(JobTemplateIdDto))
  @MessagePattern({ cmd: JOB_TEMPLATE_MESSAGES.GET_JOB_TEMPLATES_BY_UNIT })
  public async findByUnit(@Payload() payload: JobTemplateIdDto) {
    return this.jobTemplateService.findByUnit(payload.id);
  }

  @UsePipes(new ZodValidationPipe(GetJobTemplateByUnitsDto))
  @MessagePattern({ cmd: JOB_TEMPLATE_MESSAGES.GET_JOB_TEMPLATES_BY_UNITS })
  public async findByUnits(@Payload() payload: GetJobTemplateByUnitsDto) {
    return this.jobTemplateService.findByUnits(payload);
  }

  @UsePipes(new ZodValidationPipe(CreateJobTemplateDto))
  @MessagePattern({ cmd: JOB_TEMPLATE_MESSAGES.CREATE_JOB_TEMPLATE })
  public async create(@Payload() payload: CreateJobTemplateDto) {
    return this.jobTemplateService.create(payload);
  }

  @UsePipes(new ZodValidationPipe(UpdateJobTemplateDto))
  @MessagePattern({ cmd: JOB_TEMPLATE_MESSAGES.UPDATE_JOB_TEMPLATE })
  public async update(@Payload() payload: UpdateJobTemplateDto) {
    return this.jobTemplateService.update(payload);
  }

  @UsePipes(new ZodValidationPipe(DeleteJobTemplateDto))
  @MessagePattern({ cmd: JOB_TEMPLATE_MESSAGES.DELETE_JOB_TEMPLATE })
  public async delete(@Payload() payload: DeleteJobTemplateDto) {
    return this.jobTemplateService.delete(payload);
  }

  @UsePipes(new ZodValidationPipe(DeletePointJobTemplateDto))
  @MessagePattern({ cmd: JOB_TEMPLATE_MESSAGES.DELETE_POINT_JOB_TEMPLATE })
  public async deletePoint(@Payload() payload: DeletePointJobTemplateDto) {
    return this.jobTemplateService.deletePoint(payload);
  }

  @UsePipes(new ZodValidationPipe(CopyJobTemplateDto))
  @MessagePattern({ cmd: JOB_TEMPLATE_MESSAGES.COPY_JOB_TEMPLATE })
  public async copyJobTemplates(@Payload() payload: CopyJobTemplateDto) {
    return await this.jobTemplateService.copyJobTemplates(payload);
  }
}
