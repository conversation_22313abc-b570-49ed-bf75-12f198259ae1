import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';
import { LOCATION_MESSAGES } from '~/shared/messages/location.message';

import {
  CreateUpdateLocationAdditionalDto,
  GetLocationAdditionalDto,
  GetLocationGroupNamesDto,
} from '../location/dtos/location.dto';
import { LocationAdditionalService } from './location-additonal.service';

@Controller('location')
export class LocationAdditionalController {
  constructor(
    private readonly locationAdditionalService: LocationAdditionalService,
  ) {}

  // function get additional group names
  @UsePipes(new ZodValidationPipe(GetLocationGroupNamesDto))
  @MessagePattern({ cmd: LOCATION_MESSAGES.GET_ADDITIONAL_GROUP_NAMES })
  public async getAdditionalGroupNames(payload: GetLocationGroupNamesDto) {
    return this.locationAdditionalService.getAdditionalGroupNames(payload);
  }

  // function get additional by location
  @UsePipes(new ZodValidationPipe(GetLocationAdditionalDto))
  @MessagePattern({ cmd: LOCATION_MESSAGES.GET_ADDITIONAL_BY_LOCATION })
  public async getAdditionalByLocation(payload: {
    id: string;
    type: LocationAdditionalType;
  }) {
    return this.locationAdditionalService.getAdditionalByLocation(payload);
  }

  // function create/update additional by location
  @UsePipes(new ZodValidationPipe(CreateUpdateLocationAdditionalDto))
  @MessagePattern({
    cmd: LOCATION_MESSAGES.CREATE_OR_UPDATE_ADDITIONAL_BY_LOCATION,
  })
  public async createOrUpdateAdditionalByLocation(payload: {
    id: string;
    type: LocationAdditionalType;
    items: any;
  }) {
    return this.locationAdditionalService.createOrUpdateAdditionalByLocation(
      payload,
    );
  }
}
