import { mongoose } from '@typegoose/typegoose';
import _ from 'lodash';
import { Collection } from 'mongodb';
import { mongo, ObjectId, Types } from 'mongoose';
import * as path from 'path';

import { migrationV2 } from '../helpers/merge-data';
import MyWritable from '../helpers/writable';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldInspection {
  _id: string;
  active: boolean;
  equipments: ObjectId[];
  haveSentReport: boolean;
  jobType: string;
  rejected: boolean;
  status: string;
  title: string;
  type: string;
  location: any;
  dateInspection: Date;
  inspector: any;
  instruction: string;
  images: string[];
  reportTo: any;
  identifier: string;
  createdDate: Date;
  updatedDate: Date;
  createdBy: ObjectId;
  updatedBy: ObjectId;
  planner: any;
  action: string;
  feedBack: string;
  completeAt: Date;
  reviewAt: Date;
  billTo: any;
  units: ObjectId[];
  team: any;
  inspectionschedule: any;
  updateddate: Date;
  createddate: Date;
  deletedAt: Date;
}

/*const InspectionPipeLineAggregate = (skip: number, limit: number) => {
  // get all resident
  return [
    {
      $lookup: {
        from: 'inspectionschedule',
        localField: '_id',
        foreignField: 'inspection',
        as: 'inspectionschedule',
      },
    },
    {
      $set: {
        inspectionschedule: {
          $arrayElemAt: ['$inspectionschedule', 0],
        },
      },
    },
    {
      $lookup: {
        from: 'location',
        localField: 'location',
        foreignField: '_id',
        as: 'location',
      },
    },
    {
      $set: {
        location: {
          $arrayElemAt: ['$location', 0],
        },
      },
    },
    {
      $lookup: {
        from: 'team',
        let: { teamId: '$location.team' },
        as: 'team',
        pipeline: [
          {
            $match: {
              _id: '$$teamId',
            },
          },
        ],
      },
    },
    {
      $set: {
        team: {
          $arrayElemAt: ['$team', 0],
        },
      },
    },
    {
      $lookup: {
        from: 'users-permissions_user',
        localField: 'inspector',
        foreignField: '_id',
        as: 'inspector',
      },
    },
    {
      $set: {
        inspector: {
          $arrayElemAt: ['$inspector', 0],
        },
      },
    },
    {
      $lookup: {
        from: 'users-permissions_user',
        localField: 'planner',
        foreignField: '_id',
        as: 'planner',
      },
    },
    {
      $set: {
        planner: {
          $arrayElemAt: ['$planner', 0],
        },
      },
    },
    { $skip: skip },
    { $limit: limit },
  ];
};*/
const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection
    .aggregate()
    .match({
      _id: { $gt: nextId },
    })
    .sort({ _id: 1 })
    .limit(limit)
    .lookup({
      from: 'inspectionschedule',
      localField: '_id',
      foreignField: 'inspection',
      as: 'inspectionschedule',
    })
    .unwind({
      path: '$inspectionschedule',
      preserveNullAndEmptyArrays: true,
    })
    .lookup({
      from: 'location',
      localField: 'location',
      foreignField: '_id',
      as: 'location',
    })
    .unwind({
      path: '$location',
      preserveNullAndEmptyArrays: true,
    })
    .lookup({
      from: 'team',
      let: { teamId: '$location.team' },
      as: 'team',
      pipeline: [
        {
          $match: {
            $expr: {
              $eq: ['$_id', '$$teamId'],
            },
          },
        },
      ],
    })
    .unwind({
      path: '$team',
      preserveNullAndEmptyArrays: true,
    })
    .lookup({
      from: 'users-permissions_user',
      localField: 'inspector',
      foreignField: '_id',
      as: 'inspector',
    })
    .unwind({
      path: '$inspector',
      preserveNullAndEmptyArrays: true,
    })
    .lookup({
      from: 'users-permissions_user',
      localField: 'planner',
      foreignField: '_id',
      as: 'planner',
    })
    .unwind({
      path: '$planner',
      preserveNullAndEmptyArrays: true,
    });
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldInspection[];
  context: any;
}) => {
  // console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (item) => {
      let reportType: any = undefined;
      let tableQuery = '';
      if (item.reportTo) {
        switch (item.reportTo.type) {
          case 'debtor_contact':
            tableQuery = 'debtorcontact';
            reportType = 'customer';
            break;
          case 'supplier_contact':
            tableQuery = 'CreditorContact';
            reportType = 'supplier';
            break;
          case 'eeac':
            reportType = 'internal';
            tableQuery = 'debtorcontact';
            break;
        }
      }

      const result = {
        _id: item._id,
        isActive: item.active,
        identifier: item.identifier,
        title: item.title ?? '',
        status: item.status.toLowerCase(),
        type: item.type.toLowerCase(),
        jobType: item.jobType.toLowerCase(),
        plannedDate: item.dateInspection,
        instructions: item.instruction ?? '',
        assignee: item.inspector?._id,
        planner: item.planner?._id,
        location: item.location?._id,
        units: item.units ?? [],
        equipments: item.equipments ?? [],
        images: item.images ?? [],
        isSendRC: false,
        isSendRR: false,
        rtContacts: [],
        rrContacts: [],
        locationInfo: {
          _id: item.location?._id,
          fullAddress: item.location?.fullAddress,
        },
        locationTeamInfo: {
          _id: item.team?._id,
          name: item.team?.name,
        },
        assigneeInfo: {
          _id: item.inspector?._id,
          displayName: `${item.inspector?.firstName} ${item.inspector?.lastName}`,
          email: item.inspector?.email,
        },
        plannerInfo: {
          _id: item.planner?._id,
          displayName: `${item.planner?.firstName} ${item.planner?.lastName}`,
          email: item.planner?.email,
        },

        isDeleted: !!item.deletedAt,
        createdAt: item.createddate,
        updatedAt: item.updateddate,
        updatedBy: item.updatedBy,
        createdBy: item.createdBy,
      };

      if (reportType) {
        result['reportType'] = reportType;
      }

      let reportContact: any = undefined;
      if (tableQuery) {
        const sourceCursor = await context
          .sourceClient!.db()
          .collection(tableQuery)
          .aggregate([
            {
              $match: { _id: new mongoose.Types.ObjectId(item.reportTo.id) },
            },
            {
              $lookup: {
                from: 'grouping',
                localField: 'grouping',
                foreignField: '_id',
                as: 'grouping',
              },
            },
            {
              $set: {
                grouping: {
                  $arrayElemAt: ['$grouping', 0],
                },
              },
            },
            {
              $lookup: {
                from: 'person',
                localField: 'person',
                foreignField: '_id',
                as: 'person',
              },
            },
            {
              $set: {
                person: {
                  $arrayElemAt: ['$person', 0],
                },
              },
            },
          ]);
        const writable = new MyWritable({ objectMode: true });
        await new Promise((resolve, reject) => {
          sourceCursor
            .stream()
            .pipe(writable)
            .on('finish', resolve)
            .on('error', reject);
        });
        const foundReportContact = writable.data[0];
        if (foundReportContact) {
          reportContact =
            foundReportContact.grouping?._id ?? foundReportContact.person?._id;
          result['reportContactInfo'] = {
            _id: reportContact,
            displayName: foundReportContact.grouping
              ? foundReportContact.name
              : `${foundReportContact.person.firstName} ${foundReportContact.person.lastName}`,
            email: foundReportContact.grouping
              ? foundReportContact.grouping?.email
              : foundReportContact.person.email,
          };
          if (reportContact) {
            result['reportContact'] = reportContact;
          }
        } else {
          result['reportContactInfo'] = {};
        }
      }

      result['invoiceContact'] = item.billTo;

      if (['completed', 'closed'].includes(item.status.toLowerCase())) {
        const sourceCursor = await context
          .sourceClient!.db()
          .collection('upload_file')
          .find({ name: `${item.identifier}.pdf` });
        const writable = new MyWritable({ objectMode: true });
        await new Promise((resolve, reject) => {
          sourceCursor
            .stream()
            .pipe(writable)
            .on('finish', resolve)
            .on('error', reject);
        });
        const foundUploadFile = writable.data[0];
        if (foundUploadFile) {
          result['pdfPublicUrl'] = foundUploadFile.url;
          result['generatePdfStatus'] = 'done';
        } else {
          result['pdfPublicUrl'] = '';
          result['generatePdfStatus'] = 'pending';
        }

        const sourceCursor2 = await context
          .sourceClient!.db()
          .collection('activititylog')
          .find({
            identifier: item.identifier,
            type: 'Upload',
            action: 'Upload pdf',
          });
        const writable2 = new MyWritable({ objectMode: true });
        await new Promise((resolve, reject) => {
          sourceCursor2
            .stream()
            .pipe(writable2)
            .on('finish', resolve)
            .on('error', reject);
        });

        const foundActivityLog = writable2.data[0];
        if (foundActivityLog) {
          const rtContacts = foundActivityLog.request?.body?.contacts;
          if (rtContacts?.length > 0) {
            result['rtContacts'] = rtContacts.map(
              (rtContact) => new mongoose.Types.ObjectId(rtContact),
            );
            result['isSendRC'] = true;
          }
          const rrContacts = foundActivityLog.request?.body?.residents;
          if (rrContacts?.length > 0) {
            result['rrContacts'] = rrContacts.map(
              (rrContact) => new mongoose.Types.ObjectId(rrContact),
            );
            result['isSendRR'] = true;
          }
        }
      }

      if (item.type === 'Periodic') {
        result['fStartDate'] = item.inspectionschedule?.startDate;
        result['fEndDate'] = item.inspectionschedule?.endDate;
        result['fInterval'] = item.inspectionschedule?.fInterval;
        result['fRule'] = item.inspectionschedule?.fRule;
        result['fDays'] = item.inspectionschedule?.fByDay;
        result['fIdentifier'] = item.inspectionschedule?.identifier;
      }

      return result;
    }),
  );
};

/*const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('inspection');

  const pipeline = InspectionPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};*/

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'inspection',
      destinationCollectionName: 'jobs',
      pagingFunc,
      tranformDataFunc: tranformDataFunc,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
