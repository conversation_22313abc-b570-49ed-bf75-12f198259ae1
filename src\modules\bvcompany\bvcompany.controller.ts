import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { BV_COMPANY_MESSAGES } from '~/shared/messages/bvcompany.message';

import { BvCompanyService } from './bvcompany.service';

@Controller()
export class BvCompanyController {
  constructor(private bvCompanyService: BvCompanyService) {}

  @HTTPDecorators.Paginator
  @MessagePattern({ cmd: BV_COMPANY_MESSAGES.GET_LIST })
  async getList(@Payload() params: QueryParamsDto) {
    return await this.bvCompanyService.getList(params);
  }
}
