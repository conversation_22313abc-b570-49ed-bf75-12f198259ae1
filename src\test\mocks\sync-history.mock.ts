import { getModelForClass } from '@typegoose/typegoose';
import { Types } from 'mongoose';

import { SyncHistoryModel } from '~/modules/sync-history/sync-history.model';
import {
  SyncHistoryActionType,
  SyncHistoryStatus,
  SyncHistoryType,
} from '~/shared/enums/sync-history.enum';

const syncHistoryModel = getModelForClass(SyncHistoryModel);

export const mockSyncHistoryData = {
  _id: new Types.ObjectId(),
  isDeleted: false,
  type: SyncHistoryType.DEBTOR_CONTACT,
  status: SyncHistoryStatus.SUCCESS,
  actionType: SyncHistoryActionType.MANUAL,
  createdAt: new Date(),
  updatedAt: new Date(),
  syncedAt: new Date(),
};

export async function initMockSyncHistory(doc?: any) {
  const { _id, ...rest } = { ...mockSyncHistoryData, ...doc };
  await syncHistoryModel.replaceOne({ _id }, rest, { upsert: true });
}
