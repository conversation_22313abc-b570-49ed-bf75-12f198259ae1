import { Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(
      async (transactionDetail: { _id: Types.ObjectId; [key: string]: any }) =>
        omitNull({
          _id: transactionDetail._id,
          isDeleted: false,
          article: transactionDetail.article,
          transaction: transactionDetail.transaction,
          amount: transactionDetail.amount,
          removalFee: transactionDetail.removalFee || 0,
          salePrice: transactionDetail.salePrice || 0,
          purchasePrice: transactionDetail.purchasePrice || 0,
          margin: transactionDetail.margin || 0,
          laborPricePerHour: transactionDetail.laborPricePerHour || 0,
          createdAt: transactionDetail.createdAt,
          updatedAt: transactionDetail.updatedAt,
        }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'transactiondetail',
      destinationCollectionName: 'transactiondetail',
      tranformDataFunc: transformData,
      inventoryMode: true,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
