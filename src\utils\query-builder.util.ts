import mongoose, { isValidObjectId } from 'mongoose';

import { parseObjectId } from './parse.util';

export interface QueryParams {
  pageSize?: number;
  pageIndex?: number;
  sortBy?: string;
  sortDir?: 'asc' | 'desc';
  _q?: string;
  [key: string]: any;
}

export interface QueryOptions {
  query: Record<string, any>;
  options: {
    offset: number;
    limit: number;
    sort: Record<string, 1 | -1 | mongoose.Expression.Meta>;
    collation?: { locale: string };
  };
}

export function parseQueryParam(param: any): any {
  switch (param) {
    case 'true':
    case 'false':
      return param === 'true';
    default:
      return param;
  }
}

export function buildQuery(params: QueryParams, searchFields: string[] = []) {
  const {
    pageSize = 50,
    pageIndex = 1,
    sortBy = 'updatedAt',
    sortDir = 'desc',
    _q,
    ...rest
  } = params;

  const query: any = {};

  if (_q && searchFields.length) {
    let escaped_q = _q.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'); // escape special characters
    if (escaped_q.includes(' ')) {
      const keyWords = escaped_q.split(' ');
      escaped_q = keyWords.map((word) => `(?=.*${word})`).join('');
    }
    query.$or = searchFields.map((field) => ({
      [field]: { $regex: escaped_q, $options: 'i' },
    }));
  }

  for (const key in rest) {
    const value = parseQueryParam(rest[key]);

    if (Array.isArray(value)) {
      query[key] = {
        $in: value.map((v) => (isValidObjectId(v) ? parseObjectId(v) : v)),
      };
    } else {
      query[key] = isValidObjectId(value) ? parseObjectId(value) : value;
    }
  }

  const offset = (pageIndex - 1) * pageSize;
  const limit = pageSize;
  const collation = { locale: 'en' };

  // Sorting
  const sort: Record<string, number> = {};
  const sortFields = sortBy.split(',').map((field) => field.trim());

  sortFields.forEach((field) => {
    const [key, direction] = field.split(':').map((v) => v.trim());

    let order = direction === 'asc' ? 1 : -1;
    if (!['asc', 'desc'].includes(direction)) {
      order = sortDir === 'asc' ? 1 : -1;
    }

    sort[key] = order;
  });

  return { query, options: { offset, limit, sort, collation } } as QueryOptions;
}
