import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
  Severity,
} from '@typegoose/typegoose';
import mongoose from 'mongoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import {
  ContactRole,
  ContactType,
  SupplierCategoryEnum,
  SupplierType,
} from '~/shared/enums/contact.enum';
import { Gender, Language } from '~/shared/enums/tenant-user.enum';
import { BaseModel } from '~/shared/models/base.model';

import { AddressDocument, AddressModel } from '../address/address.model';

export type ContactDocument = DocumentType<ContactModel>;
export type ContactOrganizationPersonDocument =
  DocumentType<ContactOrganizationPersonModel>;

@modelOptions({
  options: { customName: 'Contact', allowMixed: Severity.ALLOW },
})
@index({ isInternal: 1 })
@index({ isActive: 1, email: 1, displayName: 1, phone1: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class ContactModel extends BaseModel {
  // Common fields
  @prop({ default: true })
  isActive!: boolean;

  @prop({ default: false })
  isInternal!: boolean;

  @prop({ trim: true, maxlength: 128 })
  displayName!: string;

  @prop({ enum: ContactType, default: ContactType.PERSON })
  contactType!: ContactType;

  @prop({ enum: ContactRole, default: ContactRole.PERSON })
  contactRole!: ContactRole;

  // Fields of personal contact
  @prop({ default: '', trim: true, maxlength: 64 })
  lastName?: string;

  @prop({ default: '', trim: true, maxlength: 64 })
  firstName?: string;

  @prop({ enum: Gender, default: Gender.OTHER })
  gender?: Gender;

  @prop({ enum: Language, default: Language.EN })
  language?: Language;

  // Fields of organization contact
  @prop({ default: '', trim: true, maxlength: 128 })
  name?: string;

  @prop({ default: '', trim: true, maxlength: 64 })
  kvk?: string;

  @prop({ default: '', trim: true, maxlength: 64 })
  snf?: string;

  @prop({ default: '', trim: true, maxlength: 64 })
  vatCode?: string;

  @prop({ ref: () => ContactModel })
  parentOrganization?: Ref<ContactDocument>;

  @prop({ required: true, trim: true, maxlength: 128 })
  email!: string;

  @prop({ default: '', trim: true, maxlength: 128 })
  warningEmail?: string;

  @prop({ default: '', trim: true, maxlength: 64 })
  phone1!: string;

  @prop({ default: '', trim: true, maxlength: 64 })
  phone2?: string;

  @prop({ ref: () => AddressModel })
  address1!: AddressDocument;

  @prop({ ref: () => AddressModel })
  address2?: AddressDocument;

  @prop({ default: '' })
  organizationNames?: string;

  @prop({ default: '' })
  website?: string;

  // Fields of debtor contact
  @prop({ default: 30, min: 0 })
  paymentTermRentInvoice!: number;

  @prop({ default: 14, min: 0 })
  paymentTermJobInvoice!: number;

  @prop({ default: '' })
  invoiceEmail?: string;

  @prop({ default: '' })
  invoiceReference?: string;

  @prop({ required: true, unique: true })
  identifier?: string;

  @prop({ default: false })
  collectiveJobInvoice?: boolean;

  @prop({ default: false })
  collectiveCustomInvoice?: boolean;

  // Fields of supplier contact
  @prop({ enum: SupplierType, default: SupplierType.REGULAR })
  supplierType?: SupplierType;

  @prop({ enum: SupplierCategoryEnum, default: null })
  supplierCategory?: SupplierCategoryEnum;

  @prop({ default: '', trim: true, maxlength: 512 })
  remark?: string;

  @prop()
  syncedAt?: Date;
}

@modelOptions({
  options: {
    customName: 'ContactOrganizationPerson',
    allowMixed: Severity.ALLOW,
  },
})
export class ContactOrganizationPersonModel extends BaseModel {
  @prop({ required: true, ref: () => ContactModel })
  organization!: mongoose.Types.ObjectId;

  @prop({ required: true, ref: () => ContactModel })
  person!: mongoose.Types.ObjectId;

  @prop({ default: '' })
  roleFunction?: string;
}
