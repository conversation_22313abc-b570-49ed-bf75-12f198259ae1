import { <PERSON>, Controller, Param, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { ROLE_GROUP_MESSAGES } from '~/shared/messages/role-group.message';

import {
  RoleGroupCreateBodyDto,
  RoleGroupDeleteDto,
  RoleGroupUpdateBodyDto,
} from './dto/role-group.dto';
import { RoleGroupService } from './role-group.service';

@Controller('role-group')
export class RoleGroupController {
  constructor(private readonly roleGroupService: RoleGroupService) {}

  @MessagePattern({ cmd: ROLE_GROUP_MESSAGES.CREATE })
  @UsePipes(new ZodValidationPipe(RoleGroupCreateBodyDto))
  create(@Body() data: RoleGroupCreateBodyDto) {
    return this.roleGroupService.create(data);
  }

  @MessagePattern({ cmd: ROLE_GROUP_MESSAGES.FIND_ALL })
  @HTTPDecorators.Paginator
  findAll(payload: any) {
    return this.roleGroupService.findAll(payload);
  }

  findOne(@Param('id') id: string) {
    return this.roleGroupService.findOne(id);
  }

  @MessagePattern({ cmd: ROLE_GROUP_MESSAGES.UPDATE })
  @UsePipes(new ZodValidationPipe(RoleGroupUpdateBodyDto))
  update(@Payload() payload: RoleGroupUpdateBodyDto) {
    return this.roleGroupService.update(payload);
  }

  @MessagePattern({ cmd: ROLE_GROUP_MESSAGES.DELETE })
  @UsePipes(new ZodValidationPipe(RoleGroupDeleteDto))
  delete(@Payload() payload: RoleGroupDeleteDto) {
    return this.roleGroupService.delete(payload);
  }
}
