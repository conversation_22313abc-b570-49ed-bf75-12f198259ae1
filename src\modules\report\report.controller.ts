import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { REPORT_MESSAGES } from '~/shared/messages/report.message';

import { GetNightRegistrationReportQueryDto } from '../night-registration/dtos/night-registration.dto';
import { NightRegistrationService } from '../night-registration/night-registration.service';
import { ListOccupationQueryDto } from '../stats-occupant/dtos/stats-occupant.dto';
import { StatsOccupantService } from '../stats-occupant/stats-occupant.service';
import {
  DebtorAndCreditorReportQueryDto,
  ReportEmployeeQueryParamsDto,
  RevenueHiredLocationQueryZodDto,
} from './dtos/report.dto';
import { LocationEnergyService } from './location-energy.service';
import { ReportService } from './report.service';

@Controller('reports')
export class ReportController {
  constructor(
    private readonly statsOccupantService: StatsOccupantService,
    private readonly reportService: ReportService,
    private readonly nightRegistrationService: NightRegistrationService,
    private readonly locationEnergyService: LocationEnergyService,
  ) {}

  @MessagePattern({ cmd: REPORT_MESSAGES.GET_LIST_OCCUPATION })
  @UsePipes(new ZodValidationPipe(ListOccupationQueryDto))
  async getListOccupation(@Payload() payload: any) {
    return this.statsOccupantService.getListOccupation(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.EXPORT_LIST_OCCUPATION })
  @UsePipes(new ZodValidationPipe(ListOccupationQueryDto))
  async exportListOccupation(@Payload() payload: any) {
    return this.statsOccupantService.exportListOccupation(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.GET_REVENUE_HIRED_LOCATION })
  @UsePipes(new ZodValidationPipe(RevenueHiredLocationQueryZodDto))
  async getRevenueHiredLocation(@Payload() payload: any) {
    return this.reportService.getRevenueHiredLocations(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.EXPORT_REVENUE_HIRED_LOCATION })
  @UsePipes(new ZodValidationPipe(RevenueHiredLocationQueryZodDto))
  async exportRevenueHiredLocation(@Payload() payload: any) {
    return this.reportService.exportRevenueHiredLocations(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.GET_DEBTOR_AND_CREDITOR_REPORT })
  @UsePipes(new ZodValidationPipe(DebtorAndCreditorReportQueryDto))
  async getDebtorAndCreditorReport(@Payload() payload: any) {
    return this.reportService.getDebtorAndRentingReport(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.EXPORT_DEBTOR_AND_CREDITOR_REPORT })
  @UsePipes(new ZodValidationPipe(DebtorAndCreditorReportQueryDto))
  async exportDebtorAndCreditorReport(@Payload() payload: any) {
    return this.reportService.exportDebtorAndRentingReport(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.GET_LIST_EMPLOYEE })
  @UsePipes(new ZodValidationPipe(ReportEmployeeQueryParamsDto))
  async getListEmployees(@Payload() payload: any) {
    return this.reportService.getListEmployees(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.EXPORT_LIST_EMPLOYEE })
  @UsePipes(new ZodValidationPipe(ReportEmployeeQueryParamsDto))
  async exportListEmployees(@Payload() payload: any) {
    return this.reportService.exportListEmployees(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.GET_NR_REPORT })
  @UsePipes(new ZodValidationPipe(GetNightRegistrationReportQueryDto))
  async getNrReport(@Payload() payload: any) {
    return this.nightRegistrationService.getReport(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.EXPORT_NR_REPORT })
  @UsePipes(new ZodValidationPipe(GetNightRegistrationReportQueryDto))
  async exportNrReport(@Payload() payload: any) {
    return this.nightRegistrationService.exportReport(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.GET_LOCATION_ENERGY })
  async getEnergyReport(@Payload() payload: any) {
    return this.locationEnergyService.getEnergyReport(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.EXPORT_LOCATION_ENERGY })
  async exportEnergyReport(@Payload() payload: any) {
    return this.locationEnergyService.exportEnergyReport(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.GET_LIST_VACANCY })
  async getListVacancies(@Payload() payload: any) {
    return this.statsOccupantService.getListVacancies(payload);
  }

  @MessagePattern({ cmd: REPORT_MESSAGES.EXPORT_LIST_VACANCY })
  async exportListVacancies(@Payload() payload: any) {
    return this.statsOccupantService.exportListVacancies(payload);
  }
}
