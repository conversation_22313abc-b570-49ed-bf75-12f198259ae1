import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

interface OldInspection {
  _id: string;
  inspectionschedule: any;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection.aggregate([
    {
      $match: {
        $and: [
          {
            _id: { $gt: nextId },
          },
          {
            createdBySchedule: { $ne: null },
          },
          {
            type: 'Regular',
          },
        ],
      },
    },
    { $sort: { _id: 1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'inspectionschedule',
        localField: 'createdBySchedule',
        foreignField: '_id',
        as: 'inspectionschedule',
      },
    },
    {
      $unwind: {
        path: '$inspectionschedule',
        preserveNullAndEmptyArrays: true,
      },
    },
  ]);
};

const transformData = ({
  data,
  context,
}: {
  data: OldInspection[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (job: OldInspection) => {
      return omitNull({
        _id: job._id,
        fIdentifier: job?.inspectionschedule?.identifier || null,
      });
    }),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();
    await migrationV2({
      context,
      sourceCollectionName: 'inspection',
      destinationCollectionName: 'jobs',
      pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
      isUpsert: false,
    });
    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
