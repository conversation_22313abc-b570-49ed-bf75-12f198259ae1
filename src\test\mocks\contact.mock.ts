import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';
import { z } from 'zod';

import { ContactModel } from '~/modules/contact/contact.model';
import { contactTest } from '~/modules/contact/test/contact.dto.test';
import {
  ContactRole,
  ContactType,
  SupplierCategoryEnum,
  SupplierType,
} from '~/shared/enums/contact.enum';
import { Gender, Language } from '~/shared/enums/tenant-user.enum';

const contactModel = getModelForClass(ContactModel);
type contactType = z.infer<typeof contactTest.modelSchema>;

export const mockContactData = {
  _id: new ObjectId(),
  address1: new ObjectId(),
  address2: null,
  contactRefId: new ObjectId(),
  contactRole: ContactRole.ORGANIZATION,
  contactType: ContactType.ORGANIZATION,
  displayName: 'Inout Werkgroep',
  email: '<EMAIL>',
  firstName: 'Inout',
  gender: Gender.MALE,
  identifier: nanoid(),
  isActive: true,
  language: Language.EN,
  lastName: 'Werkgroep',
  organizationNames: 'Werkgroep NL',
  phone1: '06-15942425',
  phone2: '',
  remark: '',
  supplierType: SupplierType.REGULAR,
  supplierCategory: SupplierCategoryEnum.AFVAL,
  isInternal: false,
  warningEmail: '<EMAIL>',
  phoneNumber1: '09999999999',
  invoiceEmail: '<EMAIL>',
};

export async function initMockContact(doc?: Partial<contactType>) {
  const { _id, ...rest } = { ...mockContactData, ...doc };
  await contactModel.replaceOne({ _id }, rest, { upsert: true });
}
