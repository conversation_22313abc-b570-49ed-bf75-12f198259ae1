import { getModelForClass } from '@typegoose/typegoose';
import { Types } from 'mongoose';

import { BvCompanyModel } from '~/modules/bvcompany/bvcompany.model';

const bvCompanyModel = getModelForClass(BvCompanyModel);

export const mockBvCompanyData = {
  _id: new Types.ObjectId(),
  identifier: '47',
  name: 'EEAC B.V.',
  createdAt: new Date(),
  updatedAt: new Date(),
};

export async function initMockBvCompany(doc?: any) {
  const { _id, ...rest } = { ...mockBvCompanyData, ...doc };
  await bvCompanyModel.replaceOne({ _id }, rest, { upsert: true });
}
