import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { CostLineModel } from '~/modules/costline/costline.model';
import { costlineTest } from '~/modules/costline/test/costline.dto.test';
import {
  AgreementLinePeriodType,
  ContractType,
  CostLineStatus,
} from '~/shared/enums/contract.enum';

import { mockAgreementLineData } from './agreementline.mock';
import { mockContactData } from './contact.mock';
import { mockCostTypeData } from './costtype.mock';
import { mockInvoiceData } from './invoice.mock';
import { mockJobData } from './job.mock';
import { mockLocationData } from './location.mock';

const costlineModel = getModelForClass(CostLineModel);
type costlineType = z.infer<typeof costlineTest.modelSchema>;

export const mockCostlineData = {
  _id: new ObjectId(),
  approvedAt: new Date(),
  contact: mockContactData._id,
  costType: mockCostTypeData._id,
  agreementLine: mockAgreementLineData._id,
  createdAt: new Date(),
  description: 'This is description of costline',
  invoice: mockInvoiceData._id,
  isCredit: false,
  isCustom: true,
  isDeleted: false,
  location: mockLocationData._id,
  position: 1,
  price: 65,
  quantity: 1,
  status: CostLineStatus.OPEN,
  totalPrice: 65,
  job: mockJobData._id,
  type: ContractType.CUSTOM,
  periodType: AgreementLinePeriodType.ONE_TIME,
  updatedAt: new Date(),
};

export async function initMockCostline(docs?: Partial<costlineType>) {
  const { _id, ...rest } = { ...mockCostlineData, ...docs };
  await costlineModel.replaceOne({ _id }, rest, { upsert: true });
}
