import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';

import { JobTemplateModel } from '~/modules/job-template/job-template.model';
import { JobTemplateTypeEnum } from '~/shared/enums/job-template.enum';

import { mockUnitData } from './unit.mock';

const jobTemplateModel = getModelForClass(JobTemplateModel);

export const mockJobTemplateData = {
  _id: new ObjectId(),
  createdAt: new Date(),
  isDeleted: false,
  name: 'INSPECTION POINTS',
  points: [
    {
      _id: nanoid(),
      description: 'Desc 1',
      position: 1,
    },
    {
      _id: nanoid(),
      description: 'Desc 2',
      position: 2,
    },
  ],
  type: JobTemplateTypeEnum.GENERAL,
  unit: mockUnitData._id,
  updatedAt: new Date(),
};

export async function initMockJobTemplate(doc?: any) {
  const { _id, ...rest } = { ...mockJobTemplateData, ...doc };
  await jobTemplateModel.replaceOne({ _id }, rest, { upsert: true });
}
