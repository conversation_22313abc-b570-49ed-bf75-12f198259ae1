import dayjs from 'dayjs';
import fs from 'fs';
import path from 'path';

async function mergeReport() {
  const reportsDir = 'dist/reports';
  const files = fs.readdirSync(reportsDir).filter((f) => f.endsWith('.json'));

  const failedTests: any[] = [];
  const summaryKeys = [
    'tests',
    'passed',
    'failed',
    'pending',
    'skipped',
    'other',
  ] as const;
  const mergedSummary = Object.fromEntries(
    summaryKeys.map((k) => [k, 0]),
  ) as Record<(typeof summaryKeys)[number], number>;
  let start = 0,
    stop = 0;

  files.forEach((file, idx) => {
    const { results } = JSON.parse(
      fs.readFileSync(path.join(reportsDir, file), 'utf-8'),
    );
    const { tests = [], summary = {} } = results || {};

    if (idx === 0) {
      start = summary.start ?? 0;
      stop = summary.stop ?? 0;
    }

    failedTests.push(...tests.filter((t: any) => t.status === 'failed'));
    summaryKeys.forEach((k) => (mergedSummary[k] += summary[k] ?? 0));
  });

  return {
    results: {
      tool: { name: 'jest' },
      summary: { ...mergedSummary, start, stop },
      tests: failedTests,
    },
  };
}

(async () => {
  const report = await mergeReport();

  const formatTime = (timestamp: number) =>
    dayjs(timestamp).format('DD/MM/YYYY HH:mm:ss');

  const failedTests = report.results.tests.map((test: any) => ({
    name: test.name,
    message: test.message,
  }));

  const colorTheme = failedTests.length > 0 ? 'e74c3c' : '17a2b8';

  const payload = {
    '@type': 'MessageCard',
    '@context': 'https://schema.org/extensions',
    summary: `🧪 [TEST-RESULT] - ✅ Passed: ${report.results.summary.passed} - ❌ Failed: ${report.results.summary.failed} - ⏭️ Skipped: ${report.results.summary.skipped}`,
    themeColor: colorTheme,
    title: '🧪 [EEAC][MULTI-TENANT][STAGING][CORE] - Unit Test Result',
    sections: [
      {
        activityTitle: new Date().toISOString(),
        facts: [
          {
            name: '📋 Total Tests',
            value: `**${report.results.summary.tests}**`,
          },
          { name: '✅ Passed', value: `**${report.results.summary.passed}**` },
          { name: '❌ Failed', value: `**${report.results.summary.failed}**` },
          {
            name: '⏭️ Skipped',
            value: `**${report.results.summary.skipped}**`,
          },
          {
            name: '⏳ Pending',
            value: `**${report.results.summary.pending}**`,
          },
          { name: '🌀 Other', value: `**${report.results.summary.other}**` },
          {
            name: '🟢 Start',
            value: `**${formatTime(report.results.summary.start)}**`,
          },
          {
            name: '🔴 Stop',
            value: `**${formatTime(report.results.summary.stop)}**`,
          },
          { name: '👤 Author', value: `${process.env.GITHUB_ACTOR}` },
          {
            name: '🔗 Workflow',
            value: `[View run](https://github.com/${process.env.GITHUB_REPOSITORY}/actions/runs/${process.env.GITHUB_RUN_ID})`,
          },
        ],
      },
      ...(failedTests.length > 0
        ? [
            {
              text: '<strong style="font-size: 20px;">❗️ Error Report:</strong>',
            },
            { text: '```\n' + JSON.stringify(failedTests, null, 2) + '\n```' },
          ]
        : []),
    ],
  };

  await fetch(
    'https://infodation.webhook.office.com/webhookb2/58e27a6e-de2e-4a43-9090-bc9caa6aa7a7@7c11714d-e26d-4173-b8a0-713f2d1b6287/IncomingWebhook/cdc1dc70a5ce4ca59c509c7bca4a6473/3681f937-281c-4401-bd46-887488c3c8af/V2crka_DOInyKiifXuzbOG42x0Sn63usMrGNCBfrZD_Xo1',
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    },
  );
})();
