import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { COSTTYPE_MESSAGES } from '~/shared/messages/costtype.message';

import { CosttypeService } from './costtype.service';
import { CostTypeQueryParamsDto } from './dtos/costtype-query-params.dto';

@Controller('costtype')
export class CosttypeController {
  constructor(private readonly costTypeService: CosttypeService) {}

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(CostTypeQueryParamsDto))
  @MessagePattern({ cmd: COSTTYPE_MESSAGES.GET_LIST_COSTTYPE })
  public async findAll(@Payload() payload: CostTypeQueryParamsDto) {
    return this.costTypeService.findAll(payload);
  }

  @MessagePattern({ cmd: COSTTYPE_MESSAGES.SYNC_COSTTYPES_FROM_THIRD_PARTY })
  public async syncFrom3rdParty(@Payload() payload: any) {
    const { type, actionType, thirdParty } = payload;
    return this.costTypeService.syncFrom3rdParty({
      type,
      actionType,
      ...thirdParty,
    });
  }
}
