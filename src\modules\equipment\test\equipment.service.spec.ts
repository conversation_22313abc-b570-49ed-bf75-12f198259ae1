import { Test, TestingModule } from '@nestjs/testing';
import { omit } from 'lodash';
import { ObjectId } from 'mongodb';

import { JobModel } from '~/modules/job/job.model';
import { TaskModel } from '~/modules/task/task.model';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import { EQUIPMENT_MESSAGE_KEYS } from '~/shared/message-keys/equipment.message-key';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockEquipment,
  mockEquipmentData,
} from '~/test/mocks/equipment.mock';
import { initMockTask } from '~/test/mocks/task.mock';

import {
  AvailableEquipmentQueryParamsDto,
  CreateEquipmentDto,
  EquipmentQueryParamsDto,
  UpdateEquipmentDto,
} from '../equipment.dto';
import { EquipmentModel } from '../equipment.model';
import { EquipmentService } from '../equipment.service';
import { equipmentTest } from './equipment.dto.test';

describe('EquipmentService', () => {
  let service: EquipmentService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        EquipmentService,
        ...testInjectModel([EquipmentModel, TaskModel, JobModel]),
      ],
    }).compile();

    service = module.get(EquipmentService);

    // Init data
    await initMockEquipment();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('createEquipment', () => {
    const payload: CreateEquipmentDto = {
      description: 'Car 1',
      isActive: true,
      type: EquipmentEnum.CAR,
    };

    it('should create equipment if not existed', async () => {
      const result = await service.createEquipment(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('description', payload.description);
      expect(result).toHaveProperty('type', EquipmentEnum.CAR);
      expect(result).toMatchSchema(equipmentTest.modelSchema);
    });

    it('should throw error if equipment existed', async () => {
      await expect(service.createEquipment(payload)).rejects.toThrow(
        EQUIPMENT_MESSAGE_KEYS.EXISTS,
      );
    });
  });

  describe('updateEquipment', () => {
    const payload: UpdateEquipmentDto = {
      id: mockEquipmentData._id.toString(),
      description: 'Car Updated',
      isActive: true,
    };

    const { id } = payload;

    it('should throw error if equipment not found', async () => {
      await expect(
        service.updateEquipment(new ObjectId().toString(), payload),
      ).rejects.toThrow(EQUIPMENT_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw error if equipment existed', async () => {
      jest
        .spyOn(service as any, 'checkIsExistedInSystem')
        .mockResolvedValue(true);

      await expect(service.updateEquipment(id, payload)).rejects.toThrow(
        EQUIPMENT_MESSAGE_KEYS.EXISTS,
      );
    });

    it('should update equipment if not existed', async () => {
      const result = await service.updateEquipment(id, payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockEquipmentData._id);
      expect(result).toHaveProperty('description', payload.description);
      expect(result).toHaveProperty('type', EquipmentEnum.CAR);
      expect(result).toMatchSchema(equipmentTest.modelSchema);
    });
  });

  describe('getListEquipment', () => {
    const payload: EquipmentQueryParamsDto = {
      type: EquipmentEnum.CAR,
    };

    it('should return list equipment', async () => {
      const result = await service.getListEquipment(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(equipmentTest.getListEquipmentSchema);
    });

    it('should return empty list if data not exist', async () => {
      const result = await service.getListEquipment({
        type: EquipmentEnum.CAR,
        pageIndex: 999,
      });

      expect(result).toBeDefined();
      expect(result.docs.length).toEqual(0);
    });

    it('should return all list equipment if pageSize is -1', async () => {
      const result = await service.getListEquipment({
        type: EquipmentEnum.CAR,
        pageSize: -1,
      });

      expect(result).toBeDefined();
      expect(result.totalDocs).toEqual(result.limit);
      expect(result.docs.length).toBeGreaterThan(0);
      expect(result.docs).toMatchSchema(equipmentTest.getListEquipmentSchema);
    });
  });

  describe('getAvailableEquipments', () => {
    const payload = {
      jobId: new ObjectId().toString(),
      taskId: new ObjectId().toString(),
      equipmentType: EquipmentEnum.CAR,
      timezone: '+07:00',
      startDate: new Date(),
      endDate: new Date(),
    };

    it('should return available equipments when data exists with payload', async () => {
      await initMockEquipment({
        description: 'Car Available',
        type: EquipmentEnum.CAR,
        isActive: true,
      });

      const result = await service.getAvailableEquipments(payload);
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty('type', EquipmentEnum.CAR);
      expect(result[0]).toHaveProperty('isActive', true);
    });

    it('should return available equipments when data exists with payload no time', async () => {
      const result = await service.getAvailableEquipments(
        omit(payload, ['startDate', 'endDate', 'timezone']),
      );
      expect(Array.isArray(result)).toBe(true);
      expect(result.length).toBeGreaterThan(0);
      expect(result[0]).toHaveProperty('type', EquipmentEnum.CAR);
      expect(result[0]).toHaveProperty('isActive', true);
    });

    it('should return empty array if no equipment matches', async () => {
      await service['equipmentModel'].deleteMany({});

      await initMockEquipment({
        description: 'Car Inactive',
        type: EquipmentEnum.CAR,
        isActive: false,
      });

      const result = await service.getAvailableEquipments(payload);
      expect(result).toEqual([]);
    });

    it('should return empty array if no equipment exists', async () => {
      await service['equipmentModel'].deleteMany({});

      const result = await service.getAvailableEquipments(payload);
      expect(result).toEqual([]);
    });

    it('should not return equipments that are booked in cars or devices of tasks', async () => {
      await service['equipmentModel'].deleteMany({});

      const carId = new ObjectId();
      const deviceId = new ObjectId();
      const now = new Date();

      await Promise.all([
        initMockEquipment({
          _id: carId,
          description: 'Car for booking',
          type: EquipmentEnum.CAR,
          isActive: true,
        }),
        initMockEquipment({
          _id: deviceId,
          description: 'Device for booking',
          type: EquipmentEnum.DEVICE,
          isActive: true,
        }),
        initMockTask({
          cars: [carId],
          devices: [deviceId],
          startDate: now,
          endDate: now,
          isDeleted: false,
        }),
      ]);

      const result = await service.getAvailableEquipmentsForTask({
        startDate: now,
        endDate: now,
        type: undefined,
        timezone: '+00:00',
      });

      expect(result.find((e) => e._id.equals(carId))).toBeUndefined();
      expect(result.find((e) => e._id.equals(deviceId))).toBeUndefined();
    });
  });

  describe('existsActiveOrThrow', () => {
    it('should not throw error if all ids are active and correct type', async () => {
      // Prepare data
      await initMockEquipment({
        description: 'Car Active',
        type: EquipmentEnum.CAR,
        isActive: true,
      });

      await expect(
        service.existsActiveOrThrow(
          [mockEquipmentData._id.toString()],
          EquipmentEnum.CAR,
        ),
      ).resolves.not.toThrow();
    });

    it('should throw if any id does not exist', async () => {
      await expect(
        service.existsActiveOrThrow(
          [new ObjectId().toString()],
          EquipmentEnum.CAR,
        ),
      ).rejects.toThrow(EQUIPMENT_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw if any id is not active', async () => {
      // Prepare data
      await initMockEquipment({
        description: 'Car Inactive',
        type: EquipmentEnum.CAR,
        isActive: false,
      });

      await expect(
        service.existsActiveOrThrow(
          [mockEquipmentData._id.toString()],
          EquipmentEnum.CAR,
        ),
      ).rejects.toThrow(EQUIPMENT_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw if any id is wrong type', async () => {
      // Prepare data
      await initMockEquipment({
        description: 'Device Active',
        type: EquipmentEnum.DEVICE,
        isActive: true,
      });

      await expect(
        service.existsActiveOrThrow(
          [mockEquipmentData._id.toString()],
          EquipmentEnum.CAR,
        ),
      ).rejects.toThrow(EQUIPMENT_MESSAGE_KEYS.NOT_FOUND);
    });
  });

  describe('findAvailableEquipments', () => {
    const now = new Date();

    beforeAll(async () => {
      await service['equipmentModel'].deleteMany({});
      await Promise.all([
        initMockEquipment({
          _id: new ObjectId(),
          description: 'Car Available',
          type: EquipmentEnum.CAR,
          isActive: true,
        }),
        initMockEquipment({
          _id: new ObjectId(),
          description: 'Device Available',
          type: EquipmentEnum.DEVICE,
          isActive: true,
        }),
      ]);
    });

    it('should return available equipments (status=available)', async () => {
      const query: AvailableEquipmentQueryParamsDto = {
        startDate: now.toISOString(),
        endDate: now.toISOString(),
        equipmentStatus: 'available',
        types: [EquipmentEnum.CAR, EquipmentEnum.DEVICE],
      };

      const result = await service.findAvailableEquipments(query);
      expect(result).toBeDefined();
      expect(Array.isArray(result.docs)).toBe(true);
      expect(result.docs.length).toBeGreaterThan(0);
      expect(result.docs.some((e) => e.type === EquipmentEnum.CAR)).toBe(true);
      expect(result.docs.some((e) => e.type === EquipmentEnum.DEVICE)).toBe(
        true,
      );
    });

    it('should return empty if no equipment matches status=unavailable', async () => {
      const query: AvailableEquipmentQueryParamsDto = {
        startDate: now.toISOString(),
        endDate: now.toISOString(),
        equipmentStatus: 'unavailable',
        types: [EquipmentEnum.CAR, EquipmentEnum.DEVICE],
      };
      const result = await service.findAvailableEquipments(query);
      expect(result).toBeDefined();
      expect(Array.isArray(result.docs)).toBe(true);
      expect(result.docs.length).toBe(0);
    });
  });
});
