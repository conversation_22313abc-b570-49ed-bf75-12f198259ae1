import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

interface OldCostLine {
  _id: Types.ObjectId;
  agreementLine?: Types.ObjectId;
  costLineGeneral?: Types.ObjectId;
  amount?: number;
  costCenter?: Types.ObjectId;
  costType?: Types.ObjectId;
  debtorContact?: {
    _id: Types.ObjectId;
    grouping?: Types.ObjectId;
    [key: string]: any;
  };
  deleted?: boolean;
  description?: string;
  inspectionItem?: {
    _id: Types.ObjectId;
    inspection?: Types.ObjectId;
    [key: string]: any;
  };
  invoice?: { _id: Types.ObjectId; createdDate: Date };
  invoiceable?: boolean;
  invoiceDate?: Date;
  isNewPrice?: boolean;
  location?: Types.ObjectId;
  period?: string;
  periodType?: string;
  price?: number;
  quantity?: number;
  reason?: string;
  status: string;
  type: string;
  unit?: Types.ObjectId;
  startDate?: Date;
  endDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection.aggregate([
    {
      $match: {
        _id: { $gt: nextId },
        type: { $in: ['SERVICE', 'JOB', 'CUSTOM', 'RENTING'] },
      },
    },
    { $sort: { _id: 1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'unit',
        localField: 'unit',
        foreignField: '_id',
        as: 'unit',
      },
    },
    {
      $lookup: {
        from: 'debtorcontact',
        localField: 'debtorContact',
        foreignField: '_id',
        as: 'debtorContact',
      },
    },
    {
      $lookup: {
        from: 'inspectionitem',
        localField: 'inspectionItem',
        foreignField: '_id',
        as: 'inspectionItem',
      },
    },
    {
      $lookup: {
        from: 'invoice',
        localField: 'invoice',
        foreignField: '_id',
        as: 'invoice',
      },
    },
    {
      $addFields: {
        unit: { $arrayElemAt: ['$unit', 0] },
        invoice: { $arrayElemAt: ['$invoice', 0] },
        inspectionItem: { $arrayElemAt: ['$inspectionItem', 0] },
        debtorContact: { $arrayElemAt: ['$debtorContact', 0] },
      },
    },
    {
      $lookup: {
        from: 'unit',
        localField: 'inspectionItem.unit',
        foreignField: '_id',
        as: 'inspectionItem.unit',
      },
    },
    {
      $addFields: {
        'inspectionItem.unit': { $arrayElemAt: ['$inspectionItem.unit', 0] },
      },
    },
    {
      $addFields: {
        costCenter: {
          $cond: [{ $eq: ['$type', 'SERVICE'] }, '$costCenter', '$$REMOVE'],
        },
        type: {
          $switch: {
            branches: [
              { case: { $eq: ['$type', 'RENTING'] }, then: 'debtor-renting' },
              { case: { $eq: ['$type', 'SERVICE'] }, then: 'debtor-service' },
            ],
            default: { $toLower: '$type' },
          },
        },
        periodType: {
          $cond: [
            { $in: ['$type', ['RENTING', 'SERVICE']] },
            { $toLower: '$periodType' },
            '$$REMOVE',
          ],
        },
        location: {
          $ifNull: ['$unit.location', '$inspectionItem.unit.location'],
        },
        unit: {
          $cond: ['$unit.parent', '$unit._id', '$$REMOVE'],
        },
        status: { $toLower: '$status' },
      },
    },
  ]);
};

const transformData = ({ data }: { data: OldCostLine[] }) => {
  return Promise.all(
    data.map(async (costLine: OldCostLine) =>
      omitNull({
        _id: costLine._id,
        agreementLine: costLine.agreementLine,
        costLineGeneral: costLine.costLineGeneral,
        approvedAt: costLine.invoice?.createdDate,
        contact: costLine.debtorContact?.grouping,
        costCenter: costLine.costCenter,
        costType: costLine.costType,
        description: costLine.description,
        invoice: costLine.invoice?._id,
        isCredit: false,
        isCustom: costLine.type === 'custom',
        isDeleted: costLine.deleted ?? false,
        job: costLine.inspectionItem?.inspection,
        location: costLine.location,
        period: costLine.period,
        periodType: costLine.periodType,
        position: 1,
        price: costLine.price ?? 0,
        quantity: costLine.quantity ?? 1,
        totalPrice: costLine.amount ?? 0,
        status: costLine.status,
        type: costLine.type,
        unit: costLine.unit,
        startDate: costLine.startDate ?? costLine.invoiceDate,
        endDate: costLine.endDate,
        createdAt: costLine.createdAt,
        updatedAt: costLine.updatedAt,
      }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'costline',
      destinationCollectionName: 'costlines',
      pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
