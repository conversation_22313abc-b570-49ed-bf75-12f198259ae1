import { z } from 'nestjs-zod/z';

import {
  CreateRentingCostLineGeneralSchema,
  CreateServiceCostLineGeneralSchema,
} from '~/modules/costlinegeneral/dtos/create-costlinegeneral.dto';
import { UpdateCostLineGeneralSchema } from '~/modules/costlinegeneral/dtos/update-costlinegeneral.dto';
import { isValidObjectId } from '~/utils';

import { CreateSupplierCostLineGeneralSchema } from '../../costlinegeneral/dtos/create-costlinegeneral.dto';

const oneOfUpdateRentingCostLineGeneralSchema = z.union([
  UpdateCostLineGeneralSchema,
  CreateRentingCostLineGeneralSchema,
]);

const oneOfUpdateServiceCostLineGeneralSchema = z.union([
  UpdateCostLineGeneralSchema,
  CreateServiceCostLineGeneralSchema,
]);

const oneOfUpdateSupplierCostLineGeneralSchema = z.union([
  UpdateCostLineGeneralSchema,
  CreateSupplierCostLineGeneralSchema,
]);

export const UpdateRentingAgreementLineSchema = z.strictObject({
  _id: z.string().refine((v) => isValidObjectId(v)),
  position: z.number().int().min(0),
  units: z
    .array(
      z
        .string()
        .trim()
        .refine((item) => isValidObjectId(item)),
    )
    .transform((arr) => Array.from(new Set(arr)))
    .optional()
    .default([]),
  costLineGenerals: z.array(oneOfUpdateRentingCostLineGeneralSchema).min(1),
});

export const UpdateServiceAgreementLineSchema = z.strictObject({
  _id: z.string().refine((v) => isValidObjectId(v)),
  position: z.number().int().min(0).optional(),
  costLineGenerals: z.array(oneOfUpdateServiceCostLineGeneralSchema).min(1),
});

export const UpdateSupplierAgreementLineSchema = z.strictObject({
  _id: z.string().refine((v) => isValidObjectId(v)),
  position: z.number().int().min(0).optional(),
  costLineGenerals: z.array(oneOfUpdateSupplierCostLineGeneralSchema).min(1),
});
