export enum NightRegistrationGender {
  MALE = 'Male',
  FEMALE = 'Female',
}

export enum NightRegistrationWarningLevel {
  GOOD = '0|good',
  VERBAL = '1|verbal',
  OFFICIAL = '2|official',
  REMOVAL = '3|removal',
}

export enum JobCheckInCheckOutEnum {
  CHECKIN = 'checkIn',
  CHECKOUT = 'checkOut',
}

export enum NRMaximumStayDurationLevel {
  EXCEEDED = 'exceeded',
  APPROACHING = 'approaching',
}

export enum NRMaximumStayDurationLevelLabel {
  EXCEEDED = 'Exceeded',
  APPROACHING = 'Within 14 days',
}
