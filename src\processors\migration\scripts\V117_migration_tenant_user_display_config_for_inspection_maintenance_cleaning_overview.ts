import * as path from 'path';

import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const GenericDisplayMapping: {
  field: string;
  isShown: boolean;
  mutable: boolean;
}[] = [
  {
    field: 'fIdentifier',
    isShown: true,
    mutable: false,
  },
  {
    field: 'identifier',
    isShown: true,
    mutable: false,
  },
  {
    field: 'title',
    isShown: true,
    mutable: true,
  },
  {
    field: 'plannedDate',
    isShown: true,
    mutable: true,
  },
  {
    field: 'location.fullAddress',
    isShown: true,
    mutable: true,
  },
  {
    field: 'units',
    isShown: true,
    mutable: true,
  },
  {
    field: 'status',
    isShown: true,
    mutable: true,
  },
  {
    field: 'type',
    isShown: true,
    mutable: true,
  },
  {
    field: 'assignee',
    isShown: true,
    mutable: true,
  },
  {
    field: 'location.team.name',
    isShown: true,
    mutable: true,
  },
] as const;

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const destinationCollectionName = 'tenantusers';
    const destinationCollection = context
      .destinationClient!.db()
      .collection(destinationCollectionName)!;

    const columnDisplayConfigs = {
      inspectionOverview: GenericDisplayMapping,
      maintenanceOverview: GenericDisplayMapping,
      cleaningOverview: GenericDisplayMapping,
    };

    const cursor = destinationCollection.find({ columnDisplayConfigs: null });
    while (await cursor.hasNext()) {
      const tenantUser = await cursor.next();

      await destinationCollection
        .findOneAndUpdate(
          {
            _id: tenantUser!._id,
          },
          {
            $set: { columnDisplayConfigs },
          },
        )
        .then(() =>
          console.log(
            `Migrated display config for tenant user=${tenantUser!._id} & name=${tenantUser!.displayName} into collection ${destinationCollectionName}`,
          ),
        )
        .catch((error) => {
          console.error(
            `Error updating tenant user ${tenantUser!._id} & name=${tenantUser!.displayName}:`,
            error,
          );
        });
    }

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
