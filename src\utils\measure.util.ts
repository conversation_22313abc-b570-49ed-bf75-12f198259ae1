import { ConsoleLogger } from '@nestjs/common';
import pidusage from 'pidusage';

export async function measurePerformance<T>(
  fn: () => Promise<T>,
  label: string,
  options?: { monitorInterval?: number },
): Promise<T> {
  console.log(`🚀 Starting: ${label}...`);

  const startUsage = await pidusage(process.pid);
  const startTime = process.hrtime();
  let peakMemory = startUsage.memory;
  let peakCPU = startUsage.cpu;
  let peakHeapUsed = process.memoryUsage().heapUsed;

  const monitorInterval = options?.monitorInterval ?? 0;
  let stopMonitoring: (() => void) | null = null;

  if (monitorInterval > 0) {
    stopMonitoring = monitoring(label, monitorInterval);
  }

  const result = await fn();

  if (stopMonitoring) {
    stopMonitoring();
  }

  const endUsage = await pidusage(process.pid);
  const heapUsage = process.memoryUsage();
  const elapsedTime = process.hrtime(startTime);

  peakMemory = Math.max(peakMemory, endUsage.memory);
  peakCPU = Math.max(peakCPU, endUsage.cpu);
  peakHeapUsed = Math.max(peakHeapUsed, heapUsage.heapUsed);

  const cpuUsed = (endUsage.cpu - startUsage.cpu).toFixed(2);
  const ramUsed = ((endUsage.memory - startUsage.memory) / 1024 / 1024).toFixed(
    2,
  );
  const cpuMaxUsed = peakCPU.toFixed(2);
  const ramMaxUsed = ((peakMemory - startUsage.memory) / 1024 / 1024).toFixed(
    2,
  );
  const cpuTime = (endUsage.ctime / 1000).toFixed(2);
  const elapsedSec = (elapsedTime[0] + elapsedTime[1] / 1e9).toFixed(3);

  console.log(
    `📊 Result | ${label}`,
    `\n   🖥️ CPU: ${cpuUsed}% | 🔺 Max CPU: ${cpuMaxUsed}%`,
    `\n   💾 RAM: ${ramUsed} MB | 🔺 Max RAM: ${ramMaxUsed} MB`,
    `\n   ⏳ Elapsed Time: ${elapsedSec}s | ⏲️ CPU Time: ${cpuTime}s`,
  );

  return result;
}

export function monitoring(
  label: string,
  interval: number,
  logs?: string[],
  startTime: number = Date.now(),
): () => void {
  let peakMemory = 0;
  let peakCPU = 0;
  let peakHeapUsed = 0;

  const monitorTimer = setInterval(async () => {
    const usage = await pidusage(process.pid);
    const heapUsage = process.memoryUsage();
    const elapsedTime = ((Date.now() - startTime) / 1000).toFixed(2);
    const logStr = `📡 ${label} | CPU: ${usage.cpu.toFixed(2)}% | RAM: ${(usage.memory / 1024 / 1024).toFixed(2)} MB | Heap: ${(heapUsage.heapUsed / 1024 / 1024).toFixed(2)} MB | Threads: ${usage.pid} | Elapsed: ${elapsedTime}s`;

    if (!logs) console.log(logStr);
    else logs.push(logStr);

    peakMemory = Math.max(peakMemory, usage.memory);
    peakCPU = Math.max(peakCPU, usage.cpu);
    peakHeapUsed = Math.max(peakHeapUsed, heapUsage.heapUsed);
  }, interval);

  return () => {
    clearInterval(monitorTimer);
  };
}

export async function measure(
  label: string,
  logger: ConsoleLogger | Console,
  fn: () => Promise<any>,
) {
  logger.log(`🚀 Starting: ${label}...`);

  const startTime = process.hrtime();
  const result = await fn();
  const elapsedTime = process.hrtime(startTime);

  const elapsedSec = (elapsedTime[0] + elapsedTime[1] / 1e9).toFixed(3);
  logger.log(
    `[P${process.pid}] Core | Monitoring | ${label} | Elapsed Time: ${elapsedSec}s`,
  );

  return result;
}
