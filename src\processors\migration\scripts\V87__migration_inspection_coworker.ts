import { Collection } from 'mongodb';
import { mongo, ObjectId, Types } from 'mongoose';
import * as path from 'path';

import { migrationV2 } from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldInspectionCoworker {
  _id: string;
  estimatedHours: number;
  coworker: ObjectId;
  inspection: ObjectId;
  actualHours: number;
  createdAt: Date;
  updatedAt: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection
    .aggregate()
    .match({
      _id: { $gt: nextId },
    })
    .sort({ _id: 1 })
    .limit(limit);
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldInspectionCoworker[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (item) => {
      return {
        _id: item._id,
        estimatedHours: item.estimatedHours ?? 0,
        actualHours: item.actualHours ?? 0,
        employee: item.coworker,
        job: item.inspection,
        isDeleted: false,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      };
    }),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'inspectionscowokers',
      destinationCollectionName: 'jobemployees',
      pagingFunc,
      tranformDataFunc: tranformDataFunc,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
