import { BadRequestException, Injectable } from '@nestjs/common';

import { InvoiceModel } from '~/modules/invoice/invoice.model';
import { SyncHistoryService } from '~/modules/sync-history/sync-history.service';
import { MyLogger } from '~/processors/logger/logger.service';
import { MicrosoftTeamService } from '~/processors/send-alert/microsoft-team.service';
import {
  transformAfasCostCenter,
  transformAfasCostType,
  transformAfasDebtor,
  transformAfasDirectInvoice,
} from '~/processors/third-party-connector/strategies/afas/afas.helper';
import { AfasService } from '~/processors/third-party-connector/strategies/afas/afas.service';
import { AFASCostCenter } from '~/processors/third-party-connector/strategies/afas/dtos/afas-cost-center.dto';
import { AFASCostType } from '~/processors/third-party-connector/strategies/afas/dtos/afas-cost-type.dto';
import { TransformedAFASDebtor } from '~/processors/third-party-connector/strategies/afas/dtos/afas-debtor.dto';
import { ThirdPartyConnectorStrategy } from '~/processors/third-party-connector/strategies/third-party-connector.strategy';
import {
  SyncHistoryActionType,
  SyncHistoryType,
} from '~/shared/enums/sync-history.enum';
import { ThirdPartyTypeEnum } from '~/shared/enums/third-party-type.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';

@Injectable()
export class ThirdPartyConnectorContext {
  constructor(
    @InjectModel(InvoiceModel)
    private readonly invoiceModel: MongooseModel<InvoiceModel>,
    private readonly afasService: AfasService,
    private readonly syncHistoryService: SyncHistoryService,
    private readonly logger: MyLogger,
    private readonly microsoftTeamService: MicrosoftTeamService,
  ) {}

  private getStrategy(type: ThirdPartyTypeEnum): ThirdPartyConnectorStrategy {
    switch (type) {
      case ThirdPartyTypeEnum.AFAS:
        return this.afasService;
      default:
        throw new BadRequestException('Invalid 3rd party');
    }
  }

  async getCostTypes(payload: {
    [key: string]: any;
    type: ThirdPartyTypeEnum;
  }) {
    const afasCostTypes = await this.getStrategy(payload.type).getCostTypes(
      payload,
    );

    return (afasCostTypes?.rows as AFASCostType[]).map(transformAfasCostType);
  }

  async getCostCenters(payload: {
    [key: string]: any;
    type: ThirdPartyTypeEnum;
  }) {
    const afasCostCenters = await this.getStrategy(payload.type).getCostCenters(
      payload,
    );

    return (afasCostCenters?.rows as AFASCostCenter[]).map(
      transformAfasCostCenter,
    );
  }

  async getDebtors(payload: {
    [key: string]: any;
    type: ThirdPartyTypeEnum;
  }): Promise<TransformedAFASDebtor[]> {
    const afasDebtors = await this.getStrategy(payload.type).getDebtors(
      payload,
    );

    return afasDebtors?.rows?.map(transformAfasDebtor);
  }

  async pushInvoice(payload: {
    [key: string]: any;
    invoice: any;
  }): Promise<any> {
    const { invoice } = payload;
    let requestBody: any = {};

    await this.syncHistoryService.startProcess(
      {
        type: SyncHistoryType.APPROVE_INVOICE,
        actionType: SyncHistoryActionType.INVOICE,
        checkForPending: false,
      },
      async ({ saveExtraData }) => {
        const aggregate = await this.invoiceModel
          .aggregate()
          .match({
            _id: invoice._id,
          })
          .lookup({
            from: 'costlines',
            localField: 'costLines',
            foreignField: '_id',
            as: 'locationCostLines',
            pipeline: [
              {
                $lookup: {
                  from: 'costtypes',
                  localField: 'costType',
                  foreignField: '_id',
                  as: 'costType',
                },
              },
              {
                $lookup: {
                  from: 'jobs',
                  localField: 'job',
                  foreignField: '_id',
                  as: 'job',
                },
              },
              {
                $addFields: {
                  costType: { $first: '$costType' },
                  job: { $first: '$job' },
                  locationId: { $ifNull: ['$location', '$costCenter'] },
                },
              },
              {
                $lookup: {
                  from: 'locations',
                  localField: 'location',
                  foreignField: '_id',
                  as: 'location',
                  pipeline: [
                    {
                      $lookup: {
                        from: 'addresses',
                        localField: 'address',
                        foreignField: '_id',
                        as: 'address',
                      },
                    },
                    {
                      $lookup: {
                        from: 'costcenters',
                        localField: 'costCenter',
                        foreignField: '_id',
                        as: 'costCenter',
                      },
                    },
                    {
                      $addFields: {
                        costCenter: { $first: '$costCenter' },
                        address: { $first: '$address' },
                      },
                    },
                  ],
                },
              },
              {
                $lookup: {
                  from: 'costcenters',
                  localField: 'costCenter',
                  foreignField: '_id',
                  as: 'costCenter',
                },
              },
              {
                $lookup: {
                  from: 'bvcompanies',
                  localField: 'bvCompany',
                  foreignField: '_id',
                  as: 'bvCompany',
                },
              },
              {
                $lookup: {
                  from: 'countries',
                  localField: 'country',
                  foreignField: '_id',
                  as: 'country',
                },
              },
              {
                $addFields: {
                  location: { $first: '$location' },
                  costCenter: { $first: '$costCenter' },
                  bvCompany: { $first: '$bvCompany' },
                  country: { $first: '$country' },
                },
              },
              {
                $group: {
                  _id: '$locationId',
                  costLines: { $push: '$$ROOT' },
                },
              },
            ],
          });

        const approvedInvoice = await this.invoiceModel.populate(aggregate[0], [
          { path: 'contact' },
          {
            path: 'locations',
            populate: [
              { path: 'bvCompany' },
              { path: 'address', populate: 'country' },
              { path: 'costCenter' },
            ],
          },
          {
            path: 'costCenters',
            populate: {
              path: 'locations',
              populate: [
                { path: 'bvCompany' },
                { path: 'address', populate: 'country' },
              ],
            },
          },
        ]);

        requestBody = transformAfasDirectInvoice(approvedInvoice, payload.dlad);
        await saveExtraData({ requestBody });

        return await this.getStrategy(payload.type)
          .pushInvoice({
            ...payload,
            invoice: requestBody,
          })
          .then(() =>
            this.logger.log(
              `Invoice ${invoice.identifier} pushed to 3rd party`,
            ),
          );
      },
      (error: any) => {
        this.logger.error(
          `Invoice ${invoice.identifier} push to 3rd party failed`,
          error,
        );
        this.pushErrorAlertToMicrosoftTeam({
          error,
          body: requestBody,
          summary: `Invoice ${invoice.identifier} push to 3rd party failed`,
          endpoint: `/connectors/FbDirectInvoice`,
          method: 'POST',
        });
      },
    );
  }

  pushErrorAlertToMicrosoftTeam(payload: any) {
    this.microsoftTeamService
      .sendErrorAlert(payload)
      .then(() => {
        this.logger.log('Error alert sent to Microsoft Team');
      })
      .catch((error) => {
        this.logger.error(
          'Error alert sending to Microsoft Team failed',
          error,
        );
      });
  }
}
