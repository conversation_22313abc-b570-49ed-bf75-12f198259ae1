import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { TEAM_MESSAGE_KEYS } from '~/shared/message-keys/team.message-keys';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockTeam, mockTeamData } from '~/test/mocks/team.mock';

import { CreateTeamDto, SortTeamDto, UpdateTeamDto } from '../dtos/team.dto';
import { TeamModel } from '../team.model';
import { TeamService } from '../team.service';
import { teamTest } from './team.dto.test';

describe('TeamService', () => {
  let service: TeamService;

  const teamId2 = new ObjectId();

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [TeamService, ...testInjectModel([TeamModel])],
    }).compile();

    service = module.get(TeamService);

    // Init data
    await Promise.all([
      initMockTeam(),
      initMockTeam({ _id: teamId2, name: 'Team 2' }),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should call fn with payload and return list data', async () => {
      const result = await service.findAll({});
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(teamTest.findAllSchema);
    });

    it('should call fn and return list empty if data not exist', async () => {
      const result = await service.findAll({ pageIndex: 999 });
      expect(result).toBeDefined();
      expect(result.docs.length).toBe(0);
    });
  });

  describe('findOne', () => {
    it('should call fn with id and return data', async () => {
      const result = await service.findOne(mockTeamData._id.toString());
      expect(result).toBeDefined();
      expect(result).toMatchSchema(teamTest.findOneSchema);
    });

    it('should call fn and return null if team not found', async () => {
      const result = await service.findOne(new ObjectId().toString());
      expect(result).toBeNull();
    });
  });

  describe('create', () => {
    const payload: CreateTeamDto = {
      name: 'Team 3',
      isActive: true,
      description: 'desc for team 3',
    };

    it('should throw error when team existed', async () => {
      await expect(
        service.create({ ...payload, name: mockTeamData.name }),
      ).rejects.toThrow(TEAM_MESSAGE_KEYS.ALREADY_EXISTS);
    });

    it('should call fn and create data', async () => {
      const result = await service.create(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(teamTest.findOneSchema);
    });
  });

  describe('update', () => {
    const payload: UpdateTeamDto = {
      id: mockTeamData._id.toString(),
      name: 'Team 1 Updated',
    };

    it('should throw error when not found team', async () => {
      const wrongPayload: UpdateTeamDto = {
        id: new ObjectId().toString(),
        name: 'Team 1 Updated',
      };

      await expect(service.update(wrongPayload)).rejects.toThrow(
        TEAM_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error when isEquipment existed', async () => {
      // reset mock data
      await initMockTeam({ isEquipment: true });

      await expect(service.update(payload)).rejects.toThrow(
        TEAM_MESSAGE_KEYS.IS_EQUIPMENT,
      );
    });

    it('should throw error when name existed', async () => {
      // reset mock data
      await initMockTeam({ isEquipment: false, name: 'Team 1' });

      const wrongPayload: UpdateTeamDto = {
        id: teamId2.toString(),
        name: mockTeamData.name,
      };

      await expect(service.update(wrongPayload)).rejects.toThrow(
        TEAM_MESSAGE_KEYS.ALREADY_EXISTS,
      );
    });

    it('should call fn with payload and update data', async () => {
      const result = await service.update(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(teamTest.updateSchema);
    });
  });

  describe('sort', () => {
    const payload: SortTeamDto = {
      teams: [
        { _id: mockTeamData._id.toString(), position: 1 },
        { _id: teamId2.toString(), position: 2 },
      ],
    };

    it('should throw error when team not found', async () => {
      const wrongPayload: SortTeamDto = {
        teams: [
          { _id: new ObjectId().toString(), position: 1 },
          { _id: teamId2.toString(), position: 2 },
        ],
      };
      await expect(service.sort(wrongPayload)).rejects.toThrow(
        TEAM_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should call fn with payload and sort data', async () => {
      const result = await service.sort(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(teamTest.findAllSchema);
    });
  });
});
