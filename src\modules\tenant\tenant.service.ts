import { BadRequestException, Injectable } from '@nestjs/common';
import { Model } from 'mongoose';

import { TENANT_MESSAGE_KEYS } from '~/shared/message-keys/tenant.message-keys';
import { InjectModel } from '~/transformers/model.transformer';

import { TenantModel } from './tenant.model';

@Injectable()
export class TenantService {
  constructor(
    @InjectModel(TenantModel)
    private readonly tenantModel: Model<TenantModel>,
  ) {}

  async getCompanyInfomation(tenantId) {
    const tenant = await this.tenantModel.findById(tenantId).lean();

    if (!tenant) {
      throw new BadRequestException(TENANT_MESSAGE_KEYS.NOT_FOUND);
    }

    const tenantConfig = tenant.tenantConfigs;
    if (!tenantConfig) {
      throw new BadRequestException(TENANT_MESSAGE_KEYS.CONFIG_NOT_FOUND);
    }

    return tenantConfig['companyInfomation'];
  }
}
