name: SonarQube Analysis
on:
  push:
    branches:
      - main
  pull_request:
    types: [opened, synchronize, reopened]
jobs:
  sonarqube-analysis: 
    name: SonarQube Analysis
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0  

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20.x'
          cache: 'npm'

      - name: Install Dependencies
        run: npm install
      
      - name: Test and Generate Coverage
        run: npm run test:cov -- --coverageReporters=lcov

      - name: Verify Coverage Report Exists
        run: ls -l dist/coverage/lcov.info

      - name: SonarQube Scan
        uses: SonarSource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: "https://sonarqube.infodation.com"
        with:
          args: >
            -Dsonar.projectKey=infodation_ee-acc-v2-core_72623152-7f46-4bd0-958e-af8c31854177
            -Dsonar.javascript.lcov.reportPaths=dist/coverage/lcov.info
            -Dsonar.exclusions=src/processors/migration/**
            -Dsonar.coverage.exclusions=**/test/**,**/scripts/**,**/common/**,**/configs/**,**/constants/**,**/transformers/**,**/main.ts,**/*.dto.ts,**/*.module.ts,**/*.controller.ts,src/utils/**