import { Module } from '@nestjs/common';

import { SyncHistoryModule } from '~/modules/sync-history/sync-history.module';
import { ThirdPartyConnectorModule } from '~/processors/third-party-connector/third-party-connector.module';

import { CosttypeController } from './costtype.controller';
import { CosttypeService } from './costtype.service';

@Module({
  imports: [ThirdPartyConnectorModule, SyncHistoryModule],
  controllers: [CosttypeController],
  providers: [CosttypeService],
})
export class CosttypeModule {}
