import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { CostTypeModel } from '~/modules/costtype/costtype.model';
import { CostTypeType } from '~/shared/enums/cost-type.enum';

const costTypeModel = getModelForClass(CostTypeModel);

export const mockCostTypeData = {
  _id: new ObjectId(),
  createdAt: new Date(),
  isActive: true,
  isDeleted: false,
  isSynced: true,
  itemCode: '8035H',
  name: '<PERSON><PERSON>',
  type: CostTypeType.CONTRACT,
  updatedAt: new Date(),
};

export async function initMockCostType(doc?: any) {
  const { _id, ...rest } = { ...mockCostTypeData, ...doc };
  await costTypeModel.replaceOne({ _id }, rest, { upsert: true });
}
