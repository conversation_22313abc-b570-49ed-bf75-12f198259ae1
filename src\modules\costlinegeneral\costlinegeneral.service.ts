import { BadRequestException, Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import { Model, Types } from 'mongoose';

import { CostLineModel } from '~/modules/costline/costline.model';
import { CostLineStatus } from '~/shared/enums/contract.enum';
import { COSTLINE_GENERAL_MESSAGE_KEYS } from '~/shared/message-keys/costlinegeneral.message-key';
import { InjectModel } from '~/transformers/model.transformer';

import { AgreementLineModel } from '../agreementline/agreementline.model';
import { ContractModel } from '../contract/contract.model';
import { CostLineGeneralModel } from './costlinegeneral.model';
import { DeleteCostliegeneralBodyDto } from './dtos/delete-costlinegeneral.dto';
import { BulkEditCostLineGeneralDto } from './dtos/update-costlinegeneral.dto';

@Injectable()
export class CostlinegeneralService {
  constructor(
    @InjectModel(CostLineGeneralModel)
    private readonly costLineGeneralModel: Model<CostLineGeneralModel>,
    @InjectModel(ContractModel)
    private readonly contractModel: Model<ContractModel>,
    @InjectModel(AgreementLineModel)
    private readonly agreementLineModel: Model<AgreementLineModel>,
    @InjectModel(CostLineModel)
    private readonly costLineModel: Model<CostLineModel>,
  ) {}

  async bulkEdit(payload: BulkEditCostLineGeneralDto) {
    const contract = await this.contractModel.findById(payload.contractId);

    if (!contract) {
      throw new BadRequestException(
        COSTLINE_GENERAL_MESSAGE_KEYS.CONTRACT_NOT_FOUND,
      );
    }

    const exitedCostlinegenerals = await this.costLineGeneralModel
      .find({
        agreementLine: { $in: contract.agreementLines },
      })
      .lean();

    const exitedCostlinegeneralIds = exitedCostlinegenerals.map(
      (exitedCostlinegeneral) => exitedCostlinegeneral._id.toString(),
    );

    const payloadCostlinegeneralIds = payload.costLineGenerals.map(
      (costLineGeneral) => costLineGeneral._id,
    );

    const isValid = payloadCostlinegeneralIds.every((v) =>
      exitedCostlinegeneralIds.includes(v),
    );

    if (!isValid) {
      throw new BadRequestException(
        COSTLINE_GENERAL_MESSAGE_KEYS.SOME_COSTLINE_GENERAL_ARE_NOT_IN_CONTRACT,
      );
    }

    const updateCostLineGeneralPromise = payload.costLineGenerals.map(
      async (costLineGeneral) => {
        this.validateBulkEdit(contract, costLineGeneral);
        const { _id, ...rest } = costLineGeneral;
        if (rest.startDate || rest.endDate) {
          //TODO: Update CostLine
        }

        return this.costLineGeneralModel.findOneAndUpdate(
          { _id: _id },
          { ...rest },
          { new: true },
        );
      },
    );

    return await Promise.all(updateCostLineGeneralPromise);
  }

  async softDelete(payload: DeleteCostliegeneralBodyDto) {
    const { costlinegeneralIds } = payload;
    const costLineGenerals = await this.costLineGeneralModel
      .find({
        _id: { $in: costlinegeneralIds },
        isDeleted: false,
      })
      .lean();

    if (costLineGenerals.length !== costlinegeneralIds.length) {
      throw new BadRequestException(COSTLINE_GENERAL_MESSAGE_KEYS.NOT_FOUND);
    }

    const deleteCostLinePromises = costLineGenerals.map(
      async (costLineGeneral) => {
        await this.costLineModel.updateMany(
          {
            costLineGeneral: new Types.ObjectId(costLineGeneral._id),
            status: CostLineStatus.OPEN,
            isCredit: false,
            approvedAt: { $exists: false },
          },
          {
            isDeleted: true,
            status: CostLineStatus.CANCELED,
            canceledAt: new Date(),
          },
        );
      },
    );

    const updateAgreementLinePromises = costLineGenerals.map(
      async (costLineGeneral) => {
        await this.agreementLineModel.updateOne(
          { _id: costLineGeneral.agreementLine },
          {
            $pull: { costLineGenerals: costLineGeneral._id },
          },
        );
      },
    );

    await Promise.all([
      ...updateAgreementLinePromises,
      ...deleteCostLinePromises,
    ]);

    return this.costLineGeneralModel.updateMany(
      { _id: { $in: costlinegeneralIds } },
      { isDeleted: true },
    );
  }

  //#region Private function
  private validateBulkEdit(contract: any, costLineGeneral: any) {
    const dayJsCostlinegeneralStartDate = costLineGeneral.startDate
      ? dayjs(costLineGeneral.startDate).utc()
      : null;
    const dayJsCostlinegeneralEndDate = costLineGeneral.endDate
      ? dayjs(costLineGeneral.endDate).utc()
      : null;
    const dayJsContractStartDate = dayjs(contract.startDate)
      .utc()
      .startOf('day');
    const dayJsContractEndDate = contract.endDate
      ? dayjs(contract.endDate).utc()
      : null;

    if (
      dayJsCostlinegeneralStartDate &&
      !dayJsCostlinegeneralStartDate
        .startOf('day')
        .isSameOrAfter(dayJsContractStartDate.startOf('day'))
    ) {
      throw new BadRequestException(
        COSTLINE_GENERAL_MESSAGE_KEYS.INVALID_START_DATE,
      );
    }

    if (
      dayJsCostlinegeneralEndDate &&
      !dayJsCostlinegeneralEndDate
        .startOf('day')
        .isSameOrAfter(dayJsContractStartDate.startOf('day'))
    ) {
      throw new BadRequestException(
        COSTLINE_GENERAL_MESSAGE_KEYS.INVALID_END_DATE,
      );
    }
    if (dayJsContractEndDate) {
      if (
        dayJsCostlinegeneralStartDate &&
        !dayJsCostlinegeneralStartDate
          .startOf('day')
          .isSameOrBefore(dayJsContractEndDate.startOf('day'))
      ) {
        throw new BadRequestException(
          COSTLINE_GENERAL_MESSAGE_KEYS.INVALID_START_DATE,
        );
      }

      if (
        dayJsCostlinegeneralEndDate &&
        !dayJsCostlinegeneralEndDate
          .startOf('day')
          .isSameOrBefore(dayJsContractEndDate.startOf('day'))
      ) {
        throw new BadRequestException(
          COSTLINE_GENERAL_MESSAGE_KEYS.INVALID_END_DATE,
        );
      }
    }
  }
  //#endregion
}
