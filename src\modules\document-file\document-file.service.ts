import { HttpService } from '@nestjs/axios';
import {
  BadRequestException,
  ForbiddenException,
  Injectable,
} from '@nestjs/common';
import * as https from 'https';
import { unset } from 'lodash';
import { Model } from 'mongoose';
import { firstValueFrom } from 'rxjs';

import { DocumentFileTypeEnum } from '~/shared/enums/document-file-type.enum';
import { TenantRoleEnum } from '~/shared/enums/tenant-role.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { DOCUMENT_FILE_MESSAGE_KEYS } from '~/shared/message-keys/document-file.message-keys';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery, parseObjectId } from '~/utils';
import { getRoleByDocumentType } from '~/utils/documentRoles.util';

import { LocationService } from '../location/location.service';
import { TenantUserService } from '../tenant-user/tenant-user.service';
import { DocumentFileModel } from './document-file.model';
import {
  DocumentFileCheckExistedParamsDto,
  DocumentFileCreateDto,
  DocumentFileQueryParamDto,
} from './dtos/docment-file.dto';
import { UploadFileModel } from './upload-file.model';

@Injectable()
export class DocumentFileService {
  constructor(
    @InjectModel(DocumentFileModel)
    private documentFileModel: MongooseModel<DocumentFileModel>,
    private locationService: LocationService,
    @InjectModel(UploadFileModel)
    private uploadFileModel: Model<UploadFileModel>,
    private httpService: HttpService,
    private tenantUserService: TenantUserService,
  ) {}

  async createDocumentFile(documentFileCreateDto: DocumentFileCreateDto) {
    const { createdBy, type } = documentFileCreateDto;
    await this.validatePermissionDocumentFile(
      createdBy as unknown as string,
      type,
    );

    const deletedFileName: string[] = [];

    const duplicateUploadFile = documentFileCreateDto.uploadFileIds.filter(
      (item, index, self) => self.indexOf(item) !== index,
    );

    if (duplicateUploadFile.length > 0) {
      throw new BadRequestException(
        DOCUMENT_FILE_MESSAGE_KEYS.DUPLICATE_UPLOAD_FILE,
      );
    }

    const documentFileType = documentFileCreateDto.type;

    if (!documentFileType) {
      throw new BadRequestException(
        DOCUMENT_FILE_MESSAGE_KEYS.CREATED_INVALID_TYPE,
      );
    }

    let location: any;
    if (documentFileCreateDto.location) {
      if (documentFileType !== DocumentFileTypeEnum.NIGHT_REGISTRATIONS) {
        throw new BadRequestException(
          DOCUMENT_FILE_MESSAGE_KEYS.LOCATION_ONLY_FOR_TYPE_NIGTH_REGISTRATION,
        );
      }

      location = await this.locationService.findOne(
        documentFileCreateDto.location.toString(),
      );
    } else if (documentFileType == DocumentFileTypeEnum.NIGHT_REGISTRATIONS) {
      throw new BadRequestException(
        DOCUMENT_FILE_MESSAGE_KEYS.TYPE_NIGTH_REGISTRATION_MUST_HAVE_LOCATION,
      );
    }
    const uploadFiles = await this.uploadFileModel.find({
      _id: { $in: documentFileCreateDto.uploadFileIds },
    });

    if (uploadFiles.length < documentFileCreateDto.uploadFileIds.length) {
      throw new BadRequestException(
        DOCUMENT_FILE_MESSAGE_KEYS.NOT_FOUND_ANY_UPLOAD_FILE,
      );
    }

    const documentFiles = uploadFiles.map((uploadFile) => {
      const documentFile = new DocumentFileModel();
      if (location !== undefined) {
        documentFile.location = location._id;
      }
      documentFile.uploadFile = uploadFile._id;
      documentFile.fileName = uploadFile.originalFilename;
      documentFile.type = documentFileType;
      documentFile.uploaderName = documentFileCreateDto.uploaderName;
      documentFile.createdBy = documentFileCreateDto.createdBy;
      deletedFileName.push(uploadFile.originalFilename);
      return documentFile;
    });

    await this.documentFileModel.deleteMany({
      type: documentFileCreateDto.type,
      fileName: { $in: deletedFileName },
    });

    const result = await this.documentFileModel.insertMany(documentFiles, {
      populate: [
        {
          path: 'uploadFile',
          select: '_id fileName extension mimeType size publicUrl provider',
        },
        {
          path: 'location',
          select: '_id fullAddress',
        },
      ],
    });
    return result;
  }

  async getListDocumentFile(payload: DocumentFileQueryParamDto) {
    // const { user, type } = payload;

    // await this.validatePermissionDocumentFile(
    //   user as string,
    //   type as DocumentFileTypeEnum,
    // );

    // unset user from payload
    unset(payload, 'user');
    const { query, options } = buildQuery(payload, ['fileName']);
    return await this.documentFileModel.paginate(
      { ...query },
      {
        ...options,
        select: '_id type fileName uploaderName createdAt updatedAt',
        populate: [
          {
            path: 'uploadFile',
            select:
              '_id originalFilename extension mimeType size publicUrl provider',
          },
          {
            path: 'location',
            select: '_id fullAddress',
          },
        ],
      },
    );
  }

  async deleteDocumentFile(id: string, headers: any, user: string) {
    const documentFile = await this.documentFileModel
      .findOne({
        _id: parseObjectId(id),
      })
      .populate([
        {
          path: 'uploadFile',
          select: '_id fileName extension mimeType size publicUrl provider',
        },
      ]);

    if (!documentFile) {
      throw new BadRequestException(
        DOCUMENT_FILE_MESSAGE_KEYS.NOT_FOUND_DOCUMENT_FILE,
      );
    }

    await this.validatePermissionDocumentFile(user, documentFile.type);

    try {
      const {
        host: _,
        connection: __,
        'content-length': ____,
        ...forwardHeaders
      } = headers;
      if (documentFile.uploadFile) {
        const url = `${process.env.UPLOAD_SERVICE_URL}/delete/${documentFile.uploadFile._id}`;
        await firstValueFrom(
          this.httpService.delete(url, {
            httpAgent: new https.Agent({
              rejectUnauthorized: false,
            }),
            headers: forwardHeaders,
          }),
        );
      }
    } catch (error: any) {
      console.log(error.message);
      throw error;
    }
    return await this.documentFileModel.deleteOne({ _id: documentFile._id });
  }

  async checkExistedDocumentFile(payload: DocumentFileCheckExistedParamsDto) {
    const { user, type, fileName } = payload;

    await this.validatePermissionDocumentFile(
      user,
      type as DocumentFileTypeEnum,
    );

    const isExisted = await this.documentFileModel
      .findOne({
        type: type,
        fileName: fileName,
      })
      .lean();
    return {
      isExisted: !!isExisted,
    };
  }

  // function check per
  private async validatePermissionOfUser(
    userId: string,
    roles: TenantRoleEnum[],
  ) {
    const isValid = await this.tenantUserService.verifyRoles(userId, roles);

    return isValid;
  }

  private async validatePermissionDocumentFile(
    userId: string,
    type: DocumentFileTypeEnum,
  ) {
    const role = getRoleByDocumentType(type);
    const isValid = await this.validatePermissionOfUser(userId, [role]);

    if (!isValid) {
      throw new ForbiddenException();
    }
  }
}
