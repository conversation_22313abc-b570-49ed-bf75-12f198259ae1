import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { validateUniqueIds } from '~/utils';

const RoleGroupCreateBodySchema = z.strictObject({
  name: z.string().max(256).min(1),
  description: z.string(),
  roles: z
    .array(z.string().refine((val) => isValidObjectId(val)))
    .min(1)
    .refine(validateUniqueIds, 'Duplicate role ids are not allowed'),
});

const RoleGroupUpdateBodySchema = z.strictObject({
  ...RoleGroupCreateBodySchema.omit({}).shape,
  id: z.string().refine((val) => isValidObjectId(val)),
  isActive: z.boolean().optional(),
});

const RoleGroupDeleteSchema = z.strictObject({
  id: z.string().refine((val) => isValidObjectId(val)),
});

export class RoleGroupCreateBodyDto extends createZodDto(
  RoleGroupCreateBodySchema,
) {}

export class RoleGroupUpdateBodyDto extends createZodDto(
  RoleGroupUpdateBodySchema,
) {}

export class RoleGroupDeleteDto extends createZodDto(RoleGroupDeleteSchema) {}
