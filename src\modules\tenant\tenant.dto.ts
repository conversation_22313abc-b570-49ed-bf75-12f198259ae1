import { z } from 'nestjs-zod/z';

export const CompanyInformationSchemas = z.strictObject({
  companyInfomation: z.strictObject({
    name: z.string().optional(),
    brandName: z.string().optional(),
    sign: z.string().optional(),
    telephone: z.string().optional(),
    email: z.string().optional(),
    website: z.string().optional(),
    address1: z.string().optional(),
    address2: z.string().optional(),
    logo: z.string().optional(),
    nrLogo: z.string().optional(),
    linkedinUrl: z.string().optional(),
    banner: z.string().optional(),
    linkedinLogo: z.string().optional(),
    sfnLogo: z.string().optional(),
    displayBanner: z.boolean().optional(),
    displayLinkedIn: z.boolean().optional(),
    typography: z
      .strictObject({
        colors: z
          .strictObject({
            primary: z.string().optional(),
            secondary: z.string().optional(),
            background: z.string().optional(),
            signText: z.string().optional(),
          })
          .optional(),
      })
      .optional(),
  }),
});
