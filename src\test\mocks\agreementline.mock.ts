import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { AgreementLineModel } from '~/modules/agreementline/agreementline.model';
import { agreementlineTest } from '~/modules/agreementline/test/agreenmentline.dto.test';
import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  AgreementLineType,
} from '~/shared/enums/contract.enum';

import { mockUnitData } from './unit.mock';

const agreementLineModel = getModelForClass(AgreementLineModel);
type agreementLineType = z.infer<typeof agreementlineTest.modelSchema>;

export const mockAgreementLineData = {
  _id: new ObjectId(),
  contract: new ObjectId(),
  createdAt: new Date(),
  isDeleted: false,
  period: AgreementLinePeriod.WEEKLY,
  periodType: AgreementLinePeriodType.PERIODIC,
  position: 1,
  type: AgreementLineType.SERVICE,
  units: [mockUnitData._id],
  updatedAt: new Date(),
  costLineGenerals: [new ObjectId()],
};

export async function initMockAgreementLine(doc?: Partial<agreementLineType>) {
  const { _id, ...rest } = { ...mockAgreementLineData, ...doc };
  await agreementLineModel.replaceOne({ _id }, rest, { upsert: true });
}
