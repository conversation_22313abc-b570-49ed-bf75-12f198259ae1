import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';

const TeamSchema = z.strictObject({
  isActive: z.boolean().optional(),
  name: z.string().max(128),
  description: z.string().max(256).optional(),
  position: z.number().min(0).optional(),
});

const CreateTeamSchema = TeamSchema.omit({
  position: true,
});

const UpdateTeamSchema = CreateTeamSchema.extend({
  id: z.string().refine(isValidObjectId),
});

const SortTeamSchema = z.strictObject({
  teams: z.array(
    z.strictObject({
      _id: z.string().refine(isValidObjectId),
      position: z.number().min(0),
    }),
  ),
});

export const TeamQueryParamsSchema = QueryParamsSchema.extend({
  isActive: z.boolean().optional(),
});

export class CreateTeamDto extends createZodDto(CreateTeamSchema) {}

export class UpdateTeamDto extends createZodDto(UpdateTeamSchema) {}

export class SortTeamDto extends createZodDto(SortTeamSchema) {}

export class TeamQueryParamsDto extends createZodDto(TeamQueryParamsSchema) {}
