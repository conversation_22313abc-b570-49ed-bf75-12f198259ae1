import { Types } from 'mongoose';
import { z } from 'zod';

import { JobPointStatusEnum, JobTypeEnum } from '~/shared/enums/job.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isGrouped: z.boolean(),
    status: z.nativeEnum(JobPointStatusEnum),
    description: z.string(),
    notes: z.string().optional(),
    position: z.number(),
    images: z.array(z.string()).optional(),
    unit: z.instanceof(Types.ObjectId),
    job: z.instanceof(Types.ObjectId),
    actions: z
      .array(
        z.object({
          isGrouped: z.boolean(),
          description: z.string(),
          type: z.nativeEnum(JobTypeEnum),
          images: z.array(z.string()),
        }),
      )
      .optional(),
    costLines: z.array(z.instanceof(Types.ObjectId)).optional(),
  })
  .extend(baseModelTestSchema);

export const jobPointTest = { modelSchema };
