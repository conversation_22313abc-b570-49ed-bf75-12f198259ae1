import { fakerNL } from '@faker-js/faker';
import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import dayjs from 'dayjs';
import { omit, pick } from 'lodash';
import { ObjectId } from 'mongodb';

import { AddressModel } from '~/modules/address/address.model';
import { AddressService } from '~/modules/address/address.service';
import { BvCompanyModel } from '~/modules/bvcompany/bvcompany.model';
import { AddressDto } from '~/modules/contact/dtos/contact.dto';
import { CostCenterModel } from '~/modules/costcenter/costcenter.model';
import { CostlineService } from '~/modules/costline/costline.service';
import { CountryModel } from '~/modules/country/country.model';
import { CountryService } from '~/modules/country/country.service';
import {
  CreateLocationDto,
  UpdateLocationDto,
} from '~/modules/location/dtos/location.dto';
import { LocationAdditionalGroupNameModel } from '~/modules/location-addtional/location-additional-group-name.model';
import { LocationAdditionalService } from '~/modules/location-addtional/location-additonal.service';
import { LocationAdditionalModel } from '~/modules/location-addtional/location-addtional.model';
import { RegionModel } from '~/modules/region/region.model';
import { TeamModel } from '~/modules/team/team.model';
import { TenantModel } from '~/modules/tenant/tenant.model';
import { unitTest } from '~/modules/unit/test/unit.dto.test';
import { UnitModel } from '~/modules/unit/unit.model';
import { UnitService } from '~/modules/unit/unit.service';
import { EventEmitterSender } from '~/processors/event-emitter/event-emitter.sender';
import { GoogleMapService } from '~/processors/google-map/google-map.service';
import { ContractType } from '~/shared/enums/contract.enum';
import { LocationAdditionalGroupName } from '~/shared/enums/location-additional.enum';
import { LOCATION_MESSAGE_KEYS } from '~/shared/message-keys/location.message-key';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockAddress, mockAddressData } from '~/test/mocks/address.mock';
import { initMockAgreementLine } from '~/test/mocks/agreementline.mock';
import {
  initMockBvCompany,
  mockBvCompanyData,
} from '~/test/mocks/bvcompany.mock';
import { initMockContact, mockContactData } from '~/test/mocks/contact.mock';
import { initMockContract, mockContractData } from '~/test/mocks/contract.mock';
import {
  initMockCostCenter,
  mockCostCenterData,
} from '~/test/mocks/costcenter.mock';
import {
  initMockCostlineGeneral,
  mockCostlineGeneralData,
} from '~/test/mocks/costlinegeneral.mock';
import { initMockCountry, mockCountryData } from '~/test/mocks/country.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { mockLocationAdditionalData } from '~/test/mocks/locationadditional.mock';
import { mockLocationAdditionalGroupNameData } from '~/test/mocks/locationadditionalgroupname.mock';
import { initMockRegion, mockRegionData } from '~/test/mocks/region.mock';
import { initMockTeam, mockTeamData } from '~/test/mocks/team.mock';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';

import { LocationModel } from '../location.model';
import { LocationService } from '../location.service';
import { locationTest } from './location.dto.test';

describe('LocationService', () => {
  let service: LocationService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        LocationService,
        ...testInjectProviders([
          // Models
          TenantModel,
          UnitModel,
          LocationModel,
          AddressModel,
          CostCenterModel,
          TeamModel,
          BvCompanyModel,
          LocationAdditionalModel,
          LocationAdditionalGroupNameModel,
          RegionModel,
          CountryModel,
          // Services
          CountryService,
          CostlineService,
          EventEmitterSender,
          LocationAdditionalService,
        ]),
        AddressService,
        UnitService,
        {
          provide: GoogleMapService,
          useValue: {
            getGeocode: jest.fn().mockResolvedValue({
              data: {
                results: [
                  {
                    geometry: {
                      location: {
                        lat: fakerNL.location.latitude(),
                        lng: fakerNL.location.longitude(),
                      },
                    },
                  },
                ],
              },
            }),
          },
        },
        ConfigService,
      ],
    }).compile();

    service = module.get<LocationService>(LocationService);

    await Promise.all([
      initMockBvCompany(),
      initMockCostCenter({ locations: [mockLocationData._id] }),
      initMockUnit(),
      initMockUnit({
        _id: new ObjectId(),
        name: 'Unit 2',
        isRoot: false,
        parent: mockUnitData._id,
      }),
      initMockLocation(),
      initMockAddress(),
      initMockRegion(),
      initMockCountry(),
      initMockTeam(),
      initMockContract({ type: ContractType.CREDITOR, endDate: undefined }),
      initMockContract({ type: ContractType.RENTING, endDate: undefined }),
      initMockContact(),
      initMockAgreementLine({
        costLineGenerals: [mockCostlineGeneralData._id],
        contract: mockContractData._id,
      }),
      initMockCostlineGeneral(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should return list of locations', async () => {
      const result = await service.findAll({});
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(locationTest.findAllSchema);
    });

    it('should return list of locations with platform mb provided', async () => {
      const result = await service.findAll({
        platform: 'mb',
        lastSyncedAt: dayjs().subtract(1, 'day').toISOString(),
      });
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(locationTest.findAllMbSchema);
    });

    it('should return empty list if no locations found', async () => {
      const result = await service.findAll({ pageIndex: 99 });
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });

  describe('findOne', () => {
    it('should throw error if location not found', async () => {
      const invalidId = new ObjectId().toString();
      await expect(service.findOne(invalidId)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should return location by id', async () => {
      const id = mockLocationData._id.toString();
      const result = await service.findOne(id);
      expect(result).toBeDefined();
      expect(result._id.toString()).toBe(id);
      expect(result).toMatchSchema(locationTest.findOneSchema);
    });
  });

  describe('create', () => {
    const createLocationRequest: CreateLocationDto = {
      bvCompany: mockBvCompanyData._id.toString(),
      isActive: true,
      team: mockTeamData._id.toString(),
      email: fakerNL.internet.email(),
      isRenting: true,
      isService: false,
      costCenter: mockCostCenterData._id.toString(),
      parkingSpaces: 0,
      maximumStayDuration: 6,
      address: {
        country: mockCountryData._id.toString(),
        region: mockRegionData._id.toString(),
        city: 'Yharnam',
        street: 'Lorem Ipsum',
        number: '99',
        suffix: undefined,
        postalCode: '9999 AA',
      },
      locationOf: [],
    };
    let location: any;

    it('should throw an error if required fields are missing', async () => {
      jest
        .spyOn(service['bvCompanyModel'], 'findById')
        .mockResolvedValueOnce(null);

      await expect(service.create(createLocationRequest)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.BV_COMPANY_NOT_FOUND,
      );
    });

    it('should throw an error if cost center is not found', async () => {
      const invalidRequest: CreateLocationDto = {
        ...createLocationRequest,
        costCenter: new ObjectId().toString(),
      };

      await expect(service.create(invalidRequest)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.COST_CENTER_NOT_FOUND,
      );
    });

    it('should throw error if team not found', async () => {
      const invalidRequest: CreateLocationDto = {
        ...createLocationRequest,
        team: new ObjectId().toString(),
      };

      await expect(service.create(invalidRequest)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.TEAM_NOT_FOUND,
      );
    });

    it('should throw error if cost center already has a location with different BV company', async () => {
      const bvCompanyId = new ObjectId();
      await initMockBvCompany({ _id: bvCompanyId });

      const invalidRequest: CreateLocationDto = {
        ...createLocationRequest,
        bvCompany: bvCompanyId.toString(),
      };
      await expect(service.create(invalidRequest)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.BV_COMPANY_CONFLICT,
      );
    });

    it('should create location successfully', async () => {
      location = await service.create(createLocationRequest);

      expect(location).toBeDefined();
      expect(location.isActive).toBe(true);
      expect(location.bvCompany?._id?.toString()).toBe(
        mockBvCompanyData._id.toString(),
      );
      expect(location.team?._id?.toString()).toBe(mockTeamData._id.toString());
      expect(location.costCenter?._id?.toString()).toBe(
        mockCostCenterData._id.toString(),
      );
      expect(location.address.country?._id?.toString()).toBe(
        mockCountryData._id.toString(),
      );
      expect(location.address.region?._id?.toString()).toBe(
        mockRegionData._id.toString(),
      );
      expect(location.address.city).toBe('Yharnam');
      expect(location.address.street).toBe('Lorem Ipsum');
      expect(location.address.number).toBe('99');
      expect(location.address.postalCode).toBe('9999 AA');
      expect(location.fullAddress).toBe('Yharnam, Lorem Ipsum 99');
      expect(location.parkingSpaces).toBe(0);
      expect(location.maximumStayDuration).toBe(6);
      expect(location.isRenting).toBe(true);
      expect(location.isService).toBe(false);
      expect(location.email).toBe(createLocationRequest.email);
      expect(location.locationOf).toEqual([]);
    });
  });

  describe('update', () => {
    const updateLocationRequest: UpdateLocationDto = {
      id: '', // This will be set later,
      isActive: true,
      team: mockTeamData._id.toString(),
      email: fakerNL.internet.email(),
      isRenting: true,
      isService: false,
      costCenter: mockCostCenterData._id.toString(),
      parkingSpaces: 0,
      maximumStayDuration: 6,
      address: {
        country: mockCountryData._id.toString(),
        region: mockRegionData._id.toString(),
        city: 'Yharnam',
        street: 'Lorem Ipsum',
        number: '99',
        suffix: 'Tres',
        postalCode: '9999 AA',
      },
      locationOf: [],
    };

    beforeAll(async () => {
      const createdLocation = await service.create({
        ...updateLocationRequest,
        bvCompany: mockBvCompanyData._id.toString(),
        parkingSpaces: 10,
        maximumStayDuration: 12,
        address: {
          ...updateLocationRequest.address,
          city: 'Yharnam_CREATE',
          street: 'Lorem Ipsum_CREATE',
          suffix: 'Tres_CREATE',
        },
      });
      updateLocationRequest.id = createdLocation._id.toString();
    });

    it('should throw error if location not found', async () => {
      const invalidId = new ObjectId().toString();
      await expect(service.update({ id: invalidId } as any)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if costcenter not found', async () => {
      const invalidRequest: UpdateLocationDto = {
        ...updateLocationRequest,
        costCenter: new ObjectId().toString(),
      };

      await expect(service.update(invalidRequest)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.COST_CENTER_NOT_FOUND,
      );
    });

    it('should throw error if team not found', async () => {
      const invalidRequest: UpdateLocationDto = {
        ...updateLocationRequest,
        team: new ObjectId().toString(),
      };

      await expect(service.update(invalidRequest)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.TEAM_NOT_FOUND,
      );
    });

    it('update location should successfully update the existing location', async () => {
      const costCenterId = new ObjectId();
      await initMockCostCenter({ _id: costCenterId });

      const updatedLocation = await service.update({
        ...updateLocationRequest,
        costCenter: costCenterId.toString(),
      });

      expect(updatedLocation).toBeDefined();
      expect(updatedLocation._id.toString()).toBe(updateLocationRequest.id);
      expect(updatedLocation.isActive).toBe(true);
      expect(updatedLocation.isRenting).toBe(true);
      expect(updatedLocation.isService).toBe(false);
      expect(updatedLocation.email).toBe(updateLocationRequest.email);
      expect(updatedLocation.parkingSpaces).toBe(0);
      expect(updatedLocation.maximumStayDuration).toBe(6);
      expect(updatedLocation.locationOf).toEqual([]);
      expect(updatedLocation.bvCompany?._id?.toString()).toBe(
        mockBvCompanyData._id.toString(),
      );
      expect(updatedLocation.team?._id?.toString()).toBe(
        mockTeamData._id.toString(),
      );
      expect(updatedLocation.costCenter?._id?.toString()).toBe(
        costCenterId._id.toString(),
      );
      expect(updatedLocation.address.country?._id?.toString()).toBe(
        mockCountryData._id.toString(),
      );
      expect(updatedLocation.address.region?._id?.toString()).toBe(
        mockRegionData._id.toString(),
      );
      expect(updatedLocation.address.city).toBe('Yharnam');
      expect(updatedLocation.address.street).toBe('Lorem Ipsum');
      expect(updatedLocation.address.number).toBe('99');
      expect(updatedLocation.address.suffix).toBe('Tres');
      expect(updatedLocation.address.postalCode).toBe('9999 AA');
      expect(updatedLocation.fullAddress).toBe('Yharnam, Lorem Ipsum 99 Tres');
    });
  });

  describe('export', () => {
    const locationAdditionalData = {
      ...omit(mockLocationAdditionalData, ['groupName', 'contact']),
      groupName: {
        ...pick(mockLocationAdditionalGroupNameData, [
          '_id',
          'position',
          'dutchDescription',
        ]),
        description: LocationAdditionalGroupName.FIRE_INSTALLATION,
      },
      contact: {
        ...pick(mockContactData, ['_id', 'displayName', 'organizationNames']),
      },
    };

    it('should export location with id provided', async () => {
      jest
        .spyOn(service['locationAdditionalService'], 'getAdditionalByLocation')
        .mockResolvedValue([locationAdditionalData]);

      const id = mockLocationData._id.toString();
      const result = await service.export(id);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('fileName');
      expect(result).toHaveProperty('content');
    });
  });

  describe('getUnitsOfTree', () => {
    const root = {
      ...mockUnitData,
      name: 'Root',
      maxOccupants: 0,
      parent: null,
    };
    const child = {
      ...root,
      _id: new ObjectId(),
      name: 'Child',
      isRoot: false,
      maxOccupants: 10,
      parent: root._id,
    };
    const grandChild = {
      ...child,
      _id: new ObjectId(),
      name: 'GrandChild',
      isRoot: false,
      maxOccupants: 20,
      parent: child._id,
    };

    it('should return units of tree', async () => {
      service.getUnitsOfTree([root]);
      const result = service.getUnitsOfTree([root, child, grandChild]);
      expect(result).toBeDefined();
      expect(result[0]).toHaveProperty('maxOccupants', 20);
      expect(result[0]).toHaveProperty('name', 'Child GrandChild');
    });
  });

  describe('getFullAddress', () => {
    const testId = new ObjectId().toString();
    const address: AddressDto = {
      city: 'Test City',
      street: 'Test Street',
      number: '123',
      suffix: 'Apt 1B',
      country: testId,
      region: testId,
      postalCode: '12345',
    };
    // Refactored: avoid nested template literals
    let fullAddress =
      address.city + ', ' + address.street + ' ' + address.number;
    if (address.suffix) {
      fullAddress += ' ' + address.suffix;
    }

    it('should return full address if it does not exist', async () => {
      const result = await service.getFullAddress(address, testId);
      expect(result).toBeDefined();
      expect(result).toBe(fullAddress);
    });

    it('should throw error if address is existed', async () => {
      await initMockLocation({
        _id: new ObjectId(),
        fullAddress,
      });

      await expect(service.getFullAddress(address, testId)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.ADDRESS_EXISTED,
      );
    });
  });

  describe('getLocationGeo', () => {
    it('should return default geo if address is not provided', async () => {
      const result = await service.getLocationGeo();
      expect(result).toBeDefined();
      expect(result).toEqual({ lng: 0, lat: 0 });
    });

    it('should return default geo if address not found', async () => {
      const result = await service.getLocationGeo(new ObjectId().toString());
      expect(result).toBeDefined();
      expect(result).toEqual({ lng: 0, lat: 0 });
    });

    it('should return default geo if geoCode not found', async () => {
      jest.spyOn(service['googleMapService'], 'getGeocode').mockResolvedValue({
        data: {
          results: [],
        },
      } as any);

      const result = await service.getLocationGeo(
        mockAddressData._id.toString(),
      );
      expect(result).toBeDefined();
      expect(result).toEqual({ lng: 0, lat: 0 });
    });

    it('should return default geo if geoCode is not valid', async () => {
      jest.spyOn(service['googleMapService'], 'getGeocode').mockResolvedValue({
        data: {
          results: [
            {
              geometry: {
                location: null,
              },
            },
          ],
        },
      } as any);

      const result = await service.getLocationGeo(
        mockAddressData._id.toString(),
      );
      expect(result).toBeDefined();
      expect(result).toEqual({ lng: 0, lat: 0 });
    });
  });

  describe('getUnitsOfLocation', () => {
    it('should throw error if location not found', async () => {
      const invalidId = new ObjectId().toString();
      await expect(
        service.getUnitsOfLocation({ id: invalidId } as any),
      ).rejects.toThrow(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    });
  });

  describe('updateUnitsOfLocation', () => {
    let locationId: string;
    let rootUnit: any;
    let childUnit: any;

    beforeAll(async () => {
      const createdLocation = await service.create({
        bvCompany: mockBvCompanyData._id.toString(),
        isActive: true,
        team: mockTeamData._id.toString(),
        email: fakerNL.internet.email(),
        isRenting: true,
        isService: false,
        costCenter: mockCostCenterData._id.toString(),
        parkingSpaces: 0,
        maximumStayDuration: 6,
        address: {
          country: mockCountryData._id.toString(),
          region: mockRegionData._id.toString(),
          city: 'UnitTestCity',
          street: 'UnitTestStreet',
          number: '1',
          postalCode: '1111 AA',
        },
        locationOf: [],
      });
      locationId = createdLocation._id.toString();

      const units = await service.getUnitsOfLocation({ id: locationId });
      rootUnit = units.find((u: any) => u.isRoot);

      childUnit = {
        _id: new ObjectId().toString(),
        name: 'ChildUnit',
        isRoot: false,
        parent: rootUnit._id.toString(),
        maxOccupants: 5,
        maxArea: 10,
        isActive: true,
        isDeleted: false,
      };
    });

    it('should throw error if location not found', async () => {
      await expect(
        service.updateUnitsOfLocation({
          id: new ObjectId().toString(),
          units: [],
        }),
      ).rejects.toThrow(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw error if duplicate unit id', async () => {
      const duplicateUnit = { ...childUnit, _id: new ObjectId() };
      await expect(
        service.updateUnitsOfLocation({
          id: locationId,
          units: [duplicateUnit, duplicateUnit],
        }),
      ).rejects.toThrow(LOCATION_MESSAGE_KEYS.DUPLICATE_UNIT_ID);
    });

    it('should update units of location successfully', async () => {
      const result = await service.updateUnitsOfLocation({
        id: locationId,
        units: [childUnit, { ...childUnit, _id: new ObjectId().toString() }],
      });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(unitTest.findUnitsOfLocationSchema);
    });
  });

  describe('reCalculateMaxOccupantsArea', () => {
    it('should throw error if location not found', async () => {
      const invalidId = new ObjectId().toString();
      await expect(
        service.reCalculateMaxOccupantsArea(invalidId),
      ).rejects.toThrow(LOCATION_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw error if root unit not found', async () => {
      jest
        .spyOn(service['unitService'], 'findRootUnitOfLocation')
        .mockResolvedValue(null);

      const id = mockLocationData._id.toString();

      await expect(service.reCalculateMaxOccupantsArea(id)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.ROOT_UNIT_NOT_FOUND,
      );
    });

    it('should return if unit is root', async () => {
      jest
        .spyOn(service['unitService'], 'findRootUnitOfLocation')
        .mockResolvedValue(mockUnitData as any);
      await initMockUnit({
        parent: mockUnitData._id,
      });

      const id = mockLocationData._id.toString();
      const result = await service.reCalculateMaxOccupantsArea(id);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        locationTest.reCalculateMaxOccupantsAreaSchema,
      );
    });
  });

  describe('getLocationNearBy', () => {
    it('should return locations nearby with default params', async () => {
      const result = await service.getLocationNearBy({});
      expect(result).toBeDefined();
      expect(result).toMatchSchema(locationTest.getLocationNearBySChema);
    });

    it('should filter locations by team', async () => {
      const result = await service.getLocationNearBy({
        team: mockTeamData._id.toString(),
      });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(locationTest.getLocationNearBySChema);
      expect(result[0].team?._id?.toString()).toBe(mockTeamData._id.toString());
    });

    it('should filter locations by locationType renting', async () => {
      const result = await service.getLocationNearBy({
        locationType: 'renting',
      });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(locationTest.getLocationNearBySChema);
      expect(result[0].isRenting).toBe(true);
    });

    it('should filter locations by locationType service', async () => {
      await initMockLocation({ isService: true });

      const result = await service.getLocationNearBy({
        locationType: 'service',
      });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(locationTest.getLocationNearBySChema);
      expect(result[0].isService).toBe(true);
    });

    it('should filter locations by vacant beds', async () => {
      const mockLocation = {
        _id: new ObjectId(),
        fullAddress: 'Mock Address',
        isRenting: true,
        isService: false,
        geo: mockLocationData.geo,
        team: { _id: mockTeamData._id.toString(), name: 'Mock Team' },
        vacantBeds: 2,
        totalBeds: 10,
      };
      jest.spyOn(service['locationModel'], 'aggregate').mockReturnValueOnce({
        exec: jest.fn().mockResolvedValue([mockLocation]),
      } as any);

      const result = await service.getLocationNearBy({ isVacanted: 'true' });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(locationTest.getLocationNearBySChema);
      expect(result[0].vacantBeds).toBeGreaterThan(0);
    });
  });

  describe('getTotalStatsOccupantOfAllActiveLocations', () => {
    it('should return total stats of all active locations', async () => {
      const mockLocations = [
        {
          _id: new ObjectId(),
          totalBeds: 10,
          vacantBeds: 2,
        },
        {
          _id: new ObjectId(),
          totalBeds: 5,
          vacantBeds: 0,
        },
        {
          _id: new ObjectId(),
          totalBeds: 8,
          vacantBeds: 3,
        },
      ];
      jest
        .spyOn(service, 'getLocationNearBy')
        .mockResolvedValueOnce(mockLocations as any);

      const result = await service.getTotalStatsOccupantOfAllActiveLocations();
      expect(result).toBeDefined();
      expect(result.totalActive).toBe(3);
      expect(result.totalBeds).toBe(23);
      expect(result.totalVacantBeds).toBe(5);
      expect(Array.isArray(result.vacantLocations)).toBe(true);
      expect(result.vacantLocations.length).toBe(2);
      expect(result.vacantLocations).toContain(mockLocations[0]._id);
      expect(result.vacantLocations).toContain(mockLocations[2]._id);
    });
  });
});
