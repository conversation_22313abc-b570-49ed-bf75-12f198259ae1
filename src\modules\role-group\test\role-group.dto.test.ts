import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isActive: z.boolean(),
    name: z.string(),
    description: z.string().optional(),
    roles: z.array(z.instanceof(ObjectId)).optional(),
    isDeleted: z.boolean().default(false),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(
  z.strictObject({
    _id: z.instanceof(ObjectId),
    isActive: z.boolean(),
    name: z.string(),
    description: z.string().optional(),
    roles: z.array(
      z.strictObject({
        _id: z.instanceof(ObjectId),
        key: z.string(),
        name: z.string(),
      }),
    ),
  }),
);

export const roleGroupTest = { modelSchema, findAllSchema };
