import { registerAs } from '@nestjs/config';

export default registerAs('email', () => ({
  host: `${process.env.MAIL_HOST || 'localhost'}`,
  port: parseInt(process.env.MAIL_PORT || '25', 10),
  user: `${process.env.MAIL_USER || ''}`,
  password: `${process.env.MAIL_PASSWORD || ''}`,
  isSecure: process.env.MAIL_SECURE === 'true',
  from: `${process.env.MAIL_FROM || 'no-reply@local'}`,
  tls: {
    rejectUnauthorized: process.env.MAIL_TLS_REJECT_UNAUTHORIZED === 'true',
  },
}));
