import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { teamTest } from '~/modules/team/test/team.dto.test';
import { ThisWeekDefaultDisplay } from '~/shared/enums/tenant-user.enum';
import { WorkingDays } from '~/shared/enums/working-days.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

import { Gender, Language } from '../tenant-user.model';

const displayMappingSchema = z.object({
  field: z.string(),
  isShown: z.boolean(),
  mutable: z.boolean(),
});

const modelSchema = z
  .object({
    isActive: z.boolean(),
    isRoot: z.boolean(),
    image: z.string().optional(),
    displayName: z.string(),
    firstName: z.string(),
    lastName: z.string(),
    username: z.string(),
    password: z.string(),
    email: z.string(),
    gender: z.nativeEnum(Gender),
    language: z.nativeEnum(Language),
    phone1: z.string(),
    phone2: z.string().optional(),
    roles: z.any(),
    team: z.instanceof(ObjectId),
    position: z.number(),
    oddWeeks: z.array(z.nativeEnum(WorkingDays)),
    evenWeeks: z.array(z.nativeEnum(WorkingDays)),
    thisWeekDefaultDisplay: z.nativeEnum(ThisWeekDefaultDisplay),
    lastChangePasswordAt: z.date().optional(),
    token: z.instanceof(ObjectId),
    tenant: z.instanceof(ObjectId),
    columnDisplayConfigs: z.object({
      inspectionOverview: z.array(displayMappingSchema).nullish().optional(),
      maintenanceOverview: z.array(displayMappingSchema).nullish().optional(),
      cleaningOverview: z.array(displayMappingSchema).nullish().optional(),
    }),
    saveToPhotos: z.boolean(),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(
  modelSchema
    .pick({
      _id: true,
      image: true,
      isActive: true,
      username: true,
      email: true,
      lastName: true,
      firstName: true,
      displayName: true,
      oddWeeks: true,
      evenWeeks: true,
    })
    .extend({
      team: teamTest.modelSchema.pick({
        _id: true,
        name: true,
      }),
    }),
);

const findOneSchema = modelSchema
  .omit({
    createdAt: true,
    updatedAt: true,
    tenant: true,
    token: true,
    isRoot: true,
    password: true,
  })
  .extend({
    team: teamTest.modelSchema.pick({
      _id: true,
      name: true,
    }),
    roles: z.array(
      z.object({
        key: z.string(),
        name: z.string(),
      }),
    ),
  });

const getTeamManagementSchema = z.array(
  modelSchema
    .pick({
      _id: true,
      displayName: true,
      lastName: true,
      firstName: true,
      roles: true,
      position: true,
      evenWeeks: true,
      oddWeeks: true,
    })
    .extend({
      roles: z.array(
        z.object({
          key: z.string(),
          name: z.string(),
        }),
      ),
    }),
);

const exportEmployeesSchema = z.object({
  content: z.string(),
  fileName: z.string().regex(/^employees-\d{2}-\d{2}-\d{4}\.csv$/),
});

const getDisplayConfigSchema = z.array(
  z.object({
    field: z.string(),
    isShown: z.boolean(),
    mutable: z.boolean(),
  }),
);

export const tenantUserTest = {
  modelSchema,
  findAllSchema,
  findOneSchema,
  getTeamManagementSchema,
  exportEmployeesSchema,
  getDisplayConfigSchema,
};
