import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { SCHEDULE_MESSAGES } from '~/shared/messages/schedule.message';

import { ScheduleService } from './schedule.service';

@Controller('schedule')
export class ScheduleController {
  constructor(private readonly scheduleService: ScheduleService) {}

  @MessagePattern({ cmd: SCHEDULE_MESSAGES.GENERATE_PERIODIC_JOB })
  public async generatePeriodicJob(@Payload() data: any) {
    return this.scheduleService.generatePeriodicJob(data);
  }

  @MessagePattern({ cmd: SCHEDULE_MESSAGES.DEACTIVE_CONTRACT })
  public async deactiveContractByCronJob() {
    return this.scheduleService.deactiveContractByCronJob();
  }

  @MessagePattern({ cmd: SCHEDULE_MESSAGES.GENERATE_COSTLINE_BY_PERIOD })
  public async generateCostLineByPeriodByCronJob(@Payload() data: any) {
    return this.scheduleService.generateCostLineByPeriodByCronJob(data);
  }

  @MessagePattern({ cmd: SCHEDULE_MESSAGES.SEND_MAIL_RESIDENT })
  public async sendMailResident(@Payload() data: any) {
    return this.scheduleService.sendMailResident(data);
  }

  @MessagePattern({ cmd: SCHEDULE_MESSAGES.CALCULATE_HIRED_LOCATIONS })
  public async calculateHiredLocations(@Payload() data: any) {
    return this.scheduleService.calculateHiredLocations(data);
  }

  @MessagePattern({
    cmd: SCHEDULE_MESSAGES.SEND_EMAIL_RESIDENTS_HAS_EXCEEDED_MAXIMUM_LENGTH_OF_STAY,
  })
  public async sendEmailResidentHasExceededMaximumLengthOfStay(
    @Payload() data: any,
  ) {
    return this.scheduleService.sendEmailResidentHasExceededMaximumLengthOfStay(
      data,
    );
  }
}
