import pathLibrary from 'path';
const imageExtensions = [
  '.xbm',
  '.tif',
  '.pjp',
  '.apng',
  '.svgz',
  '.jpg',
  '.jpeg',
  '.ico',
  '.tiff',
  '.gif',
  '.sgv',
  '.jfif',
  '.webp',
  '.png',
  '.bmp',
  '.pjpeg',
  '.avif',
];

export const isValidImageExtension = (imageUrl: string) => {
  const fileName = pathLibrary.basename(imageUrl);
  const extension = pathLibrary.extname(fileName);
  return imageExtensions.includes(extension.toLowerCase());
};
