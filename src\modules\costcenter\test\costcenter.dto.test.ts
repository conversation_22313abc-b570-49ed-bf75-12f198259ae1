import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { locationTest } from '~/modules/location/test/location.dto.test';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isActive: z.boolean(),
    identifier: z.string(),
    name: z.string(),
    locations: z.array(z.instanceof(ObjectId)).optional(),
    isSynced: z.boolean(),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(modelSchema);

const findOneSchema = modelSchema.extend({
  locations: z.array(locationTest.modelSchema).optional(),
});

export const costCenterTest = {
  modelSchema,
  findAllSchema,
  findOneSchema,
};
