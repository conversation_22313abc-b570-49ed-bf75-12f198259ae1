import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { TEAM_MESSAGES } from '~/shared/messages/team.message';

import { CreateTeamDto, SortTeamDto, UpdateTeamDto } from './dtos/team.dto';
import { TeamService } from './team.service';

@Controller('team')
export class TeamController {
  constructor(private readonly teamService: TeamService) {}

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(QueryParamsDto))
  @MessagePattern({ cmd: TEAM_MESSAGES.GET_TEAMS })
  public async findAll(@Payload() data: QueryParamsDto) {
    return this.teamService.findAll(data);
  }

  @UsePipes(new ZodValidationPipe(CreateTeamDto))
  @MessagePattern({ cmd: TEAM_MESSAGES.CREATE_TEAM })
  public async create(@Payload() data: CreateTeamDto) {
    return this.teamService.create(data);
  }

  @UsePipes(new ZodValidationPipe(UpdateTeamDto))
  @MessagePattern({ cmd: TEAM_MESSAGES.UPDATE_TEAM })
  public async update(@Payload() data: UpdateTeamDto) {
    return this.teamService.update(data);
  }

  @UsePipes(new ZodValidationPipe(SortTeamDto))
  @MessagePattern({ cmd: TEAM_MESSAGES.SORT_TEAMS })
  public async sort(@Payload() data: SortTeamDto) {
    return this.teamService.sort(data);
  }
}
