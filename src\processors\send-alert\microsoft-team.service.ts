import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { firstValueFrom } from 'rxjs';

import { TenantModel } from '~/modules/tenant/tenant.model';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';

import { getErrorAlertMessageTemplate } from './alert.util';

@Injectable()
export class MicrosoftTeamService {
  constructor(
    @InjectModel(TenantModel)
    private readonly tenantModel: MongooseModel<TenantModel>,
    private readonly configService: ConfigService,
    private readonly httpService: HttpService,
  ) {}

  async sendErrorAlert(payload) {
    const webHookUrl = this.configService.get<string>(
      'microsoftTeam.webHookUrl',
    );

    if (!webHookUrl) {
      throw new Error('Microsoft Team Webhook URL is not defined');
    }

    const tenant = await this.tenantModel.findOne({}).lean();

    const messageTemplate = getErrorAlertMessageTemplate({
      ...payload,
      tenantName: tenant?.name,
    });

    await firstValueFrom(this.httpService.post(webHookUrl, messageTemplate));
    // Send alert to Microsoft Team
  }
}
