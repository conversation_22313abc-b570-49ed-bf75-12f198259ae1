import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { BvCompanyModel } from '~/modules/bvcompany/bvcompany.model';
import { ContactModel } from '~/modules/contact/contact.model';
import { ContractModel } from '~/modules/contract/contract.model';
import { CostCenterModel } from '~/modules/costcenter/costcenter.model';
import { CostLineGeneralModel } from '~/modules/costlinegeneral/costlinegeneral.model';
import { CostTypeModel } from '~/modules/costtype/costtype.model';
import { CountryModel } from '~/modules/country/country.model';
import { LocationModel } from '~/modules/location/location.model';
import { ContactRole } from '~/shared/enums/contact.enum';
import { ContractType, CostLineStatus } from '~/shared/enums/contract.enum';
import { CostTypeType } from '~/shared/enums/cost-type.enum';
import { CONTACT_MESSAGE_KEYS } from '~/shared/message-keys/contact.message-key';
import { COSTLINE_MESSAGE_KEYS } from '~/shared/message-keys/costline.message-keys';
import { COSTTYPE_MESSAGE_KEYS } from '~/shared/message-keys/costtype.message-keys';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockBvCompany,
  mockBvCompanyData,
} from '~/test/mocks/bvcompany.mock';
import { initMockContact, mockContactData } from '~/test/mocks/contact.mock';
import {
  initMockCostCenter,
  mockCostCenterData,
} from '~/test/mocks/costcenter.mock';
import { initMockCostline, mockCostlineData } from '~/test/mocks/costline.mock';
import { initMockCostType, mockCostTypeData } from '~/test/mocks/costtype.mock';
import { initMockCountry } from '~/test/mocks/country.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';

import { CostLineModel } from '../costline.model';
import { CostlineService } from '../costline.service';
import { CreditCostLineCreateBodyDto } from '../dtos/create-credit-costline.dto';
import {
  CreateCustomCostLinesDto,
  DeleteCostlineDto,
} from '../dtos/create-custom-costline.dto';
import { costlineTest } from './costline.dto.test';

const deCountryId = new ObjectId();
const deCountryCode = 'de';

describe('CostlineService', () => {
  let service: CostlineService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        CostlineService,
        ...testInjectModel([
          CostLineModel,
          ContactModel,
          CostTypeModel,
          LocationModel,
          CostCenterModel,
          CostLineGeneralModel,
          ContractModel,
          CountryModel,
          BvCompanyModel,
        ]),
      ],
    }).compile();

    service = module.get(CostlineService);

    // Init data
    await Promise.all([
      initMockCostline(),
      initMockCountry({ _id: deCountryId, code: deCountryCode }),
      initMockCostType(),
      initMockContact(),
      initMockCostCenter(),
      initMockLocation(),
      initMockCostCenter(),
      initMockBvCompany(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('createCustomCostLineLinkToLocation', () => {
    it('should call fn with payload and return data', async () => {
      const payload: CreateCustomCostLinesDto = {
        contact: new ObjectId().toString(),
        location: new ObjectId().toString(),
        costLines: [
          {
            costType: new ObjectId().toString(),
            description: 'This is description of costline',
            price: 65,
            quantity: 1,
          },
        ],
      };

      // Mock pass validation data
      passValidationWhenCreateCustomCostLine();

      const result = await service.createCustomCostLine(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(costlineTest.createCustomCostLine);
    });
  });

  describe('createCustomCostLineLinkToCostCenter', () => {
    it('should call fn with payload and return data', async () => {
      const payload: CreateCustomCostLinesDto = {
        contact: new ObjectId().toString(),
        costCenter: new ObjectId().toString(),
        country: deCountryId.toString(),
        bvCompany: mockBvCompanyData._id.toString(),
        costLines: [
          {
            costType: new ObjectId().toString(),
            description: 'This is description of costline link to cost center',
            price: 100,
            quantity: 1234,
          },
        ],
      };

      // Mock pass validation data
      passValidationWhenCreateCustomCostLine();

      const result = await service.createCustomCostLine(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(costlineTest.createCustomCostLine);
    });

    it('should throw error when country not found', async () => {
      // Prepare data
      await initMockContact({ contactRole: ContactRole.DEBTOR });

      const payload: CreateCustomCostLinesDto = {
        contact: mockContactData._id.toString(),
        costCenter: new ObjectId().toString(),
        country: new ObjectId().toString(),
        costLines: [
          {
            costType: mockCostTypeData._id.toString(),
            description: 'This is description of costline link to cost center',
            price: 100,
            quantity: 1234,
          },
        ],
      };

      passValidationWhenCreateCustomCostLine();

      await expect(service.createCustomCostLine(payload)).rejects.toThrow(
        COSTLINE_MESSAGE_KEYS.COUNTRY_NOT_FOUND,
      );
    });

    it('should throw error when bvCompany not found', async () => {
      const payload: CreateCustomCostLinesDto = {
        contact: mockContactData._id.toString(),
        costCenter: mockCostCenterData._id.toString(),
        country: deCountryId.toString(),
        bvCompany: new ObjectId().toString(),
        costLines: [
          {
            costType: mockCostTypeData._id.toString(),
            description: 'This is description of costline link to cost center',
            price: 100,
            quantity: 1234,
          },
        ],
      };

      passValidationWhenCreateCustomCostLine();

      await expect(service.createCustomCostLine(payload)).rejects.toThrow(
        COSTLINE_MESSAGE_KEYS.BV_COMPANY_NOT_FOUND,
      );
    });
  });

  describe('deleteCostLine', () => {
    it('should throw error when costline not found', async () => {
      const payload: DeleteCostlineDto = {
        id: new ObjectId().toString(),
      };

      await expect(service.deleteCostLine(payload)).rejects.toThrow(
        COSTLINE_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should delete and return when costline exists', async () => {
      const payload: DeleteCostlineDto = {
        id: mockCostlineData._id.toString(),
      };

      const result = await service.deleteCostLine(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockCostlineData._id);
      expect(result).toMatchSchema(costlineTest.deleteCostLineSchema);

      // restore data
      await initMockCostline();
    });
  });

  describe('createCreditCostLines', () => {
    const costLine1 = { ...mockCostlineData, status: CostLineStatus.CLOSED };
    const costLine2 = {
      ...mockCostlineData,
      _id: new ObjectId(),
      status: CostLineStatus.CLOSED,
    };

    beforeEach(async () => {
      // reset data
      await Promise.all([
        initMockCostline(costLine1),
        initMockCostline(costLine2),
      ]);
    });

    it('should throw error when costline not found', async () => {
      const payload: CreditCostLineCreateBodyDto = {
        costLines: [
          { _id: new ObjectId().toString(), description: '', price: 0 },
        ],
      };
      await expect(service.createCreditCostLines(payload)).rejects.toThrow(
        COSTLINE_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error when costline have different types', async () => {
      // Update data for testcase
      await Promise.all([
        initMockCostline(costLine1),
        initMockCostline({ ...costLine2, type: ContractType.JOB }),
      ]);

      const payload: CreditCostLineCreateBodyDto = {
        costLines: [
          { _id: costLine1._id.toString(), description: '', price: 0 },
          { _id: costLine2._id.toString(), description: '', price: 0 },
        ],
      };

      await expect(service.createCreditCostLines(payload)).rejects.toThrow(
        COSTLINE_MESSAGE_KEYS.COST_LINES_MUST_HAVE_SAME_TYPE,
      );
    });

    it('should create credit costline when valid input', async () => {
      const payload: CreditCostLineCreateBodyDto = {
        costLines: [
          { _id: costLine1._id.toString(), description: '', price: 0 },
          { _id: costLine2._id.toString(), description: '', price: 0 },
        ],
      };

      const result = await service.createCreditCostLines(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(costlineTest.createCreditCostLinesSchema);
    });
  });

  describe('validateAllCostTypesExist', () => {
    const costType1 = new ObjectId();
    const costType2 = new ObjectId();

    const payload: { costType: string }[] = [
      { costType: costType1.toString() },
      { costType: costType2.toString() },
    ];

    it('should throw error when costtype not found', async () => {
      const wrongPayload: { costType: string }[] = [
        { costType: new ObjectId().toString() },
        { costType: new ObjectId().toString() },
      ];

      await expect(
        service.validateAllCostTypesExist(wrongPayload),
      ).rejects.toThrow(COSTTYPE_MESSAGE_KEYS.NOT_FOUND);
    });

    it('should throw error when costtype not job or custom', async () => {
      // Prepare data
      await Promise.all([
        initMockCostType({ _id: costType1, type: CostTypeType.CONTRACT }),
        initMockCostType({ _id: costType2, type: CostTypeType.CONTRACT }),
      ]);

      await expect(service.validateAllCostTypesExist(payload)).rejects.toThrow(
        COSTTYPE_MESSAGE_KEYS.NOT_JOB_OR_CUSTOM,
      );
    });

    it('should call fn with payload and pass when payload valid', async () => {
      // Prepare data
      await Promise.all([
        initMockCostType({ _id: costType1, type: CostTypeType.JOB_OR_CUSTOM }),
        initMockCostType({ _id: costType2, type: CostTypeType.JOB_OR_CUSTOM }),
      ]);

      const result = await service.validateAllCostTypesExist(payload);
      expect(result).toBeUndefined();
    });
  });

  describe('validateDebtorContact', () => {
    const contactId = mockContactData._id.toString();
    it('should throw error when contact not found', async () => {
      await expect(
        service.validateDebtorContact(new ObjectId().toString()),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND);
    });

    it('should throw error when contact not type debtor', async () => {
      // Prepare data
      await initMockContact();

      await expect(service.validateDebtorContact(contactId)).rejects.toThrow(
        COSTLINE_MESSAGE_KEYS.CUSTOM_COSTLINE_INVALID_DEBTOR,
      );
    });

    it('should call fn with payload and pass when payload valid', async () => {
      // Prepare data
      await initMockContact({ contactRole: ContactRole.DEBTOR });

      const result = await service.validateDebtorContact(contactId);
      expect(result).toBeUndefined();
    });
  });

  describe('validateThenGetLocationOrCostCenterType', () => {
    const locationId = mockLocationData._id.toString();
    const costCenterId = mockCostCenterData._id.toString();
    const locationWrongId = new ObjectId().toString();
    const costCenterWrongId = new ObjectId().toString();

    it('should throw error when location or costcenter not found', async () => {
      await expect(
        service.validateThenGetLocationOrCostCenterType(
          locationWrongId,
          costCenterWrongId,
        ),
      ).rejects.toThrow(
        COSTLINE_MESSAGE_KEYS.CUSTOM_COSTLINE_LOCATION_OR_COSTCENTER_NOT_FOUND,
      );
    });

    it('should call fn and pass when location or costcenter found', async () => {
      const result = await service.validateThenGetLocationOrCostCenterType(
        locationId,
        costCenterId,
      );
      expect(result).toBeUndefined();
    });
  });

  const passValidationWhenCreateCustomCostLine = () => {
    jest
      .spyOn(service, 'validateAllCostTypesExist')
      .mockResolvedValue(undefined);
    jest.spyOn(service, 'validateDebtorContact').mockResolvedValue(undefined);
    jest
      .spyOn(service, 'validateThenGetLocationOrCostCenterType')
      .mockResolvedValue(undefined);
  };
});
