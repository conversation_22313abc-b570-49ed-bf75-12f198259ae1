import { Modu<PERSON> } from '@nestjs/common';

import { EmailTemplateModule } from '~/modules/email-template/email-template.module';

import { LoggerModule } from '../logger/logger.module';
import { EmailController } from './email.controller';
import { EmailService } from './email.service';

@Module({
  imports: [LoggerModule, EmailTemplateModule],
  providers: [EmailService],
  exports: [EmailService],
  controllers: [EmailController],
})
export class EmailModule {}
