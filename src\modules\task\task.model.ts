import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import {
  EquipmentDocument,
  EquipmentModel,
} from '~/modules/equipment/equipment.model';
import {
  TenantUserDocument,
  TenantUserModel,
} from '~/modules/tenant-user/tenant-user.model';
import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { TaskCategory, TaskType } from '~/shared/enums/task.enum';
import { BaseModel } from '~/shared/models/base.model';

export type TaskUserDocument = DocumentType<TaskModel>;

@modelOptions({
  options: {
    customName: 'Task',
  },
})
@index({ employees: 1, startDate: 1, endDate: 1 })
@index({ startDate: 1, endDate: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class TaskModel extends BaseModel {
  @prop()
  title?: string;

  @prop()
  description?: string;

  @prop()
  destination?: string;

  @prop({ enum: TaskType, default: TaskType.PLANNING })
  type!: TaskType;

  @prop({ required: true, enum: TaskCategory })
  category!: TaskCategory;

  @prop({ required: true })
  startDate?: Date;

  @prop({ required: true })
  endDate?: Date;

  @prop({ ref: () => EquipmentModel })
  cars?: Ref<EquipmentDocument>[];

  @prop({ ref: () => EquipmentModel })
  devices?: Ref<EquipmentDocument>[];

  @prop({ required: true, ref: () => TenantUserModel })
  employees!: Ref<TenantUserDocument>[];
}
