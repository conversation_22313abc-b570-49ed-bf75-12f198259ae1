import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

export class SendEmailDto {
  to!: string[];
  subject!: string;
  text?: string;
  html?: string;
  bcc?: string[];
  cc?: string[];
  attachments?: any[];
}

export const SendTestEmailSchema = z.strictObject({
  to: z.array(z.string()).min(1),
  bcc: z.array(z.string()).optional(),
  cc: z.array(z.string()).optional(),
});

const SendErrorEmailToLentoSchema = z.strictObject({
  PROCESS_NAME: z.string(),
  TENANT: z.string(),
  ERROR_CODE: z.string().optional(),
  ERROR_MESSAGE: z.string().optional(),
  ERROR_DETAIL: z.string().optional(),
  REQUEST_BODY: z.string().optional(),
  TIME: z.dateString(),
});

export const SendTestEmailZodDto = createZodDto(SendTestEmailSchema);
export class SendErrorEmailToLentoDto extends createZodDto(
  SendErrorEmailToLentoSchema,
) {}
