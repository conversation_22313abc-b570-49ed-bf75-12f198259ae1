import { Module } from '@nestjs/common';

import { NightRegistrationModule } from '../night-registration/night-registration.module';
import { StatsOccupantModule } from '../stats-occupant/stats-occupant.module';
import { LocationEnergyService } from './location-energy.service';
import { ReportController } from './report.controller';
import { ReportService } from './report.service';

@Module({
  imports: [StatsOccupantModule, NightRegistrationModule],
  controllers: [ReportController],
  providers: [ReportService, LocationEnergyService],
})
export class ReportModule {}
