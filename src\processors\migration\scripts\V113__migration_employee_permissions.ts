import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';
import { sumDecimal } from '~/utils';

const fileName = path.basename(__filename);

interface OldEmployee {
  _id: string;
  roles: any[];
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection.aggregate([
    {
      $match: {
        _id: { $gt: nextId },
      },
    },
    { $sort: { _id: 1 } },
    { $limit: limit },
    {
      $lookup: {
        from: 'users-permissions_role',
        localField: 'roles',
        foreignField: '_id',
        as: 'roles',
      },
    },
  ]);
};

const transformData = ({
  data,
  context,
}: {
  data: OldEmployee[];
  context: any;
}) => {
  return Promise.all(
    data.map(async (employee: OldEmployee) => {
      const roles = await context
        .destinationClient!.db()
        .collection('tenantroles')
        .find({
          key: {
            $in: employee.roles.map((role: any) => {
              const { type } = role;
              switch (type) {
                case 'cleaning_viewer':
                  return 'cleaning_reviewer';

                case 'contact_information_manager':
                  return 'contact_infomation_manager';

                default:
                  return type;
              }
            }),
          },
        })
        .toArray();

      const roleDecimalSum = sumDecimal(roles.map((role: any) => role.decimal));

      return omitNull({
        _id: employee._id,
        roles: roleDecimalSum,
      });
    }),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'users-permissions_user',
      destinationCollectionName: 'tenantusers',
      pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
      isUpsert: false,
    });
    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
