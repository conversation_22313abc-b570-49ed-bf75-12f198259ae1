import { ArgumentMetadata, Injectable } from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';

import { ContactRole, ContactType } from '~/shared/enums/contact.enum';

import { ContactTypeDto } from '../dtos/contact.dto';
import {
  CreateDebtorOrganizationDto,
  CreateDebtorPersonDto,
} from '../dtos/debtor-contact.dto';
import { CreatePersonContactDto } from '../dtos/person-contact.dto';
import {
  CreateSupplierOrganizationDto,
  CreateSupplierPersonDto,
} from '../dtos/supplier-contact.dto';

@Injectable()
export class CreateContactZodValidationPipe extends ZodValidationPipe {
  async transform(value: any, metadata: ArgumentMetadata) {
    // Select the appropriate schema based on the type in the request body
    let schema;

    switch (value.contactRole) {
      case ContactRole.PERSON:
        schema = CreatePersonContactDto;
        break;
      case ContactRole.DEBTOR:
        if (!value.contactType) {
          schema = ContactTypeDto;
        } else {
          schema =
            value.contactType === ContactType.ORGANIZATION
              ? CreateDebtorOrganizationDto
              : CreateDebtorPersonDto;
        }
        break;
      case ContactRole.SUPPLIER:
        if (!value.contactType) {
          schema = ContactTypeDto;
        } else {
          schema =
            value.contactType === ContactType.ORGANIZATION
              ? CreateSupplierOrganizationDto
              : CreateSupplierPersonDto;
        }
        break;
      default:
        throw new Error('Invalid type');
    }

    // Call the parent class's transform() method with the selected schema
    return super.transform(value, { ...metadata, metatype: schema });
  }
}
