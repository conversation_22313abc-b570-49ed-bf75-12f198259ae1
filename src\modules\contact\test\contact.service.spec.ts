import { ConfigService } from '@nestjs/config';
import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';

import { AddressModel } from '~/modules/address/address.model';
import { ContractModel } from '~/modules/contract/contract.model';
import { CountryModel } from '~/modules/country/country.model';
import { CountryService } from '~/modules/country/country.service';
import { LocationModel } from '~/modules/location/location.model';
import { RegionModel } from '~/modules/region/region.model';
import { SyncHistoryService } from '~/modules/sync-history/sync-history.service';
import { MyLogger } from '~/processors/logger/logger.service';
import { ThirdPartyConnectorContext } from '~/processors/third-party-connector/strategies/third-party-connector.context';
import {
  ContactRole,
  ContactType,
  SupplierCategoryEnum,
  SupplierType,
} from '~/shared/enums/contact.enum';
import { ContractType } from '~/shared/enums/contract.enum';
import { Gender, Language } from '~/shared/enums/tenant-user.enum';
import { CONTACT_MESSAGE_KEYS } from '~/shared/message-keys/contact.message-key';
import { LOCATION_MESSAGE_KEYS } from '~/shared/message-keys/location.message-key';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockAddress, mockAddressData } from '~/test/mocks/address.mock';
import { initMockContact, mockContactData } from '~/test/mocks/contact.mock';
import { initMockContract } from '~/test/mocks/contract.mock';
import { initMockCostCenter } from '~/test/mocks/costcenter.mock';
import { initMockCountry, mockCountryData } from '~/test/mocks/country.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { initMockRegion, mockRegionData } from '~/test/mocks/region.mock';

import { ContactModel, ContactOrganizationPersonModel } from '../contact.model';
import { ContactService } from '../contact.service';
import { GetContactDetailDto, GetContactDto } from '../dtos/contact.dto';
import { ContactContext } from '../strategies/contact-context';
import { ContactDebtorStrategy } from '../strategies/contact-debtor.strategy';
import { ContactOrganizationStrategy } from '../strategies/contact-organization.strategy';
import { ContactPersonStrategy } from '../strategies/contact-person.strategy';
import { ContactSupplierStrategy } from '../strategies/contact-supplier.strategy';
import { contactTest } from './contact.dto.test';

describe('ContactService', () => {
  let service: ContactService;
  let countryService: CountryService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        ContactService,
        ...testInjectProviders([
          ContactModel,
          ContractModel,
          RegionModel,
          ContactOrganizationPersonModel,
          CountryModel,
          AddressModel,
          ThirdPartyConnectorContext,
          SyncHistoryService,
          CountryService,
          LocationModel,
        ]),
        ContactContext,
        ContactOrganizationStrategy,
        ContactPersonStrategy,
        ContactDebtorStrategy,
        ContactSupplierStrategy,
        ConfigService,
        MyLogger,
      ],
    }).compile();

    service = module.get(ContactService);
    countryService = module.get(CountryService);

    // Init data
    await Promise.all([
      initMockContact({ address1: mockAddressData._id }),
      initMockContract(),
      initMockLocation(),
      initMockCostCenter(),
      initMockRegion(),
      initMockAddress(),
      initMockCountry(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('[DEBTOR] should throw error if location not found', async () => {
      const payload: GetContactDto = {
        contactRole: ContactRole.DEBTOR,
        location: new ObjectId(),
      };

      await expect(service.findAll(payload)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('[DEBTOR] should call fn and return list data', async () => {
      // Prepare data
      await Promise.all([
        initMockContact({
          address1: mockAddressData._id,
          contactRole: ContactRole.DEBTOR,
        }),
        initMockContract({ type: ContractType.RENTING }),
      ]);

      const payload: GetContactDto = {
        contactRole: ContactRole.DEBTOR,
      };

      const result: any = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(contactTest.findAllTypeDebtorSchema);
    });

    it('[DEBTOR] should call fn and return list data has location in payload', async () => {
      const payload: GetContactDto = {
        contactRole: ContactRole.DEBTOR,
        location: mockLocationData._id,
      };

      const result: any = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(contactTest.findAllTypeDebtorSchema);
    });

    it('[ORGANIZATION] should call fn and return list data', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.ORGANIZATION,
      });

      const payload = {
        contactRole: ContactRole.ORGANIZATION,
        contactRoles: [ContactRole.ORGANIZATION],
      };

      const result: any = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(
        contactTest.findAllTypeOrganizationSchema,
      );
    });

    it('[PERSON] should call fn and return list data', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.PERSON,
        contactType: ContactType.PERSON,
      });

      const payload: GetContactDto = {
        contactRole: ContactRole.PERSON,
      };

      const result: any = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(contactTest.findAllTypePersonSchema);
    });

    it('[SUPPLIER] should throw error if location not found', async () => {
      // Prepare data
      await Promise.all([
        initMockContact({
          contactRole: ContactRole.SUPPLIER,
        }),
        initMockContract({ type: ContractType.CREDITOR }),
      ]);

      const payload: GetContactDto = {
        contactRole: ContactRole.SUPPLIER,
        location: new ObjectId(),
      };

      await expect(service.findAll(payload)).rejects.toThrow(
        LOCATION_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('[SUPPLIER] should call fn and return list data', async () => {
      const payload: GetContactDto = {
        contactRole: ContactRole.SUPPLIER,
      };

      const result: any = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(contactTest.findAllTypeSupplierSchema);
    });

    it('[SUPPLIER] should call fn and return list data has location in payload', async () => {
      const payload: GetContactDto = {
        contactRole: ContactRole.SUPPLIER,
        location: mockLocationData._id,
      };

      const result: any = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(contactTest.findAllTypeSupplierSchema);
    });

    it('should return list empty when data not found', async () => {
      const payload: GetContactDto = {
        contactRole: ContactRole.DEBTOR,
        pageIndex: 99,
      };

      const result: any = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });

  describe('findOne', () => {
    it('DEBTOR] should call fn and return data for contact type PERSON', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.DEBTOR,
        contactType: ContactType.PERSON,
        address1: mockAddressData._id,
      });

      const payload: GetContactDetailDto = {
        contactRole: ContactRole.DEBTOR,
        id: mockContactData._id.toString(),
      };

      const result: any = await service.findOne(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContactData._id);
      expect(result).toMatchSchema(contactTest.findOneDebtorTypePersonSchema);
    });

    it('DEBTOR] should call fn and return data for contact type ORGANIZATION', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.DEBTOR,
        contactType: ContactType.ORGANIZATION,
        address1: mockAddressData._id,
      });

      const payload: GetContactDetailDto = {
        contactRole: ContactRole.DEBTOR,
        id: mockContactData._id.toString(),
      };

      const result: any = await service.findOne(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContactData._id);
      expect(result).toMatchSchema(
        contactTest.findOneDebtorTypeOrganizationSchema,
      );
    });

    it('[PERSON] should call fn and return data', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.PERSON,
        contactType: ContactType.PERSON,
        address1: mockAddressData._id,
      });

      const payload: GetContactDetailDto = {
        contactRole: ContactRole.PERSON,
        id: mockContactData._id.toString(),
      };

      const result: any = await service.findOne(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContactData._id);
      expect(result).toMatchSchema(contactTest.findOnePersonSchema);
    });

    it('[SUPPLIER] should call fn and return data', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.SUPPLIER,
        contactType: ContactType.ORGANIZATION,
        address1: mockAddressData._id,
      });

      const payload: GetContactDetailDto = {
        contactRole: ContactRole.SUPPLIER,
        id: mockContactData._id.toString(),
      };

      const result: any = await service.findOne(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContactData._id);
      expect(result).toMatchSchema(contactTest.findOneSupplierSchema);
    });

    it('should throw error if contact not found', async () => {
      const payload: GetContactDetailDto = {
        contactRole: ContactRole.DEBTOR,
        id: new ObjectId().toString(),
      };

      await expect(service.findOne(payload)).rejects.toThrow(
        CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND,
      );
    });
  });

  describe('create', () => {
    const payload = {
      contactType: ContactType.PERSON,
      contactRole: ContactRole.DEBTOR,
      displayName: 'John Doe',
      email: '<EMAIL>',
      warningEmail: '<EMAIL>',
      phone1: '84123456789',
      address1: {
        city: 'Hanoi',
        street: '123 Main St',
        number: '10A',
        suffix: 'Block B',
        postalCode: '100000',
        country: mockCountryData._id.toString(),
        region: mockRegionData._id.toString(),
      },
      remark: 'Test remark',
      paymentTermRentInvoice: 15,
      paymentTermJobInvoice: 30,
      invoiceEmail: '<EMAIL>',
      invoiceReference: 'INV-2024-001',
      collectiveJobInvoice: true,
      collectiveCustomInvoice: false,
      gender: Gender.MALE,
      language: Language.EN,
      firstName: 'John',
      lastName: 'Doe',
    };

    it('should throw error if country not found', async () => {
      // Mock method
      countryService.validatePostalCode = jest.fn().mockResolvedValue(null);

      await expect(
        service.create({ address1: { region: new ObjectId().toString() } }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.INVALID_REGION);
    });

    it('should throw error if region not found', async () => {
      await expect(
        service.create({
          address1: {
            region: mockRegionData._id.toString(),
            country: new ObjectId().toString(),
          },
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.INVALID_REGION);
    });

    it('[DEBTOR] should throw error if contact type organizations has name existed', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.DEBTOR,
        contactType: ContactType.ORGANIZATION,
      });

      await expect(
        service.create({
          ...payload,
          contactType: ContactType.ORGANIZATION,
          name: mockContactData.displayName,
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.EXITED_ORGANIZATION_NAME);
    });

    it('[DEBTOR] should throw error if contact type person has organizations not found', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.DEBTOR,
        contactType: ContactType.PERSON,
      });

      await expect(
        service.create({
          ...payload,
          organizations: [{ contactId: new ObjectId().toString() }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.ORG_NOT_FOUND);
    });

    it('[DEBTOR] should throw error if contact type person has organizations invalid type', async () => {
      // Prepare data
      const id1 = new ObjectId();
      await initMockContact({
        _id: id1,
        identifier: nanoid(),
        contactRole: ContactRole.DEBTOR,
        contactType: ContactType.PERSON,
      });

      await expect(
        service.create({
          ...payload,
          organizations: [{ contactId: id1 }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.INVALID_ORG_TYPE);
    });

    it('[DEBTOR] should call fn with payload and create data', async () => {
      const result = await service.create(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(contactTest.findOneDebtorTypePersonSchema);
    });

    it('[PERSON] should throw error if organizations not found', async () => {
      await expect(
        service.create({
          ...payload,
          contactRole: ContactRole.PERSON,
          organizations: [{ contactId: new ObjectId().toString() }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.ORG_NOT_FOUND);
    });

    it('[PERSON] should throw error if organizations invalid type', async () => {
      // Prepare data
      const id1 = new ObjectId();
      await initMockContact({
        _id: id1,
        identifier: nanoid(),
        contactRole: ContactRole.DEBTOR,
        contactType: ContactType.PERSON,
      });

      await expect(
        service.create({
          ...payload,
          contactRole: ContactRole.PERSON,
          organizations: [{ contactId: id1 }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.INVALID_ORG_TYPE);
    });

    it('[PERSON] should call fn with payload and create data', async () => {
      const result = await service.create({
        ...payload,
        contactRole: ContactRole.PERSON,
      });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(contactTest.findOnePersonSchema);
    });

    it('[SUPPLIER] should throw error if type regular miss supplier category', async () => {
      await expect(
        service.create({
          ...payload,
          contactRole: ContactRole.SUPPLIER,
          supplierType: SupplierType.REGULAR,
        }),
      ).rejects.toThrow(
        CONTACT_MESSAGE_KEYS.SUPPLIER_TYPE_REGULAR_MUST_HAVE_CATEGORY,
      );
    });

    it('[SUPPLIER] should throw error if type rental has supplier category', async () => {
      await expect(
        service.create({
          ...payload,
          contactRole: ContactRole.SUPPLIER,
          supplierType: SupplierType.RENTAL,
          supplierCategory: SupplierCategoryEnum.AFVAL,
        }),
      ).rejects.toThrow(
        CONTACT_MESSAGE_KEYS.SUPPLIER_TYPE_RENTAL_MUST_NOT_HAVE_CATEGORY,
      );
    });

    it('[SUPPLIER] should throw error if contact type organizations has name existed', async () => {
      // Prepare data
      await initMockContact({ contactRole: ContactRole.SUPPLIER });

      await expect(
        service.create({
          ...payload,
          name: mockContactData.displayName,
          contactRole: ContactRole.SUPPLIER,
          contactType: ContactType.ORGANIZATION,
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.EXITED_ORGANIZATION_NAME);
    });

    it('[SUPPLIER] should throw error if organizations not found', async () => {
      await expect(
        service.create({
          ...payload,
          contactRole: ContactRole.SUPPLIER,
          organizations: [{ contactId: new ObjectId().toString() }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.ORG_NOT_FOUND);
    });

    it('[SUPPLIER] should throw error if organizations invalid type', async () => {
      // Prepare data
      const id1 = new ObjectId();
      await initMockContact({
        _id: id1,
        identifier: nanoid(),
        contactRole: ContactRole.SUPPLIER,
        contactType: ContactType.PERSON,
      });

      await expect(
        service.create({
          ...payload,
          contactRole: ContactRole.SUPPLIER,
          organizations: [{ contactId: id1 }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.INVALID_ORG_TYPE);
    });

    it('[SUPPLIER] should call fn with payload and create data', async () => {
      const result = await service.create({
        ...payload,
        contactRole: ContactRole.SUPPLIER,
        supplierType: SupplierType.REGULAR,
        supplierCategory: SupplierCategoryEnum.AFVAL,
      });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(contactTest.findOneSupplierSchema);
    });
  });

  describe('update', () => {
    const payload = {
      id: mockContactData._id.toString(),
      contactRole: ContactRole.DEBTOR,
      phone1: '0987654321',
    };

    it('[DEBTOR] should throw error if contact not found', async () => {
      await expect(
        service.update({ ...payload, id: new ObjectId().toString() }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND);
    });

    it('[DEBTOR] should throw error if contact invalid type', async () => {
      // Prepare data
      await expect(
        service.update({ ...payload, contactType: ContactType.PERSON }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.UPDATE_CONTACT_TYPE_NOT_ALLOWED);
    });

    it('[DEBTOR] should throw error if contact type organizations has name existed', async () => {
      // Prepare data
      await initMockContact({
        _id: new ObjectId(),
        identifier: nanoid(),
        contactRole: ContactRole.DEBTOR,
        contactType: ContactType.ORGANIZATION,
      });

      await expect(
        service.update({
          ...payload,
          name: mockContactData.displayName,
          contactType: ContactType.ORGANIZATION,
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.EXITED_ORGANIZATION_NAME);
    });

    it('[DEBTOR] should throw error if organizations not found', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.DEBTOR,
        contactType: ContactType.PERSON,
      });

      await expect(
        service.update({
          ...payload,
          contactType: ContactType.PERSON,
          organizations: [{ contactId: new ObjectId().toString() }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.ORG_NOT_FOUND);
    });

    it('[DEBTOR] should throw error if organizations invalid type', async () => {
      // Prepare data
      const id1 = new ObjectId();
      await initMockContact({
        _id: id1,
        identifier: nanoid(),
        contactRole: ContactRole.DEBTOR,
        contactType: ContactType.PERSON,
      });

      await expect(
        service.update({
          ...payload,
          contactType: ContactType.PERSON,
          organizations: [{ contactId: id1 }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.INVALID_ORG_TYPE);
    });

    it('[DEBTOR] should call fn with payload and update data', async () => {
      const result = await service.update({
        ...payload,
        contactType: ContactType.PERSON,
      });
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContactData._id);
      expect(result).toMatchSchema(contactTest.findOneDebtorTypePersonSchema);
    });

    it('[PERSON] should throw error if contact not found', async () => {
      payload.contactRole = ContactRole.PERSON;

      await expect(
        service.update({ ...payload, id: new ObjectId().toString() }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND);
    });

    it('[PERSON] should throw error if organizations not found', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.PERSON,
        contactType: ContactType.PERSON,
      });

      await expect(
        service.update({
          ...payload,
          organizations: [{ contactId: new ObjectId().toString() }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.ORG_NOT_FOUND);
    });

    it('[PERSON] should throw error if organizations invalid type', async () => {
      // Prepare data
      const id1 = new ObjectId();
      await initMockContact({
        _id: id1,
        identifier: nanoid(),
        contactRole: ContactRole.PERSON,
        contactType: ContactType.PERSON,
      });

      await expect(
        service.update({
          ...payload,
          organizations: [{ contactId: id1 }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.INVALID_ORG_TYPE);
    });

    it('[PERSON] should call fn with payload and update data', async () => {
      const result = await service.update(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContactData._id);
      expect(result).toMatchSchema(contactTest.findOnePersonSchema);
    });

    it('[SUPPLIER] should throw error if contact not found', async () => {
      payload.contactRole = ContactRole.SUPPLIER;

      await expect(
        service.update({ ...payload, id: new ObjectId().toString() }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.FIND_CONTACT_NOT_FOUND);
    });

    it('[SUPPLIER] should throw error if type regular miss supplier category', async () => {
      await expect(
        service.update({
          ...payload,
          contactType: ContactType.PERSON,
          supplierType: SupplierType.REGULAR,
        }),
      ).rejects.toThrow(
        CONTACT_MESSAGE_KEYS.SUPPLIER_TYPE_REGULAR_MUST_HAVE_CATEGORY,
      );
    });

    it('[SUPPLIER] should throw error if type rental has supplier category', async () => {
      await expect(
        service.update({
          ...payload,
          contactType: ContactType.PERSON,
          supplierType: SupplierType.RENTAL,
          supplierCategory: SupplierCategoryEnum.AFVAL,
        }),
      ).rejects.toThrow(
        CONTACT_MESSAGE_KEYS.SUPPLIER_TYPE_RENTAL_MUST_NOT_HAVE_CATEGORY,
      );
    });

    it('[SUPPLIER] should throw error if contact type invalid', async () => {
      await expect(
        service.update({
          ...payload,
          contactType: ContactType.ORGANIZATION,
          supplierType: SupplierType.REGULAR,
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.UPDATE_CONTACT_TYPE_NOT_ALLOWED);
    });

    it('[SUPPLIER] should throw error if organizations not found', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.SUPPLIER,
        contactType: ContactType.PERSON,
        supplierType: SupplierType.REGULAR,
        supplierCategory: SupplierCategoryEnum.AFVAL,
      });

      await expect(
        service.update({
          ...payload,
          contactType: ContactType.PERSON,
          supplierType: SupplierType.REGULAR,
          supplierCategory: SupplierCategoryEnum.AFVAL,
          organizations: [{ contactId: new ObjectId().toString() }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.ORG_NOT_FOUND);
    });

    it('[SUPPLIER] should throw error if organizations invalid type', async () => {
      // Prepare data
      const id1 = new ObjectId();
      await initMockContact({
        _id: id1,
        identifier: nanoid(),
        contactRole: ContactRole.SUPPLIER,
        contactType: ContactType.PERSON,
        supplierType: SupplierType.REGULAR,
        supplierCategory: SupplierCategoryEnum.AFVAL,
      });

      await expect(
        service.update({
          ...payload,
          contactType: ContactType.PERSON,
          supplierType: SupplierType.REGULAR,
          supplierCategory: SupplierCategoryEnum.AFVAL,
          organizations: [{ contactId: id1 }],
        }),
      ).rejects.toThrow(CONTACT_MESSAGE_KEYS.INVALID_ORG_TYPE);
    });

    it('[SUPPLIER] should call fn with payload and update data', async () => {
      const result = await service.update({
        ...payload,
        contactType: ContactType.PERSON,
        supplierType: SupplierType.REGULAR,
        supplierCategory: SupplierCategoryEnum.AFVAL,
      });
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContactData._id);
      expect(result).toMatchSchema(contactTest.findOneSupplierSchema);
    });
  });

  describe('getValidContact', () => {
    it('[DEBTOR] should call fn with payload and return data', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.DEBTOR,
      });

      const result = await service.getValidContact(
        mockContactData._id.toString(),
        ContactRole.DEBTOR,
      );
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContactData._id);
      expect(result).toMatchSchema(contactTest.getValidContactSchema);
    });

    it('[PERSON] should call fn with payload and return data', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.PERSON,
      });

      const result = await service.getValidContact(
        mockContactData._id.toString(),
        ContactRole.PERSON,
      );
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContactData._id);
      expect(result).toMatchSchema(contactTest.getValidContactSchema);
    });

    it('[SUPPLIER] should call fn with payload and return data', async () => {
      // Prepare data
      await initMockContact({
        contactRole: ContactRole.SUPPLIER,
      });

      const result = await service.getValidContact(
        mockContactData._id.toString(),
        ContactRole.SUPPLIER,
      );
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockContactData._id);
      expect(result).toMatchSchema(contactTest.getValidContactSchema);
    });

    it('should return null if data not found', async () => {
      const result = await service.getValidContact(
        new ObjectId().toString(),
        ContactRole.DEBTOR,
      );
      expect(result).toBeNull();
    });
  });
});
