import { Test, TestingModule } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { AgreementLineModel } from '~/modules/agreementline/agreementline.model';
import { ContractModel } from '~/modules/contract/contract.model';
import { CostLineModel } from '~/modules/costline/costline.model';
import { CostLineGeneralModel } from '~/modules/costlinegeneral/costlinegeneral.model';
import { COSTLINE_GENERAL_MESSAGE_KEYS } from '~/shared/message-keys/costlinegeneral.message-key';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockAgreementLine,
  mockAgreementLineData,
} from '~/test/mocks/agreementline.mock';
import { initMockContract, mockContractData } from '~/test/mocks/contract.mock';
import {
  initMockCostlineGeneral,
  mockCostlineGeneralData,
} from '~/test/mocks/costlinegeneral.mock';

import { CostlinegeneralService } from '../costlinegeneral.service';
import { DeleteCostliegeneralBodyDto } from '../dtos/delete-costlinegeneral.dto';
import { BulkEditCostLineGeneralDto } from '../dtos/update-costlinegeneral.dto';
import { costLineGeneralTest } from './costlinegeneral.dto.test';

describe('CostlinegeneralService', () => {
  let service: CostlinegeneralService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        CostlinegeneralService,
        ...testInjectModel([
          CostLineGeneralModel,
          ContractModel,
          AgreementLineModel,
          CostLineModel,
        ]),
      ],
    }).compile();

    service = module.get(CostlinegeneralService);

    // Init data
    await Promise.all([
      initMockCostlineGeneral({
        agreementLine: mockAgreementLineData._id,
      }),
      initMockContract({
        agreementLines: [mockAgreementLineData._id],
      }),
      initMockAgreementLine({
        costLineGenerals: [mockCostlineGeneralData._id],
        contract: mockContractData._id,
      }),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('bulkEdit', () => {
    const today = new Date();
    const contractStartDate = new Date(today.getFullYear() + 1, 0, 1);
    const contractEndDate = new Date(today.getFullYear() + 1, 11, 31);

    beforeEach(async () => {
      await initMockContract({
        startDate: contractStartDate,
        endDate: contractEndDate,
      });
    });

    it('should throw error when contract not found', async () => {
      const payload: BulkEditCostLineGeneralDto = {
        contractId: new ObjectId().toString(),
        costLineGenerals: [],
      };

      await expect(service.bulkEdit(payload)).rejects.toThrow(
        COSTLINE_GENERAL_MESSAGE_KEYS.CONTRACT_NOT_FOUND,
      );
    });

    it('should throw error when some costlinegeneral not in contract', async () => {
      const payload: BulkEditCostLineGeneralDto = {
        contractId: mockContractData._id.toString(),
        costLineGenerals: [
          {
            _id: new ObjectId().toString(),
          },
        ],
      };
      await expect(service.bulkEdit(payload)).rejects.toThrow(
        COSTLINE_GENERAL_MESSAGE_KEYS.SOME_COSTLINE_GENERAL_ARE_NOT_IN_CONTRACT,
      );
    });

    it.each([
      {
        description: 'startDate before contract startDate',
        startDate: new Date(today.getFullYear(), 11, 31).toISOString(),
        endDate: null,
        expectedError: COSTLINE_GENERAL_MESSAGE_KEYS.INVALID_START_DATE,
      },
      {
        description: 'endDate before contract startDate',
        startDate: null,
        endDate: new Date(today.getFullYear(), 11, 31).toISOString(),
        expectedError: COSTLINE_GENERAL_MESSAGE_KEYS.INVALID_END_DATE,
      },
      {
        description: 'startDate after contract endDate',
        startDate: new Date(today.getFullYear() + 2, 0, 1).toISOString(),
        endDate: null,
        expectedError: COSTLINE_GENERAL_MESSAGE_KEYS.INVALID_START_DATE,
      },
      {
        description: 'endDate after contract endDate',
        startDate: null,
        endDate: new Date(today.getFullYear() + 2, 0, 1).toISOString(),
        expectedError: COSTLINE_GENERAL_MESSAGE_KEYS.INVALID_END_DATE,
      },
    ])(
      'should throw error when $description',
      async ({ startDate, endDate, expectedError }) => {
        const payload: BulkEditCostLineGeneralDto = {
          contractId: mockContractData._id.toString(),
          costLineGenerals: [
            {
              _id: mockCostlineGeneralData._id.toString(),
              ...(startDate && { startDate }),
              ...(endDate && { endDate }),
            },
          ],
        };

        await expect(service.bulkEdit(payload)).rejects.toThrow(expectedError);
      },
    );

    it('should update successfully when dates are within contract range', async () => {
      const payload: BulkEditCostLineGeneralDto = {
        contractId: mockContractData._id.toString(),
        costLineGenerals: [
          {
            _id: mockCostlineGeneralData._id.toString(),
            description: 'Updated description',
            price: 100,
            startDate: new Date(today.getFullYear() + 1, 5, 1).toISOString(),
            endDate: new Date(today.getFullYear() + 1, 10, 30).toISOString(),
          },
        ],
      };

      const result = await service.bulkEdit(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(costLineGeneralTest.bulkEditSchema);
    });

    it('should update costLineGenerals successfully', async () => {
      await initMockContract();

      const payload: BulkEditCostLineGeneralDto = {
        contractId: mockContractData._id.toString(),
        costLineGenerals: [
          {
            _id: mockCostlineGeneralData._id.toString(),
            description: 'Updated description',
            price: 100,
          },
        ],
      };

      const result = await service.bulkEdit(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(costLineGeneralTest.bulkEditSchema);
    });
  });

  describe('softDelete', () => {
    // Reset data
    beforeEach(async () => {
      await initMockCostlineGeneral();
    });

    it('should throw error when costlinegeneral not found', async () => {
      const payload: DeleteCostliegeneralBodyDto = {
        costlinegeneralIds: [
          mockCostlineGeneralData._id.toString(),
          new ObjectId().toString(),
        ],
      };

      await expect(service.softDelete(payload)).rejects.toThrow(
        COSTLINE_GENERAL_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should soft delete costLineGenerals successfully', async () => {
      const payload: DeleteCostliegeneralBodyDto = {
        costlinegeneralIds: [mockCostlineGeneralData._id.toString()],
      };
      const result = await service.softDelete(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('acknowledged');
    });
  });
});
