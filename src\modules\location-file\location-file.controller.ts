import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { LOCATION_FILE_MESSAGES } from '~/shared/messages/location-file.message';

import {
  LocationFileCheckExistedParamsDto,
  LocationFileCreateDto,
  LocationUploadFileParamsDto,
} from './dtos/location-file.dto';
import { LocationFileService } from './location-file.service';

@Controller()
export class LocationFileController {
  constructor(private locationFileService: LocationFileService) {}

  // get list uploaded files of location
  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(LocationUploadFileParamsDto))
  @MessagePattern({ cmd: LOCATION_FILE_MESSAGES.GET_UPLOAD_FILES_OF_LOCATION })
  async getUploadFilesOfLocation(
    @Payload() payload: LocationUploadFileParamsDto,
  ) {
    return await this.locationFileService.getUploadFilesOfLocation(payload);
  }

  // upload file to location
  @UsePipes(new ZodValidationPipe(LocationFileCreateDto))
  @MessagePattern({ cmd: LOCATION_FILE_MESSAGES.UPLOAD_FILE_TO_LOCATION })
  async uploadFileToLocation(@Payload() payload: LocationFileCreateDto) {
    return await this.locationFileService.uploadFileToLocation(payload);
  }

  // delete uploaded file of location
  @MessagePattern({ cmd: LOCATION_FILE_MESSAGES.DELETE_LOCATION_FILE })
  async deleteLocationFile(@Payload() payload: any) {
    const { id, headers } = payload;
    return await this.locationFileService.deleteLocationFile(id, headers);
  }

  // check existed location file
  @UsePipes(new ZodValidationPipe(LocationFileCheckExistedParamsDto))
  @MessagePattern({
    cmd: LOCATION_FILE_MESSAGES.CHECK_EXISTED_LOCATION_FILE,
  })
  async checkExistedLocationFile(@Payload() payload: any) {
    return await this.locationFileService.checkExistedLocationFile(payload);
  }

  // download all uploaded files of location
  @MessagePattern({
    cmd: LOCATION_FILE_MESSAGES.DOWNLOAD_FILES_OF_LOCATION,
  })
  async downloadFilesOfLocation(@Payload() payload: any) {
    return this.locationFileService.downloadFilesOfLocation(payload);
  }

  @MessagePattern({
    cmd: LOCATION_FILE_MESSAGES.DOWNLOAD_ALL_FILES_OF_LOCATION,
  })
  async downloadAllFilesOfLocation(@Payload() payload: any) {
    const { id, headers } = payload;
    return this.locationFileService.downloadAllFilesOfLocation(id, headers);
  }
}
