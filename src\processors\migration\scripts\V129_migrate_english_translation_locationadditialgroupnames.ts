import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';

import { MigrationContext } from '../migration.service';

interface ITranslation {
  key: string;
  newENDesc: string;
  position?: number;
}

const translations: { [k in LocationAdditionalType]: ITranslation[] } = {
  [LocationAdditionalType.FEATURE_AND_SUPPLIER]: [
    { key: 'Afval', newENDesc: 'Rubbish' },
    { key: 'Was<PERSON><PERSON><PERSON> & Droger', newENDesc: 'Washing machines and dryers' },
    { key: 'Meubilering', newENDesc: 'Furnishings' },
    { key: 'Tuinen', newENDesc: '<PERSON><PERSON>' },
    { key: 'Camerasysteem', newENDesc: 'CCTV' },
  ],
  [LocationAdditionalType.CERTIFICATE_AND_CONTROL]: [
    { key: 'HeatingVersion', newENDesc: 'Heating system' },
    { key: 'FireInstallation', newENDesc: 'Fire alarm system' },
    { key: 'Legionella', newENDesc: 'Legionella management' },
  ],
  [LocationAdditionalType.GWE_AND_METER_READING]: [],
};

const up = async (context: MigrationContext) => {
  try {
    const locationAdditionalGroupNameCollection = context?.destinationClient
      ?.db()
      .collection('locationadditionalgroupnames');

    for (const type in translations) {
      const translationList = translations[type as LocationAdditionalType];

      for (const { key, newENDesc } of translationList) {
        const locationAdditionalGroupName =
          await locationAdditionalGroupNameCollection?.findOne({ type, key });

        if (!locationAdditionalGroupName) {
          console.warn(
            `Location additional group name not found for type="${type}", key="${key}"`,
          );
          continue;
        }

        const result = await locationAdditionalGroupNameCollection?.updateOne(
          { _id: locationAdditionalGroupName._id },
          {
            $set: {
              description: newENDesc,
              updatedAt: new Date(),
            },
          },
        );

        console.info(
          ` Updated ${result?.modifiedCount} location additional group name: [${key}] "${locationAdditionalGroupName.description}" to "${newENDesc}"`,
        );
      }
    }
  } catch (error) {
    console.error('Error updating translations:', error);
  }
};

export default up;
