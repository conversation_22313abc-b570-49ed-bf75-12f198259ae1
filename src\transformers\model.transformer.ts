import { Inject, Provider } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { getModelForClass } from '@typegoose/typegoose';
import { Connection } from 'mongoose';

import {
  DB_CONNECTION_TOKEN,
  DB_MODEL_TOKEN_SUFFIX,
} from '~/constants/app.constant';

export interface TypegooseClass {
  new (...args: unknown[]);
}

export function getModelToken(modelName: string): string {
  return modelName + DB_MODEL_TOKEN_SUFFIX;
}

// Get Provider by Class
export function getProviderByTypegooseClass(
  typegooseClass: TypegooseClass,
): Provider {
  return {
    provide: getModelToken(typegooseClass.name),
    useFactory: (connection: Connection, configService: ConfigService) => {
      const model = getModelForClass(typegooseClass, {
        existingConnection: connection,
      });

      const autoIndex = configService.get<boolean>('database.autoIndex', false);
      if (autoIndex) {
        model.syncIndexes();
      }

      return model;
    },
    inject: [DB_CONNECTION_TOKEN, ConfigService],
  };
}

// Model injecter
export function InjectModel(model: TypegooseClass) {
  return Inject(getModelToken(model.name));
}
