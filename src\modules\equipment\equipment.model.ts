import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import { BaseModel } from '~/shared/models/base.model';

export type EquipmentDocument = DocumentType<EquipmentModel>;

@modelOptions({
  options: {
    customName: 'Equipment',
  },
})
@index({ isActive: 1, type: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class EquipmentModel extends BaseModel {
  @prop({ trim: true, maxlength: 255 })
  description!: string;

  @prop({ enum: EquipmentEnum })
  type!: EquipmentEnum;

  @prop({ default: true })
  isActive!: boolean;
}
