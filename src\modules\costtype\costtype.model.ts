import {
  DocumentType,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { CostTypeType } from '~/shared/enums/cost-type.enum';
import { BaseModel } from '~/shared/models/base.model';

import { CountryDocument, CountryModel } from '../country/country.model';

export type CostTypeDocument = DocumentType<CostTypeModel>;

@modelOptions({
  options: {
    customName: 'CostType',
  },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class CostTypeModel extends BaseModel {
  @prop({ default: true })
  isActive!: boolean;

  @prop({ default: true })
  isSynced!: boolean;

  @prop()
  itemCode!: string;

  @prop()
  name!: string;

  @prop({ enum: CostTypeType })
  type!: CostTypeType;

  @prop({ ref: () => CountryModel })
  country?: Ref<CountryDocument>;
}
