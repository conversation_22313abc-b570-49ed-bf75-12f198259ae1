import { nanoid } from 'nanoid';
import * as path from 'path';

import { ContactRole, ContactType } from '~/shared/enums/contact.enum';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldOrganization {
  _id: string;
  active: boolean;
  name: string;
  email: string;
  phoneNumber1: string;
  phoneNumber2: string;
  address1: string;
  address2: string;
  website: string;
  warningEmail: string;
  remark: string;
  parentId: string;
  creditorContact: Record<string, any>;
}

const organizationPipeLineAggregate = (skip: number, limit: number) => {
  return [
    {
      $match: {
        creditorContact: { $ne: null },
        _replace: { $exists: false },
      },
    },
    {
      $lookup: {
        from: 'CreditorContact',
        localField: 'creditorContact',
        foreignField: '_id',
        as: 'creditorContact',
      },
    },
    {
      $unwind: {
        path: '$creditorContact',
        preserveNullAndEmptyArrays: true,
      },
    },
    {
      $match: {
        creditorContact: { $ne: null },
      },
    },
    { $skip: skip },
    {
      $limit: limit,
    },
  ];
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldOrganization[];
  context: any;
}) => {
  return Promise.all(
    data.map(async (item) => {
      const tranformed: any = {
        _id: item._id,
        isActive: item.active,
        displayName: item.name || '',
        name: item.name || '',
        contactType: ContactType.ORGANIZATION,
        contactRole: ContactRole.SUPPLIER,
        email: item.email || '',
        warningEmail: item.warningEmail || '',
        phone1: item.phoneNumber1 || '',
        phone2: item.phoneNumber2 || '',
        address1: item.address1,
        ...(item.address2 && { address2: item.address2 }),
        remark: item.remark || '',
        contactRefId: item.creditorContact?._id,
        identifier: nanoid(),
        parentOrganization: item.parentId,
        kvk: item.creditorContact?.KVK || '',
        snf: item.creditorContact?.SNF || '',
        vatCode: item.creditorContact?.VATCode || '',
        supplierCategory: item.creditorContact?.supplierCategory,
        supplierType: item.creditorContact?.supplierType,
        isDeleted: false,
        isInternal: false,
      };

      if (item.address1) {
        const address = await context
          .sourceClient!.db()
          .collection('address')
          .findOne({ _id: item.address1 });

        if (address) {
          await context
            .destinationClient!.db()
            .collection('addresses')
            .findOneAndUpdate(
              { _id: address._id },
              {
                $set: {
                  _id: address._id,
                  isActive: address.active,
                  street: address.street,
                  city: address.city,
                  postalCode: address.postalCode,
                  number: address.number,
                  country: address.country,
                  region: address.region,
                  contact: tranformed._id,
                },
              },
              {
                upsert: true,
              },
            );
        }
      }

      if (item.address2) {
        const address = await context
          .sourceClient!.db()
          .collection('address')
          .findOne({ _id: item.address2 });

        if (address?.country) {
          await context
            .destinationClient!.db()
            .collection('addresses')
            .findOneAndUpdate(
              { _id: address._id },
              {
                $set: {
                  _id: address._id,
                  isActive: address.active,
                  street: address.street,
                  city: address.city,
                  postalCode: address.postalCode,
                  number: address.number,
                  country: address.country,
                  region: address.region,
                  contact: tranformed._id,
                },
              },
              {
                upsert: true,
              },
            );
        } else {
          tranformed.address2 = null;
        }
      }

      return tranformed;
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('grouping');

  const pipeline = organizationPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'grouping',
      destinationCollectionName: 'contacts',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
