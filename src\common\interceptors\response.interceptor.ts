import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  NestInterceptor,
} from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import isArrayLike from 'lodash/isArrayLike';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

import { HTTP_RES_TRANSFORM_PAGINATE } from '~/constants/app.constant';
import { transformDataToPaginate } from '~/transformers/paginate.transformer';

export interface Response<T> {
  data: T;
}

@Injectable()
export class ResponseInterceptor<T> implements NestInterceptor<T, Response<T>> {
  constructor(private readonly reflector: Reflector) {}
  intercept(
    context: ExecutionContext,
    next: CallHandler,
  ): Observable<Response<T>> {
    if (!context.switchToHttp().getRequest()) {
      return next.handle();
    }

    const handler = context.getHandler();

    return next.handle().pipe(
      map((data) => {
        if (this.reflector.get(HTTP_RES_TRANSFORM_PAGINATE, handler)) {
          return {
            ...transformDataToPaginate(data),
          };
        }

        return isArrayLike(data) ? [...data] : data;
      }),
    );
  }
}
