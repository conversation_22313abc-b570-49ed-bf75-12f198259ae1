import { Language } from '@googlemaps/google-maps-services-js';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { addressTest } from '~/modules/address/test/address.dto.test';
import { countryTest } from '~/modules/country/test/country.dto.test';
import { regionTest } from '~/modules/region/test/region.dto.test';
import {
  ContactRole,
  ContactType,
  SupplierCategoryEnum,
  SupplierType,
} from '~/shared/enums/contact.enum';
import { Gender } from '~/shared/enums/tenant-user.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isActive: z.boolean(),
    isInternal: z.boolean(),
    displayName: z.string(),
    contactType: z.nativeEnum(ContactType),
    contactRole: z.nativeEnum(ContactRole),
    lastName: z.string().optional(),
    firstName: z.string().optional(),
    gender: z.nativeEnum(Gender).optional(),
    language: z.nativeEnum(Language).optional(),
    name: z.string().optional(),
    kvk: z.string().optional(),
    snf: z.string().optional(),
    vatCode: z.string().optional(),
    parentOrganization: z.instanceof(ObjectId).optional(),
    email: z.string(),
    warningEmail: z.string().optional(),
    phone1: z.string(),
    phone2: z.string().optional(),
    address1: z.instanceof(ObjectId),
    address2: z.instanceof(ObjectId).optional(),
    organizationNames: z.string().optional(),
    website: z.string().optional(),
    paymentTermRentInvoice: z.number(),
    paymentTermJobInvoice: z.number(),
    invoiceEmail: z.string().optional(),
    invoiceReference: z.string().optional(),
    identifier: z.string().optional(),
    collectiveJobInvoice: z.boolean().optional(),
    collectiveCustomInvoice: z.boolean().optional(),
    supplierType: z.nativeEnum(SupplierType).optional(),
    supplierCategory: z.nativeEnum(SupplierCategoryEnum).optional(),
    remark: z.string().optional(),
    syncedAt: z.date().optional(),
  })
  .extend(baseModelTestSchema);

const findAllTypeDebtorSchema = z.array(
  modelSchema.pick({
    _id: true,
    isActive: true,
    name: true,
    displayName: true,
    email: true,
    phone1: true,
    supplierType: true,
    supplierCategory: true,
    contactType: true,
    snf: true,
  }),
);

const findAllTypeOrganizationSchema = z.array(
  modelSchema.pick({
    _id: true,
    isActive: true,
    name: true,
    displayName: true,
    contactRole: true,
  }),
);

const findAllTypePersonSchema = z.array(
  modelSchema.pick({
    _id: true,
    isActive: true,
    lastName: true,
    firstName: true,
    email: true,
    phone1: true,
    displayName: true,
    organizationNames: true,
    contactType: true,
    contactRole: true,
  }),
);

const findAllTypeSupplierSchema = z.array(
  modelSchema.pick({
    _id: true,
    isActive: true,
    name: true,
    displayName: true,
    email: true,
    phone1: true,
    supplierType: true,
    supplierCategory: true,
    contactType: true,
  }),
);

const findOneBaseAddressSchema = addressTest.modelSchema
  .pick({
    _id: true,
    city: true,
    street: true,
    number: true,
    suffix: true,
    postalCode: true,
  })
  .extend({
    country: countryTest.modelSchema
      .pick({
        _id: true,
        name: true,
        code: true,
      })
      .optional(),
    region: regionTest.modelSchema
      .pick({
        _id: true,
        name: true,
      })
      .optional(),
  });

const findOneDebtorBaseSchema = modelSchema
  .pick({
    _id: true,
    isActive: true,
    contactType: true,
    contactRole: true,
    displayName: true,
    remark: true,
    phone1: true,
    phone2: true,
    email: true,
    warningEmail: true,
    paymentTermRentInvoice: true,
    paymentTermJobInvoice: true,
    invoiceEmail: true,
    invoiceReference: true,
    identifier: true,
    collectiveJobInvoice: true,
    collectiveCustomInvoice: true,
  })
  .extend({
    address1: findOneBaseAddressSchema.nullable().optional(),
    address2: findOneBaseAddressSchema.nullable().optional(),
  });

const findOneDebtorTypeOrganizationSchema = findOneDebtorBaseSchema.merge(
  modelSchema
    .pick({
      name: true,
      snf: true,
      kvk: true,
      vatCode: true,
      website: true,
    })
    .extend({
      parentOrganization: z
        .object({
          name: z.string(),
          displayName: z.string(),
        })
        .optional(),
    }),
);

const findOneDebtorTypePersonSchema = findOneDebtorBaseSchema.merge(
  modelSchema.pick({
    gender: true,
    language: true,
    lastName: true,
    firstName: true,
    organizationNames: true,
  }),
);

const findOnePersonSchema = modelSchema
  .pick({
    _id: true,
    isActive: true,
    lastName: true,
    firstName: true,
    email: true,
    warningEmail: true,
    gender: true,
    language: true,
    phone1: true,
    phone2: true,
    address1: true,
    address2: true,
    remark: true,
    contactType: true,
    contactRole: true,
  })
  .extend({
    address1: findOneBaseAddressSchema.nullable().optional(),
    address2: findOneBaseAddressSchema.nullable().optional(),
  });

const findOneSupplierSchema = modelSchema
  .pick({
    _id: true,
    isActive: true,
    contactType: true,
    contactRole: true,
    displayName: true,
    remark: true,
    phone1: true,
    phone2: true,
    address1: true,
    address2: true,
    email: true,
    warningEmail: true,
    supplierType: true,
    supplierCategory: true,
  })
  .extend({
    address1: findOneBaseAddressSchema.nullable().optional(),
    address2: findOneBaseAddressSchema.nullable().optional(),
  });

const getValidContactSchema = modelSchema.pick({
  _id: true,
  displayName: true,
  isInternal: true,
});

export const contactTest = {
  modelSchema,
  findAllTypeDebtorSchema,
  findAllTypeOrganizationSchema,
  findAllTypePersonSchema,
  findAllTypeSupplierSchema,
  findOneDebtorTypeOrganizationSchema,
  findOneDebtorTypePersonSchema,
  findOnePersonSchema,
  findOneSupplierSchema,
  getValidContactSchema,
};
