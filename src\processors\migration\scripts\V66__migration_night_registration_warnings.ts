import { ObjectId } from 'mongoose';
import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface WarningImages {
  _id: string;
  url: string;
}

interface OldWarning {
  _id: string;
  dateToLeave: number;
  level: string;
  description: string;
  warningDate: Date;
  bed: string;
  images: WarningImages[];
  resident: ObjectId;
  emailTemplateOption: string;
  warningCategory: ObjectId;
  unit: ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const WarningPipeLineAggregate = (skip: number, limit: number) => {
  // get all resident
  return [{ $skip: skip }, { $limit: limit }];
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldWarning[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (item) => {
      return {
        _id: item._id,
        resident: item.resident,
        dayToLeave: item.dateToLeave,
        level: item.level,
        description: item.description,
        warningDate: item.warningDate,
        images:
          item.images && item.images.length > 0
            ? item.images?.map((image) => image.url)
            : [],
        emailTemplateOption: item.emailTemplateOption
          ? Number(item.emailTemplateOption)
          : 1,
        warningCategory: item.warningCategory,
        isDeleted: false,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      };
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('night-registration_warning');

  const pipeline = WarningPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'night-registration_warning',
      destinationCollectionName: 'nightregistrationwarnings',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
