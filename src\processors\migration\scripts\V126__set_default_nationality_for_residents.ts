import { MigrationContext } from '../migration.service';

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const nationalityCollection = context.destinationClient
      ?.db()
      .collection('nightregistrationnationalities');
    const residentCollection = context.destinationClient
      ?.db()
      .collection('nightregistrationresidents');

    // Find the _id of the nationality with code "NOCODE"
    const defaultNationality = await nationalityCollection?.findOne({
      code: 'NOCODE',
    });

    if (!defaultNationality) {
      throw new Error('Default nationality with code "NOCODE" not found.');
    }

    // Update all residents to set the default nationality
    const result = await residentCollection?.updateMany(
      {},
      { $set: { nationality: defaultNationality._id } },
    );

    console.log(
      `Updated ${result?.modifiedCount} residents with default nationality.`,
    );

    const after = new Date().getTime();
    console.log(
      `#endregion migrate V126__set_default_nationality_for_reservations with: ${after - before} ms`,
    );
  } catch (error) {
    console.error('Error during migration:', error);
  }
};

export default up;
