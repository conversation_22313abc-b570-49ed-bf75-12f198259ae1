import { Test } from '@nestjs/testing';

import { SyncHistoryService } from '~/modules/sync-history/sync-history.service';
import { ThirdPartyConnectorContext } from '~/processors/third-party-connector/strategies/third-party-connector.context';
import { ThirdPartyTypeEnum } from '~/shared/enums/third-party-type.enum';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';

import { CostTypeModel } from '../costtype.model';
import { CosttypeService } from '../costtype.service';
import { CostTypeQueryParamsDto } from '../dtos/costtype-query-params.dto';
import { costTypeTest } from './costtype.dto.test';

describe('CosttypeService', () => {
  let service: CosttypeService;
  let thirdPartyContext: ThirdPartyConnectorContext;
  let syncHistoryService: SyncHistoryService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        CosttypeService,
        ...testInjectModel([CostTypeModel]),
        {
          provide: ThirdPartyConnectorContext,
          useValue: {
            getCostTypes: jest.fn(),
          },
        },
        {
          provide: SyncHistoryService,
          useValue: {
            isPending: jest.fn(),
            createNewPending: jest.fn(),
            updateToSuccess: jest.fn(),
            updateToFailed: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get(CosttypeService);
    thirdPartyContext = module.get(ThirdPartyConnectorContext);
    syncHistoryService = module.get(SyncHistoryService);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should call fn with payload and return list data', async () => {
      const payload: CostTypeQueryParamsDto = {
        isActive: true,
      };

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(costTypeTest.findAllSchema);
    });

    it('should call fn and return empty list when data not exist', async () => {
      const payload: CostTypeQueryParamsDto = {
        pageIndex: 999,
        isActive: false,
      };

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });

  describe('syncFrom3rdParty', () => {
    const payload = {
      actionType: 'sync',
      type: ThirdPartyTypeEnum.AFAS,
    };

    it('should throw error when sync is already in progress', async () => {
      (syncHistoryService.isPending as jest.Mock).mockResolvedValue(true);

      await expect(service.syncFrom3rdParty(payload)).rejects.toThrow(
        'Sync is already in progress by another user',
      );
    });

    it('should call fn and return result when sync succeeds', async () => {
      const mockCostTypes = [{ itemCode: 'CODE1' }];

      (syncHistoryService.isPending as jest.Mock).mockResolvedValue(false);
      (syncHistoryService.createNewPending as jest.Mock).mockResolvedValue({
        _id: 'mockSyncId',
      });
      (thirdPartyContext.getCostTypes as jest.Mock).mockResolvedValue(
        mockCostTypes,
      );

      (syncHistoryService.updateToSuccess as jest.Mock).mockResolvedValue(
        undefined,
      );

      const result = await service.syncFrom3rdParty(payload);

      expect(syncHistoryService.isPending).toHaveBeenCalled();
      expect(syncHistoryService.createNewPending).toHaveBeenCalled();
      expect(thirdPartyContext.getCostTypes).toHaveBeenCalled();
      expect(syncHistoryService.updateToSuccess).toHaveBeenCalled();

      expect(result).toHaveProperty('upsertedCount', 1);
    });

    it('findAll should return item with itemCode "CODE1"', async () => {
      const payload: CostTypeQueryParamsDto = {
        isActive: true,
      };

      const result = await service.findAll(payload);
      expect(result).toHaveProperty('docs');
      expect(result.docs[0]).toHaveProperty('itemCode', 'CODE1');
    });
  });
});
