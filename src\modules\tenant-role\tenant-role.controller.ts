import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { ROLE_MESSAGES } from '~/shared/messages/role.message';

import { TenantRoleService } from './tenant-role.service';

@Controller('roles')
export class TenantRoleController {
  constructor(private readonly tenantRoleService: TenantRoleService) {}
  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(QueryParamsDto))
  @MessagePattern({ cmd: ROLE_MESSAGES.GET_ROLES })
  public async findAll(@Payload() data: QueryParamsDto) {
    return this.tenantRoleService.findAll(data);
  }
}
