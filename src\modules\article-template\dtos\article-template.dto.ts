import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

export const ArticleListItemSchema = z.strictObject({
  _id: z.string().optional(),
  article: z.string(),
  amount: z.number().min(1),
  position: z.number().min(0),
});

export const CreateArticleTemplateSchema = z.strictObject({
  name: z.string().max(256),
  storage: z.string(),
  articleList: z.array(ArticleListItemSchema),
});

export const UpdateArticleTemplateSchema =
  CreateArticleTemplateSchema.partial().extend({
    _id: z.string(),
  });

export class CreateArticleTemplateDto extends createZodDto(
  CreateArticleTemplateSchema,
) {}

export class UpdateArticleTemplateDto extends createZodDto(
  UpdateArticleTemplateSchema,
) {}
