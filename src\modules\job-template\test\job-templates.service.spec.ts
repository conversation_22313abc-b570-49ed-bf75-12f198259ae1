import { ForbiddenException } from '@nestjs/common';
import { Test } from '@nestjs/testing';
import { omit } from 'lodash';
import { ObjectId } from 'mongodb';

import { TenantUserService } from '~/modules/tenant-user/tenant-user.service';
import { UnitModel } from '~/modules/unit/unit.model';
import { JobTemplateTypeEnum } from '~/shared/enums/job-template.enum';
import { JOB_TEMPLATE_MESSAGE_KEY } from '~/shared/message-keys/job-template.message-keys';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import {
  initMockJobTemplate,
  mockJobTemplateData,
} from '~/test/mocks/jobtemplate.mock';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';

import {
  CopyJobTemplateDto,
  CreateJobTemplateDto,
  DeleteJobTemplateDto,
  DeletePointJobTemplateDto,
  GetJobTemplateByUnitsDto,
  UpdateJobTemplateDto,
} from '../dtos/job-template.dto';
import { JobTemplateModel } from '../job-template.model';
import { JobTemplateService } from '../job-template.service';
import { jobTemplateTest } from './job-templates.dto.test';

describe('JobTemplateService', () => {
  let service: JobTemplateService;
  const jobTemplateId = new ObjectId();

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        JobTemplateService,
        ...testInjectProviders([
          JobTemplateModel,
          UnitModel,
          TenantUserService,
        ]),
      ],
    }).compile();

    service = module.get(JobTemplateService);

    // Init mock data
    await Promise.all([
      initMockJobTemplate(),
      initMockJobTemplate({
        _id: jobTemplateId,
        type: JobTemplateTypeEnum.INSPECTION,
      }),
      initMockUnit(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should return a paginated list of job templates', async () => {
      const result = await service.findAll({});
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(jobTemplateTest.findAllSchema);
    });

    it('should return list empty if data not exist', async () => {
      const result = await service.findAll({ pageIndex: 99 });
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });

  describe('findOne', () => {
    it('should call fn with id and return data', async () => {
      const result = await service.findOne(mockJobTemplateData._id.toString());
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockJobTemplateData._id);
      expect(result).toMatchSchema(jobTemplateTest.findOneSchema);
    });

    it('should return null if data not found', async () => {
      const result = await service.findOne(new ObjectId().toString());
      expect(result).toBeNull();
    });
  });

  describe('findByUnit', () => {
    it('should return job templates by unit ID', async () => {
      const result = await service.findByUnit(mockUnitData._id.toString());
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTemplateTest.findByUnitSchema);
    });

    it('should return null if unit not found', async () => {
      const result = await service.findByUnit(new ObjectId().toString());
      expect(result).toBeNull();
    });
  });

  describe('findByUnits', () => {
    it('should return job templates by multiple unit IDs', async () => {
      const payload: GetJobTemplateByUnitsDto = {
        units: [mockUnitData._id.toString()],
      };

      const result = await service.findByUnits(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTemplateTest.findByUnitsSchema);
    });

    it('should return empty list if no job templates found', async () => {
      const payload: GetJobTemplateByUnitsDto = {
        units: [new ObjectId().toString()],
      };

      const result = await service.findByUnits(payload);
      expect(result).toBeDefined();
      expect(result).toHaveLength(0);
    });
  });

  describe('create', () => {
    const payload: CreateJobTemplateDto = {
      name: 'New Job Template',
      type: JobTemplateTypeEnum.GENERAL,
      points: [
        { description: 'Point 1', position: 1 },
        { description: 'Point 2', position: 2 },
      ],
      user: new ObjectId().toString(),
    };
    it('should throw error if user does not have permission', async () => {
      await expect(service.create(payload)).rejects.toThrow(ForbiddenException);
    });

    it('should throw error if type general have unit in payload', async () => {
      // Mock private method
      service['validatePermissionForJobTemplate'] = jest
        .fn()
        .mockResolvedValue(null);

      const wrongPayload = {
        ...payload,
        unit: mockUnitData._id.toString(),
      };

      await expect(service.create(wrongPayload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.TYPE_GENERAL_DOES_NOT_HAVE_UNIT,
      );
    });

    it('should throw error if type general do not have name in payload', async () => {
      const wrongPayload = { ...payload, name: undefined };
      await expect(service.create(wrongPayload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.TYPE_GENERAL_MUST_HAVE_NAME,
      );
    });

    it('should throw error if type general have name is existed in DB', async () => {
      const wrongPayload = { ...payload, name: mockJobTemplateData.name };

      await expect(service.create(wrongPayload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.EXISTED_IN_SYSTEM,
      );
    });

    it('should throw error if type inspection do not have unit in payload', async () => {
      const wrongPayload = {
        ...payload,
        type: JobTemplateTypeEnum.INSPECTION,
      };

      await expect(service.create(wrongPayload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.TYPE_INSPECTION_MUST_HAVE_UNIT,
      );
    });

    it('should throw error if type inspection have name in payload', async () => {
      const wrongPayload = {
        ...payload,
        unit: mockUnitData._id.toString(),
        name: 'New Job Template',
        type: JobTemplateTypeEnum.INSPECTION,
      };

      await expect(service.create(wrongPayload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.TYPE_INSPECTION_CANNOT_CUSTOM_NAME,
      );
    });

    it('should throw error if type inspection do not have unit in DB', async () => {
      const wrongPayload = {
        ...payload,
        name: undefined,
        unit: new ObjectId().toString(),
        type: JobTemplateTypeEnum.INSPECTION,
      };

      await expect(service.create(wrongPayload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.UNIT_NOT_FOUND,
      );
    });

    it('should create new job template with type general', async () => {
      const result = await service.create(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTemplateTest.createSchema);
    });

    it('should create new job template with type inspection', async () => {
      const result = await service.create({
        unit: mockUnitData._id.toString(),
        type: JobTemplateTypeEnum.INSPECTION,
        points: [
          { description: 'Point 3', position: 3 },
          { description: 'Point 4', position: 4 },
        ],
        user: new ObjectId().toString(),
      });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTemplateTest.createSchema);
    });
  });

  describe('update', () => {
    const wrongPayload: UpdateJobTemplateDto = {
      id: mockJobTemplateData._id.toString(),
      name: 'INSPECTION POINTS',
      points: [
        {
          _id: new ObjectId().toString(),
          description: 'Updated Point 1',
          position: 1,
        },
        {
          _id: new ObjectId().toString(),
          description: 'Updated Point 2',
          position: 2,
        },
      ],
      user: new ObjectId().toString(),
    };

    it('should throw error if user does not have permission', async () => {
      // Mock private method
      service['validatePermissionForJobTemplate'] = jest
        .fn()
        .mockImplementation(() => {
          throw new ForbiddenException();
        });

      await expect(service.update(wrongPayload)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw error when job template not found', async () => {
      // Mock private method
      service['validatePermissionForJobTemplate'] = jest
        .fn()
        .mockResolvedValue(null);

      await expect(
        service.update({ ...wrongPayload, id: new ObjectId().toString() }),
      ).rejects.toThrow(JOB_TEMPLATE_MESSAGE_KEY.NOT_FOUND);
    });

    it('should throw error when type GENERAL and name is existed', async () => {
      await expect(service.update(wrongPayload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.EXISTED_IN_SYSTEM,
      );
    });

    it('should throw error when type GENERAL and point different', async () => {
      await expect(
        service.update({ ...wrongPayload, name: 'ABC' }),
      ).rejects.toThrow(JOB_TEMPLATE_MESSAGE_KEY.POINTS_NOT_FOUND);
    });

    it('should update an existing job template', async () => {
      const payload: UpdateJobTemplateDto = {
        id: mockJobTemplateData._id.toString(),
        name: 'Updated Job Template',
        points: [
          { description: 'Updated Point 1', position: 1 },
          { description: 'Updated Point 2', position: 2 },
        ],
        user: new ObjectId().toString(),
      };

      const result = await service.update(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockJobTemplateData._id);
      expect(result).toMatchSchema(jobTemplateTest.updateSchema);
    });

    it('should throw error when type INSPECTION and different name', async () => {
      await expect(
        service.update({
          ...wrongPayload,
          id: jobTemplateId.toString(),
          name: 'ABC',
        }),
      ).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.TYPE_INSPECTION_CANNOT_CUSTOM_NAME,
      );
    });

    it('should throw error when type INSPECTION and different unit', async () => {
      await expect(
        service.update({
          ...omit(wrongPayload, 'name'),
          id: jobTemplateId.toString(),
        }),
      ).rejects.toThrow(JOB_TEMPLATE_MESSAGE_KEY.CANNOT_ADD_OR_REMOVE_POINTS);
    });
  });

  describe('delete', () => {
    const payload: DeleteJobTemplateDto = {
      id: mockJobTemplateData._id.toString(),
      user: new ObjectId().toString(),
    };

    it('should throw an error if job template not found', async () => {
      await expect(
        service.delete({ ...payload, id: new ObjectId().toString() }),
      ).rejects.toThrow(JOB_TEMPLATE_MESSAGE_KEY.NOT_FOUND);
    });

    it('should throw error if user does not have permission', async () => {
      const jobTemplateId = new ObjectId();
      await initMockJobTemplate({ _id: jobTemplateId });

      const payload: DeleteJobTemplateDto = {
        id: jobTemplateId.toString(),
        user: new ObjectId().toString(),
      };

      service['validatePermissionForJobTemplate'] = jest
        .fn()
        .mockImplementation(() => {
          throw new ForbiddenException();
        });

      await expect(service.delete(payload)).rejects.toThrow(ForbiddenException);
    });

    it('should delete a job template', async () => {
      service['validatePermissionForJobTemplate'] = jest
        .fn()
        .mockResolvedValue(null);

      const result = await service.delete(payload);
      expect(result).toBeDefined();
    });
  });

  describe('deletePoint', () => {
    it('should throw an error if job template not found', async () => {
      await initMockJobTemplate({ points: [] });

      const payload: DeletePointJobTemplateDto = {
        id: new ObjectId().toString(),
        pointId: new ObjectId().toString(),
        user: new ObjectId().toString(),
      };

      await expect(service.deletePoint(payload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.NOT_FOUND,
      );
    });

    it('should throw an error if point not found in job template', async () => {
      const payload: DeletePointJobTemplateDto = {
        id: mockJobTemplateData._id.toString(),
        pointId: new ObjectId().toString(),
        user: new ObjectId().toString(),
      };

      await expect(service.deletePoint(payload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.POINT_NOT_FOUND,
      );
    });

    it('should throw an error if user does not have permission', async () => {
      service['validatePermissionForJobTemplate'] = jest
        .fn()
        .mockImplementation(() => {
          throw new ForbiddenException();
        });

      const payload: DeletePointJobTemplateDto = {
        id: mockJobTemplateData._id.toString(),
        pointId: mockJobTemplateData.points[0]._id.toString(),
        user: new ObjectId().toString(),
      };

      await expect(service.deletePoint(payload)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should delete a point from the job template', async () => {
      await initMockJobTemplate();
      service['validatePermissionForJobTemplate'] = jest
        .fn()
        .mockResolvedValue(null);

      const payload: DeletePointJobTemplateDto = {
        id: mockJobTemplateData._id.toString(),
        pointId: mockJobTemplateData.points[0]._id.toString(),
        user: new ObjectId().toString(),
      };

      const result = await service.deletePoint(payload);
      expect(result).toHaveProperty('_id', mockJobTemplateData._id);
      expect(result).toMatchSchema(jobTemplateTest.deletePointSchema);
    });
  });

  describe('copyJobTemplates', () => {
    it('should throw an error if user does not have permission', async () => {
      service['validatePermissionForJobTemplate'] = jest
        .fn()
        .mockImplementation(() => {
          throw new ForbiddenException();
        });

      const payload: CopyJobTemplateDto = {
        sourceUnitIds: [mockUnitData._id.toString()],
        targetUnitIds: [mockUnitData._id.toString()],
        user: new ObjectId().toString(),
      };

      await expect(service.copyJobTemplates(payload)).rejects.toThrow(
        ForbiddenException,
      );
    });

    it('should throw an error if source units are not found', async () => {
      service['validatePermissionForJobTemplate'] = jest
        .fn()
        .mockResolvedValue(null);

      const payload: CopyJobTemplateDto = {
        sourceUnitIds: [mockUnitData._id.toString()],
        targetUnitIds: [new ObjectId().toString()],
        user: new ObjectId().toString(),
      };

      await expect(service.copyJobTemplates(payload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.UNITS_NOT_FOUND,
      );
    });

    it('should throw an error if target units are not found', async () => {
      const payload = {
        sourceUnitIds: [mockUnitData._id.toString()],
        targetUnitIds: [new ObjectId().toString()],
        user: new ObjectId().toString(),
      };

      await expect(service.copyJobTemplates(payload)).rejects.toThrow(
        JOB_TEMPLATE_MESSAGE_KEY.UNITS_NOT_FOUND,
      );
    });

    it('should copy job templates from source units to target units', async () => {
      const payload = {
        sourceUnitIds: [mockUnitData._id.toString()],
        targetUnitIds: [mockUnitData._id.toString()],
        user: new ObjectId().toString(),
      };

      const result = await service.copyJobTemplates(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(jobTemplateTest.copyJobTemplatesSchema);
    });
  });
});
