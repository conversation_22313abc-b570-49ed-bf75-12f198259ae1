import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { COSTCENTER_MESSAGES } from '~/shared/messages/costcenter.message';

import { CostCenterQueryDto } from './costcenter.dto';
import { CostCenterService } from './costcenter.service';

@Controller()
export class CostCenterController {
  constructor(private readonly costCenterService: CostCenterService) {}

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(CostCenterQueryDto))
  @MessagePattern({ cmd: COSTCENTER_MESSAGES.GET_LIST })
  async findAll(params: CostCenterQueryDto) {
    return this.costCenterService.findAll(params);
  }

  @MessagePattern({ cmd: COSTCENTER_MESSAGES.GET_ONE })
  async findOne(id: string) {
    return this.costCenterService.findOne(id);
  }

  @MessagePattern({
    cmd: COSTCENTER_MESSAGES.SYNC_COSTCENTERS_FROM_THIRD_PARTY,
  })
  public async syncFrom3rdParty(@Payload() payload: any) {
    const { type, actionType, thirdParty } = payload;
    return this.costCenterService.syncFrom3rdParty({
      type,
      actionType,
      ...thirdParty,
    });
  }
}
