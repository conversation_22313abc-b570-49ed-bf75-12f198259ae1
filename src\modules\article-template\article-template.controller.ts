import { Body, Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { ARTICLE_TEMPLATE_MESSAGES } from '~/shared/messages/article-template.message';

import { ArticleTemplateService } from './article-template.service';
import {
  CreateArticleTemplateDto,
  UpdateArticleTemplateDto,
} from './dtos/article-template.dto';

@Controller('article-templates')
export class ArticleTemplateController {
  constructor(
    private readonly articleTemplateService: ArticleTemplateService,
  ) {}

  @UsePipes(new ZodValidationPipe(CreateArticleTemplateDto))
  @MessagePattern({ cmd: ARTICLE_TEMPLATE_MESSAGES.CREATE_TEMPLATE })
  async create(@Body() payload: CreateArticleTemplateDto) {
    return this.articleTemplateService.create(payload);
  }

  @MessagePattern({ cmd: ARTICLE_TEMPLATE_MESSAGES.GET_LIST })
  async getList(@Payload() params: QueryParamsDto) {
    return this.articleTemplateService.findAll(params);
  }

  @MessagePattern({ cmd: ARTICLE_TEMPLATE_MESSAGES.GET_TEMPLATE })
  async findOne(@Payload() id: string) {
    return this.articleTemplateService.findOne(id);
  }

  @UsePipes(new ZodValidationPipe(UpdateArticleTemplateDto))
  @MessagePattern({ cmd: ARTICLE_TEMPLATE_MESSAGES.UPDATE_TEMPLATE })
  async update(@Body() payload: UpdateArticleTemplateDto) {
    return this.articleTemplateService.update(payload);
  }

  @MessagePattern({ cmd: ARTICLE_TEMPLATE_MESSAGES.DELETE_TEMPLATE })
  async delete(@Payload() id: string) {
    return this.articleTemplateService.delete(id);
  }
}
