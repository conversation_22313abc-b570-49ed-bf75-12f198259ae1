import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';

const ListOccupationQuerySchema = z
  .strictObject({ dateOfReport: z.dateString().optional() })
  .merge(
    QueryParamsSchema.pick({
      sortBy: true,
      sortDir: true,
    }),
  );
const ListVacancyQuerySchema = z
  .strictObject({
    dateOfReport: z.dateString().optional(),
  })
  .merge(
    QueryParamsSchema.pick({
      sortBy: true,
      sortDir: true,
    }),
  );

export class ListOccupationQueryDto extends createZodDto(
  ListOccupationQuerySchema,
) {}

export class ListVacancyQueryDto extends createZodDto(ListVacancyQuerySchema) {}
