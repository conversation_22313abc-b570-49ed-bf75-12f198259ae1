import { Modu<PERSON> } from '@nestjs/common';

import { CheckOutService } from '~/modules/night-registration/check-out/check-out.service';
import { EmailModule } from '~/processors/email/email.module';
import { LoggerModule } from '~/processors/logger/logger.module';
import { TcpClientsModule } from '~/processors/tcp-client/tcp-client.module';

import { LentoIntegrateCheckinService } from './lento-integrate-checkin.service';
import { NightRegistrationNationalityModel } from './models/night-registration-nationality.model';
import { NationalityService } from './nationality.service';
import { NightRegistratrionController } from './night-registration.controller';
import { NightRegistrationService } from './night-registration.service';
import { TenantMovedService } from './tenant-moved.service';

@Module({
  controllers: [NightRegistratrionController],
  providers: [
    NightRegistrationService,
    NationalityService,
    TenantMovedService,
    {
      provide: 'NIGHT_REGISTRATION_NATIONALITY_MODEL',
      useValue: NightRegistrationNationalityModel,
    },
    LentoIntegrateCheckinService,
    CheckOutService,
  ],
  imports: [EmailModule, TcpClientsModule, LoggerModule],
  exports: [NightRegistrationService, NationalityService, TenantMovedService],
})
export class NightRegistrationModule {}
