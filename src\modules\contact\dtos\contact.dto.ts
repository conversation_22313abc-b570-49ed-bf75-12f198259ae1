import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { ContactRole, ContactType } from '~/shared/enums/contact.enum';
import { isValidObjectId } from '~/utils';

const contactRoleValues = Object.values(ContactRole) as [string, ...string[]];
const contactTypeValues = Object.values(ContactType) as [string, ...string[]];

export const AddressSchema = z.strictObject({
  _id: z
    .string()
    .refine((val) => isValidObjectId(val))
    .optional(),
  country: z.string().refine((val) => isValidObjectId(val)),
  region: z.string().refine((val) => isValidObjectId(val)),
  street: z.string().max(128).trim().min(1),
  city: z.string().max(128).trim().min(1),
  number: z.string().max(128).trim().min(1),
  suffix: z
    .string()
    .max(128)
    .optional()
    .nullable()
    .transform((val) => {
      if (val === null) return '';
      else return val;
    }),
  postalCode: z.string().max(16),
});

export const ContactIdSchema = z.strictObject({
  id: z.string().refine((val) => isValidObjectId(val)),
});

export const ContactTypeSchema = z.strictObject({
  contactType: z.enum(contactTypeValues),
});

export const ContactRoleSchema = z.strictObject({
  contactRole: z.enum(contactRoleValues),
});

export const GetContactSchema = z.strictObject({
  contactRole: z.enum(contactRoleValues),
  isInternal: z.boolean().optional(),
});

const phoneRegex = /^[0-9.\+\-\/\(\)\s]+$/;
export const BaseContactSchema = z.strictObject({
  id: z
    .string()
    .refine((val) => isValidObjectId(val))
    .optional(),
  isActive: z.boolean().optional(),
  email: z.string().email().max(128).trim(),
  warningEmail: z
    .string()
    .email()
    .max(128)
    .trim()
    .optional()
    .nullable()
    .transform((val) => {
      if (val === null) return '';
      else return val;
    }),
  phone1: z.string().min(8).max(24).trim().regex(phoneRegex),
  phone2: z
    .string()
    .min(8)
    .max(24)
    .trim()
    .regex(phoneRegex)
    .optional()
    .nullable()
    .transform((val) => {
      if (val === null) return '';
      else return val;
    }),
  address1: AddressSchema,
  address2: AddressSchema.optional().nullable(),
  remark: z
    .string()
    .max(512)
    .optional()
    .nullable()
    .transform((val) => {
      if (val === null) return '';
      else return val;
    }),
  website: z
    .string()
    .default('')
    .optional()
    .nullable()
    .transform((val) => {
      if (val === null) return '';
      else return val;
    }),
});

export class GetContactDto extends createZodDto(
  GetContactSchema.merge(QueryParamsSchema),
) {}

export class GetContactDetailDto extends createZodDto(
  GetContactSchema.merge(ContactIdSchema),
) {}

export class ContactTypeDto extends createZodDto(ContactTypeSchema) {}

export class AddressDto extends createZodDto(AddressSchema) {}
