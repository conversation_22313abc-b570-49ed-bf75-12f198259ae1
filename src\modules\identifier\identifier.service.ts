import { Injectable } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Model } from 'mongoose';

import { JobModel } from '~/modules/job/job.model';
import { IdentifierType } from '~/shared/enums/identifier.enum';
import { JobPeriodTypeEnum } from '~/shared/enums/job.enum';
import { InjectModel } from '~/transformers/model.transformer';

import { ContractModel } from '../contract/contract.model';
import { InvoiceModel } from '../invoice/invoice.model';
import { IdentifierModel } from './identifier.model';

@Injectable()
export class IdentifierService {
  constructor(
    @InjectModel(JobModel)
    private readonly jobModel: Model<JobModel>,
    @InjectModel(IdentifierModel)
    private readonly identifierModel: Model<IdentifierModel>,
    @InjectModel(ContractModel)
    private readonly contractModel: Model<ContractModel>,
    @InjectModel(InvoiceModel)
    private readonly invoiceModel: Model<InvoiceModel>,

    private readonly configService: ConfigService,
  ) {}

  async generateIdentifier(type: IdentifierType) {
    switch (type) {
      case IdentifierType.JOB:
        return await this.generateIdentifierForJob();
      case IdentifierType.SCHEDULER:
        return await this.generateIdentifierForScheduler();
      case IdentifierType.CONTRACT:
        return await this.generateIdentifierForContract();
      case IdentifierType.INVOICE:
        return await this.generateIdentifierForInvoice();
      default:
        return null;
    }
  }

  async deleteIdentifier(type: IdentifierType, maxIdentifier: any) {
    await this.identifierModel
      .deleteOne({
        type: type,
        maxIdentifier: maxIdentifier,
        isUsed: true,
      })
      .exec();
  }

  private async generateIdentifierForJob() {
    const currentDate = new Date();
    const year = currentDate.getFullYear();
    const month = (currentDate.getMonth() + 1).toString().padStart(2, '0');
    const date = currentDate.getDate().toString().padStart(2, '0');
    const fullYearOfCurrentDate = `${year}${month}${date}`;

    const { fullYearOfIdentifier, maxIdentifier } = await this.getMaxIdentifier(
      IdentifierType.JOB,
    );
    let nextIdentifier = maxIdentifier + 1;

    if (fullYearOfCurrentDate !== fullYearOfIdentifier) {
      nextIdentifier = 1;
    }

    let fullMaxIdentifier = `${fullYearOfCurrentDate}${nextIdentifier.toString().padStart(4, '0')}`;
    let isUsed = await this.isMaxIdentifierAlreadyUsed(
      IdentifierType.JOB,
      fullMaxIdentifier,
    );
    while (isUsed) {
      nextIdentifier++;
      fullMaxIdentifier = `${fullYearOfCurrentDate}${nextIdentifier.toString().padStart(4, '0')}`;
      isUsed = await this.isMaxIdentifierAlreadyUsed(
        IdentifierType.JOB,
        fullMaxIdentifier,
      );
    }
    await this.createMaxIdentifier(IdentifierType.JOB, fullMaxIdentifier);
    return fullMaxIdentifier;
  }

  private async isMaxIdentifierAlreadyUsed(
    type: IdentifierType,
    identifier: string,
  ) {
    let isUsed = false;
    switch (type) {
      case IdentifierType.JOB: {
        isUsed = !!(await this.identifierModel.exists({
          maxIdentifier: identifier,
          isUsed: true,
          type: IdentifierType.JOB,
        }));
        break;
      }
      case IdentifierType.SCHEDULER:
        break;
      case IdentifierType.INVOICE: {
        isUsed = !!(await this.identifierModel.exists({
          maxIdentifier: identifier,
          isUsed: true,
          type: IdentifierType.INVOICE,
        }));
        break;
      }
      default:
        break;
    }
    return isUsed;
  }

  private async createMaxIdentifier(
    type: IdentifierType,
    maxIdentifier: string,
  ) {
    await this.identifierModel.create({
      type: type,
      maxIdentifier: maxIdentifier,
      isUsed: true,
    });
  }

  private async generateIdentifierForScheduler() {
    const { maxIdentifier } = await this.getMaxIdentifier(
      IdentifierType.SCHEDULER,
    );

    const nextIdentifier = maxIdentifier + 1;

    return `${nextIdentifier.toString().padStart(5, '0')}`;
  }

  public async getMaxIdentifier(identifierType: IdentifierType) {
    let maxIdentifier = 0;
    let fullYearOfIdentifier = '';

    switch (identifierType) {
      case IdentifierType.JOB: {
        const result = await this.getMaxIdentifierForJob();
        maxIdentifier = result.maxIdentifier;
        fullYearOfIdentifier = result.fullYearOfIdentifier;
        break;
      }
      case IdentifierType.SCHEDULER: {
        const result = await this.getMaxIdentifierForScheduler();
        maxIdentifier = result.maxIdentifier;
        break;
      }
      case IdentifierType.CONTRACT: {
        const result = await this.getMaxIdentifierForContract();
        maxIdentifier = result.maxIdentifier;
        fullYearOfIdentifier = result.fullYearOfIdentifier;
        break;
      }
      case IdentifierType.INVOICE: {
        const result = await this.getMaxIdentifierForInvoice();
        maxIdentifier = result.maxIdentifier;
        fullYearOfIdentifier = result.fullYearOfIdentifier;
        break;
      }
      default:
        break;
    }

    return { fullYearOfIdentifier, maxIdentifier };
  }

  private async getMaxIdentifierForJob() {
    const result = await this.jobModel.aggregate([
      {
        $group: {
          _id: '',
          identifier: { $max: '$identifier' },
        },
      },
    ]);

    const identifier =
      result && result.length > 0 && result[0].identifier
        ? result[0].identifier
        : undefined;

    let maxIdentifier = 0;
    let fullYearOfIdentifier = '';

    if (identifier) {
      maxIdentifier = parseInt(identifier.substring(8, 12));
      fullYearOfIdentifier = identifier.substring(0, 8);
    }

    return { maxIdentifier, fullYearOfIdentifier };
  }

  private async getMaxIdentifierForScheduler() {
    const result = await this.jobModel.aggregate([
      {
        $match: {
          type: JobPeriodTypeEnum.PERIODIC,
        },
      },
      {
        $group: {
          _id: '',
          fIdentifier: { $max: '$fIdentifier' },
        },
      },
    ]);

    const identifier =
      result && result.length > 0 && result[0].fIdentifier
        ? result[0].fIdentifier
        : undefined;

    let maxIdentifier = 0;

    if (identifier) {
      maxIdentifier = parseInt(identifier);
    }

    return { maxIdentifier };
  }

  private async getMaxIdentifierForContract() {
    const result = await this.contractModel.aggregate([
      {
        $group: {
          _id: '',
          identifier: { $max: '$identifier' },
        },
      },
    ]);

    const identifier =
      result && result.length > 0 && result[0].identifier
        ? result[0].identifier
        : undefined;

    let maxIdentifier = 0;
    let fullYearOfIdentifier = '';

    if (identifier) {
      maxIdentifier = parseInt(identifier.substring(4));
      fullYearOfIdentifier = identifier.substring(0, 4);
    }

    return { maxIdentifier, fullYearOfIdentifier };
  }

  private async getMaxIdentifierForInvoice() {
    const result = await this.invoiceModel.aggregate([
      {
        $group: {
          _id: '',
          identifier: { $max: '$identifier' },
        },
      },
    ]);

    const identifier =
      result && result.length > 0 && result[0].identifier
        ? result[0].identifier
        : undefined;

    let maxIdentifier = 0;
    let fullYearOfIdentifier = '';

    if (identifier) {
      maxIdentifier = parseInt(identifier.substring(5, 12));
      fullYearOfIdentifier = identifier.substring(0, 4);
    }

    return { maxIdentifier, fullYearOfIdentifier };
  }

  private async generateIdentifierForContract() {
    const { fullYearOfIdentifier, maxIdentifier } = await this.getMaxIdentifier(
      IdentifierType.CONTRACT,
    );

    const nextIdentifier = maxIdentifier + 1;
    const currentYear = `${new Date().getFullYear()}`;
    const prefix =
      fullYearOfIdentifier !== currentYear ? currentYear : fullYearOfIdentifier;

    return `${prefix}${nextIdentifier.toString().padStart(6, '0')}`;
  }

  private async generateIdentifierForInvoice() {
    const invoiceIdentifierDigit = this.configService.get<number>(
      'app.invoiceIdentifierDigit',
    )!;
    const { fullYearOfIdentifier, maxIdentifier } = await this.getMaxIdentifier(
      IdentifierType.INVOICE,
    );
    const fullYearOfCurrentDate = `${new Date().getFullYear()}`;
    let nextIdentifier = maxIdentifier + 1;

    if (fullYearOfCurrentDate !== fullYearOfIdentifier) {
      nextIdentifier = 1;
    }

    let fullMaxIdentifier = `${fullYearOfCurrentDate}${invoiceIdentifierDigit}${nextIdentifier.toString().padStart(7, '0')}`;
    let isUsed = await this.isMaxIdentifierAlreadyUsed(
      IdentifierType.INVOICE,
      fullMaxIdentifier,
    );
    while (isUsed) {
      nextIdentifier++;
      fullMaxIdentifier = `${fullYearOfCurrentDate}${invoiceIdentifierDigit}${nextIdentifier.toString().padStart(7, '0')}`;
      isUsed = await this.isMaxIdentifierAlreadyUsed(
        IdentifierType.INVOICE,
        fullMaxIdentifier,
      );
    }
    await this.createMaxIdentifier(IdentifierType.INVOICE, fullMaxIdentifier);
    return fullMaxIdentifier;
  }

  public async generateDummyIdentifierForInvoice() {
    const invoiceIdentifierDigit = this.configService.get<number>(
      'app.invoiceIdentifierDigit',
    )!;
    const fullYearOfCurrentDate = `${new Date().getFullYear()}`;
    const nextIdentifier = 0;

    return `${fullYearOfCurrentDate}${invoiceIdentifierDigit}${nextIdentifier.toString().padStart(7, '0')}`;
  }
}
