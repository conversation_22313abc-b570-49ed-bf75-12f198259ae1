import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { Gender, Language } from '~/shared/enums/tenant-user.enum';
import { isValidObjectId } from '~/utils';

import {
  BaseContactSchema,
  ContactIdSchema,
  ContactRoleSchema,
} from './contact.dto';

const genderValues = Object.values(Gender) as [string, ...string[]];
const languageValues = Object.values(Language) as [string, ...string[]];

export const OrganizationPersonSchema = z.strictObject({
  contactId: z.string().refine((value) => isValidObjectId(value)),
  roleFunction: z
    .string()
    .max(64)
    .trim()
    .optional()
    .nullable()
    .transform((val) => {
      if (val === null) return '';
      else return val;
    }),
});

export const PersonContactSchema = z
  .strictObject({
    firstName: z.string().min(1).max(64).trim(),
    lastName: z.string().min(1).max(64).trim(),
    gender: z.enum(genderValues),
    language: z.enum(languageValues),
  })
  .merge(BaseContactSchema)
  .merge(ContactRoleSchema);

export const CreatePersonContactSchema = z
  .strictObject({
    organizations: z
      .array(OrganizationPersonSchema)
      .refine((value) => {
        // Uniqe organization ids
        const organizationIds = value.map((org) => org.contactId);

        return new Set(organizationIds).size === organizationIds.length;
      }, 'Organization ids must be unique')
      .optional(),
    contactType: z.enum(['person']),
  })
  .merge(PersonContactSchema);

export const UpdateOrganizationPersonSchema = z
  .strictObject({
    organizations: z
      .array(OrganizationPersonSchema)
      .refine((value) => {
        // Unique organization ids
        const organizationIds = value.map((org) => org.contactId);

        return new Set(organizationIds).size === organizationIds.length;
      }, 'Organization ids must be unique')
      .optional(),
    contactType: z.enum(['person']),
  })
  .merge(PersonContactSchema)
  .merge(ContactIdSchema);

export class CreatePersonContactDto extends createZodDto(
  CreatePersonContactSchema,
) {}

export class UpdatePersonContactDto extends createZodDto(
  UpdateOrganizationPersonSchema,
) {}

export class OrganizationPersonDto extends createZodDto(
  OrganizationPersonSchema,
) {}
