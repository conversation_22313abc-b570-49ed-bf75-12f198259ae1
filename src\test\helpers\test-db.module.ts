import { <PERSON><PERSON><PERSON>, OnModuleDestroy, OnModuleInit } from '@nestjs/common';
import dayjs from 'dayjs';
import isoWeek from 'dayjs/plugin/isoWeek';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import utc from 'dayjs/plugin/utc';
import mongoose from 'mongoose';

dayjs.extend(utc);
dayjs.extend(isoWeek);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);

@Module({})
export class TestDBModule implements OnModuleInit, OnModuleDestroy {
  async onModuleInit() {
    const uri = process.env.JEST_MONGO_URI;
    if (!uri) {
      throw new Error('JEST_MONGO_URI is not defined');
    }
    await mongoose.connect(uri);
  }

  async onModuleDestroy() {
    await mongoose.disconnect();
  }
}
