import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { NightRegistrationNationalityModel } from '~/modules/night-registration/models/night-registration-nationality.model';

const nightRegistrationNationalityModel = getModelForClass(
  NightRegistrationNationalityModel,
);

export const mockNightRegistrationNationalityData = {
  _id: new ObjectId(),
  name: 'Albanian',
  code: 'AL',
};

export async function initMockNightRegistrationNationality(doc?: any) {
  const { _id, ...rest } = { ...mockNightRegistrationNationalityData, ...doc };
  await nightRegistrationNationalityModel.replaceOne({ _id }, rest, {
    upsert: true,
  });
}
