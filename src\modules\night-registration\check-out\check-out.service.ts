import { HttpStatus, Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';

import { LocationModel } from '~/modules/location/location.model';
import { NightRegistrationReservationModel } from '~/modules/night-registration/models/night-registration-reservation.model';
import { NightRegistrationResidentModel } from '~/modules/night-registration/models/night-registration-resident.model';
import { UnitModel } from '~/modules/unit/unit.model';
import { LentoIntegrateErrorCodesEnum } from '~/shared/enums/lento-integrate.enum';
import { LentoIntegrateException } from '~/shared/exception/lento-integrate-exception.dto';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';

@Injectable()
export class CheckOutService {
  constructor(
    @InjectModel(NightRegistrationResidentModel)
    private readonly residentModel: MongooseModel<NightRegistrationResidentModel>,
    @InjectModel(NightRegistrationReservationModel)
    private readonly reservationModel: MongooseModel<NightRegistrationReservationModel>,
    @InjectModel(LocationModel)
    private readonly locationModel: MongooseModel<LocationModel>,
    @InjectModel(UnitModel)
    private readonly unitModel: MongooseModel<UnitModel>,
  ) {}

  async checkOut(payload: any) {
    const { originalBody } = payload;
    const { resident: reqResident } = originalBody;
    let errorMessage = '';
    const processName = 'Check-Out';

    const resident = await this.findOneResident(reqResident);

    if (!resident) {
      errorMessage = 'Resident not found';
      throw new LentoIntegrateException(
        errorMessage,
        HttpStatus.NOT_FOUND,
        LentoIntegrateErrorCodesEnum.RESIDENT_NOT_FOUND,
        processName,
      );
    }

    const location = await this.locationModel
      .findOne({
        fullAddress: originalBody.location,
      })
      .lean();

    if (!location) {
      errorMessage = `Location not found for address: ${originalBody.location}`;
      throw new LentoIntegrateException(
        errorMessage,
        HttpStatus.NOT_FOUND,
        LentoIntegrateErrorCodesEnum.LOCATION_NOT_FOUND,
        processName,
      );
    }

    const targetUnit = await this.unitModel
      .findOne({
        name: originalBody.unit.details.description.name,
        location: location._id,
        isRoot: false,
        isActive: true,
      })
      .lean();

    if (!targetUnit) {
      errorMessage = `Unit not found: ${originalBody.unit.details.description.name}`;
      throw new LentoIntegrateException(
        errorMessage,
        HttpStatus.NOT_FOUND,
        LentoIntegrateErrorCodesEnum.UNIT_NOT_FOUND,
        processName,
      );
    }

    console.info('Test', targetUnit._id.toString());

    let correctUnitId: ObjectId | null = null;
    const subUnitCount = await this.unitModel.countDocuments({
      parent: targetUnit._id,
      isActive: true,
    });

    if (subUnitCount) {
      const subUnit = await this.unitModel.findOne({
        name: originalBody.room.description.name,
        parent: targetUnit._id,
        isActive: true,
      });

      if (!subUnit) {
        errorMessage = `Sub-unit not found: ${originalBody.room.description.name}`;
        throw new LentoIntegrateException(
          errorMessage,
          HttpStatus.NOT_FOUND,
          LentoIntegrateErrorCodesEnum.UNIT_NOT_FOUND,
          processName,
        );
      }
      correctUnitId = subUnit._id;
    } else {
      correctUnitId = targetUnit._id;
    }

    const reservation = (
      await this.reservationModel
        .find({
          isDeleted: false,
          resident: resident._id,
          unit: correctUnitId,
          $or: [
            { departureDate: { $exists: false } },
            { departureDate: null },
            { departureDate: { $gt: originalBody.departureDate } },
          ],
        })
        .sort({ arrivalDate: -1 })
        .limit(1)
        .lean()
    )[0];

    if (!reservation) {
      errorMessage =
        'Reservation not found for the resident in the specified unit';
      throw new LentoIntegrateException(
        errorMessage,
        HttpStatus.NOT_FOUND,
        LentoIntegrateErrorCodesEnum.RESERVATION_NOT_FOUND,
        processName,
      );
    }

    return this.reservationModel.updateOne(
      { _id: reservation._id },
      {
        $set: {
          departureDate: originalBody.departureDate,
          remarks: originalBody.remarks,
        },
      },
    );
  }

  public async findOneResident(resident: any) {
    return this.residentModel
      .findOne({
        firstName: resident.firstName,
        lastName: resident.lastName,
        $expr: {
          $eq: [
            { $dateToString: { format: '%d/%m/%Y', date: '$dateOfBirth' } },
            {
              $dateToString: {
                format: '%d/%m/%Y',
                date: dayjs.utc(resident.dateOfBirth).toDate(),
              },
            },
          ],
        },
      })
      .lean();
  }
}
