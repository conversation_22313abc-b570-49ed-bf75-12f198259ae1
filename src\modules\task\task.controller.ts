import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import {
  CheckOverLapTaskDto,
  CreateTaskDto,
  UpdateTaskDto,
} from '~/modules/task/dtos/create-task.dto';
import { TaskService } from '~/modules/task/task.service';
import { TASK_MESSAGES } from '~/shared/messages/task.message';

@Controller('task')
export class TaskController {
  constructor(private readonly taskService: TaskService) {}

  @UsePipes(new ZodValidationPipe(CreateTaskDto))
  @MessagePattern({ cmd: TASK_MESSAGES.CREATE_TASK })
  async create(@Payload() task: CreateTaskDto) {
    return this.taskService.create(task);
  }

  @MessagePattern({ cmd: TASK_MESSAGES.GET_TASK })
  async findOne(@Payload() id: string) {
    return this.taskService.findOne(id);
  }

  @UsePipes(new ZodValidationPipe(UpdateTaskDto))
  @MessagePattern({ cmd: TASK_MESSAGES.UPDATE_TASK })
  async update(@Payload() task: UpdateTaskDto) {
    return this.taskService.update(task);
  }

  @MessagePattern({ cmd: TASK_MESSAGES.DELETE_TASK })
  async delete(@Payload() id: string) {
    return this.taskService.delete(id);
  }

  @UsePipes(new ZodValidationPipe(CheckOverLapTaskDto))
  @MessagePattern({ cmd: TASK_MESSAGES.CHECK_OVERLAP_TASK })
  async checkOverlapTask(@Payload() payload: CheckOverLapTaskDto) {
    return this.taskService.checkOverlapTask(payload);
  }
}
