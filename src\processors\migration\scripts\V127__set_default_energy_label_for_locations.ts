import { EnergyLabel } from '~/shared/enums/location.enum';

import { MigrationContext } from '../migration.service';

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const locationCollection = context.destinationClient
      ?.db()
      .collection('locations');

    // Update all locations to set the default energyLabel to EnergyLabel.G
    const result = await locationCollection?.updateMany(
      { energyLabel: { $exists: false } },
      { $set: { energyLabel: EnergyLabel.G } },
    );

    console.log(
      `Updated ${result?.modifiedCount} locations with default energyLabel.`,
    );

    const after = new Date().getTime();
    console.log(
      `#endregion migrate V127__set_default_energy_label_for_locations with: ${after - before} ms`,
    );
  } catch (error) {
    console.error('Error during migration:', error);
  }
};

export default up;
