import { Controller } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { REGION_MESSAGES } from '~/shared/messages/region.message';

import { RegionService } from './region.service';

@Controller('region')
export class RegionController {
  constructor(private regionService: RegionService) {}

  @HTTPDecorators.Paginator
  @MessagePattern({ cmd: REGION_MESSAGES.GET_LIST })
  async getList(@Payload() payload: any) {
    return await this.regionService.getList(payload);
  }
}
