import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { EquipmentEnum } from '~/shared/enums/equipment.enum';

const PlanningOverviewQueryParamsBaseSchema = z.strictObject({
  isoWeek: z.coerce.number(),
  year: z.coerce.number(),
  timezone: z
    .string()
    .optional()
    .default('+00:00')
    .refine((value) => /^[+-][0-1][0-9]:[0-5][0-9]$/.test(value), {
      message: 'Invalid timezone format',
    }),
  location: z.string().refine(isValidObjectId).optional(),
});

const TeamPlanningOverviewQueryParamsSchema =
  PlanningOverviewQueryParamsBaseSchema.extend({
    team: z.string().refine(isValidObjectId),
    employees: z
      .union([
        z.string().refine(isValidObjectId),
        z.array(z.string().refine(isValidObjectId)),
      ])
      .transform((value) => (Array.isArray(value) ? value : [value]))
      .optional(),
  });

const EquipmentPlanningOverviewQueryParamsSchema =
  PlanningOverviewQueryParamsBaseSchema.extend({
    equipmentType: z.nativeEnum(EquipmentEnum),
  });

export class TeamPlanningOverviewQueryParamsDto extends createZodDto(
  TeamPlanningOverviewQueryParamsSchema,
) {}

export class EquipmentPlanningOverviewQueryParamsDto extends createZodDto(
  EquipmentPlanningOverviewQueryParamsSchema,
) {}
