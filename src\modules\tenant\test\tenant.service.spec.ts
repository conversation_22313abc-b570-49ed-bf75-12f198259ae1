import { BadRequestException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { TENANT_MESSAGE_KEYS } from '~/shared/message-keys/tenant.message-keys';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockTenant, mockTenantData } from '~/test/mocks/tenant.mock';

import { TenantModel } from '../tenant.model';
import { TenantService } from '../tenant.service';
import { tenantTest } from './tenant.dto.test';

describe('TenantService', () => {
  let service: TenantService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [TenantService, ...testInjectModel([TenantModel])],
    }).compile();

    service = module.get(TenantService);

    // Init data
    await initMockTenant();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getCompanyInfomation', () => {
    it('should return company information when tenant exists', async () => {
      const result = await service.getCompanyInfomation(
        mockTenantData._id.toString(),
      );
      expect(result).toBeDefined();
      expect(result).toMatchSchema(tenantTest.getCompanyInfomationSchema);
    });

    it('should throw error when tenant does not exist', async () => {
      await expect(
        service.getCompanyInfomation(new ObjectId().toString()),
      ).rejects.toThrow(new BadRequestException(TENANT_MESSAGE_KEYS.NOT_FOUND));
    });

    it('should throw error when tenant config does not exist', async () => {
      const tenantWithoutConfig = { ...mockTenantData, tenantConfigs: null };
      await initMockTenant(tenantWithoutConfig);

      await expect(
        service.getCompanyInfomation(mockTenantData._id.toString()),
      ).rejects.toThrow(
        new BadRequestException(TENANT_MESSAGE_KEYS.CONFIG_NOT_FOUND),
      );
    });
  });
});
