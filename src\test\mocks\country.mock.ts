import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { CountryModel } from '~/modules/country/country.model';

const countryModel = getModelForClass(CountryModel);

export const mockCountryData = {
  _id: new ObjectId(),
  code: 'nl',
  name: 'Netherlands',
  createAt: new Date(),
  updateAt: new Date(),
};

export async function initMockCountry(doc?: any) {
  const { _id, ...rest } = { ...mockCountryData, ...doc };
  await countryModel.replaceOne({ _id }, rest, { upsert: true });
}
