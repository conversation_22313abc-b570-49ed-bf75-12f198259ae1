import { ConfigService } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { PDF_SERVICE_CLIENT } from '~/constants/app.constant';
import { ContactModel } from '~/modules/contact/contact.model';
import { ContractModel } from '~/modules/contract/contract.model';
import { EmailTemplateModel } from '~/modules/email-template/email-template.model';
import { EmailTemplateService } from '~/modules/email-template/email-template.service';
import { LocationModel } from '~/modules/location/location.model';
import { UnitModel } from '~/modules/unit/unit.model';
import { EmailService } from '~/processors/email/email.service';
import { MyLogger } from '~/processors/logger/logger.service';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockAgreementLine,
  mockAgreementLineData,
} from '~/test/mocks/agreementline.mock';
import { initMockContact, mockContactData } from '~/test/mocks/contact.mock';
import { initMockContract, mockContractData } from '~/test/mocks/contract.mock';
import { initMockCostCenter } from '~/test/mocks/costcenter.mock';
import {
  initMockCostlineGeneral,
  mockCostlineGeneralData,
} from '~/test/mocks/costlinegeneral.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';

import { LentoIntegrateCheckinService } from '../lento-integrate-checkin.service';
import { NightRegistrationNationalityModel } from '../models/night-registration-nationality.model';
import { NightRegistrationReservationModel } from '../models/night-registration-reservation.model';
import { NightRegistrationResidentModel } from '../models/night-registration-resident.model';
import { NightRegistrationWarningModel } from '../models/night-registration-warning.model';
import { NightRegistrationWarningCategoryModel } from '../models/night-registration-warning-category';
import { NightRegistrationService } from '../night-registration.service';

describe('LentoIntegrateCheckinService', () => {
  let service: LentoIntegrateCheckinService;
  const unitName: string = 'Test Unit';

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        LentoIntegrateCheckinService,
        NightRegistrationService,
        ...testInjectModel([
          LocationModel,
          ContractModel,
          NightRegistrationReservationModel,
          NightRegistrationResidentModel,
          UnitModel,
          NightRegistrationWarningCategoryModel,
          NightRegistrationWarningModel,
          EmailTemplateModel,
          ContactModel,
          NightRegistrationNationalityModel,
        ]),
        ConfigService,
        {
          provide: EmailService,
          useValue: {},
        },
        {
          provide: PDF_SERVICE_CLIENT,
          useValue: { send: jest.fn() },
        },
        MyLogger,
        EmailTemplateService,
      ],
    }).compile();

    service = module.get(LentoIntegrateCheckinService);
    const subUnitId = new ObjectId();

    // Init data
    await Promise.all([
      initMockUnit({ parent: null }),
      initMockUnit({
        _id: subUnitId,
        name: unitName,
        isRoot: false,
        parent: mockUnitData._id,
        maxOccupants: 3,
      }),
      initMockLocation({ maximumStayDuration: 1 }),
      initMockContact(),
      initMockContract({
        contact: mockContactData._id,
        location: mockLocationData._id,
        endDate: null,
        agreementLines: [mockAgreementLineData._id],
      }),
      initMockCostCenter({
        locations: [mockLocationData._id],
      }),
      initMockAgreementLine({
        contract: mockContractData._id,
        costLineGenerals: [mockCostlineGeneralData._id],
        units: [mockUnitData._id],
      }),
      initMockCostlineGeneral({
        agreementLine: mockAgreementLineData._id,
        endDate: null,
      }),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('lentoIntegrateProcessCheckIn', () => {
    it('should process check-in successfully', async () => {
      const payload = {
        messageId: new ObjectId(),
        endpoint: '/api/night-registrations/reservations/process-checkin',
        originalBody: {
          resident: {
            customer: mockContactData.displayName,
            firstName: 'John',
            lastName: 'Doe',
            dateOfBirth: '1990-01-01',
            email: '<EMAIL>',
            phoneNumber: '0123456789',
            gender: 'Male',
            nationality: '',
          },
          arrivalDate: new Date(),
          remarks: 'Test check-in',
          location: mockLocationData.fullAddress,
          isUnitHasParent: false,
          unit: unitName,
          bed: {
            description: { name: unitName },
            capacity: mockUnitData.maxOccupants,
            availability: true,
            beds: [],
          },
        },
        timestamp: new Date(),
        source: 'webhook-cronjob',
        processedAt: new Date(),
      };

      const result = await service.lentoIntegrateProcessCheckIn(payload);
      expect(result).toBeDefined();
    });
    it('Should not found contact', async () => {
      const payload = {
        messageId: new ObjectId(),
        endpoint: '/api/night-registrations/reservations/process-checkin',
        originalBody: {
          resident: {
            customer: 'Unknown Customer',
            firstName: 'John',
            lastName: 'Doe',
            dateOfBirth: '1990-01-01',
            email: '<EMAIL>',
            phoneNumber: '0123456789',
            gender: 'Male',
            nationality: '',
          },
          arrivalDate: new Date(),
          remarks: 'Test check-in',
          location: mockLocationData.fullAddress,
          isUnitHasParent: false,
          unit: unitName,
          bed: {
            description: { name: unitName },
            capacity: mockUnitData.maxOccupants,
            availability: true,
            beds: [],
          },
        },
        timestamp: new Date(),
        source: 'webhook-cronjob',
        processedAt: new Date(),
      };

      await expect(
        service.lentoIntegrateProcessCheckIn(payload),
      ).rejects.toThrow(
        `Contact not found for resident: ${payload.originalBody.resident.customer}`,
      );
    });

    it('Should not found parent unit', async () => {
      const notFoundUnitName = 'Nonexistent Unit';
      const payload = {
        messageId: new ObjectId(),
        endpoint: '/api/night-registrations/reservations/process-checkin',
        originalBody: {
          resident: {
            customer: mockContactData.displayName,
            firstName: 'John',
            lastName: 'Doe',
            dateOfBirth: '1990-01-01',
            email: '<EMAIL>',
            phoneNumber: '0123456789',
            gender: 'Male',
            nationality: '',
          },
          arrivalDate: new Date(),
          remarks: 'Test check-in',
          location: mockLocationData.fullAddress,
          isUnitHasParent: false,
          unit: notFoundUnitName,
          bed: {
            description: { name: notFoundUnitName },
            capacity: mockUnitData.maxOccupants,
            availability: true,
            beds: [],
          },
        },
        timestamp: new Date(),
        source: 'webhook-cronjob',
        processedAt: new Date(),
      };

      await expect(
        service.lentoIntegrateProcessCheckIn(payload),
      ).rejects.toThrow(`Parent unit not found: ${notFoundUnitName}`);
    });

    it('Should not found bed unit', async () => {
      const notFoundUnitName = 'Nonexistent Unit';
      const payload = {
        messageId: new ObjectId(),
        endpoint: '/api/night-registrations/reservations/process-checkin',
        originalBody: {
          resident: {
            customer: mockContactData.displayName,
            firstName: 'John',
            lastName: 'Doe',
            dateOfBirth: '1990-01-01',
            email: '<EMAIL>',
            phoneNumber: '0123456789',
            gender: 'Male',
            nationality: '',
          },
          arrivalDate: new Date(),
          remarks: 'Test check-in',
          location: mockLocationData.fullAddress,
          isUnitHasParent: true,
          unit: mockUnitData.name,
          bed: {
            description: { name: notFoundUnitName },
            capacity: mockUnitData.maxOccupants,
            availability: true,
            beds: [],
          },
        },
        timestamp: new Date(),
        source: 'webhook-cronjob',
        processedAt: new Date(),
      };

      await expect(
        service.lentoIntegrateProcessCheckIn(payload),
      ).rejects.toThrow(`Bed unit not found: ${notFoundUnitName}`);
    });
  });
});
