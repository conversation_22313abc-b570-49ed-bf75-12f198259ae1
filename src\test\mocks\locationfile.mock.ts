import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { LocationFileModel } from '~/modules/location-file/location-file.model';
import { locationFileTest } from '~/modules/location-file/test/location-file.dto.test';

import { mockLocationData } from './location.mock';
import { mockUploadFileData } from './uploadfile.mock';

const locationFileModel = getModelForClass(LocationFileModel);

export const mockLocationFileData = {
  _id: new ObjectId(),
  createdAt: new Date(),
  description: 'Energielabel (D) - Meerkerk_Energieweg 116',
  isDeleted: false,
  location: mockLocationData._id,
  updatedAt: new Date(),
  uploadFile: mockUploadFileData._id,
};

export async function initMockLocationFile(
  doc?: Partial<z.infer<typeof locationFileTest.modelSchema>>,
) {
  const { _id, ...rest } = { ...mockLocationFileData, ...doc };
  await locationFileModel.replaceOne({ _id }, rest, { upsert: true });
}
