import { HttpService } from '@nestjs/axios';
import { Injectable } from '@nestjs/common';
import { firstValueFrom } from 'rxjs';

import { MyLogger } from '~/processors/logger/logger.service';
import { ThirdPartyConnectorStrategy } from '~/processors/third-party-connector/strategies/third-party-connector.strategy';

@Injectable()
export class AfasService implements ThirdPartyConnectorStrategy {
  constructor(
    private readonly httpService: HttpService,
    private readonly logger: MyLogger,
  ) {}

  async getCostTypes(params: any): Promise<any> {
    const { data } = await firstValueFrom(
      this.httpService.get<any>(
        `${params.baseUrl}/connectors/Homee_Artikelen`,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `AfasToken ${params.token}`,
          },
          params: {
            take: 1000,
          },
        },
      ),
    );

    return data;
  }

  async getCostCenters(params: any): Promise<any> {
    const endPoint = params.companyIdentifier
      ? `Homee_Codes_verbijzondering_${params.companyIdentifier}`
      : 'Homee_Codes_verbijzondering';

    const { data } = await firstValueFrom(
      this.httpService.get<any>(`${params.baseUrl}/connectors/${endPoint}`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `AfasToken ${params.token}`,
        },
        params: {
          take: 1000,
        },
      }),
    );

    return data;
  }

  async getDebtors(params: any): Promise<any> {
    const endPoint = params.companyIdentifier
      ? `Homee_Contacts_afleveradressen_${params.companyIdentifier}`
      : 'Homee_Contacts_afleveradressen';

    const { data } = await firstValueFrom(
      this.httpService.get<any>(`${params.baseUrl}/connectors/${endPoint}`, {
        headers: {
          'Content-Type': 'application/json',
          Authorization: `AfasToken ${params.token}`,
        },
        params: {
          take: 50,
          skip: params.page ? (params.page - 1) * 50 : 0,
          filterfieldids: encodeURI('Type,Verkooprelatie'),
          filtervalues: encodeURI('ORG,[is niet leeg]'),
          operatortypes: encodeURI('1,9'),
        },
      }),
    );

    return data;
  }

  async pushInvoice(params: any): Promise<any> {
    const endPoint = 'FbDirectInvoice';
    const { invoice } = params;

    this.logger.log(
      'Pushing invoice to AFAS',
      'AfasService',
      JSON.stringify(invoice),
    );

    const { data } = await firstValueFrom(
      this.httpService.post<any>(
        `${params.baseUrl}/connectors/${endPoint}`,
        invoice,
        {
          headers: {
            'Content-Type': 'application/json',
            Authorization: `AfasToken ${params.token}`,
          },
        },
      ),
    );

    return data;
  }
}
