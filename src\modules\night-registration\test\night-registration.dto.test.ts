import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { contactTest } from '~/modules/contact/test/contact.dto.test';
import { unitTest } from '~/modules/unit/test/unit.dto.test';
import {
  NightRegistrationGender,
  NightRegistrationWarningLevel,
} from '~/shared/enums/night-registration.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const nationalModelSchema = z
  .object({
    name: z.string(),
    code: z.string(),
  })
  .extend(baseModelTestSchema);

const reservationModelSchema = z
  .object({
    resident: z.instanceof(ObjectId).nullable().optional(),
    contact: z.instanceof(ObjectId).nullable().optional(),
    unit: z.instanceof(ObjectId),
    remarks: z.string().nullable().optional(),
    bed: z.number().nullable().optional(),
    arrivalDate: z.date().nullable().optional(),
    departureDate: z.date().nullable().optional(),
    isVirtual: z.boolean().nullable().optional(),
    job: z
      .array(
        z.object({
          id: z.instanceof(ObjectId),
          type: z.enum(['check-in', 'check-out']),
        }),
      )
      .nullable()
      .optional(),
  })
  .extend(baseModelTestSchema);

const residentModelSchema = z
  .object({
    firstName: z.string(),
    lastName: z.string(),
    displayName: z.string().optional(),
    dateOfBirth: z.date(),
    gender: z.nativeEnum(NightRegistrationGender),
    clientId: z.string().optional(),
    email: z.string().optional(),
    nationality: z.instanceof(ObjectId).optional(),
    phoneNumber: z.string().optional(),
    contact: z.instanceof(ObjectId).optional(),
  })
  .extend(baseModelTestSchema);

const warningCategoryModelSchema = z
  .object({
    identifier: z.string(),
    category: z.string(),
  })
  .extend(baseModelTestSchema);

const warningModelSchema = z
  .object({
    resident: z.instanceof(ObjectId).optional(),
    reservation: z.instanceof(ObjectId).optional(),
    dayToLeave: z.date().optional(),
    level: z.nativeEnum(NightRegistrationWarningLevel),
    description: z.string().optional(),
    warningDate: z.date(),
    warningTime: z.string().optional(),
    images: z.array(z.string()),
    emailTemplateOption: z.number(),
    warningCategory: z.instanceof(ObjectId),
  })
  .extend(baseModelTestSchema);

const getNationalitiesSchema = z.array(nationalModelSchema);

const getListReservationSchema = z.object({
  totalAvailableBeds: z.number(),
  totalReservedBeds: z.number(),
  totalOccupiedBeds: z.number(),
  totalOnlyForMaleBeds: z.number(),
  totalOnlyForFemaleBeds: z.number(),
  beds: z.array(
    z
      .object({
        nanoid: z.string(),
        unitId: z.instanceof(ObjectId),
        position: z.number(),
        bedNo: z.number(),
        bedNoWithName: z.union([z.string(), z.number()]),
        unitName: z.string(),
        contact: contactTest.modelSchema
          .pick({
            _id: true,
            displayName: true,
            firstName: true,
            lastName: true,
          })
          .optional(),
        reservation: reservationModelSchema
          .pick({
            _id: true,
            remarks: true,
            bed: true,
            isVirtual: true,
          })
          .extend({
            arrivalDate: z.date().nullable().optional(),
            departureDate: z.date().nullable().optional(),
            resident: z.object({
              _id: z.instanceof(ObjectId).nullable().optional(),
              lastName: z.string().nullable().optional(),
              firstName: z.string().nullable().optional(),
              displayName: z.string().optional(),
              gender: z.string().nullable().optional(),
              clientId: z.string().optional(),
              phoneNumber: z.string().optional(),
              email: z.string().optional(),
              dateOfBirth: z.date().nullable().optional(),
              nationality: nationalModelSchema
                .pick({
                  _id: true,
                  name: true,
                  code: true,
                })
                .optional(),
              contact: contactTest.modelSchema
                .pick({
                  _id: true,
                  displayName: true,
                })
                .optional(),
            }),
            unit: z
              .object({
                _id: z.instanceof(ObjectId),
                name: z.string().optional(),
              })
              .optional(),
            contact: z
              .object({
                _id: z.instanceof(ObjectId),
                displayName: z.string().optional(),
                identifier: z.string().optional(),
              })
              .optional(),
            job: z.array(z.instanceof(ObjectId)).optional(),
            level: z.string().optional(),
            emailTemplateOption: z.number().optional(),
          })
          .optional(),
        availableGender: z.nativeEnum(NightRegistrationGender).optional(),
      })
      .optional(),
  ),
});

const getContactsByLocationSchema = z.array(
  contactTest.modelSchema.pick({
    _id: true,
    displayName: true,
    firstName: true,
    lastName: true,
  }),
);

const getListResidentsSchema = z.array(
  residentModelSchema
    .pick({
      _id: true,
      firstName: true,
      lastName: true,
      gender: true,
      dateOfBirth: true,
      clientId: true,
      phoneNumber: true,
      email: true,
      displayName: true,
    })
    .extend({
      fullInfomation: z.string().optional(),
      contact: contactTest.modelSchema
        .pick({
          _id: true,
          displayName: true,
        })
        .optional(),
      nationality: nationalModelSchema.optional(),
      level: z.string().optional(),
    }),
);

const findOneSchema = reservationModelSchema.extend({
  resident: residentModelSchema
    .extend({
      nationality: nationalModelSchema.optional(),
      fullInfomation: z.string().optional(),
    })
    .optional(),
  unit: unitTest.modelSchema.pick({
    _id: true,
    name: true,
  }),
  contact: contactTest.modelSchema
    .pick({
      displayName: true,
      identifier: true,
    })
    .nullable()
    .optional(),
  level: z.string().optional(),
  emailTemplateOption: z.number().optional(),
});

const getWarningCategoriesSChema = z.array(
  warningCategoryModelSchema.pick({
    _id: true,
    category: true,
  }),
);

const importResidentsSchema = z.array(residentModelSchema);

const getWarningsSchema = z.array(
  warningModelSchema.extend({
    warningCategory: warningCategoryModelSchema.pick({
      _id: true,
      category: true,
    }),
  }),
);

const checkInOfCheckOutOfUnitBaseSchema = z.object({
  residentId: z.instanceof(ObjectId),
  resident: z.string().nullable().optional(),
  reservation: z.instanceof(ObjectId),
  checked: z.boolean(),
});

const getLastCheckInAndLastCheckOutOfUnitSchema = z.array(
  z.object({
    bed: z.string().optional(),
    lastCheckIn: checkInOfCheckOutOfUnitBaseSchema.nullable().optional(),
    lastCheckOut: checkInOfCheckOutOfUnitBaseSchema.nullable().optional(),
  }),
);

const getReportSchema = z.array(
  z.object({
    _id: z.instanceof(ObjectId),
    contact: contactTest.modelSchema.pick({
      _id: true,
      displayName: true,
      firstName: true,
      lastName: true,
    }),
    totalHiredDays: z.number().optional(),
    totalVacantDays: z.number().optional(),
    totalOccupiedDays: z.number().optional(),
  }),
);

export const nightRegistrationTest = {
  nationalModelSchema,
  reservationModelSchema,
  residentModelSchema,
  warningCategoryModelSchema,
  warningModelSchema,
  getNationalitiesSchema,
  getListReservationSchema,
  getContactsByLocationSchema,
  getListResidentsSchema,
  findOneSchema,
  getWarningCategoriesSChema,
  getWarningsSchema,
  importResidentsSchema,
  getLastCheckInAndLastCheckOutOfUnitSchema,
  getReportSchema,
};
