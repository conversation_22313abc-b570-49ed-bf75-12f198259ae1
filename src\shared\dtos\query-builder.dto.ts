import isNaN from 'lodash/isNaN';
import isNumber from 'lodash/isNumber';
import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

export const QueryParamsSchema = z
  .object({
    pageIndex: z
      .string()
      .min(1, 'Page index must be greater than 0')
      .refine((v) => {
        const num = parseInt(v);

        return !v || (!isNaN(num) && isNumber(num));
      }, 'Page index must be a number')
      .transform((value) => (value ? parseInt(value) : 1))
      .refine((v) => v > 0, 'Page index must be greater than 0')
      .optional(),
    pageSize: z
      .string()
      .min(-1, 'Page size must be greater than -1')
      .refine((v) => {
        const num = parseInt(v);

        return !v || (!isNaN(num) && isNumber(num));
      }, 'Page size must be a number')
      .transform((value) => (value ? parseInt(value) : 50))
      .refine((v) => v >= -1, 'Page size must be greater than 0')
      .optional(),
    sortBy: z.string().min(1).default('updatedAt').optional(),
    sortDir: z.enum(['asc', 'desc']).default('desc').optional(),
    _q: z.string().optional(),
  })
  .passthrough();

export class QueryParamsDto extends createZodDto(QueryParamsSchema) {}
