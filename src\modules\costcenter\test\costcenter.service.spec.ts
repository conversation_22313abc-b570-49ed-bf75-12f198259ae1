import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { SyncHistoryService } from '~/modules/sync-history/sync-history.service';
import { ThirdPartyConnectorContext } from '~/processors/third-party-connector/strategies/third-party-connector.context';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import { initMockAddress, mockAddressData } from '~/test/mocks/address.mock';
import {
  initMockCostCenter,
  mockCostCenterData,
} from '~/test/mocks/costcenter.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';

import { CostCenterQueryDto } from '../costcenter.dto';
import { CostCenterModel } from '../costcenter.model';
import { CostCenterService } from '../costcenter.service';
import { costCenterTest } from './costcenter.dto.test';

describe('CostcenterService', () => {
  let service: CostCenterService;
  let thirdPartyContext: jest.Mocked<ThirdPartyConnectorContext>;
  let syncHistoryService: jest.Mocked<SyncHistoryService>;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        CostCenterService,
        ...testInjectModel([CostCenterModel]),
        {
          provide: ThirdPartyConnectorContext,
          useValue: {
            getCostCenters: jest.fn(),
          },
        },
        {
          provide: SyncHistoryService,
          useValue: {
            isPending: jest.fn(),
            createNewPending: jest.fn(),
            updateToSuccess: jest.fn(),
            updateToFailed: jest.fn(),
          },
        },
      ],
    }).compile();

    service = module.get(CostCenterService);
    thirdPartyContext = module.get(ThirdPartyConnectorContext);
    syncHistoryService = module.get(SyncHistoryService);

    // Init data
    await Promise.all([
      initMockCostCenter({
        locations: [mockLocationData._id],
      }),
      initMockLocation({
        address: mockAddressData._id,
      }),
      initMockAddress(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should call fn with payload and return list data', async () => {
      const payload: CostCenterQueryDto = {
        isActive: true,
      };
      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(costCenterTest.findAllSchema);
    });

    it('should call fn with payload and return empty list data', async () => {
      const payload: CostCenterQueryDto = {
        isActive: false,
        pageIndex: 99,
      };
      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });

  describe('findOne', () => {
    it('should call fn with id and return data', async () => {
      const id = mockCostCenterData._id.toString();
      const result = await service.findOne(id);

      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockCostCenterData._id);
      expect(result).toMatchSchema(costCenterTest.findOneSchema);
    });
  });

  describe('syncFrom3rdParty', () => {
    const payload = { thirdParty: 'Info', type: 'Foin' };

    it('should call and return data when not pending', async () => {
      syncHistoryService.isPending.mockResolvedValue(false);
      syncHistoryService.createNewPending.mockResolvedValue({
        _id: new ObjectId(),
      } as any);
      thirdPartyContext.getCostCenters.mockResolvedValue([]);
      syncHistoryService.updateToSuccess.mockResolvedValue({
        _id: new ObjectId(),
      } as any);

      const result = await service.syncFrom3rdParty(payload as any);
      expect(result).toBeDefined();
    });

    it('should throw error when pending', async () => {
      syncHistoryService.isPending.mockResolvedValue(true);
      await expect(service.syncFrom3rdParty(payload as any)).rejects.toThrow();
    });
  });
});
