import { padStart } from 'lodash';
import * as path from 'path';

import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const padPrefix = (id: number) => padStart(id.toString(), 9, '0');

interface ContractType {
  identifier: string;
  name: string;
}

export const ItemCategories = {
  Garbage: { nl: 'Afval', en: 'Garbage', identifier: padPrefix(1) },
  Internet: { nl: 'Internet', en: 'Internet', identifier: padPrefix(2) },
  'Kitchen Appliances': {
    nl: 'Keukenapparatuur',
    en: 'Kitchen Appliances',
    identifier: padPrefix(3),
  },
  'Washing machine & Dryer': {
    nl: 'Wasmachine & Droger',
    en: 'Washing machine & Dryer',
    identifier: padPrefix(4),
  },
  Upholstery: { nl: 'Stoffering', en: 'Upholstery', identifier: padPrefix(5) },
  Furniture: { nl: 'Meubilering', en: 'Furniture', identifier: padPrefix(6) },
  Gardens: { nl: 'Tuinen', en: 'Gardens', identifier: padPrefix(7) },
  'Lock/card system': {
    nl: 'Sloten/card systeem',
    en: 'Lock/card system',
    identifier: padPrefix(8),
  },
  'Vending machines': {
    nl: 'Vendingmachines',
    en: 'Vending machines',
    identifier: padPrefix(9),
  },
  'Camera system': {
    nl: 'Camerasysteem',
    en: 'Camera system',
    identifier: padPrefix(10),
  },
  Security: { nl: 'Beveiliging', en: 'Security', identifier: padPrefix(11) },
  'Key plan': { nl: 'Sleutelplan', en: 'Key plan', identifier: padPrefix(12) },
  'Pest control': {
    nl: 'Ongediertebestrijding',
    en: 'Pest control',
    identifier: padPrefix(13),
  },
  'Legionella beheer': {
    nl: 'Legionella beheer',
    en: 'Legionella beheer', // This is not even English like wth is this???
    identifier: padPrefix(14),
  },
  Gas: { nl: 'Gas', en: 'Gas', identifier: padPrefix(15) },
  Water: { nl: 'Water', en: 'Water', identifier: padPrefix(16) },
  Electricity: { nl: 'Elektra', en: 'Electricity', identifier: padPrefix(17) },
  Others: { nl: 'Overig', en: 'Others', identifier: padPrefix(18) },
} as const;

const generateContractTypes = (
  names: { identifier: string; name: string }[],
): ContractType[] =>
  names.map(({ identifier: id, name }) => ({
    identifier: id,
    name,
  }));

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const destinationCollectionName = 'contracttypes';
    const destinationCollection = context
      .destinationClient!.db()
      .collection(destinationCollectionName)!;

    // Please DO NOT CHANGE the identifier values
    const contractTypes = generateContractTypes(
      Object.values(ItemCategories).map((category) => ({
        identifier: category.identifier,
        name: category.en,
      })),
    );

    if (!(await destinationCollection.indexExists('name_1'))) {
      destinationCollection.createIndex({ name: 1 }, { unique: true });
    }

    if (!(await destinationCollection.indexExists('identifier_1'))) {
      destinationCollection.createIndex({ identifier: 1 }, { unique: true });
    }

    const upsertPromises = contractTypes.map((doc) =>
      destinationCollection
        .findOneAndUpdate(
          { identifier: doc.identifier },
          { $set: doc },
          { upsert: true, returnDocument: 'after' }, // Use returnDocument: 'after' to get the updated document
        )
        .then(() => {
          console.log(
            `Migrated contract type with identifier=${doc.identifier} & name=${doc.name} into collection ${destinationCollectionName}`,
          );
        })
        .catch((error) => {
          console.error(
            `Error upserting document with identifier=${doc.identifier} & name=${doc.name}:`,
            error,
          );
        }),
    );

    await Promise.all(upsertPromises)
      .then(() => {
        console.log(
          `Migrated ${contractTypes.length} documents to collection ${destinationCollectionName}`,
        );
      })
      .catch((error) => {
        console.error('Error during upsert operations:', error);
      });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
