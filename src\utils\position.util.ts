import { BadRequestException } from '@nestjs/common';

import { ModuleNameEnum } from '~/shared/enums/module-name.enum';

export function validteAndSortPosition(
  positionObject: any,
  moduleName: ModuleNameEnum,
  canStartByZero: boolean = false,
) {
  if (positionObject && positionObject.length > 0) {
    const sortedPositionObject = Array.from(positionObject).sort(
      (a: any, b: any) => a.position - b.position,
    );

    const positionsSet = new Set(
      sortedPositionObject.map((value: any) => value.position),
    );

    if (positionsSet.size !== positionObject.length) {
      throw new BadRequestException(`${moduleName}.form.position_is_unique`);
    }

    let areConsecutive = true;
    const positionArray = Array.from(positionsSet);
    if (positionArray[0] !== 1 && !canStartByZero) {
      throw new BadRequestException(
        `${moduleName}.form.position_must_start_by_1`,
      );
    }
    for (let i = 1; i < positionArray.length; i++) {
      if (positionArray[i] !== positionArray[i - 1] + 1) {
        areConsecutive = false;
        break;
      }
    }

    if (!areConsecutive) {
      throw new BadRequestException(
        `${moduleName}.form.position_must_be_conecutive`,
      );
    }

    return sortedPositionObject;
  }
  return positionObject;
}
