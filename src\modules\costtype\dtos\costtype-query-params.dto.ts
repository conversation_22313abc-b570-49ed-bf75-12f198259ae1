import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { CostTypeType } from '~/shared/enums/cost-type.enum';

export const CostTypeQueryParamsSchema = QueryParamsSchema.extend({
  isActive: z
    .enum(['true', 'false'])
    .optional()
    .default('true')
    .transform((val) => val === 'true'),
  type: z.nativeEnum(CostTypeType).optional(),
});

export class CostTypeQueryParamsDto extends createZodDto(
  CostTypeQueryParamsSchema,
) {}
