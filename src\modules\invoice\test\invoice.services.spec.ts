import { Test } from '@nestjs/testing';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { nanoid } from 'nanoid';

import { ContractModel } from '~/modules/contract/contract.model';
import { CostLineModel } from '~/modules/costline/costline.model';
import { IdentifierService } from '~/modules/identifier/identifier.service';
import { JobModel } from '~/modules/job/job.model';
import { ThirdPartyConnectorContext } from '~/processors/third-party-connector/strategies/third-party-connector.context';
import {
  AgreementLinePeriodType,
  ContractType,
  CostLineStatus,
  InvoiceType,
} from '~/shared/enums/contract.enum';
import { INVOICE_MESSAGE_KEYS } from '~/shared/message-keys/invoice.message-key';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockAddress } from '~/test/mocks/address.mock';
import {
  initMockAgreementLine,
  mockAgreementLineData,
} from '~/test/mocks/agreementline.mock';
import { initMockBvCompany } from '~/test/mocks/bvcompany.mock';
import { initMockContact, mockContactData } from '~/test/mocks/contact.mock';
import { initMockContract, mockContractData } from '~/test/mocks/contract.mock';
import { initMockCostCenter } from '~/test/mocks/costcenter.mock';
import { initMockCostline, mockCostlineData } from '~/test/mocks/costline.mock';
import { initMockCostlineGeneral } from '~/test/mocks/costlinegeneral.mock';
import { initMockCostType } from '~/test/mocks/costtype.mock';
import { initMockCountry, mockCountryData } from '~/test/mocks/country.mock';
import { initMockInvoice, mockInvoiceData } from '~/test/mocks/invoice.mock';
import { initMockJob } from '~/test/mocks/job.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';

import {
  ApprovedInvoicesQueryParamsDto,
  ApprovedInvoicesQueryType,
  InvoiceApproveBodyDto,
} from '../dtos/invoice-approve.dto';
import {
  ContractInvoiceReviewQueryParamsDto,
  JobInvoiceReviewQueryParamsDto,
} from '../dtos/invoice-review.dto';
import { InvoiceModel } from '../invoice.model';
import { InvoiceService } from '../invoice.service';
import { invoiceTest } from './invoice.dto.test';

enum InvoiceQueryType {
  CONTRACT = 'contract',
  JOB = 'job',
}

describe('InvoiceService', () => {
  let service: InvoiceService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        InvoiceService,
        ...testInjectProviders([
          ContractModel,
          CostLineModel,
          InvoiceModel,
          JobModel,
          IdentifierService,
          ThirdPartyConnectorContext,
        ]),
      ],
    }).compile();

    service = module.get(InvoiceService);

    // Init data
    await Promise.all([
      initMockInvoice({ contact: mockContactData._id }),
      initMockInvoice({
        invoiceReference: mockInvoiceData._id,
      }),
      initMockAgreementLine({
        contract: mockContractData._id,
        units: [mockUnitData._id],
        periodType: AgreementLinePeriodType.ONE_TIME,
      }),
      initMockCostline(),
      initMockCostType(),
      initMockCostlineGeneral({
        agreementLine: mockAgreementLineData._id,
      }),
      initMockContract({
        agreementLines: [mockAgreementLineData._id],
      }),
      initMockContact(),
      initMockJob({}),
      initMockBvCompany(),
      initMockLocation(),
      initMockCostCenter({
        locations: [mockLocationData._id],
      }),
      initMockUnit(),
      initMockCountry(),
      initMockAddress({
        country: mockCountryData._id,
      }),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findInvoicesReviewTypeContract', () => {
    const params: ContractInvoiceReviewQueryParamsDto = {
      type: InvoiceQueryType.CONTRACT,
      periodType: AgreementLinePeriodType.ONE_TIME,
    };

    it('should call fn with payload and return list data', async () => {
      const result = await service.findInvoicesReviewTypeContract(params);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        invoiceTest.findInvoicesReviewTypeContractSchema,
      );
    });

    it('should return empty list when data not exist', async () => {
      const result = await service.findInvoicesReviewTypeContract({} as any);
      expect(result).toBeDefined();
      expect(result).toHaveLength(0);
    });
  });

  describe('findInvoicesReviewTypeJob', () => {
    const params: JobInvoiceReviewQueryParamsDto = {
      type: InvoiceQueryType.JOB,
    };

    it('should call fn with payload and return list data', async () => {
      // Prepare data
      await service['costLineModel'].findByIdAndUpdate(
        { _id: mockCostlineData._id },
        { type: ContractType.JOB, $unset: { invoice: '' } },
      );

      const result = await service.findInvoicesReviewTypeJob(params);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(invoiceTest.findInvoicesReviewTypeJobSchema);
    });

    it('should return empty list when data not exist', async () => {
      // Prepare data
      await service['costLineModel'].findByIdAndUpdate(
        { _id: mockCostlineData._id },
        { type: ContractType.SERVICE },
      );

      const result = await service.findInvoicesReviewTypeJob({} as any);
      expect(result).toBeDefined();
      expect(result).toHaveLength(0);
    });
  });

  describe('approveInvoices', () => {
    it('should throw error if have approved in another invoice', async () => {
      const wrongPayload: InvoiceApproveBodyDto = {
        invoices: [
          {
            type: ContractType.JOB,
            contact: new ObjectId().toString(),
            costLines: [mockInvoiceData.costLines[0].toString()],
          },
        ],
        user: new ObjectId().toString(),
        thirdParties: {},
      };

      await expect(service.approveInvoices(wrongPayload)).rejects.toThrow(
        INVOICE_MESSAGE_KEYS.COST_LINES_HAVE_APPROVED_IN_ANOTHER_INVOICE,
      );
    });

    it('should throw error when costline not found', async () => {
      const wrongPayload: InvoiceApproveBodyDto = {
        invoices: [
          {
            type: ContractType.JOB,
            contact: new ObjectId().toString(),
            costLines: [new ObjectId().toString()],
          },
        ],
        user: new ObjectId().toString(),
        thirdParties: {},
      };
      await expect(service.approveInvoices(wrongPayload)).rejects.toThrow(
        INVOICE_MESSAGE_KEYS.COST_LINES_NOT_FOUND,
      );
    });

    it('should throw error when costline have same type', async () => {
      // Mock private method
      jest
        .spyOn(service['indentifierService'], 'generateIdentifier')
        .mockResolvedValue(nanoid(12));

      // Prepare data
      const costLineId = new ObjectId();

      await initMockCostline({
        _id: costLineId,
        type: ContractType.JOB,
        status: CostLineStatus.OPEN,
      });

      const wrongPayload: InvoiceApproveBodyDto = {
        invoices: [
          {
            type: InvoiceType.JOB,
            contact: new ObjectId().toString(),
            costLines: [mockCostlineData._id.toString(), costLineId.toString()],
          },
        ],
        user: new ObjectId().toString(),
        thirdParties: {},
      };

      await expect(service.approveInvoices(wrongPayload)).rejects.toThrow(
        INVOICE_MESSAGE_KEYS.COST_LINES_MUST_HAVE_SAME_TYPE,
      );
    });

    it('should throw error when costline have type different from invoice', async () => {
      // Prepare data
      const costLineId = new ObjectId();

      await initMockCostline({
        _id: costLineId,
        type: ContractType.CREDITOR,
        status: CostLineStatus.OPEN,
      });

      const payload: InvoiceApproveBodyDto = {
        invoices: [
          {
            type: ContractType.JOB,
            contact: new ObjectId().toString(),
            costLines: [costLineId.toString()],
          },
        ],
        user: new ObjectId().toString(),
        thirdParties: {},
      };

      await expect(service.approveInvoices(payload)).rejects.toThrow(
        INVOICE_MESSAGE_KEYS.COST_LINES_HAVE_TYPE_DIFFERENT_FROM_INVOICE,
      );
    });

    it('should call fn with payload and create invoice', async () => {
      // Prepare data
      const costLineId = new ObjectId();
      await initMockCostline({
        _id: costLineId,
        type: ContractType.JOB,
        status: CostLineStatus.OPEN,
      });

      // Mock private method
      jest
        .spyOn(service as any, 'updateReferenceAfterApproveInvoices')
        .mockResolvedValue(null);
      jest
        .spyOn(service['indentifierService'], 'generateIdentifier')
        .mockResolvedValue(nanoid(12));
      jest
        .spyOn(service['thirdPartyContext'], 'pushInvoice')
        .mockResolvedValue(null);

      const payload: InvoiceApproveBodyDto = {
        invoices: [
          {
            type: ContractType.JOB,
            contact: new ObjectId().toString(),
            costLines: [costLineId.toString()],
          },
        ],
        user: new ObjectId().toString(),
        thirdParties: {},
      };

      const result = await service.approveInvoices(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(invoiceTest.approveInvoicesSchema);
      expect(dayjs(result[0].approvedAt).isSame(dayjs(), 'day')).toBe(true);
    });
  });

  describe('findApprovedInvoices', () => {
    const payload: ApprovedInvoicesQueryParamsDto = {
      type: ApprovedInvoicesQueryType.JOB_AND_CUSTOM,
    };

    it('should call fn with payload and return list data', async () => {
      const result = await service.findApprovedInvoices(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(invoiceTest.findApprovedInvoicesSchema);
    });

    it('should return empty list when data not exist', async () => {
      const result = await service.findApprovedInvoices({
        ...payload,
        pageIndex: 999,
      });
      expect(result).toBeDefined();
      expect(result.docs).toHaveLength(0);
    });
  });

  describe('getApprovedInvoiceDetail', () => {
    it('should call fn with id and return data', async () => {
      const result = await service.getApprovedInvoiceDetail(
        mockInvoiceData._id.toString(),
      );
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockInvoiceData._id);
      expect(result).toMatchSchema(invoiceTest.getApprovedInvoiceDetailSchema);
    });

    it('should call fn and return null when data not found', async () => {
      const result = await service.getApprovedInvoiceDetail(
        new ObjectId().toString(),
      );
      expect(result).toBeNull();
    });
  });
});
