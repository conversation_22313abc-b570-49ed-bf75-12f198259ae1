import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { JOB_MESSAGES } from '~/shared/messages/job.message';

import {
  CheckOverlapJobDto,
  CreateJobDto,
  DeleteJobDto,
  DeleteScheduleJobDto,
  GetPdfReportJobDto,
  JobDetailDto,
  JobExportCsvDto,
  JobQuerySchemaDto,
  JobUpdateStatusDto,
  MobileCoWorkerSyncDataOfJobDto,
  MobileJobQuerySchemaDto,
  UpdatePlanningJobDto,
  UpdateScheduleJobSettingsDto,
  ViewSummaryJobDto,
} from './dtos/job.dto';
import { JobService } from './job.service';
import { CreateJobZodValidationPipe } from './pipes/create-job.pipe';

@Controller('jobs')
export class JobController {
  constructor(private readonly jobService: JobService) {}

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(JobQuerySchemaDto))
  @MessagePattern({ cmd: JOB_MESSAGES.GET_JOBS })
  public async findAll(@Payload() payload: JobQuerySchemaDto) {
    return this.jobService.findAll(payload);
  }

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(MobileJobQuerySchemaDto))
  @MessagePattern({ cmd: JOB_MESSAGES.GET_JOBS_MOBILE })
  public async getJobsMobile(@Payload() payload: any) {
    return this.jobService.findAllMobile(payload);
  }

  @UsePipes(new ZodValidationPipe(JobDetailDto))
  @MessagePattern({ cmd: JOB_MESSAGES.DETAIL_JOB })
  public async findOne(@Payload() payload: any) {
    return this.jobService.findOne(payload);
  }

  @UsePipes(new ZodValidationPipe(JobDetailDto))
  @MessagePattern({ cmd: JOB_MESSAGES.DETAIL_JOB_REVIEW })
  public async findReviewDetail(@Payload() payload: any) {
    return this.jobService.findReviewDetail(payload);
  }

  @UsePipes(new CreateJobZodValidationPipe())
  @MessagePattern({ cmd: JOB_MESSAGES.CREATE_JOB })
  public async create(@Payload() payload: CreateJobDto) {
    return this.jobService.create(payload);
  }

  @UsePipes(new ZodValidationPipe(UpdateScheduleJobSettingsDto))
  @MessagePattern({ cmd: JOB_MESSAGES.UPDATE_SCHEDULE_JOB_SETTINGS })
  public async updateScheduleJobSettings(
    @Payload() payload: UpdateScheduleJobSettingsDto,
  ) {
    return this.jobService.updateScheduleJobSettings(payload);
  }

  @MessagePattern({ cmd: JOB_MESSAGES.UPDATE_JOB })
  public async updateJob(@Payload() payload: any) {
    const { id, headers, ...data } = payload;
    return this.jobService.updateJob(id, data, headers);
  }

  @UsePipes(new ZodValidationPipe(JobUpdateStatusDto))
  @MessagePattern({ cmd: JOB_MESSAGES.UPDATE_STATUS_JOB })
  public async updateStatusJob(@Payload() payload: JobUpdateStatusDto) {
    return this.jobService.updateStatus(payload);
  }

  @UsePipes(new ZodValidationPipe(DeleteJobDto))
  @MessagePattern({ cmd: JOB_MESSAGES.DELETE_JOB })
  public async deleteJobs(@Payload() payload: DeleteJobDto) {
    return this.jobService.deleteJobs(payload);
  }

  @UsePipes(new ZodValidationPipe(ViewSummaryJobDto))
  @MessagePattern({ cmd: JOB_MESSAGES.VIEW_SUMMARY_JOB })
  public async viewSummaryJob(@Payload() payload: ViewSummaryJobDto) {
    return this.jobService.viewSummaryJob(payload);
  }

  @UsePipes(new ZodValidationPipe(CheckOverlapJobDto))
  @MessagePattern({ cmd: JOB_MESSAGES.CHECK_OVERLAP_JOB })
  public async checkOverlapJob(@Payload() payload: CheckOverlapJobDto) {
    return this.jobService.checkOverlapJob(payload);
  }

  @UsePipes(new ZodValidationPipe(MobileCoWorkerSyncDataOfJobDto))
  @MessagePattern({ cmd: JOB_MESSAGES.SYNC_DATA_OF_JOB })
  public async coWorkerSyncDataOfJob(
    @Payload() payload: MobileCoWorkerSyncDataOfJobDto,
  ) {
    return this.jobService.coWorkerSyncDataOfJob(payload);
  }

  @UsePipes(new ZodValidationPipe(GetPdfReportJobDto))
  @MessagePattern({ cmd: JOB_MESSAGES.GET_PDF_REPORT_OF_JOB })
  public async getPdfReport(@Payload() payload: GetPdfReportJobDto) {
    return this.jobService.getPdfReport(payload);
  }

  @UsePipes(new ZodValidationPipe(JobExportCsvDto))
  @MessagePattern({ cmd: JOB_MESSAGES.EXPORT_CSV })
  public async exportCsv(@Payload() payload: any) {
    return this.jobService.exportJobDetail(payload);
  }

  @UsePipes(new ZodValidationPipe(DeleteScheduleJobDto))
  @MessagePattern({ cmd: JOB_MESSAGES.DELETE_SCHEDULE_JOB })
  public async deleteScheduleJob(@Payload() payload: DeleteScheduleJobDto) {
    return this.jobService.deleteScheduleJob(payload);
  }

  @MessagePattern({ cmd: JOB_MESSAGES.GET_PLANNING_JOB })
  public async detailsPlanningJob(@Payload() id: string) {
    return this.jobService.detailsPlanningJob(id);
  }

  @MessagePattern({ cmd: JOB_MESSAGES.UPDATE_PLANNING_JOB })
  public async updatePlanningJob(@Payload() payload: UpdatePlanningJobDto) {
    return this.jobService.updatePlanningJob(payload);
  }
}
