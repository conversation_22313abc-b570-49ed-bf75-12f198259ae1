import { HttpStatus, Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import _ from 'lodash';
import { Model } from 'mongoose';

import { MyLogger } from '~/processors/logger/logger.service';
import { ContractType } from '~/shared/enums/contract.enum';
import { LentoIntegrateErrorCodesEnum } from '~/shared/enums/lento-integrate.enum';
import { LentoIntegrateException } from '~/shared/exception/lento-integrate-exception.dto';
import { InjectModel } from '~/transformers/model.transformer';

import { ContactModel } from '../contact/contact.model';
import { ContractModel } from '../contract/contract.model';
import { LocationModel } from '../location/location.model';
import { NightRegistrationNationalityModel } from './models/night-registration-nationality.model';
import { NightRegistrationReservationModel } from './models/night-registration-reservation.model';
import { NightRegistrationService } from './night-registration.service';

@Injectable()
export class LentoIntegrateCheckinService {
  constructor(
    @InjectModel(ContactModel)
    private readonly contactModel: Model<ContactModel>,

    @InjectModel(LocationModel)
    private readonly locationModel: Model<LocationModel>,

    @InjectModel(ContractModel)
    private readonly contractModel: Model<ContractModel>,

    @InjectModel(NightRegistrationReservationModel)
    private readonly reservationModel: Model<NightRegistrationReservationModel>,

    @InjectModel(NightRegistrationNationalityModel)
    private readonly nationalityModel: Model<NightRegistrationNationalityModel>,

    private readonly nightRegistrationService: NightRegistrationService,
    private readonly logger: MyLogger,
  ) {
    this.logger.setContext(LentoIntegrateCheckinService.name);
  }

  async lentoIntegrateProcessCheckIn(payload: any) {
    const { originalBody } = payload;
    this.logger.log(
      `Processing check-in for payload: ${JSON.stringify(payload)}`,
    );
    const { resident } = originalBody;
    const customerName = resident.customer.replace(/\./g, '');
    let errorMessage = '';
    const processName = 'Lento Integrate Check-In';

    let reservation: any = null;
    const residentContact = await this.contactModel
      .findOne({
        displayName: customerName,
      })
      .lean();
    if (!residentContact) {
      errorMessage = `Contact not found for resident: ${customerName}`;
      this.logger.error(errorMessage);
      throw new LentoIntegrateException(
        errorMessage,
        HttpStatus.NOT_FOUND,
        LentoIntegrateErrorCodesEnum.CONTACT_NOT_FOUND,
        processName,
      );
    }
    const defaultNationality = await this.nationalityModel
      .findOne({
        code: 'NOCODE',
      })
      .lean();

    const foundResident =
      await this.nightRegistrationService.createOrUpdateResident({
        firstName: resident.firstName,
        lastName: resident.lastName,
        dateOfBirth: resident.dateOfBirth,
        clientId: resident.clientId,
        gender: resident.gender,
        email: resident.email,
        phoneNumber: resident.phoneNumber,
        contact: residentContact?._id,
        nationality: _.isEmpty(resident.nationality)
          ? defaultNationality?._id
          : resident.nationality,
      });
    const locationInfos = await this.locationModel
      .aggregate([
        {
          $match: {
            fullAddress: originalBody.location,
          },
        },
        {
          $lookup: {
            from: 'units',
            localField: '_id',
            foreignField: 'location',
            as: 'units',
            pipeline: [
              {
                $match: {
                  isActive: true,
                },
              },
              {
                $lookup: {
                  from: 'units',
                  localField: 'parent',
                  foreignField: '_id',
                  as: 'parent',
                },
              },
              {
                $set: {
                  parent: {
                    $arrayElemAt: ['$parent', 0],
                  },
                },
              },
              {
                $project: {
                  isRoot: 1,
                  name: 1,
                  maxOccupants: 1,
                  parent: 1,
                  position: 1,
                },
              },
            ],
          },
        },
        {
          $project: {
            units: 1,
            fullAddress: 1,
            costCenter: 1,
            maximumStayDuration: 1,
          },
        },
      ])
      .exec();
    const foundLocation = locationInfos[0];
    if (!foundLocation) {
      errorMessage = `Location not found for address: ${originalBody.location}`;
      this.logger.error(errorMessage);
      throw new LentoIntegrateException(
        errorMessage,
        HttpStatus.NOT_FOUND,
        LentoIntegrateErrorCodesEnum.LOCATION_NOT_FOUND,
        processName,
      );
    }

    const foundContracts = await this.contractModel
      .aggregate([
        {
          $match: {
            isActive: true,
            type: {
              $in: [ContractType.RENTING, ContractType.SERVICE],
            },
            $or: [
              {
                location: foundLocation._id,
              },
              ...(foundLocation.costCenter
                ? [{ costCenter: foundLocation.costCenter }]
                : []),
            ],
            $and: [
              {
                startDate: {
                  $lte: dayjs().utc().toDate(),
                },
              },
              {
                $or: [
                  {
                    endDate: {
                      $gte: dayjs().utc().startOf('day').toDate(),
                    },
                  },
                  {
                    endDate: null,
                  },
                ],
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'agreementlines',
            localField: '_id',
            foreignField: 'contract',
            as: 'agreementlines',
            pipeline: [
              {
                $lookup: {
                  from: 'costlinegenerals',
                  localField: '_id',
                  foreignField: 'agreementLine',
                  as: 'costLineGenerals',
                  pipeline: [
                    {
                      $match: {
                        $and: [
                          {
                            unit: {
                              $exists: true,
                            },
                          },
                          {
                            startDate: {
                              $lte: dayjs().utc().toDate(),
                            },
                          },
                          {
                            $or: [
                              {
                                endDate: {
                                  $gte: dayjs().utc().startOf('day').toDate(),
                                },
                              },
                              {
                                endDate: null,
                              },
                            ],
                          },
                        ],
                      },
                    },
                    {
                      $lookup: {
                        from: 'units',
                        localField: 'unit',
                        foreignField: '_id',
                        as: 'unit',
                        pipeline: [
                          {
                            $match: {
                              isActive: true,
                            },
                          },
                        ],
                      },
                    },
                    {
                      $unwind: {
                        path: '$unit',
                        preserveNullAndEmptyArrays: true,
                      },
                    },
                  ],
                },
              },
            ],
          },
        },
        {
          $lookup: {
            from: 'contacts',
            localField: 'contact',
            foreignField: '_id',
            as: 'contact',
            pipeline: [
              {
                $project: {
                  firstName: 1,
                  lastName: 1,
                  displayName: 1,
                },
              },
            ],
          },
        },
        {
          $set: {
            contact: {
              $arrayElemAt: ['$contact', 0],
            },
          },
        },
      ])
      .option({ allowDiskUse: true })
      .exec();

    if (foundContracts.length === 0) {
      errorMessage = `No active contract found for location: ${originalBody.location}`;
      this.logger.error(errorMessage);
      throw new LentoIntegrateException(
        errorMessage,
        HttpStatus.NOT_FOUND,
        LentoIntegrateErrorCodesEnum.NO_ACTIVE_CONTRACT_NOT_FOUND_FOR_LOCATION,
        processName,
      );
    }

    const parentUnit = foundLocation.units.find(
      (unit) => unit.name.toString() === originalBody.unit,
    );

    if (!parentUnit) {
      errorMessage = `Parent unit not found: ${originalBody.unit}`;
      this.logger.error(errorMessage);
      throw new LentoIntegrateException(
        errorMessage,
        HttpStatus.NOT_FOUND,
        LentoIntegrateErrorCodesEnum.UNIT_NOT_FOUND,
        processName,
      );
    }

    let foundBedUnit = parentUnit;
    if (originalBody.isUnitHasParent) {
      const bedFromLento = originalBody.bed;
      foundBedUnit = foundLocation.units.find(
        (unit) =>
          unit.name.toString() === bedFromLento.description.name &&
          unit.parent?._id.toString() === parentUnit._id.toString(),
      );
    }

    if (!foundBedUnit) {
      errorMessage = `Bed unit not found: ${originalBody.bed.description.name}`;
      this.logger.error(errorMessage);
      throw new LentoIntegrateException(
        errorMessage,
        HttpStatus.NOT_FOUND,
        LentoIntegrateErrorCodesEnum.BED_NOT_FOUND,
        processName,
      );
    }

    const unitOfLocations = foundLocation.units.filter((unit) => {
      return (
        unit._id.toString() == parentUnit._id.toString() ||
        (originalBody.isUnitHasParent &&
          unit.parent?._id.toString() === parentUnit._id.toString())
      );
    });

    const beds =
      await this.nightRegistrationService.generateBedFromLocationAndContract(
        unitOfLocations,
        foundContracts,
      );
    const bedWithReservations =
      await this.nightRegistrationService.mapReservationToBeds(
        beds,
        false,
        foundLocation.maximumStayDuration,
      );
    const filteredEmptyBeds = bedWithReservations
      .filter(
        (bed) =>
          bed.unitName == parentUnit.name &&
          bed.unitId.toString() == foundBedUnit._id.toString() &&
          !bed.reservation._id,
      )
      .sort((a, b) => a.bedNo - b.bedNo);

    if (filteredEmptyBeds.length === 0) {
      errorMessage = 'No empty bed found for the check-in process.';
      this.logger.error(errorMessage);
      throw new LentoIntegrateException(
        errorMessage,
        HttpStatus.BAD_REQUEST,
        LentoIntegrateErrorCodesEnum.EMPTY_BED_NOT_FOUND,
        processName,
      );
    }

    const emptyBed = filteredEmptyBeds[0];
    reservation = await this.reservationModel.create({
      resident: foundResident._id,
      unit: emptyBed.unitId,
      bed: emptyBed.bedNo,
      arrivalDate:
        originalBody.arrivalDate ?? dayjs().utc().startOf('day').toDate(),
      remarks: originalBody.remarks ?? '',
      contact: foundResident.contact?._id ?? null,
    });

    this.logger.log(
      `Check-in successful for resident: ${foundResident.firstName} ${foundResident.lastName}, bed: ${emptyBed.bedNo}, unit: ${emptyBed.unitName}`,
    );
    return reservation;
  }
}
