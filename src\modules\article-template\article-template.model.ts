import {
  DocumentType,
  index,
  modelOptions,
  prop,
  Severity,
} from '@typegoose/typegoose';
import { nanoid } from 'nanoid';

import { MAX_DESCRIPTION_LENGTH } from '~/constants/app.constant';
import { BaseModel } from '~/shared/models/base.model';

export type ArticleTemplateDocument = DocumentType<ArticleTemplateModel>;

export type ArticleListItem = {
  _id: string;
  article: string;
  amount: number;
  position: number;
};

@modelOptions({
  options: { customName: 'ArticleTemplate', allowMixed: Severity.ALLOW },
})
@index({ name: 1 })
export class ArticleTemplateModel extends BaseModel {
  @prop({ required: true, trim: true, maxlength: MAX_DESCRIPTION_LENGTH })
  name!: string;

  @prop({ required: true })
  storage!: string;

  @prop({
    default: [],
    set: (articles: ArticleListItem[]) =>
      articles.map((article) => ({
        ...article,
        _id: article._id || nanoid(),
      })),
  })
  articleList!: ArticleListItem[];
}
