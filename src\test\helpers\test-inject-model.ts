import { getModelForClass } from '@typegoose/typegoose';

import { getModelToken } from '~/transformers/model.transformer';

export function testInjectModel(models: any[]) {
  return models.map((ModelClass) => ({
    provide: getModelToken(ModelClass.name),
    useValue: getModelForClass(ModelClass),
  }));
}

export function testInjectProviders(classes: any[]) {
  return classes.map((Cls) => {
    const name = Cls.name;

    if (name.endsWith('Model')) {
      return {
        provide: getModelToken(name),
        useValue: getModelForClass(Cls),
      };
    }

    const methods = Object.fromEntries(
      Object.getOwnPropertyNames(Cls.prototype)
        .filter(
          (m) => m !== 'constructor' && typeof Cls.prototype[m] === 'function',
        )
        .map((m) => [m, jest.fn()]),
    );

    return { provide: Cls, useValue: methods };
  });
}
