export interface AfasPushInvoiceDto {
  data: AfasDirectInvoice;
}

export interface AfasDirectInvoice {
  FbDirectInvoice: {
    Element: {
      Fields: {
        DbId: string;
        RfCs: string;
        Unit: number | null;
        VaDu?: string;
        DlAd?: string;
      };
      Objects: {
        FbDirectInvoiceLines: {
          Element: AfasDirectInvoiceLine[];
        };
      }[];
    };
  };
}

export interface AfasDirectInvoiceLine {
  Fields: {
    VaIt: string;
    ItCd: string;
    Ds: string;
    BiUn: string;
    QuUn: string;
    Qu: string;
    Upri: string;
    CoPr: string;
    V1Cd: string;
    UE40B6A14A62F450F80EED84ABE2AC52F: string;
    U774BDD4BD90F42E7B8DDE7F90C1FC3CC: string;
  };
}
