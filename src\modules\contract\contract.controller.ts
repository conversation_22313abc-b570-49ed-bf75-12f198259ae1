import { Controller, NotFoundException, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { CONTRACT_MESSAGE_KEYS } from '~/shared/message-keys/contract.message-keys';
import { CONTRACT_MESSAGES } from '~/shared/messages/contract.message';

import { ContractService } from './contract.service';
import {
  CreateContractDto,
  CreateContractZodDto,
} from './dtos/create-contract.dto';

@Controller('contracts')
export class ContractController {
  constructor(private readonly contractService: ContractService) {}

  @HTTPDecorators.Paginator
  @MessagePattern({ cmd: CONTRACT_MESSAGES.GET_CONTRACTS })
  public async findAll(@Payload() payload: any) {
    return this.contractService.findAll(payload);
  }

  @MessagePattern({ cmd: CONTRACT_MESSAGES.DETAIL_CONTRACT })
  public async findOne(@Payload() payload: any) {
    const result = await this.contractService.findDetailOfOne(payload);
    if (!result) {
      throw new NotFoundException(CONTRACT_MESSAGE_KEYS.NOT_FOUND);
    }

    return result;
  }

  @MessagePattern({ cmd: CONTRACT_MESSAGES.SUPPLIER_CONTRACT_LOCATION })
  public async findLocationOfContract(@Payload() payload: any) {
    return this.contractService.findLocationOfContract(payload);
  }

  @UsePipes(new ZodValidationPipe(CreateContractZodDto))
  @MessagePattern({ cmd: CONTRACT_MESSAGES.CREATE_CONTRACT })
  public async create(@Payload() payload: CreateContractDto) {
    return this.contractService.create(payload);
  }

  @MessagePattern({ cmd: CONTRACT_MESSAGES.UPDATE_CONTRACT })
  public async update(@Payload() payload: any) {
    return this.contractService.update(payload);
  }
}
