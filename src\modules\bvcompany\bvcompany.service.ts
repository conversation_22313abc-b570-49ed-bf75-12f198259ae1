import { Injectable } from '@nestjs/common';

import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { BvCompanyModel } from './bvcompany.model';

@Injectable()
export class BvCompanyService {
  constructor(
    @InjectModel(BvCompanyModel)
    private bvCompanyModel: MongooseModel<BvCompanyModel>,
  ) {}
  async getList(params: QueryParamsDto) {
    const { query, options } = buildQuery(params, ['name', 'identifier']);
    return this.bvCompanyModel.paginate(
      { ...query },
      { ...options, select: '_id identifier name' },
    );
  }
}
