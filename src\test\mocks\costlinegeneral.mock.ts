import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { CostLineGeneralModel } from '~/modules/costlinegeneral/costlinegeneral.model';
import { costLineGeneralTest } from '~/modules/costlinegeneral/test/costlinegeneral.dto.test';

import { mockAgreementLineData } from './agreementline.mock';
import { mockCostTypeData } from './costtype.mock';
import { mockUnitData } from './unit.mock';

const costlineGeneralModel = getModelForClass(CostLineGeneralModel);
type costlineGeneralType = z.infer<typeof costLineGeneralTest.modelSchema>;

export const mockCostlineGeneralData = {
  _id: new ObjectId(),
  agreementLine: mockAgreementLineData._id,
  costType: mockCostTypeData._id,
  createdAt: new Date(),
  description: 'Hua',
  endDate: new Date(),
  futureGenerationDate: new Date(),
  isDeleted: false,
  position: 0,
  price: 650,
  quantity: 1,
  startDate: new Date(),
  unit: mockUnitData._id,
};

export async function initMockCostlineGeneral(
  doc?: Partial<costlineGeneralType>,
) {
  const { _id, ...rest } = { ...mockCostlineGeneralData, ...doc };
  await costlineGeneralModel.replaceOne({ _id }, rest, { upsert: true });
}
