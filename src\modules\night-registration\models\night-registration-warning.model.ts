import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
} from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { HOUR_MINUTE_FORMAT } from '~/constants/app.constant';
import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { NightRegistrationWarningLevel } from '~/shared/enums/night-registration.enum';
import { BaseModel } from '~/shared/models/base.model';

import {
  NightRegistrationReservationDocument,
  NightRegistrationReservationModel,
} from './night-registration-reservation.model';
import {
  NightRegistrationResidentDocument,
  NightRegistrationResidentModel,
} from './night-registration-resident.model';
import {
  NightRegistrationWarningCategoryDocument,
  NightRegistrationWarningCategoryModel,
} from './night-registration-warning-category';

export type NightRegistrationWarningDocument =
  DocumentType<NightRegistrationWarningModel>;

@modelOptions({
  options: { customName: 'NightRegistrationWarning' },
})
@index({ resident: -1, level: -1, emailTemplateOption: -1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class NightRegistrationWarningModel extends BaseModel {
  @prop({ ref: () => NightRegistrationResidentModel })
  resident!: Ref<NightRegistrationResidentDocument>;

  @prop({ ref: () => NightRegistrationReservationModel })
  reservation?: Ref<NightRegistrationReservationDocument>;

  @prop()
  dayToLeave?: Date;

  @prop({
    required: true,
    enum: NightRegistrationWarningLevel,
    default: NightRegistrationWarningLevel.GOOD,
  })
  level!: NightRegistrationWarningLevel;

  @prop()
  description?: string;

  @prop({ default: new Date() })
  warningDate!: Date;

  @prop({ format: HOUR_MINUTE_FORMAT })
  warningTime?: string;

  @prop({ default: [] })
  images!: string[];

  @prop({ default: 1 })
  emailTemplateOption!: number;

  @prop({ required: true, ref: () => NightRegistrationWarningCategoryModel })
  warningCategory!: Ref<NightRegistrationWarningCategoryDocument>;
}
