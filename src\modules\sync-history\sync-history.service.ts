import { Injectable } from '@nestjs/common';
import { Types } from 'mongoose';

import { SyncHistoryModel } from '~/modules/sync-history/sync-history.model';
import {
  SyncHistoryActionType,
  SyncHistoryStatus,
  SyncHistoryType,
} from '~/shared/enums/sync-history.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';

@Injectable()
export class SyncHistoryService {
  constructor(
    @InjectModel(SyncHistoryModel)
    private readonly syncHistoryModel: MongooseModel<SyncHistoryModel>,
  ) {}

  async createNewPending(
    type: SyncHistoryType,
    actionType: SyncHistoryActionType = SyncHistoryActionType.MANUAL,
    extraData?: any,
  ) {
    const newSyncHistory: SyncHistoryModel = {
      type,
      status: SyncHistoryStatus.PENDING,
      actionType,
      ...(extraData && { extraData }),
    };

    return this.syncHistoryModel.create(newSyncHistory);
  }

  async updateToSuccess(syncHistoryId: string | Types.ObjectId) {
    return this.syncHistoryModel.findByIdAndUpdate(
      syncHistoryId,
      {
        status: SyncHistoryStatus.SUCCESS,
        syncedAt: new Date(),
      },
      { new: true },
    );
  }

  async updateToFailed(
    syncHistoryId: string | Types.ObjectId,
    failedReason?: string,
  ) {
    return this.syncHistoryModel.findByIdAndUpdate(
      syncHistoryId,
      {
        status: SyncHistoryStatus.FAILED,
        failedReason,
        syncedAt: new Date(),
      },
      { new: true },
    );
  }

  async findLatestSyncHistory(
    type: SyncHistoryType,
    status?: SyncHistoryStatus,
  ): Promise<SyncHistoryModel | null> {
    const query = { type, ...(status && { status }) };
    const results = await this.syncHistoryModel
      .find(query)
      .sort({ syncedAt: -1 })
      .limit(1)
      .lean();
    return (results[0] as any) ?? null;
  }

  async isPending(type: SyncHistoryType): Promise<boolean> {
    const latestSyncHistory = await this.findLatestSyncHistory(type);
    return latestSyncHistory?.status === SyncHistoryStatus.PENDING;
  }

  async findLatestSuccessfulSyncedDate(
    type: SyncHistoryType,
  ): Promise<Date | null> {
    const latestSyncHistory = await this.findLatestSyncHistory(
      type,
      SyncHistoryStatus.SUCCESS,
    );
    return latestSyncHistory?.syncedAt ?? null;
  }

  async startProcess(
    {
      type,
      extraData,
      actionType,
      checkForPending = true,
    }: {
      type: SyncHistoryType;
      extraData?: any;
      actionType?: SyncHistoryActionType;
      checkForPending?: boolean;
    },
    process: <T>({
      saveExtraData,
    }: {
      saveExtraData: (extraData: any) => Promise<any>;
    }) => Promise<T | void>,
    fallback?: <T>(error: any) => void | Promise<T | void>,
  ) {
    if (checkForPending && (await this.isPending(type))) {
      throw new Error(`Sync history for ${type} is still pending`);
    }

    const syncHistory = await this.createNewPending(
      type,
      actionType,
      extraData,
    );

    const saveExtraData = async (extraData: any) => {
      return this.syncHistoryModel.findByIdAndUpdate(
        syncHistory._id,
        { $set: { extraData } },
        { new: true },
      );
    };

    try {
      await process({ saveExtraData });
      await this.updateToSuccess(syncHistory._id);
    } catch (error: any) {
      let message: string = error.message;
      if (error.response?.data) {
        const data = error.response.data;
        message += `. Error=${data === Object(data) ? JSON.stringify(error.response.data) : data}`;
      }

      await this.updateToFailed(syncHistory._id, message);
      if (fallback) {
        await fallback(error);
      } else {
        throw error;
      }
    }
  }
}
