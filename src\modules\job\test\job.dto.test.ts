import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { contactTest } from '~/modules/contact/test/contact.dto.test';
import { equipmentTest } from '~/modules/equipment/test/equipment.dto.test';
import { jobPointTest } from '~/modules/job-point/test/job-point.dto.test';
import { tenantUserTest } from '~/modules/tenant-user/test/tenant-user.dto.test';
import { unitTest } from '~/modules/unit/test/unit.dto.test';
import {
  GeneratePdfStatusEnum,
  JobFDaysEnum,
  JobFRuleEnum,
  JobPeriodTypeEnum,
  JobReportTypeEnum,
  JobStatusEnum,
  JobTypeEnum,
} from '~/shared/enums/job.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const jobUserInfoSchema = z.object({
  _id: z.string().or(z.instanceof(ObjectId)),
  displayName: z.string(),
  email: z.string(),
});

const modelSchema = z
  .object({
    isActive: z.boolean(),
    identifier: z.string(),
    title: z.string().optional(),
    status: z.nativeEnum(JobStatusEnum),
    type: z.nativeEnum(JobPeriodTypeEnum),
    jobType: z.nativeEnum(JobTypeEnum),
    reportType: z.nativeEnum(JobReportTypeEnum),
    reportContact: z.instanceof(ObjectId).optional(),
    plannedDate: z.date(),
    plannedEndDate: z.date().optional(),
    isOverdue: z.boolean().optional(),
    instructions: z.string().optional(),
    feedbacks: z.string().optional(),
    fuaDescriptions: z.string().optional(),
    rejectionReason: z.string().optional(),
    parent: z.instanceof(ObjectId).optional(),
    assignee: z.instanceof(ObjectId),
    planner: z.instanceof(ObjectId).optional(),
    location: z.instanceof(ObjectId),
    units: z.array(z.instanceof(ObjectId)),
    equipments: z.array(z.instanceof(ObjectId)).optional(),
    images: z.array(z.string()).optional(),
    updatedBy: z.instanceof(ObjectId).optional(),
    rejectedBy: z.instanceof(ObjectId).optional(),
    completedBy: z.instanceof(ObjectId).optional(),
    performedAt: z.date().optional(),
    readiedAt: z.date().optional(),
    rejectedAt: z.date().optional(),
    completedAt: z.date().optional(),
    fInterval: z.number().optional(),
    fRule: z.nativeEnum(JobFRuleEnum).optional(),
    fDays: z.array(z.nativeEnum(JobFDaysEnum)).optional(),
    fDayInMonth: z.number().optional(),
    fStartDate: z.date().optional(),
    fEndDate: z.date().optional(),
    fIdentifier: z.string().optional(),
    isSendRC: z.boolean(),
    isSendRR: z.boolean(),
    rtContacts: z.array(z.instanceof(ObjectId)).optional(),
    rrContacts: z.array(z.instanceof(ObjectId)).optional(),
    invoiceContact: z.instanceof(ObjectId),
    locationInfo: z
      .object({
        _id: z.string().or(z.instanceof(ObjectId)),
        fullAddress: z.string(),
      })
      .optional(),
    locationTeamInfo: z
      .object({
        _id: z.instanceof(ObjectId),
        name: z.string(),
      })
      .optional(),
    assigneeInfo: jobUserInfoSchema.optional(),
    plannerInfo: jobUserInfoSchema.optional(),
    reportContactInfo: jobUserInfoSchema.optional(),
    pdfPublicUrl: z.string().optional(),
    generatePdfStatus: z.nativeEnum(GeneratePdfStatusEnum).optional(),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(
  modelSchema
    .pick({
      _id: true,
      isActive: true,
      fIdentifier: true,
      fStartDate: true,
      fEndDate: true,
      identifier: true,
      title: true,
      status: true,
      type: true,
      plannedDate: true,
      plannedEndDate: true,
      units: true,
      createdAt: true,
      updatedAt: true,
    })
    .extend({
      assignee: jobUserInfoSchema.optional(),
      location: z.object({
        _id: z.string().or(z.instanceof(ObjectId)),
        fullAddress: z.string(),
        team: z.object({
          _id: z.string().or(z.instanceof(ObjectId)),
          name: z.string(),
        }),
      }),
    }),
);

const findAllMobileSchema = z.array(
  modelSchema
    .pick({
      _id: true,
      isActive: true,
      isDeleted: true,
      identifier: true,
      title: true,
      status: true,
      type: true,
      jobType: true,
      plannedDate: true,
      plannedEndDate: true,
      assignee: true,
    })
    .extend({
      location: modelSchema.shape.locationInfo,
    }),
);

const findOneSchema = modelSchema
  .omit({
    isSendRC: true,
    isSendRR: true,
    invoiceContact: true,
  })
  .extend({
    reportType: z.nativeEnum(JobReportTypeEnum).nullish(),
    location: z
      .object({
        _id: z.string().or(z.instanceof(ObjectId)),
        fullAddress: z.string(),
        locationOf: z.array(z.any()).optional(),
      })
      .nullish(),
    units: z
      .array(
        unitTest.modelSchema
          .pick({
            _id: true,
            name: true,
            parent: true,
          })
          .nullish(),
      )
      .nullish(),
    assignee: modelSchema.shape.assigneeInfo.nullish(),
    planner: modelSchema.shape.plannerInfo.nullish(),
    employees: z
      .array(
        z.object({
          employee: contactTest.modelSchema.pick({
            _id: true,
            displayName: true,
          }),
          estimatedHours: z.number().optional(),
          actualHours: z.number().optional(),
          plannedDate: z.date().optional(),
        }),
      )
      .nullish(),
    equipments: z
      .array(
        z.object({
          _id: z.string().or(z.instanceof(ObjectId)),
          equipment: equipmentTest.modelSchema.pick({
            _id: true,
            description: true,
            type: true,
          }),
          plannedDate: z.date().optional(),
        }),
      )
      .nullish(),
    jobPoints: z
      .array(
        jobPointTest.modelSchema
          .pick({
            _id: true,
            description: true,
            position: true,
          })
          .extend({
            unit: unitTest.modelSchema.pick({
              _id: true,
              name: true,
              parent: true,
            }),
          }),
      )
      .nullish(),
  });

const checkOverlapJobSchema = z.object({
  employees: z
    .array(
      tenantUserTest.modelSchema
        .pick({
          oddWeeks: true,
          evenWeeks: true,
          displayName: true,
        })
        .extend({
          plannedDate: z.string().datetime(),
        })
        .passthrough(),
    )
    .nullish()
    .optional(),
  equipments: z
    .array(
      equipmentTest.modelSchema
        .pick({
          type: true,
          description: true,
          isActive: true,
        })
        .extend({
          plannedDate: z.string().datetime(),
        })
        .passthrough(),
    )
    .nullish()
    .optional(),
});

const viewSummaryJobSchema = z.object({
  job: z.object({
    _id: z.string().or(z.instanceof(ObjectId)),
    identifier: z.string(),
    title: z.string().optional(),
    status: z.nativeEnum(JobStatusEnum),
    type: z.nativeEnum(JobPeriodTypeEnum),
    jobType: z.nativeEnum(JobTypeEnum),
    reportType: z.nativeEnum(JobReportTypeEnum).optional(),
    reportContact: contactTest.modelSchema
      .pick({
        _id: true,
        contactType: true,
        contactRole: true,
        displayName: true,
      })
      .optional(),
    isSendRC: z.boolean().optional(),
    isSendRR: z.boolean().optional(),
    rtContacts: z
      .array(
        contactTest.modelSchema.pick({
          _id: true,
          contactType: true,
          contactRole: true,
          displayName: true,
        }),
      )
      .optional(),
    rrContacts: z
      .array(
        contactTest.modelSchema.pick({
          _id: true,
          contactType: true,
          contactRole: true,
          displayName: true,
        }),
      )
      .optional(),
    invoiceContact: contactTest.modelSchema
      .pick({
        _id: true,
        contactType: true,
        contactRole: true,
        displayName: true,
      })
      .optional(),
    location: z.string().or(z.instanceof(ObjectId)),
    units: z
      .array(
        unitTest.modelSchema.pick({
          _id: true,
          name: true,
          parent: true,
          position: true,
          isRoot: true,
        }),
      )
      .optional(),
    equipments: z
      .array(
        equipmentTest.modelSchema.pick({
          _id: true,
          description: true,
          type: true,
        }),
      )
      .optional(),
  }),
  employees: z.array(
    z.object({
      _id: z.string().or(z.instanceof(ObjectId)),
      employee: z.object({
        _id: z.string().or(z.instanceof(ObjectId)),
        displayName: z.string(),
      }),
      estimatedHours: z.number().optional(),
      actualHours: z.number().optional(),
      plannedDate: z.date().optional(),
    }),
  ),
  units: z.array(
    z.object({
      _id: z.string().or(z.instanceof(ObjectId)),
      name: z.string(),
      parent: z
        .object({
          _id: z.string().or(z.instanceof(ObjectId)),
          name: z.string(),
          position: z.number(),
        })
        .nullable()
        .optional(),
      position: z.number(),
      isRoot: z.boolean().optional(),
      points: z.array(
        jobPointTest.modelSchema
          .pick({
            _id: true,
            description: true,
            position: true,
            status: true,
            notes: true,
            images: true,
          })
          .extend({
            unit: unitTest.modelSchema
              .pick({
                _id: true,
                name: true,
              })
              .extend({
                parent: z
                  .object({
                    _id: z.string().or(z.instanceof(ObjectId)),
                    name: z.string(),
                    position: z.number(),
                  })
                  .nullable()
                  .optional(),
              }),
          }),
      ),
    }),
  ),
  totalGreenResult: z.number(),
  totalYellowResult: z.number(),
  totalRedResult: z.number(),
});

const findReviewDetailSchema = z.object({
  units: z.array(
    z.object({
      _id: z.string().or(z.instanceof(ObjectId)),
      name: z.string(),
      parent: z
        .object({
          _id: z.string().or(z.instanceof(ObjectId)),
          name: z.string(),
          position: z.number(),
        })
        .nullable()
        .optional(),
      position: z.number(),
      isRoot: z.boolean().optional(),
      points: z.array(
        jobPointTest.modelSchema
          .pick({
            _id: true,
            description: true,
            position: true,
            status: true,
            notes: true,
            images: true,
            actions: true,
          })
          .extend({
            unit: unitTest.modelSchema
              .pick({
                _id: true,
                name: true,
                position: true,
              })
              .extend({
                parent: z
                  .object({
                    _id: z.string().or(z.instanceof(ObjectId)),
                  })
                  .nullable()
                  .optional(),
              }),
            costLines: z
              .array(
                z.object({
                  _id: z.string().or(z.instanceof(ObjectId)),
                  description: z.string(),
                  position: z.number(),
                  price: z.number(),
                  quantity: z.number(),
                  totalPrice: z.number(),
                  costType: z.object({
                    _id: z.string().or(z.instanceof(ObjectId)),
                    name: z.string(),
                    itemCode: z.string(),
                  }),
                }),
              )
              .optional(),
          }),
      ),
      reservations: z
        .array(
          z.object({
            _id: z.string().or(z.instanceof(ObjectId)).optional(),
            contact: z
              .object({
                _id: z.string().or(z.instanceof(ObjectId)),
                displayName: z.string(),
              })
              .optional(),
            checkInAt: z.date().optional(),
            checkOutAt: z.date().optional(),
            bedNo: z.number().optional(),
          }),
        )
        .optional(),
    }),
  ),
});

const detailsPlanningJobSchema = z.object({
  _id: z.string().or(z.instanceof(ObjectId)),
  identifier: z.string(),
  title: z.string().optional(),
  status: z.nativeEnum(JobStatusEnum),
  fIdentifier: z.string().optional(),
  locationInfo: z.any().optional(),
  plannedDate: z.date(),
  instructions: z.string().optional(),
  isOverdue: z.boolean().optional(),
  scheduler: z.string().or(z.instanceof(ObjectId)).optional(),
  jobType: z.nativeEnum(JobTypeEnum),
  employees: z.array(z.string().or(z.instanceof(ObjectId))),
});

export const jobTest = {
  modelSchema,
  findAllSchema,
  findAllMobileSchema,
  findOneSchema,
  checkOverlapJobSchema,
  viewSummaryJobSchema,
  findReviewDetailSchema,
  detailsPlanningJobSchema,
};
