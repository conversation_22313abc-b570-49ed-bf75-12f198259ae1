import dayjs from 'dayjs';
import _ from 'lodash';

export const buildDateRangeQueryParams = ({
  year,
  week = undefined,
  month = undefined,
  timezone = '+00:00',
}: {
  year: string;
  week?: string;
  month?: string;
  timezone?: string;
}) => {
  const yearNum = Number(year);
  const startDate = dayjs().utcOffset(timezone).year(yearNum);
  const endDate = dayjs().utcOffset(timezone).year(yearNum);

  if (!_.isNil(week)) {
    const weekNum = Number(week);
    return {
      startDate: startDate.isoWeek(weekNum).startOf('isoWeek').toDate(),
      endDate: endDate.isoWeek(weekNum).endOf('isoWeek').toDate(),
    };
  }

  if (!_.isNil(month)) {
    let monthNum = Number(month);
    monthNum = monthNum > 0 ? monthNum - 1 : 0;
    return {
      startDate: startDate.month(monthNum).startOf('month').toDate(),
      endDate: endDate.month(monthNum).endOf('month').toDate(),
    };
  }

  return {
    startDate: startDate.startOf('year').toDate(),
    endDate: endDate.endOf('year').toDate(),
  };
};

export const getGroupTypeDisplay = (tenantName?: string) => {
  switch (tenantName?.toLowerCase()) {
    case 'kafra':
      return 'KaFra Exploitatie B.V.';
    case 'logejo':
    // return '???'; // TBD
    case 'homee':
    default:
      return 'EEAC';
  }
};
