import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldDocument {
  _id: string;
  type: 'HR' | 'FACILITIES' | 'NIGHT_REGISTRATIONS' | 'SUPPORT';
  location: string;
  updatedBy: string;
  documentFile: string;
  uploaderName: string;
  fileName: string;
  createdAt: Date;
  updatedAt: Date;
  uploadFile: any[];
}

const DocumentPipeLineAggregate = (skip: number, limit: number) => {
  // get all document file with upload file lookup to upload_file collection by filename
  return [
    {
      $lookup: {
        from: 'upload_file',
        localField: 'fileName',
        foreignField: 'name',
        as: 'uploadFile',
      },
    },
    {
      $sort: {
        _id: 1,
      },
    },
    { $skip: skip },
    { $limit: limit },
  ];
};

const transformDataFunc = ({
  data,
  context,
}: {
  data: OldDocument[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (item) => {
      const transformedData = {
        _id: item._id,
        type: item.type,
        location: item.location,
        uploaderName: item.uploaderName,
        fileName: item.fileName,
        isDeleted: false,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      };

      if (item?.uploadFile?.length > 0) {
        // get the first upload file
        const uploadFileItem = item?.uploadFile[0];
        return {
          ...transformedData,
          uploadFile: uploadFileItem._id,
        };
      } else {
        return transformedData;
      }
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('documentfile');

  const pipeline = DocumentPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'documentfile',
      destinationCollectionName: 'documentfiles',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: transformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
