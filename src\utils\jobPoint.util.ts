import _ from 'lodash';

export function GroupJobPointsBySortedUnit(jobPoints: any, rootUnit: any) {
  const jobPointsGroupByUnit = _.groupBy(jobPoints, 'unit._id');
  const jobPointsGroupByUnitSorted = Object.keys(jobPointsGroupByUnit)
    .map((unitId) => {
      const points = jobPointsGroupByUnit[unitId];
      const unit = points[0].unit as any;

      const isRoot = unit._id.toString() === rootUnit?._id.toString();

      const parentIsRoot =
        !isRoot && unit.parent._id.toString() === rootUnit?._id.toString();

      const position = isRoot
        ? 0
        : parentIsRoot
          ? unit.position
          : Number(`${unit.parent.position}.${unit.position}`);

      return {
        ...unit,
        position,
        points: points.sort((a, b) => {
          return a.position - b.position;
        }),
      };
    })
    .sort((a, b) => {
      return a.position - b.position;
    });

  return jobPointsGroupByUnitSorted;
}
