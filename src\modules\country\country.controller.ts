import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { QueryParamsDto } from '~/shared/dtos/query-builder.dto';
import { COUNTRY_MESSAGES } from '~/shared/messages/country.message';

import { CountryService } from './country.service';

@Controller('country')
export class CountryController {
  constructor(private countryService: CountryService) {}

  @HTTPDecorators.Paginator
  @MessagePattern({ cmd: COUNTRY_MESSAGES.GET_LIST_COUNTRY })
  @UsePipes(new ZodValidationPipe(QueryParamsDto))
  async getList(@Payload() payload: QueryParamsDto) {
    return await this.countryService.getList(payload);
  }
}
