export const DB_CONNECTION_TOKEN = '__db_connection_token__';
export const DB_MODEL_TOKEN_SUFFIX = '__model_token_suffix__';

export const HTTP_RES_TRANSFORM_PAGINATE = '__customHttpResTransformPagenate__';

export const INVENTORY_SERVICE_CLIENT = '__inventory_service_client__';
export const PDF_SERVICE_CLIENT = '__pdf_service_client__';
export const X_TENANT_ID_HEADER = 'x-tenant-id';
export const X_TENANT_PLATFORM_HEADER = 'x-tenant-platform';

export const MAX_TEXT_LENGTH = 2048;
export const MAX_LONG_TEXT_LENGTH = 4096;
export const MAX_DESCRIPTION_LENGTH = 256;

// date format
export const DATE_FORMAT_SLASH = 'DD/MM/YYYY';
export const DATE_FORMAT_HYPHEN = 'DD-MM-YYYY';
export const DATE_FORMAT_ABBREVIATION = 'DD-MMM-YYYY';
export const HOUR_MINUTE_FORMAT = 'HH:mm';

export const DEPOSIT_COST_TYPE_ITEM_CODE = '1350N';
