import mongoose from 'mongoose';

import { zodMatchers } from './helpers/test-expect';

jest.setTimeout(300000);

expect.extend(zodMatchers);

beforeAll(async () => {
  await mongoose.connect(process.env.JEST_MONGO_URI as string);
});

afterAll(async () => {
  console.log('Cleaning up test database...');

  if (mongoose.connection.readyState !== 1) {
    throw new Error('Mongoose is not connected');
  }

  const collections = Object.keys(mongoose.connection.collections);
  await Promise.all(
    collections.map((collectionName) => {
      const collection = mongoose.connection.collections[collectionName];
      return collection.deleteMany({});
    }),
  );

  await mongoose.disconnect();
});
