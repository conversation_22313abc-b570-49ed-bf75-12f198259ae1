import { Modu<PERSON> } from '@nestjs/common';

import { SyncHistoryModule } from '~/modules/sync-history/sync-history.module';
import { LoggerModule } from '~/processors/logger/logger.module';
import { AfasModule } from '~/processors/third-party-connector/strategies/afas/afas.module';
import { ThirdPartyConnectorContext } from '~/processors/third-party-connector/strategies/third-party-connector.context';

import { SendAlertModule } from '../send-alert/send-alert.module';

@Module({
  imports: [AfasModule, LoggerModule, SyncHistoryModule, SendAlertModule],
  providers: [ThirdPartyConnectorContext],
  exports: [AfasModule, ThirdPartyConnectorContext],
})
export class ThirdPartyConnectorModule {}
