import { DocumentType, modelOptions, plugin, prop } from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { BaseModel } from '~/shared/models/base.model';

export type NightRegistrationNationalityDocument =
  DocumentType<NightRegistrationNationalityModel>;

@modelOptions({
  options: { customName: 'NightRegistrationNationality' },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class NightRegistrationNationalityModel extends BaseModel {
  @prop({ required: true, unique: true })
  name!: string;

  @prop({ required: true, unique: true })
  code!: string;
}
