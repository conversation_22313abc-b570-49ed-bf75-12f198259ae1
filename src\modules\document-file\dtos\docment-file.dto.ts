import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { DocumentFileTypeEnum } from '~/shared/enums/document-file-type.enum';
import { parseObjectId } from '~/utils';

const documentFileTypes = Object.values(DocumentFileTypeEnum) as [
  DocumentFileTypeEnum,
  ...DocumentFileTypeEnum[],
];

const DocumentFileCreateSchema = z.strictObject({
  type: z.enum(documentFileTypes),
  uploaderName: z.string(),
  createdBy: z.string().transform((value) => {
    return parseObjectId(value);
  }),
  location: z
    .string()
    .transform((value) => {
      return parseObjectId(value);
    })
    .optional(),
  uploadFileIds: z
    .array(
      z.string().transform((value) => {
        return parseObjectId(value);
      }),
    )
    .min(1),
});

const DocumentFileQueryParamsSchema = z
  .strictObject({
    type: z.enum(documentFileTypes).optional(),
  })
  .merge(QueryParamsSchema);

const DocumentFileCheckExistedParamsSchema = z.strictObject({
  type: z.enum(documentFileTypes).optional(),
  fileName: z.string(),
  user: z.string(),
});

export class DocumentFileCreateDto extends createZodDto(
  DocumentFileCreateSchema,
) {}

export class DocumentFileQueryParamDto extends createZodDto(
  DocumentFileQueryParamsSchema,
) {}

export class DocumentFileCheckExistedParamsDto extends createZodDto(
  DocumentFileCheckExistedParamsSchema,
) {}
