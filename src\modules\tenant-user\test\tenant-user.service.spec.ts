import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';
import mongoose from 'mongoose';

import { EmailTemplateModel } from '~/modules/email-template/email-template.model';
import { EmailTemplateService } from '~/modules/email-template/email-template.service';
import { TeamModel } from '~/modules/team/team.model';
import { TenantModel } from '~/modules/tenant/tenant.model';
import { TenantService } from '~/modules/tenant/tenant.service';
import { TenantPermissionModel } from '~/modules/tenant-permission/tenant-permission.model';
import { TenantRoleModel } from '~/modules/tenant-role/tenant-role.model';
import { TokenModel } from '~/modules/token/token.model';
import { EmailService } from '~/processors/email/email.service';
import { EmailTemplateEnum } from '~/shared/enums/email-template.enum';
import { ThisWeekDefaultDisplay } from '~/shared/enums/tenant-user.enum';
import { WorkingDays } from '~/shared/enums/working-days.enum';
import { TENANT_USER_MESSAGE_KEYS } from '~/shared/message-keys/tenant-user.message-key';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockEmailTemplate } from '~/test/mocks/emailtemplate.mock';
import { initMockTeam, mockTeamData } from '~/test/mocks/team.mock';
import { initMockTenant } from '~/test/mocks/tenant.mock';
import { initMockTenantPermission } from '~/test/mocks/tenantpermission.mock';
import {
  initMockTenantRole,
  mockTenantRoleData,
} from '~/test/mocks/tenantrole.mock';
import {
  initMockTenantUser,
  mockTenantUserData,
} from '~/test/mocks/tenantuser.mock';

import {
  CreateTenantUserDto,
  EmployeeQueryParamsDto,
  UpdateTeamManagementDto,
  UpdateTenantUserDto,
} from '../dtos/tenant-user.dto';
import { Gender, Language, TenantUserModel } from '../tenant-user.model';
import { TenantUserService } from '../tenant-user.service';
import { tenantUserTest } from './tenant-user.dto.test';

describe('TenantUserService', () => {
  let service: TenantUserService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        TenantUserService,
        ...testInjectProviders([
          TeamModel,
          TenantUserModel,
          TenantRoleModel,
          TokenModel,
          TenantPermissionModel,
          TenantModel,
          EmailTemplateModel,
          // Service
          EmailService,
        ]),
        TenantService,
        EmailTemplateService,
      ],
    }).compile();

    service = module.get(TenantUserService);

    await Promise.all([
      initMockTenantUser(),
      initMockTenantRole(),
      initMockTenant(),
      initMockTenantPermission(),
      initMockTeam(),
      initMockEmailTemplate({
        name: EmailTemplateEnum.CREATE_USER,
        html: '<div>hello world</div>',
      }),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    it('should return list users when no roles in payload', async () => {
      const result = await service.findAll({});
      expect(result.docs).toBeDefined();
      expect(result.docs).toMatchSchema(tenantUserTest.findAllSchema);
    });

    it('should return list users when valid roles in payload', async () => {
      const payload: EmployeeQueryParamsDto = {
        roles: [mockTenantRoleData.key],
      };

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(tenantUserTest.findAllSchema);
    });

    it('should return empty list if data not exist', async () => {
      const payload: EmployeeQueryParamsDto = {
        pageIndex: 99,
      };

      const result = await service.findAll(payload);
      expect(result.docs).toEqual([]);
    });

    it('should throw BadRequestException if roles invalid', async () => {
      const payload: EmployeeQueryParamsDto = {
        roles: ['invalidRole'],
      };

      await expect(service.findAll(payload)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.INVALID_ROLE,
      );
    });
  });

  describe('findOne', () => {
    it('should return user when valid id in payload', async () => {
      const id = mockTenantUserData._id.toString();
      const result = await service.findOne(id, []);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(tenantUserTest.findOneSchema);
    });

    it('should throw NotFoundException if user not found', async () => {
      const id = new ObjectId().toString();
      await expect(service.findOne(id, [])).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND,
      );
    });
  });

  describe('create', () => {
    const basePayload = {
      isActive: true,
      firstName: 'New',
      lastName: 'User',
      phone1: '0123456789',
      gender: Gender.MALE,
      language: Language.EN,
      roles: [mockTenantRoleData.key],
      team: mockTenantUserData.team.toString(),
      oddWeeks: [WorkingDays.MONDAY],
      evenWeeks: [WorkingDays.MONDAY],
      thisWeekDefaultDisplay: ThisWeekDefaultDisplay.SHOPPING_LIST,
      saveToPhotos: true,
    };

    it('should create user successfully with generated password', async () => {
      const payload: CreateTenantUserDto = {
        ...basePayload,
        username: 'newuser1',
        email: '<EMAIL>',
        isGeneratePassword: true,
      };
      const result = await service.create(payload);
      expect(result).toBeDefined();
      expect(result!.username).toBe(payload.username);
      expect(result!.email).toBe(payload.email);
      expect(result).toMatchSchema(tenantUserTest.findOneSchema);
    });

    it('should create user successfully with provided password', async () => {
      const payload: CreateTenantUserDto = {
        ...basePayload,
        username: 'newuser2',
        email: '<EMAIL>',
        password: 'Abcdef1!',
        isGeneratePassword: false,
      };

      const result = await service.create(payload);
      expect(result).toBeDefined();
      expect(result!.username).toBe(payload.username);
      expect(result!.email).toBe(payload.email);
      expect(result).toMatchSchema(tenantUserTest.findOneSchema);
    });

    it('should throw error if email or username already exists', async () => {
      const payload: CreateTenantUserDto = {
        ...basePayload,
        username: mockTenantUserData.username,
        email: mockTenantUserData.email,
        password: 'Abcdef1!',
        isGeneratePassword: false,
      };
      await expect(service.create(payload)).rejects.toThrow();
    });

    it('should throw error if team is invalid', async () => {
      const payload: CreateTenantUserDto = {
        ...basePayload,
        username: 'newuser3',
        email: '<EMAIL>',
        password: 'Abcdef1!',
        isGeneratePassword: false,
        team: new ObjectId().toString(),
      };
      await expect(service.create(payload)).rejects.toThrow();
    });

    it('should throw error if roles are invalid', async () => {
      const payload: CreateTenantUserDto = {
        ...basePayload,
        username: 'newuser4',
        email: '<EMAIL>',
        password: 'Abcdef1!',
        isGeneratePassword: false,
        roles: ['invalidRole'],
      };
      await expect(service.create(payload)).rejects.toThrow();
    });

    it('should throw error if isGeneratePassword is false but password is missing', async () => {
      const payload: CreateTenantUserDto = {
        ...basePayload,
        username: 'newuser5',
        email: '<EMAIL>',
        isGeneratePassword: false,
      };
      const result = await service.create(payload);
      expect(result).toBeUndefined();
    });
  });

  describe('update', () => {
    const team2Id = new ObjectId();

    const payload: UpdateTenantUserDto = {
      id: mockTenantUserData._id.toString(),
      firstName: 'Updated',
      lastName: 'User',
      phone1: '0987654321',
      gender: Gender.MALE,
      language: Language.EN,
      roles: [mockTenantRoleData.key],
      team: team2Id.toString(),
      oddWeeks: [WorkingDays.MONDAY],
      evenWeeks: [WorkingDays.MONDAY],
      thisWeekDefaultDisplay: ThisWeekDefaultDisplay.SHOPPING_LIST,
      saveToPhotos: false,
      isActive: false,
      email: mockTenantUserData.email,
    };

    it('should update user successfully', async () => {
      await initMockTeam({ _id: team2Id, name: 'Team 2' });

      const result = await service.update(payload, false);
      expect(result).toBeDefined();
      expect(result.firstName).toBe('Updated');
      expect(result).toMatchSchema(tenantUserTest.findOneSchema);
    });

    it('should throw error if id invalid', async () => {
      const invalidPayload = {
        ...payload,
        id: new ObjectId().toString(),
      };
      await expect(service.update(invalidPayload, false)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND,
      );
      await initMockTenantUser({ isRoot: true });
      await expect(service.update(payload, false)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND,
      );
      await initMockTenantUser({ isRoot: false });
    });

    it('should throw error if roles not found', async () => {
      const invalidRolesPayload = {
        ...payload,
        roles: ['invalidRole'],
      };
      await expect(service.update(invalidRolesPayload, false)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.ROLES_REQUIRED,
      );
    });

    it('should throw error if roles invalid', async () => {
      jest
        .spyOn(service['tenantRoleModel'], 'find')
        .mockResolvedValueOnce([{}, {}]);

      await expect(service.update(payload, false)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.INVALID_ROLE,
      );
    });

    it('should throw error if email is existing', async () => {
      const userId = new ObjectId();
      await initMockTenantUser({ _id: userId, email: '<EMAIL>' });

      const existingEmailPayload: UpdateTenantUserDto = {
        ...payload,
        id: userId.toString(),
        email: mockTenantUserData.email,
      };
      await expect(service.update(existingEmailPayload, false)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.EMAIL_EXISTS,
      );
    });

    it('should throw error if invalid team', async () => {
      const invalidTeamPayload: UpdateTenantUserDto = {
        ...payload,
        team: new ObjectId().toString(),
      };
      await expect(service.update(invalidTeamPayload, false)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.INVALID_TEAM,
      );
    });
  });

  describe('updatePassword', () => {
    it('should throw error if user not found', async () => {
      const id = new ObjectId().toString();
      await expect(service.updatePassword({ id }, false)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if has isChange option and password changed < 6 months', async () => {
      const id = mockTenantUserData._id.toString();
      await expect(service.updatePassword({ id }, true)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.CHANGE_PASSWORD_BEFORE_SIX_MONTHS,
      );
    });

    it('should throw error if old password is wrong', async () => {
      const id = mockTenantUserData._id.toString();
      const oldPassword = 'wrongOldPassword';
      await expect(
        service.updatePassword({ id, oldPassword }, false),
      ).rejects.toThrow(TENANT_USER_MESSAGE_KEYS.WRONG_PASSWORD);
    });

    it('should throw error if new password is same as old password', async () => {
      const id = mockTenantUserData._id.toString();
      const oldPassword = '123456zZ@';
      const password = '123456zZ@';
      await expect(
        service.updatePassword({ id, oldPassword, password }, false),
      ).rejects.toThrow(TENANT_USER_MESSAGE_KEYS.OLD_PASSWORD);
    });

    it('should update password successfully', async () => {
      const id = mockTenantUserData._id.toString();
      const oldPassword = '123456zZ@';
      const password = '123456aA@';
      const result = await service.updatePassword(
        { id, oldPassword, password },
        false,
      );
      expect(result).toBeDefined();
      expect(result).toMatchSchema(tenantUserTest.findOneSchema);
    });
  });

  describe('resetPassword', () => {
    it('should throw error if user not found', async () => {
      const id = new ObjectId().toString();
      await expect(service.resetPassword({ id } as any)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if password is same as old password', async () => {
      const id = mockTenantUserData._id.toString();
      const password = '123456aA@';
      await expect(service.resetPassword({ id, password })).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.OLD_PASSWORD,
      );
    });

    it('should reset password successfully', async () => {
      const id = mockTenantUserData._id.toString();
      const password = ' 123456zZ@';
      const result = await service.resetPassword({ id, password });
      expect(result).toBeDefined();
      expect(result).toMatchSchema(tenantUserTest.findOneSchema);
    });
  });

  describe('getTeamManagement', () => {
    it('should throw error if team not found', async () => {
      const teamId = new ObjectId().toString();
      await expect(service.getTeamManagement(teamId)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.INVALID_TEAM,
      );
    });

    it('should empty list if team has no users', async () => {
      const teamId = new ObjectId().toString();
      await initMockTeam({ _id: teamId, name: 'Empty Team' });

      const result = await service.getTeamManagement(teamId);
      expect(result).toBeDefined();
      expect(result).toEqual([]);
    });

    it('should return team management data', async () => {
      const teamId = mockTenantUserData.team.toString();
      const result = await service.getTeamManagement(teamId);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(tenantUserTest.getTeamManagementSchema);
    });
  });

  describe('updateTeamManagement', () => {
    it('should throw error if team not found', async () => {
      const payload = {
        team: new ObjectId().toString(),
        employees: [],
      };
      await expect(service.updateTeamManagement(payload)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.INVALID_TEAM,
      );
    });

    it('should throw error if user not found', async () => {
      await service['tenantUserModel'].deleteMany({});

      const payload: UpdateTeamManagementDto = {
        team: mockTeamData._id.toString(),
        employees: [{ _id: mockTenantUserData._id.toString(), position: 1 }],
      };
      await expect(service.updateTeamManagement(payload)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should throw error if user not in team', async () => {
      await initMockTenantUser({ team: new ObjectId() });

      const payload: UpdateTeamManagementDto = {
        team: mockTeamData._id.toString(),
        employees: [{ _id: mockTenantUserData._id.toString(), position: 1 }],
      };
      await expect(service.updateTeamManagement(payload)).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.USER_NOT_EXIST_IN_TEAM,
      );
    });

    it('should update team management successfully', async () => {
      await initMockTenantUser();

      const payload: UpdateTeamManagementDto = {
        team: mockTeamData._id.toString(),
        employees: [{ _id: mockTenantUserData._id.toString(), position: 1 }],
      };
      const result = await service.updateTeamManagement(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(tenantUserTest.getTeamManagementSchema);
    });
  });

  describe('exportEmployees', () => {
    it('should call fn and return data formatted as csv', async () => {
      const result = await service.exportEmployees();
      expect(result).toBeDefined();
      expect(result).toMatchSchema(tenantUserTest.exportEmployeesSchema);
    });

    it('should throw error if generate content failed', async () => {
      const circularObj: any = {};
      circularObj.self = circularObj;

      jest.spyOn(service['tenantUserModel'], 'find').mockReturnValueOnce({
        select: () => ({
          lean: () =>
            Promise.resolve([
              {
                username: circularObj,
                roles: mongoose.Types.Decimal128.fromString('1'),
              },
            ]),
        }),
      } as any);

      await expect(service.exportEmployees()).rejects.toThrow();
    });
  });

  describe('sendEmailInviteToNewSystem', () => {
    it('should send email invite to new system', async () => {
      await service.sendEmailInviteToNewSystem();
      jest
        .spyOn(service['emailService'], 'sendEmail')
        .mockRejectedValueOnce(new Error('Email send failed'));
      const result = await service.sendEmailInviteToNewSystem();
      expect(result).toStrictEqual({});
    });

    it('should not send email if user not email', async () => {
      await initMockTenantUser({ email: '' });

      const result = await service.sendEmailInviteToNewSystem();
      expect(result).toBeUndefined();
    });

    it('should not send email if no users', async () => {
      await service['tenantUserModel'].deleteMany({});

      const result = await service.sendEmailInviteToNewSystem();
      expect(result).toStrictEqual({});
    });

    it('should not send email if not tenant', async () => {
      await service['tenantModel'].deleteMany({});

      const result = await service.sendEmailInviteToNewSystem();
      expect(result).toBeUndefined();
    });
  });

  describe('sendEmailWhenCreateOrResetPasswordUser', () => {
    it('should return if tenant not found', async () => {
      const result = await (
        service as any
      ).sendEmailWhenCreateOrResetPasswordUser(
        EmailTemplateEnum.CREATE_USER,
        mockTenantUserData,
        '123456zZ@',
      );
      expect(result).toBeUndefined();
    });

    it('should return if email template not found', async () => {
      await initMockTenant();

      const result = await (
        service as any
      ).sendEmailWhenCreateOrResetPasswordUser(
        EmailTemplateEnum.CREATE_USER,
        mockTenantUserData,
        '123456zZ@',
      );
      expect(result).toBeUndefined();
    });

    it('should log error if email sending fails', async () => {
      jest
        .spyOn(service['emailService'], 'sendEmail')
        .mockRejectedValueOnce(new Error('Email send failed'));

      const result = await (
        service as any
      ).sendEmailWhenCreateOrResetPasswordUser(
        EmailTemplateEnum.RESET_PASSWORD_USER,
        mockTenantUserData,
        '123456zZ@',
      );
      expect(result).toBeUndefined();
    });

    it('should send email successfully', async () => {
      const result = await (
        service as any
      ).sendEmailWhenCreateOrResetPasswordUser(
        EmailTemplateEnum.CREATE_USER,
        mockTenantUserData,
        '123456zZ@',
      );
      expect(result).toBeUndefined();
    });
  });

  describe('validateRole', () => {
    it('should throw error if roles are invalid', async () => {
      const rolesDecimal = mongoose.Types.Decimal128.fromString('1');
      await expect(
        (service as any).validateRole([rolesDecimal], false),
      ).rejects.toThrow(TENANT_USER_MESSAGE_KEYS.INVALID_ROLE);
    });

    it('should throw error if roles are admin and not root user', async () => {
      await initMockTenantRole({ key: 'administrator' });
      const rolesDecimal = mongoose.Types.Decimal128.fromString('2');
      await expect(
        (service as any).validateRole([rolesDecimal], true),
      ).rejects.toThrow(TENANT_USER_MESSAGE_KEYS.INVALID_ROLE);
    });
  });

  describe('handleRoles', () => {
    it('should return default roles if no roles provided', async () => {
      const result = await (service as any).handleRoles([]);
      expect(result).toEqual(mongoose.Types.Decimal128.fromString('0'));
    });
  });

  describe('handleTeam', () => {
    it('should return empty if no team provided', async () => {
      const result = await (service as any).handleTeam(null, null, []);
      expect(result).toBeUndefined();
    });
  });

  describe('verifyRBAC', () => {
    it('should throw error if user not found', async () => {
      const id = new ObjectId().toString();
      await expect(service.verifyRBAC(id, [])).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should return false if user has no roles', async () => {
      await Promise.all([initMockTenantRole(), initMockTenantUser()]);
      const id = mockTenantUserData._id.toString();
      const result = await service.verifyRBAC(id, []);
      expect(result).toBe(false);
    });

    it('should return true if user has permission', async () => {
      const id = mockTenantUserData._id.toString();
      const result = await service.verifyRBAC(id, [2]);
      expect(result).toBe(true);
    });
  });

  describe('verifyRoles', () => {
    it('should throw error if user not found', async () => {
      const id = new ObjectId().toString();
      await expect(service.verifyRoles(id, [])).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND,
      );
    });

    it('should return false if user has no roles', async () => {
      const id = mockTenantUserData._id.toString();
      const result = await service.verifyRoles(id, []);
      expect(result).toBe(false);
    });

    it('should return true if user has role', async () => {
      await Promise.all([
        initMockTenantRole({
          key: 'administrator',
          name: 'Administrator',
          decimal: mongoose.Types.Decimal128.fromString('8'),
          permissions: mongoose.Types.Decimal128.fromString('16'),
        }),
        initMockTenantUser({
          roles: mongoose.Types.Decimal128.fromString('8'),
        }),
      ]);

      const id = mockTenantUserData._id.toString();
      const result = await service.verifyRoles(id, ['administrator'] as any);
      expect(result).toBe(true);
    });
  });

  describe('existsOrThrow', () => {
    it('should throw error if user not found', async () => {
      const id = new ObjectId().toString();
      await expect(service.existsOrThrow([id])).rejects.toThrow(
        TENANT_USER_MESSAGE_KEYS.NOT_FOUND,
      );
    });
  });

  describe('getDisplayConfig', () => {
    it('should return default display config', async () => {
      const id = mockTenantUserData._id.toString();
      const result = await service.getDisplayConfig(id, 'inspectionOverview');
      expect(result).toBeDefined();
      expect(result).toMatchSchema(tenantUserTest.getDisplayConfigSchema);
    });

    it('should throw error if page is not found', async () => {
      const id = mockTenantUserData._id.toString();
      await expect(
        service.getDisplayConfig(id, 'pageNotFound'),
      ).rejects.toThrow(TENANT_USER_MESSAGE_KEYS.NOT_FOUND_DISPLAY_CONFIG);
    });
  });

  describe('updateDisplayConfig', () => {
    it('should update display config successfully', async () => {
      const id = mockTenantUserData._id.toString();
      const page = 'inspectionOverview';
      const updatedDisplayConfig = {
        field: 'title',
        isShown: false,
      };
      const result = await service.updateDisplayConfig(
        id,
        page,
        updatedDisplayConfig,
      );
      expect(result.find((c: any) => c.field === 'title').isShown).toBe(false);
      expect(result).toMatchSchema(tenantUserTest.getDisplayConfigSchema);
    });

    it('should throw error if page is not found', async () => {
      const id = mockTenantUserData._id.toString();
      const page = 'notExistPage';
      const updatedDisplayConfig = {
        field: 'title',
        isShown: false,
      };
      await expect(
        service.updateDisplayConfig(id, page, updatedDisplayConfig),
      ).rejects.toThrow(TENANT_USER_MESSAGE_KEYS.NOT_FOUND_DISPLAY_CONFIG);
    });

    it('should throw error if field is not mutable', async () => {
      const id = mockTenantUserData._id.toString();
      const page = 'inspectionOverview';
      const updatedDisplayConfig = {
        field: 'identifier',
        isShown: false,
      };
      await expect(
        service.updateDisplayConfig(id, page, updatedDisplayConfig),
      ).rejects.toThrow(TENANT_USER_MESSAGE_KEYS.DISPLAY_CONFIG_NOT_MUTABLE);
    });

    it('should not change config if field does not exist', async () => {
      const id = mockTenantUserData._id.toString();
      const page = 'inspectionOverview';
      const updatedDisplayConfig = {
        field: 'notExistField',
        isShown: false,
      };
      const result = await service.updateDisplayConfig(
        id,
        page,
        updatedDisplayConfig,
      );

      expect(
        result.find((c: any) => c.field === 'notExistField'),
      ).toBeUndefined();
    });
  });
});
