import { Decimal128, ObjectId } from 'mongodb';
import { z } from 'zod';

import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isActive: z.boolean(),
    key: z.string(),
    name: z.string(),
    decimal: z.instanceof(Decimal128),
    permissions: z.instanceof(Decimal128),
    tenant: z.instanceof(ObjectId),
  })
  .extend(baseModelTestSchema);

const findAllSchema = z.array(
  modelSchema.pick({
    _id: true,
    name: true,
    key: true,
  }),
);

export const tenantRoleTest = {
  modelSchema,
  findAllSchema,
};
