import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const tranformDataFunc = ({ data }: { data: any[]; context: any }) => {
  return data;
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('region');

  return sourceCollection.find().skip(skip).limit(limit);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'region',
      destinationCollectionName: 'regions',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
