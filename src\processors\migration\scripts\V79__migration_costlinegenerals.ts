import dayjs from 'dayjs';
import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import {
  calculate2PeriodsFutureGenerationDate,
  calculateNextFutureGenerationDate,
  getGreatestAgreementLinePeriod,
} from '~/modules/contract/contract.helper';
import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';
import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
} from '~/shared/enums/contract.enum';

const fileName = path.basename(__filename);

interface OldAggregatedContract {
  _id: Types.ObjectId;
  invoiceNoticePeriod: number;
  isGenerateInvoice: boolean;
  type: string;
  agreementLines: OldAggregatedAgreementLine[];
}

interface OldAggregatedAgreementLine {
  _id: Types.ObjectId;
  periodType: AgreementLinePeriodType;
  period?: AgreementLinePeriod;
  costLineGenerals: OldCostLineGeneral[];
}

interface OldCostLineGeneral {
  _id: Types.ObjectId;
  startDate: Date;
  endDate?: Date;
  quantity: number;
  description: string;
  amount: number;
  position: number;
  invoiceDate: null | Date;
  price?: number | null;
  agreementLine: Types.ObjectId;
  costType?: Types.ObjectId;
  unit?: Types.ObjectId;
  createdAt: Date;
  updatedAt: Date;
}

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return collection
    .aggregate()
    .match({
      _id: { $gt: nextId },
    })
    .sort({ _id: 1 })
    .limit(limit)
    .lookup({
      from: 'agreementline',
      as: 'agreementLines',
      let: { contractId: '$_id' },
      pipeline: [
        { $match: { $expr: { $eq: ['$rentingContract', '$$contractId'] } } },
        {
          $lookup: {
            from: 'costlinegeneral',
            as: 'costLineGenerals',
            localField: '_id',
            foreignField: 'agreementLine',
          },
        },
        {
          $addFields: {
            periodType: { $toLower: '$periodType' },
            period: { $toLower: '$period' },
          },
        },
      ],
    });
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const transformData = ({ data }: { data: OldAggregatedContract[] }) => {
      const now = dayjs().utc().startOf('day');
      return Promise.all(
        data.map(async (contract: OldAggregatedContract) => {
          const { isFuture, smallestStartDate, greatestPeriodType } =
            getGreatestAgreementLinePeriod(
              contract.agreementLines as any[],
              now,
            );

          let futureGenerationDate: Date | null | undefined;
          const canGenerateInvoice =
            ['Debtor', 'Service'].includes(contract.type) &&
            contract.isGenerateInvoice;
          if (canGenerateInvoice) {
            if (isFuture) {
              futureGenerationDate = calculate2PeriodsFutureGenerationDate({
                greatestPeriodType,
                startDate: smallestStartDate,
                now,
              });
            }
            futureGenerationDate = isFuture
              ? calculate2PeriodsFutureGenerationDate({
                  greatestPeriodType,
                  startDate: smallestStartDate,
                  now,
                })
              : undefined;
          }
          const noFutureGenerationDate =
            !futureGenerationDate || now.isSameOrAfter(futureGenerationDate);

          return {
            _id: contract._id,
            subDocs: contract.agreementLines.flatMap((agreementLine) =>
              agreementLine.costLineGenerals.map((costLineGeneral) => {
                const costLineGeneralStartDate = dayjs(
                  costLineGeneral.startDate ?? costLineGeneral.invoiceDate,
                )
                  .utc()
                  .startOf('day');
                const startDate = now.isSameOrAfter(costLineGeneralStartDate)
                  ? now
                  : costLineGeneralStartDate;

                if (canGenerateInvoice) {
                  futureGenerationDate = noFutureGenerationDate
                    ? calculateNextFutureGenerationDate({
                        period: agreementLine.period,
                        generatePeriod: contract.invoiceNoticePeriod ?? 1,
                        startDate,
                        now,
                      })
                    : futureGenerationDate;
                }

                return omitNull({
                  _id: costLineGeneral._id,
                  isDeleted: false,
                  position: costLineGeneral.position,
                  agreementLine: costLineGeneral.agreementLine,
                  description: costLineGeneral.description,
                  startDate:
                    costLineGeneral.startDate ?? costLineGeneral.invoiceDate,
                  endDate: costLineGeneral.endDate,
                  quantity: costLineGeneral.quantity,
                  price: costLineGeneral.price,
                  costType: costLineGeneral.costType,
                  unit: costLineGeneral.unit,
                  futureGenerationDate: futureGenerationDate,
                  createdAt: costLineGeneral.createdAt,
                  updatedAt: costLineGeneral.updatedAt,
                });
              }),
            ),
          };
        }),
      );
    };

    await migrationV2({
      context,
      sourceCollectionName: 'rentingcontract',
      destinationCollectionName: 'costlinegenerals',
      pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
      count: await context
        .sourceClient!.db()
        .collection('costlinegeneral')!
        .countDocuments(),
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
