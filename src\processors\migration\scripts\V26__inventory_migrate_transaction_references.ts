import { Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';

const fileName = path.basename(__filename);

const transformData = ({ data }: { data: any[] }) => {
  return Promise.all(
    data.map(
      async (transactionRef: { _id: Types.ObjectId; [key: string]: any }) =>
        omitNull({
          _id: transactionRef._id,
          referenceCollection: transactionRef.referenceCollection,
          referenceId: transactionRef.referenceId,
          isDeleted: false,
          createdAt: transactionRef.createdAt,
          updatedAt: transactionRef.updatedAt,
        }),
    ),
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migrationV2({
      context,
      sourceCollectionName: 'transactionreference',
      destinationCollectionName: 'transactionreference',
      tranformDataFunc: transformData,
      inventoryMode: true,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
