import { TeamModel } from '~/modules/team/team.model';

import { MigrationContext } from '../migration.service';

const up = async (context: MigrationContext) => {
  try {
    const teamCollection = context?.destinationClient?.db().collection('teams');

    // get last position
    const lastTeam = await teamCollection?.findOne(
      {},
      { sort: { position: -1 } },
    );

    let position = 100;
    if (lastTeam) {
      position = parseInt(lastTeam?.position || 0) + 1;
    }

    // create 2 new Car and Device teams
    const teams: TeamModel[] = [
      {
        name: 'Cars',
        description: 'Cars team',
        isActive: true,
        isDeleted: false,
        isEquipment: true,
        position: position,
        tenantUsers: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
      {
        name: 'Devices',
        description: 'Devices team',
        isActive: true,
        isDeleted: false,
        isEquipment: true,
        position: position + 1,
        tenantUsers: [],
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    await teamCollection?.insertMany(teams);
  } catch (error) {
    console.error('Error importing data:', error);
  }
};

export default up;
