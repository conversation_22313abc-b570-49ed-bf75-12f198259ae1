import { Module } from '@nestjs/common';

import { LoggerModule } from '~/processors/logger/logger.module';
import { ThirdPartyConnectorModule } from '~/processors/third-party-connector/third-party-connector.module';

import { IdentifierModule } from '../identifier/identifier.module';
import { InvoiceController } from './invoice.controller';
import { InvoiceService } from './invoice.service';

@Module({
  imports: [IdentifierModule, LoggerModule, ThirdPartyConnectorModule],
  controllers: [InvoiceController],
  providers: [InvoiceService],
})
export class InvoiceModule {}
