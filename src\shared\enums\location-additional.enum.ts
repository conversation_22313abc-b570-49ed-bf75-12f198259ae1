export enum LocationAdditionalType {
  FEATURE_AND_SUPPLIER = 'feature_and_supplier',
  CERTIFICATE_AND_CONTROL = 'certificate_and_control',
  GWE_AND_METER_READING = 'gwe_and_meter_reading',
}

export enum LocationAdditionalGroupName {
  //#region Feature and supplier
  GARBAGE = 'Garbage',
  INTERNET = 'Internet',
  KITCHEN_APPLIANCES = 'Kitchen appliances',
  WASHING_MACHINE_AND_DRYER = 'Washing machine & Dryer',
  UPHOLSTERY = 'Upholstery',
  FURNITURE = 'Furniture',
  GARDENS = 'Gardens',
  LOCKCARD_SYSTEM = 'Lock/card system',
  VENDING_MACHINES = 'Vending machines',
  CAMERA_SYSTEN = 'Camera system',
  SECURITY = 'Security',
  KEY_PLAN = 'Key plan',
  PEST_CONTROL = 'Pest control',
  LEGIONELLA_BEHEER = 'Legionella beheer',
  //#endregion
  //#region Certificate and control
  HEATING_VERSION = 'Heating version',
  FIRE_KILL_RESOURCES = 'Fire kill resources',
  FIRE_INSTALLATION = 'Fire installation',
  LEGIONELLA = 'Legionella',
  THERMOSTAT = 'Thermostat',
  EMERGENCY_LIGHTING = 'Emergency lighting',
  //#endregion
  //#region GWE and meter reading
  GAS = 'Gas',
  WATER = 'Water',
  ELEKTRA_1 = 'Elektra 1',
  ELEKTRA_2 = 'Elektra 2',
  //#endregion
  OTHERS = 'Others',
}

export enum LocationAdditionalGroupType {
  OWNER = 'owner',
  EEAC = 'eeac',
  CUSTOMER = 'customer',
  NONE = 'none',
}

export enum CertificateAndControlInspectionType {
  MAINTENANCE = 'Maintenance',
  INSPECTION = 'Inspection',
  CERTIFICATION = 'Certification',
  SAMPLING = 'Sampling',
}
