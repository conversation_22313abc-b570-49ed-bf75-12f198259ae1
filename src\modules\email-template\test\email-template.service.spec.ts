import { Test, TestingModule } from '@nestjs/testing';

import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockEmailTemplate,
  mockEmailTemplateData,
} from '~/test/mocks/emailtemplate.mock';

import { EmailTemplateModel } from '../email-template.model';
import { EmailTemplateService } from '../email-template.service';
import { emailTemplateTest } from './email-template.dto.test';

describe('EmailTemplateService', () => {
  let service: EmailTemplateService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        EmailTemplateService,
        ...testInjectModel([EmailTemplateModel]),
      ],
    }).compile();

    service = module.get(EmailTemplateService);

    // Init data
    await initMockEmailTemplate();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getEmailTemplateByName', () => {
    it('should return email template when name exists', async () => {
      const result = await service.getEmailTemplateByName(
        mockEmailTemplateData.name,
      );
      expect(result).toBeDefined();
      expect(result).toMatchSchema(emailTemplateTest.modelSchema);
    });

    it('should return null when name does not exist', async () => {
      const result = await service.getEmailTemplateByName('non_existing_name');
      expect(result).toBeNull();
    });
  });
});
