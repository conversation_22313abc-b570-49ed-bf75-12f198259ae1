import { BadRequestException, Injectable } from '@nestjs/common';

import { SyncHistoryService } from '~/modules/sync-history/sync-history.service';
import { ThirdPartyConnectorContext } from '~/processors/third-party-connector/strategies/third-party-connector.context';
import { SyncHistoryType } from '~/shared/enums/sync-history.enum';
import { ThirdPartyTypeEnum } from '~/shared/enums/third-party-type.enum';
import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery } from '~/utils';

import { CostTypeModel } from './costtype.model';
import { CostTypeQueryParamsDto } from './dtos/costtype-query-params.dto';

@Injectable()
export class CosttypeService {
  constructor(
    @InjectModel(CostTypeModel)
    private readonly costTypeModel: MongooseModel<CostTypeModel>,

    private readonly thirdPartyContext: ThirdPartyConnectorContext,

    private readonly syncHistoryService: SyncHistoryService,
  ) {}

  async findAll(payload: CostTypeQueryParamsDto) {
    const { query, options } = buildQuery(payload, ['name']);

    const finalOptions =
      payload.pageSize === -1 ? { ...options, pagination: false } : options;

    return await this.costTypeModel.paginate(query, finalOptions);
  }

  async syncFrom3rdParty(payload: {
    [key: string]: any;
    type: ThirdPartyTypeEnum;
  }) {
    if (await this.syncHistoryService.isPending(SyncHistoryType.COST_TYPE)) {
      throw new BadRequestException(
        'Sync is already in progress by another user',
      );
    }

    const syncHistory = await this.syncHistoryService.createNewPending(
      SyncHistoryType.COST_TYPE,
      payload.actionType,
    );

    try {
      const costTypes = await this.thirdPartyContext.getCostTypes(payload);

      const bulkOperations: any[] = costTypes.map((costType) => ({
        updateOne: {
          filter: { itemCode: costType.itemCode },
          update: { $set: costType },
          upsert: true,
        },
      }));

      const keptCostTypeCodes = costTypes.map((costType) => costType.itemCode);
      bulkOperations.push({
        updateMany: {
          filter: {
            itemCode: { $nin: keptCostTypeCodes },
            $or: [{ isActive: true }, { isDeleted: false }],
          },
          update: { $set: { isActive: false, isDeleted: true } },
        },
      });

      const bulkWriteResult =
        await this.costTypeModel.bulkWrite(bulkOperations);
      await this.syncHistoryService.updateToSuccess(syncHistory._id!);

      return bulkWriteResult;
    } catch (ex: any) {
      await this.syncHistoryService.updateToFailed(
        syncHistory._id!,
        ex.message,
      );

      throw ex;
    }
  }
}
