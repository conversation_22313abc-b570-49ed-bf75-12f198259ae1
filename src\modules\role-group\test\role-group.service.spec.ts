import { Test } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { TenantRoleModel } from '~/modules/tenant-role/tenant-role.model';
import { ROLE_GROUP_MESSAGES_KEYS } from '~/shared/message-keys/role-group.message-keys';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockRoleGroup,
  mockRoleGroupData,
} from '~/test/mocks/rolegroup.mock';
import { initMockTenantRole } from '~/test/mocks/tenantrole.mock';
import { QueryParams } from '~/utils';

import {
  RoleGroupCreateBodyDto,
  RoleGroupDeleteDto,
  RoleGroupUpdateBodyDto,
} from '../dto/role-group.dto';
import { RoleGroupModel } from '../role-group.model';
import { RoleGroupService } from '../role-group.service';
import { roleGroupTest } from './role-group.dto.test';

describe('RoleGroupService', () => {
  let service: RoleGroupService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        RoleGroupService,
        ...testInjectModel([RoleGroupModel, TenantRoleModel]),
      ],
    }).compile();

    service = module.get(RoleGroupService);

    // Init data
    await initMockTenantRole();
    await initMockRoleGroup();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('findAll', () => {
    const payload: QueryParams = {
      pageIndex: 1,
      pageSize: -1,
      sortBy: 'name',
      sortDir: 'asc',
    };
    it('should return all role groups', async () => {
      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs.length).toBeGreaterThanOrEqual(0);
      expect(result.docs).toMatchSchema(roleGroupTest.findAllSchema);
    });

    it('should return paginated role groups without deleted role group', async () => {
      const deletedRoleGroupId = new ObjectId();
      await initMockRoleGroup({
        _id: deletedRoleGroupId,
        isDeleted: true,
        name: 'Deleted Role Group',
      });

      const result = await service.findAll(payload);
      expect(result).toBeDefined();
      expect(result.docs.length).toBeGreaterThanOrEqual(0);
      expect(
        result.docs.find((doc) => doc._id === deletedRoleGroupId),
      ).toBeUndefined();
      expect(result.docs).toMatchSchema(roleGroupTest.findAllSchema);
    });
  });

  describe('create', () => {
    it('should create success role group', async () => {
      const data: RoleGroupCreateBodyDto = {
        name: 'Test Role Group',
        description: 'Test Description',
        roles: [],
      };
      const result = await service.create(data);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(roleGroupTest.modelSchema);
    });

    it('should throw error when creating role group with existing name', async () => {
      const data: RoleGroupCreateBodyDto = {
        name: mockRoleGroupData.name, // Using existing name
        description: 'Test Description',
        roles: [],
      };
      await expect(service.create(data)).rejects.toThrow(
        ROLE_GROUP_MESSAGES_KEYS.NAME_IS_EXIST,
      );
    });

    it('should throw error when creating role is not exist', async () => {
      const data: RoleGroupCreateBodyDto = {
        name: 'Test Role Group 1',
        description: 'Test Description 1',
        roles: [new ObjectId().toString()], // Using a non-existing role ID
      };
      await expect(service.create(data)).rejects.toThrow(
        ROLE_GROUP_MESSAGES_KEYS.SOME_ROLE_NOT_EXIST,
      );
    });
  });

  describe('findOne', () => {
    it('should return a role group by id', async () => {
      const id = mockRoleGroupData._id.toString();
      const result = await service.findOne(id);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockRoleGroupData._id);
    });
  });

  describe('update', () => {
    let payload: RoleGroupUpdateBodyDto = {
      id: mockRoleGroupData._id.toString(),
      name: 'Updated Role Group',
      description: 'Updated Description',
      isActive: false,
      roles: [],
    };
    it('should update a role group', async () => {
      const roleGroup = await service.findOne(payload.id);
      console.log('roleGroup', roleGroup);
      const result = await service.update(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockRoleGroupData._id);
    });

    it('should throw error when role group not found', async () => {
      const newPayload = {
        ...payload,
        id: new ObjectId().toString(), // Non-existing ID
      };
      await expect(service.update(newPayload)).rejects.toThrow(
        ROLE_GROUP_MESSAGES_KEYS.NOT_FOUND,
      );
    });

    it('should throw error when updating role group with existing name', async () => {
      await initMockRoleGroup({
        _id: new ObjectId(),
        name: 'Existing Role Group',
      });
      const newPayload = {
        ...payload,
        name: 'Existing Role Group',
      };
      await expect(service.update(newPayload)).rejects.toThrow(
        ROLE_GROUP_MESSAGES_KEYS.NAME_IS_EXIST,
      );
    });

    it('should throw error when updating role group with non-existing role', async () => {
      payload = {
        ...payload,
        roles: [new ObjectId().toString()],
      };
      await expect(service.update(payload)).rejects.toThrow(
        ROLE_GROUP_MESSAGES_KEYS.SOME_ROLE_NOT_EXIST,
      );
    });
  });

  describe('delete', () => {
    const payload: RoleGroupDeleteDto = {
      id: mockRoleGroupData._id.toString(),
    };
    it('should delete a role group', async () => {
      const payload: RoleGroupDeleteDto = {
        id: mockRoleGroupData._id.toString(),
      };
      const result = await service.delete(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockRoleGroupData._id);
    });

    it('should throw error when deleting a non-existing role group', async () => {
      payload.id = new ObjectId().toString(); // Non-existing ID
      await expect(service.delete(payload)).rejects.toThrow(
        ROLE_GROUP_MESSAGES_KEYS.NOT_FOUND,
      );
    });
  });
});
