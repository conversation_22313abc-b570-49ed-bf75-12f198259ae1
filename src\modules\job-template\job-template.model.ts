import {
  DocumentType,
  index,
  modelOptions,
  plugin,
  prop,
  Ref,
  Severity,
} from '@typegoose/typegoose';
import { nanoid } from 'nanoid';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { JobTemplateTypeEnum } from '~/shared/enums/job-template.enum';
import { BaseModel } from '~/shared/models/base.model';

import { UnitDocument, UnitModel } from '../unit/unit.model';

export type JobTemplateDocument = DocumentType<JobTemplateModel>;

export type JobTemplatePoint = {
  _id?: string;
  description: string;
  position: number;
};

@modelOptions({
  options: { customName: 'JobTemplate', allowMixed: Severity.ALLOW },
})
@index({ name: 1 })
@index({ unit: 1 })
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class JobTemplateModel extends BaseModel {
  @prop({ enum: JobTemplateTypeEnum, default: JobTemplateTypeEnum.GENERAL })
  type!: JobTemplateTypeEnum;

  @prop({ required: true, trim: true, maxlength: 256 })
  name!: string;

  @prop({
    default: [],
    set: (points: JobTemplatePoint[]) =>
      points.map((point) => ({
        _id: point._id ? point._id : nanoid(),
        ...point,
      })),
  })
  points!: JobTemplatePoint[];

  @prop({ ref: () => UnitModel })
  unit!: Ref<UnitDocument>;
}
