import _ from 'lodash';
import mongoose, { ObjectId } from 'mongoose';
import { nanoid } from 'nanoid';
import * as path from 'path';

import migration from '../helpers/merge-data';
import MyWritable from '../helpers/writable';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldInspectionTemplate {
  _id: string;
  itemDescription: string;
  position: number;
  unitInspection: any;
  createdDate: Date;
  createdBy: ObjectId;
}

const InspectionTemplatePipeLineAggregate = (skip: number, limit: number) => {
  // get all document file with upload file lookup to upload_file collection by filename
  return [
    { $skip: skip },
    { $limit: limit },
    {
      $lookup: {
        from: 'unit',
        let: { unitId: '$unitInspection' },
        as: 'unitInspection',
        pipeline: [
          {
            $match: {
              $expr: {
                $eq: ['$_id', '$$unitId'],
              },
            },
          },
          {
            $project: {
              name: 1,
            },
          },
        ],
      },
    },
    {
      $unwind: {
        path: '$unitInspection',
        preserveNullAndEmptyArrays: true,
      },
    },
  ];
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('inspectiontemplate');

  const pipeline = InspectionTemplatePipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const tranformDataFunc = async ({
  data,
  context,
}: {
  data: OldInspectionTemplate[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  const datas = await Promise.all(
    data.map(async (item) => {
      return {
        _id: nanoid(),
        type: 'inspection',
        name: `INSPECTION POINTS FOR ${item.unitInspection ? item.unitInspection.name.toUpperCase() : ''}`,
        description: item.itemDescription,
        position: item.position,
        unit: item.unitInspection ? item.unitInspection?._id.toString() : null,
      };
    }),
  );
  const filterDatas = datas.filter((item) => item.unit);
  const groupedDatas = _.groupBy(filterDatas, 'unit');
  const results: any[] = [];
  for (const unitId in groupedDatas) {
    if (unitId) {
      const unitData = groupedDatas[unitId];
      const name = unitData[0].name;
      const sourceCursor = await context
        .destinationClient!.db()
        .collection('jobtemplates')
        .find({ unit: new mongoose.Types.ObjectId(unitId) });

      const writable = new MyWritable({ objectMode: true });
      await new Promise((resolve, reject) => {
        sourceCursor
          .stream()
          .pipe(writable)
          .on('finish', resolve)
          .on('error', reject);
      });

      const jobTemplate = writable.data[0];
      if (jobTemplate) {
        const result = {
          _id: jobTemplate._id,
          type: jobTemplate.type,
          name: name,
          points: [
            ...jobTemplate.points,
            ...unitData.map((item) => {
              return {
                _id: item._id,
                description: item.description,
                position: item.position ?? 0,
              };
            }),
          ],
          unit: jobTemplate.unit,
          isDeleted: jobTemplate.isDeleted,
          createdAt: jobTemplate.createdAt,
          updatedAt: jobTemplate.updatedAt,
        };
        results.push(result);
      } else {
        const result = {
          _id: new mongoose.Types.ObjectId(),
          type: 'inspection',
          name: name,
          points: unitData
            ? unitData.map((item) => {
                return {
                  _id: item._id,
                  description: item.description,
                  position: item.position ?? 0,
                };
              })
            : [],
          unit: new mongoose.Types.ObjectId(unitId),
          isDeleted: false,
          createdAt: new Date(),
          updatedAt: new Date(),
        };
        results.push(result);
      }
    }
  }
  return results;
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'inspectiontemplate',
      destinationCollectionName: 'jobtemplates',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
