import { getModelForClass } from '@typegoose/typegoose';
import { Decimal128, ObjectId } from 'mongodb';
import { z } from 'zod';

import { GenericDisplayMapping } from '~/modules/tenant-user/dtos/tenant-user.util';
import { TenantUserModel } from '~/modules/tenant-user/tenant-user.model';
import { tenantUserTest } from '~/modules/tenant-user/test/tenant-user.dto.test';
import {
  Gender,
  Language,
  ThisWeekDefaultDisplay,
} from '~/shared/enums/tenant-user.enum';
import { WorkingDays } from '~/shared/enums/working-days.enum';

import { mockTeamData } from './team.mock';
import { mockTenantData } from './tenant.mock';

const tenantUserModel = getModelForClass(TenantUserModel);
type tenantUserType = z.infer<typeof tenantUserTest.modelSchema>;

export const mockTenantUserData = {
  _id: new ObjectId(),
  isDeleted: false,
  isActive: true,
  isRoot: false,
  displayName: '<PERSON>',
  firstName: '<PERSON>',
  lastName: 'Do<PERSON>',
  username: 'joyrode',
  password: '123456zZ@',
  email: '<EMAIL>',
  gender: Gender.MALE,
  language: Language.EN,
  phone1: '123456789',
  phone2: '',
  roles: Decimal128.fromString('1099444518910'),
  team: mockTeamData._id,
  position: 0,
  oddWeeks: Object.values(WorkingDays),
  evenWeeks: Object.values(WorkingDays),
  thisWeekDefaultDisplay: ThisWeekDefaultDisplay.SHOPPING_LIST,
  lastChangePasswordAt: new Date(),
  createdAt: new Date(),
  updatedAt: new Date(),
  columnDisplayConfigs: {
    inspectionOverview: GenericDisplayMapping,
    maintenanceOverview: GenericDisplayMapping,
    cleaningOverview: GenericDisplayMapping,
  },
  saveToPhotos: true,
  token: new ObjectId(),
  tenant: mockTenantData._id,
};

export async function initMockTenantUser(doc?: Partial<tenantUserType>) {
  const { _id, ...rest } = { ...mockTenantUserData, ...doc };
  await tenantUserModel.replaceOne({ _id }, rest, { upsert: true });
}
