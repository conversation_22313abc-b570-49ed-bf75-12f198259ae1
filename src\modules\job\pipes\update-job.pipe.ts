import {
  ArgumentMetadata,
  BadRequestException,
  Injectable,
} from '@nestjs/common';
import { ZodValidationPipe } from 'nestjs-zod';

import { JobStatusEnum } from '~/shared/enums/job.enum';

@Injectable()
export class UpdateZodValidationPipe extends ZodValidationPipe {
  async transform(value: any, metadata: ArgumentMetadata) {
    // Select the appropriate schema based on the type in the request body
    let schema;

    const nextStatus = value.nextStatus;

    const { platform } = value;

    if (platform === 'mb') {
      if (
        ![JobStatusEnum.IN_PROGRESS, JobStatusEnum.READY].includes(nextStatus)
      ) {
        throw new BadRequestException('Invalid job status flow');
      }
    } else {
      if (![JobStatusEnum.COMPLETE].includes(nextStatus)) {
        throw new BadRequestException('Invalid job status flow');
      }
    }

    // Call the parent class's transform() method with the selected schema
    return super.transform(value, { ...metadata, metatype: schema });
  }
}
