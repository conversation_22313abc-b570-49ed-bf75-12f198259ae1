import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

const defaultType = 'gwe_and_meter_reading';

interface OldLocationAdditional {
  _id: string;
  ean_code: string;
  contract_number: string;
  position: number;
  group_name: string;
  smart_meter: boolean;
  location: string;
  record_logs: any;
  grouping_id: string;
  contact: string;
  rentingContract: string;
  createdAt: Date;
  updatedAt: Date;
}

const LocationAdditionalPipeLineAggregate = (skip: number, limit: number) => {
  return [{ $skip: skip }, { $limit: limit }];
};

const transformDataFunc = ({
  data,
  context,
}: {
  data: OldLocationAdditional[];
  context: any;
}) => {
  return Promise.all(
    data.map(async (item) => {
      const { group_name, grouping_id, contact } = item;

      const transformedItem = {
        _id: item._id,
        type: defaultType,
        position: item.position,
        contact: contact ? contact : grouping_id,
        meterNumber: item.ean_code,
        contractNumber: item.contract_number,
        smartMeter: item.smart_meter,
        recordLogs: item.record_logs,
        contract: item.rentingContract,
        location: item.location,
        createdAt: item.createdAt || new Date(),
        updatedAt: item.updatedAt || new Date(),
        isDeleted: false,
      };

      if (group_name) {
        const foundGroupName = await context
          .destinationClient!.db()
          .collection('locationadditionalgroupnames')
          .findOne({ key: group_name, type: defaultType });

        if (foundGroupName) {
          return {
            ...transformedItem,
            groupName: foundGroupName._id,
          };
        }
      } else {
        return transformedItem;
      }
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('location_additional_gweandmeterreadings');

  const pipeline = LocationAdditionalPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'location_additional_gweandmeterreadings',
      destinationCollectionName: 'locationadditionals',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: transformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
