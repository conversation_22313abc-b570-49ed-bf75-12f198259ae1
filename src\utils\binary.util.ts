import { mongoose } from '@typegoose/typegoose';

export const sumDecimal = (
  decimals: Array<mongoose.Types.Decimal128>,
): mongoose.Types.Decimal128 => {
  // Conver to BigInt to avoid floating point errors
  const sum = decimals.reduce((acc, curr) => {
    return acc + parseDecimalToBigInt(curr);
  }, BigInt(0));

  return parseBigInttoDecimal(sum);
};

export const parseDecimalToBigInt = (decimal: mongoose.Types.Decimal128) => {
  return BigInt(decimal.toString());
};

export const parseBigInttoDecimal = (bigInt: bigint) => {
  return mongoose.Types.Decimal128.fromString(bigInt.toString());
};

export const splitDecimalSum = (n: bigint) => {
  const result = [] as bigint[];
  let power = BigInt(0);

  while (n > BigInt(0)) {
    if (n % BigInt(2) === BigInt(1)) {
      result.push(BigInt(2) ** power);
    }
    n = n / BigInt(2);
    power++;
  }

  return result;
};

export function decimalToBitPosition(decimal: mongoose.Types.Decimal128) {
  let bigInt = parseDecimalToBigInt(decimal);

  if (bigInt === 0n) return -1;

  let position = 0n;
  while (bigInt > 1n) {
    bigInt = bigInt >> 1n;
    position++;
  }

  return Number(position);
}

export function decimalToBitMask(decimal: mongoose.Types.Decimal128) {
  const bigInt = parseDecimalToBigInt(decimal);

  return bigInt.toString();
}
