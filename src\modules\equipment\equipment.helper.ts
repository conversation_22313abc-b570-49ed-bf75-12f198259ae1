import dayjs from 'dayjs';

import { AvailableEquipmentQueryParamsDto } from '~/modules/equipment/equipment.dto';
import { EquipmentEnum } from '~/shared/enums/equipment.enum';
import { JobStatusEnum } from '~/shared/enums/job.enum';
import { buildQuery } from '~/utils';

export const buildFindAvailableEquipmentsAggregation = (
  query: AvailableEquipmentQueryParamsDto,
) => {
  const { startDate, endDate, equipmentStatus, types } = query;
  const startDateValue = dayjs(startDate).utc().toDate();
  const endDateValue = dayjs(endDate).utc().toDate();
  const jobStatuses = [
    JobStatusEnum.OPEN,
    JobStatusEnum.IN_PROGRESS,
    JobStatusEnum.READY,
  ];

  const stages: any[] = [];
  stages.push(
    {
      $match: {
        isActive: true,
        type: { $in: types },
      },
    },
    {
      $lookup: {
        from: 'jobs',
        localField: '_id',
        foreignField: 'equipments',
        as: 'jobs',
        pipeline: [
          {
            $match: {
              isDeleted: false,
              plannedDate: { $gte: startDateValue, $lte: endDateValue },
              status: { $in: jobStatuses },
            },
          },
          { $limit: 1 },
          { $project: { _id: 1 } },
        ],
      },
    },
  );

  if (types.includes(EquipmentEnum.CAR)) {
    stages.push({
      $lookup: {
        from: 'tasks',
        localField: '_id',
        foreignField: 'cars',
        as: 'tasksUsingCar',
        pipeline: [
          {
            $match: {
              isDeleted: false,
              startDate: { $gte: startDateValue },
              endDate: { $lte: endDateValue },
            },
          },
          { $limit: 1 },
          { $project: { _id: 1 } },
        ],
      },
    });
  }

  if (types.includes(EquipmentEnum.DEVICE)) {
    stages.push({
      $lookup: {
        from: 'tasks',
        localField: '_id',
        foreignField: 'devices',
        as: 'tasksUsingDevice',
        pipeline: [
          {
            $match: {
              isDeleted: false,
              startDate: { $gte: startDateValue },
              endDate: { $lte: endDateValue },
            },
          },
          { $limit: 1 },
          { $project: { _id: 1 } },
        ],
      },
    });
  }

  if (equipmentStatus === 'available') {
    stages.push({
      $match: {
        'jobs.0': { $exists: false },
        'tasksUsingCar.0': { $exists: false },
        'tasksUsingDevice.0': { $exists: false },
      },
    });
  } else {
    stages.push({
      $match: {
        $or: [
          { 'jobs.0': { $exists: true } },
          { 'tasksUsingCar.0': { $exists: true } },
          { 'tasksUsingDevice.0': { $exists: true } },
        ],
      },
    });
  }

  stages.push({ $project: { description: 1, type: 1, isActive: 1 } });
  const { options } = buildQuery(query);
  return { stages, options };
};
