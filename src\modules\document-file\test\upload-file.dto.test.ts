import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { UploadFileProviderEnum } from '~/shared/enums/upload-file-provider.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    originalFilename: z.string(),
    filenameOnStorage: z.string(),
    folderPathOnStorage: z.string(),
    extension: z.string(),
    mimeType: z.string(),
    size: z.number(),
    publicUrl: z.string(),
    provider: z.nativeEnum(UploadFileProviderEnum),
    documentFile: z.instanceof(ObjectId),
    locationFile: z.instanceof(ObjectId).optional(),
  })
  .extend(baseModelTestSchema);

export const uploadFileTest = {
  modelSchema,
};
