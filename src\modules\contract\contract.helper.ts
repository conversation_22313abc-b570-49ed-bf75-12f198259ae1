import dayjs, { Dayjs } from 'dayjs';
import { omit } from 'lodash';

import { CreateAgreementLineDto } from '~/modules/agreementline/dtos/create-agreementline.dto';
import {
  AgreementLinePeriod,
  AgreementLinePeriodType,
  CostLineStatus,
} from '~/shared/enums/contract.enum';
import { DateLike } from '~/utils/date.util';

const DAYS_IN_WEEK = 7;
const DAYS_IN_FOUR_WEEKS = 28;
const DAYS_IN_YEAR = 365;
const MONTHS_IN_YEAR = 12;
export const AVG_WEEKS_IN_MONTH = 4.333333;
const PERIOD_RANKS = [
  AgreementLinePeriod.MONTHLY,
  AgreementLinePeriod.FOUR_WEEKLY,
  AgreementLinePeriod.WEEKLY,
];

export const FOUR_WEEKLY_END_WEEK_NUMBERS = [
  4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 53,
] as const;

export interface Period {
  start: Date;
  end: Date;
}

export type CostLineGeneralUpdateField =
  | 'price'
  | 'startDate'
  | 'endDate'
  | 'generatePeriod';

/**
 * Generates an array of periods based on the provided start date, end date, period type, and period.
 *
 * @param {Object} params - The parameters for generating periods.
 * @param {Date} params.startDate - The start date of the period generation.
 * @param {Date | null} params.endDate - The end date of the period generation. If null, periods will be generated indefinitely.
 * @param {AgreementLinePeriodType} params.periodType - The type of period (e.g., ONE_TIME, RECURRING).
 * @param {AgreementLinePeriod} params.period - The period configuration.
 * @param {number} params.generatePeriod - The number of periods to generate.
 * @returns {Period[]} An array of generated periods, each with a start and end date.
 */
export function generatePeriods({
  startDate,
  endDate,
  periodType,
  period,
  generatePeriod,
  now,
}: {
  startDate: DateLike;
  endDate: DateLike | null | undefined;
  periodType: AgreementLinePeriodType;
  period: AgreementLinePeriod;
  generatePeriod: number;
  now?: Dayjs;
}): Period[] {
  now = now ?? dayjs().utc().startOf('day');
  const start = dayjs(startDate).utc().startOf('day');
  const end = endDate ? dayjs(endDate).utc().endOf('day') : null;
  const periods: Period[] = [];

  let currentStart = start;
  const periodAmount =
    periodType === AgreementLinePeriodType.ONE_TIME ? 0 : generatePeriod;

  if (periodType === AgreementLinePeriodType.ONE_TIME) {
    periods.push({
      start: currentStart.toDate(),
      end: end?.toDate() || start.endOf('day').toDate(),
    });

    return periods;
  }

  // Calculate the end of the first period
  // placed outside the loop because we want to
  // correctly get the start iso week for the first period
  // in the case of four-weekly periods (isFirstPeriod = true)
  let periodEnd = calculatePeriodEnd({
    period,
    maxEnd: end,
    currentStart,
    isFirstPeriod: true,
  });

  for (let i = 0; i <= periodAmount; !currentStart.isBefore(now) && i++) {
    // Placing push at the start of the loop because
    // we always want to include the first period (outside the loop)
    periods.push({
      start: currentStart.toDate(),
      end: periodEnd?.toDate(),
    });

    // If we've reached the endDate, stop generating periods
    if (end && periodEnd?.isSame(end)) {
      break;
    }

    // Set the start of the next period
    currentStart = periodEnd?.startOf('day').add(1, 'day');

    periodEnd = calculatePeriodEnd({ period, maxEnd: end, currentStart });
  }

  return periods;
}

export function generateRangeDatesMatchConditions(
  djsStartDate?: Dayjs | null,
  djsEndDate?: Dayjs | null,
) {
  const matchConditions = [] as any[];

  if (djsStartDate && djsEndDate) {
    matchConditions.push({
      $and: [
        {
          startDate: {
            $gte: djsStartDate.toDate(),
          },
        },
        {
          startDate: {
            $lte: djsEndDate.toDate(),
          },
        },
      ],
    });
  } else if (djsStartDate) {
    matchConditions.push({
      startDate: {
        $gte: djsStartDate.toDate(),
      },
    });
  } else if (djsEndDate) {
    matchConditions.push({
      startDate: {
        $lte: djsEndDate.toDate(),
      },
    });
  }

  return matchConditions;
}

/**
 * Calculate total price for the period.
 * Formula for calculating total price:
 * - {@link https://infodation.atlassian.net/wiki/x/MIACxQ | Formula confluence}
 *
 * @param weeklyPrice Price per week
 * @param periodType Period type
 * @param period Start and end date of the period
 * @param isNew Should contract use new calculation
 * @returns Total price for the period
 */
export function calculateTotalPrice(
  weeklyPrice: number,
  periodType: AgreementLinePeriod,
  period: Period,
  isNew: boolean = true,
): number {
  const dailyPrice = weeklyPrice / DAYS_IN_WEEK;
  const totalDaysInPeriod = dayjs(period.end).diff(period.start, 'days') + 1; // +1 to include the end date

  const calculateDailyTotalPrice = () => dailyPrice * totalDaysInPeriod;

  switch (periodType) {
    case AgreementLinePeriod.WEEKLY: {
      if (totalDaysInPeriod < DAYS_IN_WEEK) {
        return calculateDailyTotalPrice();
      }
      return weeklyPrice;
    }
    case AgreementLinePeriod.FOUR_WEEKLY: {
      if (totalDaysInPeriod < DAYS_IN_FOUR_WEEKS) {
        return calculateDailyTotalPrice();
      }
      return weeklyPrice * 4;
    }
    case AgreementLinePeriod.MONTHLY: {
      const daysInMonth = dayjs(period.start).daysInMonth();
      if (totalDaysInPeriod < daysInMonth) {
        return calculateDailyTotalPrice();
      }

      if (!isNew) {
        return weeklyPrice * AVG_WEEKS_IN_MONTH;
      }

      return (dailyPrice * DAYS_IN_YEAR) / MONTHS_IN_YEAR;
    }
    default:
      return weeklyPrice;
  }
}

// Helper functions

/**
 * Finds the nearest ISO week number from a predefined list of four-weekly week numbers
 * that is greater than or equal to the ISO week number of the given date.
 *
 * @param {Dayjs} date - The date for which to find the nearest ISO week number.
 * @returns {number | undefined} The nearest ISO week number that is greater than or equal to the input date's ISO week number,
 * or undefined if no such week number is found.
 */
function findNearestIsoWeekFourWeekly(date: Dayjs): number | undefined {
  const inputIsoWeek = date.isoWeek();
  return FOUR_WEEKLY_END_WEEK_NUMBERS.find(
    (isoWeek) => isoWeek >= inputIsoWeek,
  );
}

/**
 * Calculates the end period of a four-week cycle starting from a given date.
 * The end period is the end of the week that is four weeks after the start date.
 *
 * @param start - The start date of the cycle as a Dayjs object.
 * @returns The end date of the four-week cycle as a Dayjs object.
 */
function calculateFourWeeklyEndPeriod(start: Dayjs): Dayjs {
  const end = start.endOf('isoWeek');

  if (end.isoWeek() === 53) {
    return end;
  } else {
    return end.add(3, 'week');
  }
}

/**
 * Calculates the end date of a period based on the given parameters.
 * The period end is calculated based on the period type and the current start date.
 *
 * @param {Object} params - The parameters for calculating the period end.
 * @param {AgreementLinePeriod} params.period - The type of period (e.g., WEEKLY, FOUR_WEEKLY, MONTHLY).
 * @param {Dayjs | null} params.maxEnd - The maximum end date allowed. If the calculated period end exceeds this date, maxEnd will be used as the period end.
 * @param {Dayjs} params.currentStart - The start date of the current period.
 * @param {boolean} [params.isFirstPeriod=false] - A flag indicating if this is the first period. This affects the calculation for FOUR_WEEKLY periods.
 * @returns {Dayjs} The calculated end date of the period.
 */
function calculatePeriodEnd({
  period,
  maxEnd,
  currentStart,
  isFirstPeriod = false,
}: {
  period: AgreementLinePeriod;
  maxEnd?: Dayjs | null;
  currentStart: Dayjs;
  isFirstPeriod?: boolean;
}): Dayjs {
  let periodEnd: Dayjs;

  switch (period) {
    case AgreementLinePeriod.WEEKLY:
      periodEnd = currentStart.endOf('isoWeek');
      break;
    case AgreementLinePeriod.FOUR_WEEKLY:
      // If it's the first period, set the periodEnd to
      // the end of the nearest week (following 4-weekly week numbers)
      if (isFirstPeriod) {
        const nearestWeek = findNearestIsoWeekFourWeekly(currentStart);
        periodEnd = currentStart.isoWeek(nearestWeek!).endOf('isoWeek');
      } else {
        periodEnd = calculateFourWeeklyEndPeriod(currentStart);
      }
      break;
    case AgreementLinePeriod.MONTHLY:
      periodEnd = currentStart.endOf('month');
      break;
  }

  // If endDate is not null and periodEnd exceeds it, use endDate as periodEnd
  if (maxEnd && periodEnd.isAfter(maxEnd)) {
    periodEnd = maxEnd;
  }

  return periodEnd;
}

/**
 * Determines the greatest agreement line period and the smallest start date from the provided agreement lines.
 *
 * @param {CreateAgreementLineDto[]} agreementLines - The array of agreement lines.
 * @param {Dayjs} now - The current date.
 * @returns {Object} An object containing:
 *  - isFuture: A boolean indicating if the smallest start date is in the future.
 *  - smallestStartDate: The smallest start date among the agreement lines.
 *  - greatestPeriodType: The greatest period type among the agreement lines.
 */
export function getGreatestAgreementLinePeriod(
  agreementLines: CreateAgreementLineDto[],
  now: Dayjs,
): {
  isFuture: boolean;
  smallestStartDate: Dayjs;
  greatestPeriodType?: AgreementLinePeriod;
} {
  // Find the smallest startDate in agreementLines.costLineGenerals
  const smallestStartDate = dayjs(
    Math.min(
      ...agreementLines
        .map((line) => line.costLineGenerals)
        .flat()
        .map((costLine) => dayjs(costLine.startDate).valueOf()),
    ),
  )
    .utc()
    .startOf('day');

  if (smallestStartDate.isSameOrBefore(now)) {
    return { isFuture: false, smallestStartDate };
  }

  let greatestPeriodType: AgreementLinePeriod;
  const isAllOneTime = agreementLines.every(
    ({ periodType }) => periodType === AgreementLinePeriodType.ONE_TIME,
  );

  if (isAllOneTime) {
    greatestPeriodType = AgreementLinePeriod.MONTHLY;
  } else {
    const sortedPeriodTypes = agreementLines
      .map(({ period }) => period ?? AgreementLinePeriod.WEEKLY)
      .sort((a, b) => PERIOD_RANKS.indexOf(a) - PERIOD_RANKS.indexOf(b));

    greatestPeriodType = sortedPeriodTypes[0];
  }

  return { isFuture: true, smallestStartDate, greatestPeriodType };
}

/**
 * Calculates the future generation date based on the greatest period type, start date, and current date.
 *
 * @param {Object} params - The parameters for calculating the future generation date.
 * @param {AgreementLinePeriod} [params.greatestPeriodType] - The greatest period type (e.g., WEEKLY, FOUR_WEEKLY, MONTHLY).
 * @param {Dayjs} params.startDate - The start date for the calculation.
 * @param {Dayjs} params.now - The current date.
 * @returns {Date} The calculated future generation date.
 */
export function calculate2PeriodsFutureGenerationDate({
  greatestPeriodType,
  startDate,
  now,
}: {
  greatestPeriodType?: AgreementLinePeriod;
  startDate: Dayjs;
  now: Dayjs;
}): Date {
  let generateAt: Dayjs;

  switch (greatestPeriodType) {
    case AgreementLinePeriod.WEEKLY:
      generateAt = startDate.subtract(2, 'week');
      break;
    case AgreementLinePeriod.FOUR_WEEKLY:
      generateAt = startDate.subtract(8, 'week');
      break;
    case AgreementLinePeriod.MONTHLY:
      generateAt = startDate.subtract(2, 'month');
      break;
    default:
      generateAt = now;
  }

  return generateAt.toDate();
}

export function calculateNextFutureGenerationDate({
  period,
  generatePeriod,
  startDate,
  endDate,
  now,
}: {
  period?: AgreementLinePeriod;
  generatePeriod: number;
  startDate: Dayjs;
  endDate?: Dayjs;
  now: Dayjs;
}): Date | null {
  if (!period) {
    return null;
  }

  startDate = startDate.utc().startOf('day');
  const startOfNow = now.utc().startOf('day');
  const max = generatePeriod - 1;
  let index = 0;

  do {
    const periodEnd = calculatePeriodEnd({
      period,
      maxEnd: endDate,
      currentStart: startDate,
      isFirstPeriod: index === 0,
    });

    startDate = periodEnd?.add(1, 'day');
    if (startDate.isAfter(startOfNow)) {
      index++;
    }
  } while (index < max);

  return startDate.startOf('day').toDate();
}

/**
 * Calculates the future generation date based on the provided agreement line status and the current date.
 *
 * @param {Object} greatestAgreementLineStatus - Greatest agreement line status object.
 * @param {Dayjs} now - The current date.
 * @returns {Date | undefined} The calculated future generation date, or undefined if the smallest start date is not in the future.
 */
export function getFutureGenerationDate(
  greatestAgreementLineStatus: {
    isFuture: boolean;
    greatestPeriodType: any;
    smallestStartDate: any;
  },
  now: any,
): Date | undefined {
  if (!greatestAgreementLineStatus.isFuture) {
    return undefined;
  }

  return calculate2PeriodsFutureGenerationDate({
    greatestPeriodType: greatestAgreementLineStatus.greatestPeriodType,
    startDate: greatestAgreementLineStatus.smallestStartDate,
    now,
  });
}

export function getCostLineGeneralUpdatedFields({
  existedContract,
  updatedContract,
  existedCostLineGeneral,
  updatedCostLineGeneral,
}: {
  existedContract: any;
  updatedContract: any;
  existedCostLineGeneral: any;
  updatedCostLineGeneral: any;
}): CostLineGeneralUpdateField[] {
  const fields: CostLineGeneralUpdateField[] = [];

  if (existedContract.generatePeriod !== updatedContract.generatePeriod) {
    fields.push('generatePeriod');
  }

  const oldStartDate = dayjs(existedCostLineGeneral.startDate).utc();
  const oldEndDate = existedCostLineGeneral.endDate
    ? dayjs(existedCostLineGeneral.endDate).utc()
    : null;

  if (existedCostLineGeneral.price !== updatedCostLineGeneral.price) {
    fields.push('price');
  }

  if (!updatedCostLineGeneral.startDate.isSame(oldStartDate, 'day')) {
    fields.push('startDate');
  }

  if (
    (updatedCostLineGeneral.endDate &&
      !updatedCostLineGeneral.endDate.isSame(oldEndDate, 'day')) ||
    (oldEndDate && !oldEndDate.isSame(updatedCostLineGeneral.endDate, 'day'))
  ) {
    fields.push('endDate');
  }

  return fields;
}

export function findIndexCostLineInPeriod({
  costLines,
  comparableCostLine,
  extraFilter,
  customFilter,
}: {
  costLines: any[];
  comparableCostLine: any;
  extraFilter?: (costLine: any) => boolean;
  customFilter?: (costLine: any) => boolean;
}) {
  return costLines.findIndex((costLine) => {
    if (customFilter) {
      return customFilter(costLine);
    }

    const satisfiedDayWithin =
      dayjs(comparableCostLine.endDate).isAfter(
        dayjs(costLine.startDate),
        'day',
      ) &&
      dayjs(comparableCostLine.startDate).isBefore(
        dayjs(costLine.endDate),
        'day',
      );

    if (extraFilter) {
      return satisfiedDayWithin && extraFilter(costLine);
    }

    return satisfiedDayWithin;
  });
}

export function convertCostLineToCreditLine(oldCostLine: any) {
  return {
    ...omit(oldCostLine, ['_id', 'approvedAt', 'createdAt', 'updatedAt']),
    isCredit: true,
    isDeleted: false,
    status: CostLineStatus.OPEN,
    price: oldCostLine.price,
    totalPrice: -oldCostLine.totalPrice,
  };
}

export function createCreditLineFromCostLine(
  contract: any,
  agreementLine: any,
  oldCostLine: any,
  newCostLine: any,
  newPeriod: Period,
) {
  const creditPrice = calculateCreditPrice(
    oldCostLine.price,
    newCostLine.price,
    agreementLine.period,
    newPeriod,
    contract.isNew,
  );

  return {
    ...omit(oldCostLine, ['_id', 'approvedAt', 'createdAt', 'updatedAt']),
    isCredit: true,
    isDeleted: false,
    status: CostLineStatus.OPEN,
    price: newCostLine.price,
    totalPrice: creditPrice,
    startDate: newPeriod.start,
    endDate: newPeriod.end,
    parent: oldCostLine._id,
  };
}

export function convertCostLineToCreditLines(
  contract: any,
  agreementLine: any,
  oldCostLine: any,
  newCostLine: any,
) {
  const credits: any[] = [];
  const startDate = dayjs(newCostLine.startDate).utc();
  const endDate = dayjs(newCostLine.endDate).utc();
  const isStartDateAfter = startDate.isAfter(oldCostLine.startDate);
  const isEndDateBefore = endDate.isBefore(oldCostLine.endDate);

  if (
    ((!isStartDateAfter && !isEndDateBefore) ||
      agreementLine.periodType === AgreementLinePeriodType.ONE_TIME) &&
    oldCostLine.totalPrice !== newCostLine.totalPrice
  ) {
    return [
      {
        ...omit(oldCostLine, ['_id', 'approvedAt', 'createdAt', 'updatedAt']),
        isCredit: true,
        isDeleted: false,
        status: CostLineStatus.OPEN,
        price: newCostLine.price,
        totalPrice: newCostLine.totalPrice - oldCostLine.totalPrice,
      },
    ];
  }

  if (isStartDateAfter) {
    const newEndDate = startDate.subtract(1, 'day').endOf('day');
    const newPeriod: Period = {
      start: oldCostLine.startDate,
      end: newEndDate.toDate(),
    };

    if (
      oldCostLine.startDateBound &&
      newEndDate.isAfter(oldCostLine.startDateBound, 'date')
    ) {
      newPeriod.start = dayjs(oldCostLine.startDateBound)
        .utc()
        .add(1, 'day')
        .startOf('day')
        .toDate();
    }

    credits.push(
      createCreditLineFromCostLine(
        contract,
        agreementLine,
        oldCostLine,
        newCostLine,
        newPeriod,
      ),
    );
  }

  if (isEndDateBefore) {
    const newStartDate = endDate.add(1, 'day').startOf('day');
    const newPeriod: Period = {
      start: newStartDate.toDate(),
      end: oldCostLine.endDate,
    };

    if (
      oldCostLine.endDateBound &&
      newStartDate.isBefore(oldCostLine.endDateBound, 'date')
    ) {
      newPeriod.end = dayjs(oldCostLine.endDateBound)
        .utc()
        .subtract(1, 'day')
        .endOf('day')
        .toDate();
    }

    credits.push(
      createCreditLineFromCostLine(
        contract,
        agreementLine,
        oldCostLine,
        newCostLine,
        newPeriod,
      ),
    );
  }

  return credits;
}

export function isSamePeriod(
  source: DateLike,
  target: DateLike,
  period: AgreementLinePeriod,
) {
  if (period === AgreementLinePeriod.WEEKLY) {
    return dayjs(source).isSame(target, 'isoWeek');
  }

  if (period === AgreementLinePeriod.FOUR_WEEKLY) {
    const firstWeekSource = findNearestIsoWeekFourWeekly(dayjs(source));
    const firstWeekTarget = findNearestIsoWeekFourWeekly(dayjs(target));
    return firstWeekSource === firstWeekTarget;
  }

  if (period === AgreementLinePeriod.MONTHLY) {
    return dayjs(source).isSame(target, 'month');
  }

  return false;
}

export function calculateCreditPrice(
  oldWeeklyPrice: number,
  newWeeklyPrice: number,
  periodType: AgreementLinePeriod | undefined | null,
  period: Period,
  isNew: boolean = true,
): number {
  const oldDailyPrice = oldWeeklyPrice / DAYS_IN_WEEK;
  const newDailyPrice = newWeeklyPrice / DAYS_IN_WEEK;
  const totalDaysInPeriod = dayjs(period.end).diff(period.start, 'days') + 1; // +1 to include the end date

  switch (periodType) {
    case AgreementLinePeriod.WEEKLY: {
      if (totalDaysInPeriod < DAYS_IN_WEEK) {
        return -totalDaysInPeriod * (2 * newDailyPrice - oldDailyPrice);
      }

      return -2 * newWeeklyPrice + oldWeeklyPrice;
    }
    case AgreementLinePeriod.FOUR_WEEKLY: {
      if (totalDaysInPeriod < DAYS_IN_FOUR_WEEKS) {
        return -totalDaysInPeriod * (2 * newDailyPrice - oldDailyPrice);
      }
      return -4 * (2 * newWeeklyPrice - oldWeeklyPrice);
    }
    case AgreementLinePeriod.MONTHLY: {
      if (!isNew) {
        return -AVG_WEEKS_IN_MONTH * (2 * newDailyPrice - oldDailyPrice);
      }

      const daysInMonth = dayjs(period.start).daysInMonth();
      if (totalDaysInPeriod < daysInMonth) {
        return -totalDaysInPeriod * (2 * newDailyPrice - oldDailyPrice);
      }

      return (
        -(DAYS_IN_YEAR / MONTHS_IN_YEAR) * (2 * newDailyPrice - oldDailyPrice)
      );
    }
    default:
      return -2 * newWeeklyPrice + oldWeeklyPrice;
  }
}

export function isSameTotalPrice(oldCostLine: any, newCostLine: any) {
  return (
    oldCostLine.totalPrice.toFixed(2) === newCostLine.totalPrice.toFixed(2)
  );
}
