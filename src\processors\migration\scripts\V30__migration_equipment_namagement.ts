import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldEquipment {
  _id: string;
  active: boolean;
  type: 'CAR' | 'DEVICE';
  description: string;
}

const EquipmentPipeLineAggregate = (skip: number, limit: number) => {
  // get all equipment
  return [{ $skip: skip }, { $limit: limit }];
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldEquipment[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  return Promise.all(
    data.map(async (item) => {
      return {
        _id: item._id,
        isActive: item.active,
        type: item.type,
        description: item.description,
        createdDate: new Date(),
        updatedDate: new Date(),
      };
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('planning_equipment');

  const pipeline = EquipmentPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'planning_equipment',
      destinationCollectionName: 'equipment',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
