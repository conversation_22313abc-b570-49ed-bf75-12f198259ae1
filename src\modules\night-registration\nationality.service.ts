import { Injectable } from '@nestjs/common';
import { PipelineStage } from 'mongoose';

import { MongooseModel } from '~/shared/interfaces/mongoose.interface';
import { InjectModel } from '~/transformers/model.transformer';
import { buildQuery, NATIONALITY_NO_CODE } from '~/utils';

import { NightRegistrationNationalityModel } from './models/night-registration-nationality.model';

@Injectable()
export class NationalityService {
  constructor(
    @InjectModel(NightRegistrationNationalityModel)
    private readonly nationalityModel: MongooseModel<NightRegistrationNationalityModel>,
  ) {}

  async getNationalities(payload: any) {
    const { query, options } = buildQuery(
      {
        sortBy: 'name',
        sortDir: 'asc',
        ...payload,
      },
      ['name', 'code', 'nameDutch'],
    );

    const aggregates: PipelineStage[] = [
      { $match: query },
      {
        $addFields: {
          isNoCode: { $cond: [{ $eq: ['$code', NATIONALITY_NO_CODE] }, 1, 0] },
        },
      },
      {
        $sort: {
          isNoCode: -1, // Ensure NOCODE is on top
          ...options.sort,
        },
      },
      {
        $project: {
          _id: 1,
          name: 1,
          code: 1,
          nameDutch: 1,
        },
      },
    ];

    return await this.nationalityModel.aggregatePaginate(
      this.nationalityModel.aggregate(aggregates),
      options,
    );
  }
}
