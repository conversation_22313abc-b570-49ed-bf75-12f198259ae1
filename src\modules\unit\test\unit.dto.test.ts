import { ObjectId } from 'mongodb';
import { z } from 'zod';

import { baseModelTestSchema } from '~/test/utils/base-schema';

const modelSchema = z
  .object({
    isActive: z.boolean(),
    isRoot: z.boolean(),
    name: z.string(),
    maxOccupants: z.number(),
    maxArea: z.number(),
    position: z.number(),
    parent: z.instanceof(ObjectId).nullish().optional(),
    location: z.instanceof(ObjectId).nullish(),
  })
  .extend(baseModelTestSchema);

const findRootUnitOfLocationSchema = modelSchema.extend({
  isRoot: z.literal(true),
});

const findUnitsOfLocationSchema = z.array(modelSchema.omit({ location: true }));
const findChildrenOfUnitSchema = z.array(
  modelSchema.extend({
    parent: z.instanceof(ObjectId),
  }),
);

export const unitTest = {
  modelSchema,
  findRootUnitOfLocationSchema,
  findUnitsOfLocationSchema,
  findChildrenOfUnitSchema,
};
