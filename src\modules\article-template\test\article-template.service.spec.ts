import { NotFoundException } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectProviders } from '~/test/helpers/test-inject-model';
import { initMockArticle, mockArticleData } from '~/test/mocks/article.mock';
import { initMockArticleCategory } from '~/test/mocks/articlecategory.mock';
import {
  initMockArticleTemplate,
  mockArticleTemplateData,
} from '~/test/mocks/articletemplate.mock';
import { initMockStorage, mockStorageData } from '~/test/mocks/storage.mock';

import { ArticleTemplateModel } from '../article-template.model';
import { ArticleTemplateService } from '../article-template.service';
import {
  CreateArticleTemplateDto,
  UpdateArticleTemplateDto,
} from '../dtos/article-template.dto';
import { articleTemplateTest } from './article-template.dto.test';

describe('ArticleTemplateService', () => {
  let service: ArticleTemplateService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        ArticleTemplateService,
        ...testInjectProviders([ArticleTemplateModel]),
      ],
    }).compile();

    service = module.get<ArticleTemplateService>(ArticleTemplateService);

    // Init data
    await Promise.all([
      initMockArticleTemplate(),
      initMockArticle(),
      initMockArticleCategory(),
      initMockStorage(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('create', () => {
    it('should create a new article template', async () => {
      const payload: CreateArticleTemplateDto = {
        name: 'Template 1',
        storage: mockStorageData._id.toString(),
        articleList: [
          {
            article: mockArticleData._id.toString(),
            amount: 10,
            position: 1,
            _id: mockArticleData._id.toString(),
          },
        ],
      };

      const result = await service.create(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(articleTemplateTest.modelSchema);
    });
  });

  describe('findAll', () => {
    it('should call fn and return list article templates', async () => {
      const result = await service.findAll({});
      expect(result).toBeDefined();
      expect(result).toMatchSchema(articleTemplateTest.findAllSchema);
    });

    it('should call fn with pageSize -1 and return all article templates', async () => {
      const limit = await service['articleTemplateModel'].countDocuments({
        _id: { $ne: mockArticleTemplateData._id },
      });

      const result = await service.findAll({
        _id: mockArticleTemplateData._id.toString(),
        pageSize: -1,
      });
      expect(result).toBeDefined();
      expect(result).toHaveLength(limit);
      expect(result).toMatchSchema(articleTemplateTest.findAllSchema);
    });

    it('should return empty list article templates when data not found', async () => {
      const result = await service.findAll({ pageIndex: 99 });
      expect(result).toEqual([]);
    });
  });

  describe('findOne', () => {
    it('should return a single article template by ID', async () => {
      const id = mockArticleTemplateData._id.toString();
      const result = await service.findOne(id);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockArticleTemplateData._id);
      expect(result).toMatchSchema(articleTemplateTest.modelSchema);
    });

    it('should throw NotFoundException if template not found', async () => {
      await expect(service.findOne(new ObjectId().toString())).rejects.toThrow(
        NotFoundException,
      );
    });
  });

  describe('update', () => {
    it('should update an article template', async () => {
      const payload: UpdateArticleTemplateDto = {
        _id: mockArticleTemplateData._id.toString(),
        name: 'Updated Template',
        articleList: [
          {
            article: mockArticleData._id.toString(),
            amount: 40,
            position: 2,
            _id: mockArticleData._id.toString(),
          },
        ],
      };

      const result = await service.update(payload);
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockArticleTemplateData._id);
      expect(result.name).toBe('Updated Template');
      expect(result).toMatchSchema(articleTemplateTest.modelSchema);
    });

    it('should throw NotFoundException if template not found', async () => {
      await expect(
        service.update({
          _id: new ObjectId().toString(),
          name: 'Updated Template',
        }),
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('delete', () => {
    it('should delete an article template', async () => {
      const result = await service.delete(
        mockArticleTemplateData._id.toString(),
      );
      expect(result).toBeDefined();
      expect(result).toHaveProperty('_id', mockArticleTemplateData._id);
      expect(result).toMatchSchema(articleTemplateTest.modelSchema);
    });

    it('should throw NotFoundException if template not found', async () => {
      await expect(
        service.delete(mockArticleTemplateData._id.toString()),
      ).rejects.toThrow(NotFoundException);
    });
  });
});
