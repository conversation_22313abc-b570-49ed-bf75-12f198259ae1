import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldLocation {
  _id: string;
  active: boolean;
  city: string;
  street: string;
  number: number;
  postalCode: string;
  route: string;
  suffix: string;
  costcenter: string;
  country: string;
  region: string;
  descriptions: any;
  updatedby: string;
  isRenting: boolean;
  isService: boolean;
  team: string;
  geocode: any;
  bvCompany: string;
  emailAddress: string;
  units: string[];
  fullAddress: string;
  updatedDate: Date;
  createdDate: Date;
}

const LocationAdditionalPipeLineAggregate = (skip: number, limit: number) => {
  return [{ $skip: skip }, { $limit: limit }];
};

const transformDataFunc = ({
  data,
  context,
}: {
  data: OldLocation[];
  context: any;
}) => {
  return Promise.all(
    data.map(async (item) => {
      const transformedItem = {
        _id: item._id,
        isActive: item.active,
        isRenting: item.isRenting,
        isService: item.isService,
        maxOccupants: 0,
        maxArea: 0,
        bvCompany: '',
        fullAddress: item.fullAddress,
        address: null,
        email: item.emailAddress,
        locationOf: item.descriptions,
        geo: item.geocode,
        team: item.team,
        costCenter: item.costcenter,
        createdAt: item.createdDate || new Date(),
        updatedAt: item.updatedDate || new Date(),
      };

      // convert item.bvCompany to ObjectId
      const foundCompany = await context
        .destinationClient!.db()
        .collection('bvcompanies')
        .findOne({ identifier: item.bvCompany });

      if (foundCompany) {
        transformedItem.bvCompany = foundCompany._id;
      }

      // create new addresses using the old address data
      const newAddress = await context
        .destinationClient!.db()
        .collection('addresses')
        .insertOne({
          isDeleted: false,
          street: item.street,
          city: item.city,
          postalCode: item.postalCode,
          number: item.number,
          country: item.country,
          region: item.region,
          location: transformedItem._id,
          createdAt: new Date(),
          updatedAt: new Date(),
        });

      transformedItem.address = newAddress.insertedId;
      return transformedItem;
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('location');

  const pipeline = LocationAdditionalPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'location',
      destinationCollectionName: 'locations',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: transformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
