import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ClientsModule, Transport } from '@nestjs/microservices';

import {
  INVENTORY_SERVICE_CLIENT,
  PDF_SERVICE_CLIENT,
} from '~/constants/app.constant';

@Module({
  imports: [
    ClientsModule.registerAsync([
      {
        name: INVENTORY_SERVICE_CLIENT,
        useFactory: async (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('inventory.tcpHost'),
            port: configService.get('inventory.tcpPort'),
          },
        }),
        inject: [ConfigService],
      },
      {
        name: PDF_SERVICE_CLIENT,
        useFactory: async (configService: ConfigService) => ({
          transport: Transport.TCP,
          options: {
            host: configService.get('pdf.tcpHost'),
            port: configService.get('pdf.tcpPort'),
          },
        }),
        inject: [ConfigService],
      },
    ]),
  ],
  exports: [ClientsModule],
})
@Global()
export class TcpClientsModule {}
