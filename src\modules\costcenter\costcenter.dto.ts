import { createZodDto } from 'nestjs-zod';
import { z } from 'nestjs-zod/z';

import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { isValidObjectId } from '~/utils';

const CostCenterQuerySchema = z.strictObject({
  country: z
    .string()
    .refine((val) => isValidObjectId(val))
    .optional(),
  bvCompany: z
    .string()
    .refine((val) => isValidObjectId(val))
    .optional(),
  isUsed: z
    .enum(['true', 'false'])
    .optional()
    .transform((val) => (val ? val === 'true' : val)),
  location: z
    .string()
    .refine((val) => isValidObjectId(val))
    .optional(),
  isActive: z
    .enum(['true', 'false'])
    .optional()
    .default('true')
    .transform((val) => val === 'true'),
});

export class CostCenterQueryDto extends createZodDto(
  CostCenterQuerySchema.merge(QueryParamsSchema),
) {}
