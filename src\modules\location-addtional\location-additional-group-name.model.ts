import { DocumentType, modelOptions, plugin, prop } from '@typegoose/typegoose';
import { patchHistoryPlugin } from 'ts-patch-mongoose';

import { getDefaultPluginOptions } from '~/processors/database/events/listeners';
import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';
import { BaseModel } from '~/shared/models/base.model';

export type LocationAdditionalGroupNameDocument =
  DocumentType<LocationAdditionalGroupNameModel>;

@modelOptions({
  options: { customName: 'locationAdditionalGroupName' },
})
@plugin(patchHistoryPlugin, getDefaultPluginOptions())
export class LocationAdditionalGroupNameModel extends BaseModel {
  @prop()
  identifier!: string;

  @prop({ required: true, enum: LocationAdditionalType })
  type!: LocationAdditionalType;

  @prop({ required: true })
  key!: string;

  @prop({ required: true })
  description!: string;

  @prop()
  dutchDescription?: string;

  @prop({ required: true })
  position!: number;
}
