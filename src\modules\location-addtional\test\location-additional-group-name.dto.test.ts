import { z } from 'zod';

import { LocationAdditionalType } from '~/shared/enums/location-additional.enum';
import { baseModelTestSchema } from '~/test/utils/base-schema';
const modelSchema = z
  .object({
    identifier: z.string(),
    type: z.nativeEnum(LocationAdditionalType),
    key: z.string(),
    description: z.string(),
    dutchDescription: z.string().optional(),
    position: z.number(),
  })
  .extend(baseModelTestSchema);

export const locationAdditionalGroupNameTest = {
  modelSchema,
};
