import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldUnit {
  _id: string;
  name: string;
  maxOccupants: number;
  area: number;
  parent: string;
  location: string;
  status: boolean;
  position: number;
}

const PipeLineAggregate = (skip: number, limit: number) => {
  return [{ $skip: skip }, { $limit: limit }];
};

const transformDataFunc = ({
  data,
  context,
}: {
  data: OldUnit[];
  context: any;
}) => {
  return Promise.all(
    data.map(async (item) => {
      const transformedItem = {
        _id: item._id,
        isActive: item.status,
        isRoot: !item.parent,
        name: item.name,
        maxOccupants: item.maxOccupants,
        maxArea: item.area,
        position: item.position,
        ...(item.parent && { parent: item.parent }),
        location: item.location,
        createdAt: new Date(),
        updatedAt: new Date(),
        isDeleted: false,
      };

      // recalculate the maxOccupants and maxArea in location
      await context
        .destinationClient!.db()
        .collection('locations')
        .findOneAndUpdate(
          { _id: item.location },
          {
            $inc: {
              maxOccupants: item.maxOccupants || 0,
              maxArea: item.area || 0,
            },
          },
        );

      return transformedItem;
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context.sourceClient!.db().collection('unit');

  const pipeline = PipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'unit',
      destinationCollectionName: 'units',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: transformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
