import { ConfigService } from '@nestjs/config';
import { ClientProxy } from '@nestjs/microservices';
import { Test, TestingModule } from '@nestjs/testing';
import dayjs from 'dayjs';
import { ObjectId } from 'mongodb';
import { of } from 'rxjs';
import * as XLSX from 'xlsx';

import { PDF_SERVICE_CLIENT } from '~/constants/app.constant';
import { ContactModel } from '~/modules/contact/contact.model';
import { ContractModel } from '~/modules/contract/contract.model';
import { EmailTemplateModel } from '~/modules/email-template/email-template.model';
import { LocationModel } from '~/modules/location/location.model';
import { UnitModel } from '~/modules/unit/unit.model';
import { EmailService } from '~/processors/email/email.service';
import { ContractType } from '~/shared/enums/contract.enum';
import { JobPeriodTypeEnum } from '~/shared/enums/job.enum';
import {
  NightRegistrationWarningLevel,
  NRMaximumStayDurationLevel,
} from '~/shared/enums/night-registration.enum';
import { NIGHT_REGISTRATION_MESSAGE_KEYS } from '~/shared/message-keys/night-registration.message-keys';
import { TestDBModule } from '~/test/helpers/test-db.module';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockAgreementLine,
  mockAgreementLineData,
} from '~/test/mocks/agreementline.mock';
import { initMockContact, mockContactData } from '~/test/mocks/contact.mock';
import { initMockContract, mockContractData } from '~/test/mocks/contract.mock';
import { initMockCostCenter } from '~/test/mocks/costcenter.mock';
import {
  initMockCostlineGeneral,
  mockCostlineGeneralData,
} from '~/test/mocks/costlinegeneral.mock';
import { initMockJob, mockJobData } from '~/test/mocks/job.mock';
import { initMockLocation, mockLocationData } from '~/test/mocks/location.mock';
import {
  initMockNightRegistrationNationality,
  mockNightRegistrationNationalityData,
} from '~/test/mocks/nightregistrationnationality.mock';
import {
  initMockNightRegistrationReservation,
  mockNightRegistrationReservationData,
} from '~/test/mocks/nightregistrationreservation.mock';
import {
  initMockNightRegistrationResident,
  mockNightRegistrationResidentData,
} from '~/test/mocks/nightregistrationresident.mock';
import { initMockNightRegistrationWarning } from '~/test/mocks/nightregistrationwarning.mock';
import {
  initMockNightRegistrationWarningCategory,
  mockNightRegistrationWarningCategoryData,
} from '~/test/mocks/nightregistrationwarningcategory.mock';
import { initMockUnit, mockUnitData } from '~/test/mocks/unit.mock';

import {
  GetNightRegistrationReportQueryDto,
  ReservationCreateBodyDto,
  ReservationQueryParamsDto,
  ReservationUpdateBodyDto,
  VirtualReservationCreateBodyDto,
  WarningCreateBodyDto,
  WarningQueryParamsDto,
} from '../dtos/night-registration.dto';
import { NightRegistrationNationalityModel } from '../models/night-registration-nationality.model';
import { NightRegistrationReservationModel } from '../models/night-registration-reservation.model';
import { NightRegistrationResidentModel } from '../models/night-registration-resident.model';
import { NightRegistrationWarningModel } from '../models/night-registration-warning.model';
import { NightRegistrationWarningCategoryModel } from '../models/night-registration-warning-category';
import { NightRegistrationService } from '../night-registration.service';
import { HEADERS } from '../night-registration.util';
import { nightRegistrationTest } from './night-registration.dto.test';

describe('Night Registration Service', () => {
  let service: NightRegistrationService;
  let pdfClient: ClientProxy;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [TestDBModule],
      providers: [
        NightRegistrationService,
        ...testInjectModel([
          LocationModel,
          ContractModel,
          NightRegistrationReservationModel,
          NightRegistrationResidentModel,
          UnitModel,
          NightRegistrationWarningCategoryModel,
          NightRegistrationWarningModel,
          EmailTemplateModel,
          ContactModel,
          NightRegistrationNationalityModel,
        ]),
        ConfigService,
        {
          provide: EmailService,
          useValue: {},
        },
        {
          provide: PDF_SERVICE_CLIENT,
          useValue: { send: jest.fn() },
        },
      ],
    }).compile();

    service = module.get(NightRegistrationService);
    pdfClient = module.get(PDF_SERVICE_CLIENT);

    const subUnitId = new ObjectId();

    // Init data
    await Promise.all([
      initMockUnit({ parent: null }),
      initMockUnit({
        _id: subUnitId,
        isRoot: false,
        parent: mockUnitData._id,
        maxOccupants: 3,
      }),
      initMockLocation({ maximumStayDuration: 1 }),
      initMockContact(),
      initMockContract({
        contact: mockContactData._id,
        location: mockLocationData._id,
        endDate: null,
        agreementLines: [mockAgreementLineData._id],
      }),
      initMockCostCenter({
        locations: [mockLocationData._id],
      }),
      initMockAgreementLine({
        contract: mockContractData._id,
        costLineGenerals: [mockCostlineGeneralData._id],
        units: [mockUnitData._id],
      }),
      initMockCostlineGeneral({
        agreementLine: mockAgreementLineData._id,
        endDate: null,
      }),
      initMockJob({ type: JobPeriodTypeEnum.PERIODIC }),
      initMockNightRegistrationReservation({
        job: [mockJobData._id],
        unit: subUnitId,
        arrivalDate: dayjs().utc().subtract(20, 'day').toDate(),
      }),
      initMockNightRegistrationReservation({
        _id: new ObjectId(),
        job: [mockJobData._id],
        unit: subUnitId,
        bed: 3,
        arrivalDate: dayjs().utc().subtract(32, 'day').toDate(),
      }),
      initMockNightRegistrationResident(),
      initMockNightRegistrationWarningCategory(),
      initMockNightRegistrationWarning(),
    ]);
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  describe('getListReservation', () => {
    const payload: ReservationQueryParamsDto = {
      location: mockLocationData._id.toString(),
      checkOut: 'false',
    };
    let result: any;

    it('should call fn with payload and return list data', async () => {
      result = await service.getListReservation(payload);

      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        nightRegistrationTest.getListReservationSchema,
      );
    });

    it("reservation's maximum stay duration should be null", async () => {
      const testResult = await service.getListReservation(payload);
      expect(Array.isArray(testResult)).toBe(false);
      const bed1 = (testResult as any).beds.find((bed: any) => bed.bedNo === 2);
      expect(bed1).toBeDefined();
      expect(bed1.reservation).toBeInstanceOf(Object);
      expect(bed1.reservation.maximumStayWarning).toBeNull();
    });

    it("reservation's maximum stay duration should be approaching", async () => {
      const testResult = await service.getListReservation(payload);
      expect(Array.isArray(testResult)).toBe(false);
      const bed1 = (testResult as any).beds.find((bed: any) => bed.bedNo === 1);
      expect(bed1).toBeDefined();
      expect(bed1.reservation).toBeInstanceOf(Object);
      expect(bed1.reservation.maximumStayWarning).toEqual(
        NRMaximumStayDurationLevel.APPROACHING,
      );
    });

    it("reservation's maximum stay duration should be exceeded", async () => {
      const testResult = await service.getListReservation(payload);
      expect(Array.isArray(testResult)).toBe(false);
      const bed1 = (testResult as any).beds.find((bed: any) => bed.bedNo === 3);

      expect(bed1).toBeDefined();
      expect(bed1.reservation).toBeInstanceOf(Object);
      expect(bed1.reservation.maximumStayWarning).toEqual(
        NRMaximumStayDurationLevel.EXCEEDED,
      );
    });

    it('should return list empty when contract not found', async () => {
      // Prepare mock data
      await initMockContract({ isActive: false });

      const result = await service.getListReservation(payload);
      expect(result).toEqual([]);
    });

    it('should return resident with isBRP field', async () => {
      // Ensure we have a reservation with resident data
      await initMockNightRegistrationReservation({
        resident: mockNightRegistrationResidentData._id,
        isVirtual: false,
        unit: mockUnitData._id,
        bed: 1,
      });

      const result = await service.getListReservation(payload);

      // Handle both cases - when result is an array (no contracts found) or an object
      if (Array.isArray(result)) {
        return;
      }

      const bedWithResident = (result as any).beds.find(
        (bed: any) => bed.reservation?.resident?._id,
      );

      if (bedWithResident) {
        expect(bedWithResident.reservation.resident).toBeDefined();
        expect(bedWithResident.reservation.resident.isBRP).toBeDefined();
        expect(typeof bedWithResident.reservation.resident.isBRP).toBe(
          'boolean',
        );
      }
    });
  });

  describe('getContactsByLocation', () => {
    it('should throw error when location not found', async () => {
      await expect(
        service.getContactsByLocation(new ObjectId().toString()),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.LOCATION_NOT_FOUND);
    });

    it('should call fn with payload and return list data', async () => {
      // Prepare mock data
      await initMockContract({
        contact: mockContactData._id,
        location: mockLocationData._id,
        endDate: null,
      });

      const result = await service.getContactsByLocation(
        mockLocationData._id.toString(),
      );
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        nightRegistrationTest.getContactsByLocationSchema,
      );
    });
  });

  describe('getListResidents', () => {
    it('should call fn with payload and return list data', async () => {
      const result = await service.getListResidents({});
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(
        nightRegistrationTest.getListResidentsSchema,
      );
    });

    it('should return empty list when no residents found', async () => {
      const result = await service.getListResidents({ pageIndex: 99 });
      expect(result.docs).toEqual([]);
    });
  });

  describe('createVirtualReservation', () => {
    const payload: VirtualReservationCreateBodyDto = {
      unit: mockUnitData._id.toString(),
      bed: 1,
      isVirtual: true,
    };

    it('should throw error when bed is reserved', async () => {
      await initMockNightRegistrationReservation({
        _id: new ObjectId(),
        unit: mockUnitData._id,
        bed: 1,
        isVirtual: true,
        departureDate: dayjs().toDate(),
      });

      // Mock private method
      service['checkBedReserved'] = jest.fn().mockResolvedValue(true);
      await expect(service.createVirtualReservation(payload)).rejects.toThrow(
        NIGHT_REGISTRATION_MESSAGE_KEYS.BED_ALREADY_RESERVED,
      );
    });

    it('should throw error when unit not found', async () => {
      // Mock private method
      service['checkBedReserved'] = jest.fn().mockResolvedValue(false);

      await expect(
        service.createVirtualReservation({
          ...payload,
          unit: new ObjectId().toString(),
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.UNIT_NOT_FOUND);
    });

    it('should throw error when bed not found', async () => {
      await expect(
        service.createVirtualReservation({
          ...payload,
          bed: 999,
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.BED_NOT_FOUND);
    });

    it('should call fn with payload and create data', async () => {
      const result = await service.createVirtualReservation(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(nightRegistrationTest.findOneSchema);
    });
  });

  describe('createReservation', () => {
    const payload: ReservationCreateBodyDto = {
      resident: {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: new Date().toISOString(),
        gender: 'Male',
        nationality: mockNightRegistrationNationalityData._id.toString(),
      },
      arrivalDate: new Date().toISOString(),
      contact: mockContactData._id.toString(),
      unit: mockUnitData._id.toString(),
      bed: 1,
      companyInfomation: {},
    };

    it('should throw error when bed is reserved', async () => {
      // Mock private method
      service['checkBedReserved'] = jest.fn().mockResolvedValue(true);

      await expect(service.createReservation(payload)).rejects.toThrow(
        NIGHT_REGISTRATION_MESSAGE_KEYS.BED_ALREADY_RESERVED,
      );
    });

    it('should throw error when unit not found', async () => {
      // Mock private method
      service['checkBedReserved'] = jest.fn().mockResolvedValue(false);

      await expect(
        service.createReservation({
          ...payload,
          unit: new ObjectId().toString(),
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.UNIT_NOT_FOUND);
    });

    it('should throw error when contact not found', async () => {
      jest
        .spyOn(service, 'getContactsByLocation')
        .mockResolvedValue([{ _id: new ObjectId() }]);

      await expect(service.createReservation(payload)).rejects.toThrow(
        NIGHT_REGISTRATION_MESSAGE_KEYS.CONTACT_NOT_FOUND,
      );
    });

    it('should throw error when bed not found', async () => {
      await expect(
        service.createReservation({
          ...payload,
          bed: 999,
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.BED_NOT_FOUND);
    });

    it('should call fn with payload and create data', async () => {
      const result = await service.createReservation(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(nightRegistrationTest.findOneSchema);
    });

    it('should create reservation with isBRP field', async () => {
      // Mock private method
      service['checkBedReserved'] = jest.fn().mockResolvedValue(false);

      const payloadWithIsBRP = {
        ...payload,
        resident: {
          ...payload.resident,
          isBRP: true,
        },
      };

      const result = await service.createReservation(payloadWithIsBRP);
      expect(result).toBeDefined();
      expect(result.resident).toBeDefined();
      expect(result.resident.isBRP).toBe(true);
    });

    it('should create reservation with isBRP field set to false', async () => {
      const payloadWithIsBRP = {
        ...payload,
        resident: {
          ...payload.resident,
          isBRP: false,
        },
      };

      const result = await service.createReservation(payloadWithIsBRP);
      expect(result).toBeDefined();
      expect(result.resident).toBeDefined();
      expect(result.resident.isBRP).toBe(false);
    });
  });

  describe('deleteVirtualReservation', () => {
    it('should throw error when reservation not found', async () => {
      await expect(
        service.deleteVirtualReservation(new ObjectId().toString()),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.RESERVATION_NOT_FOUND);
    });

    it('should throw error when reservation is not virtual', async () => {
      // Prepare mock data
      await initMockNightRegistrationReservation({
        isVirtual: false,
      });

      await expect(
        service.deleteVirtualReservation(
          mockNightRegistrationReservationData._id.toString(),
        ),
      ).rejects.toThrow(
        NIGHT_REGISTRATION_MESSAGE_KEYS.ONLY_DELETE_VIRTUAL_RESERVATION,
      );
    });

    it('should call fn with payload and delete data', async () => {
      // Prepare mock data
      await initMockNightRegistrationReservation({
        isVirtual: true,
      });

      const result = await service.deleteVirtualReservation(
        mockNightRegistrationReservationData._id.toString(),
      );
      expect(result).toBeDefined();
      expect(result).toBeInstanceOf(Object);
      expect(result).toHaveProperty('acknowledged');
    });
  });

  describe('findOne', () => {
    it('should call fn with payload and return data', async () => {
      // Prepare mock data
      await initMockNightRegistrationReservation();

      const result = await service.findOne(
        mockNightRegistrationReservationData._id,
      );
      expect(result).toBeDefined();
      expect(result).toMatchSchema(nightRegistrationTest.findOneSchema);
    });
  });

  describe('updateReservation', () => {
    const payload: ReservationUpdateBodyDto = {
      _id: mockNightRegistrationReservationData._id.toString(),
      resident: {
        firstName: 'John',
        lastName: 'Doe',
        dateOfBirth: new Date().toISOString(),
        gender: 'Female',
        nationality: mockNightRegistrationNationalityData._id.toString(),
      },
      arrivalDate: new Date().toISOString(),
      contact: mockContactData._id.toString(),
      unit: mockUnitData._id.toString(),
      bed: 1,
      companyInfomation: {},
    };

    it('should throw error when reservation not found', async () => {
      await expect(
        service.updateReservation({
          ...payload,
          _id: new ObjectId().toString(),
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.RESERVATION_NOT_FOUND);
    });

    it('should throw error when unit not found', async () => {
      await expect(
        service.updateReservation({
          ...payload,
          unit: new ObjectId().toString(),
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.UNIT_NOT_FOUND);
    });

    it('should throw error when contact not found', async () => {
      jest
        .spyOn(service, 'getContactsByLocation')
        .mockResolvedValue([{ _id: new ObjectId() }]);

      await expect(service.updateReservation(payload)).rejects.toThrow(
        NIGHT_REGISTRATION_MESSAGE_KEYS.CONTACT_NOT_FOUND,
      );
    });

    it('should throw error when bed not found', async () => {
      await expect(
        service.updateReservation({
          ...payload,
          bed: 999,
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.BED_NOT_FOUND);
    });

    it('should throw error when bed is reserved', async () => {
      // Prepare mock data
      const reservationId = new ObjectId();
      const unitId = new ObjectId();

      await Promise.all([
        initMockNightRegistrationReservation({
          _id: reservationId,
          unit: unitId,
          bed: 1,
          isVirtual: true,
          departureDate: dayjs().toDate(),
        }),
        initMockUnit({ _id: unitId }),
      ]);

      // Mock private method
      service['checkBedReserved'] = jest.fn().mockResolvedValue(true);

      await expect(
        service.updateReservation({
          ...payload,
          _id: reservationId.toString(),
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.BED_ALREADY_RESERVED);
    });

    it('should call fn with payload and update data', async () => {
      // Mock private method
      service['checkBedReserved'] = jest.fn().mockResolvedValue(false);

      const result = await service.updateReservation(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(nightRegistrationTest.findOneSchema);
    });

    it('should update reservation with isBRP field', async () => {
      // Mock private method
      service['checkBedReserved'] = jest.fn().mockResolvedValue(false);

      const payloadWithIsBRP = {
        ...payload,
        resident: {
          ...payload.resident,
          isBRP: true,
        },
      };

      const result = await service.updateReservation(payloadWithIsBRP);
      expect(result).toBeDefined();
      expect(result.resident).toBeDefined();
      expect(result.resident.isBRP).toBe(true);
    });

    it('should update reservation with isBRP field set to false', async () => {
      // Mock private method
      service['checkBedReserved'] = jest.fn().mockResolvedValue(false);

      const payloadWithIsBRP = {
        ...payload,
        resident: {
          ...payload.resident,
          isBRP: false,
        },
      };

      const result = await service.updateReservation(payloadWithIsBRP);
      expect(result).toBeDefined();
      expect(result.resident).toBeDefined();
      expect(result.resident.isBRP).toBe(false);
    });
  });

  describe('deleteReservation', () => {
    it('should throw error when reservation not found', async () => {
      await expect(
        service.deleteReservation(new ObjectId().toString()),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.RESERVATION_NOT_FOUND);
    });

    it('should call fn with payload and delete data', async () => {
      const result = await service.deleteReservation(
        mockNightRegistrationReservationData._id.toString(),
      );
      expect(result).toBeDefined();
      expect(result).toBeInstanceOf(Object);
      expect(result).toHaveProperty('acknowledged');

      // Restore data
      await initMockNightRegistrationReservation();
    });
  });

  describe('getWarningCategories', () => {
    it('should call fn and return list data', async () => {
      const result = await service.getWarningCategories({});
      expect(result).toBeDefined();
      expect(result.docs).toMatchSchema(
        nightRegistrationTest.getWarningCategoriesSChema,
      );
    });

    it('should return empty list when no categories found', async () => {
      const result = await service.getWarningCategories({
        pageIndex: 99,
        pageSize: 5,
      });
      expect(result.docs).toEqual([]);
    });
  });

  describe('createWarning', () => {
    const payload: WarningCreateBodyDto = {
      resident: mockNightRegistrationResidentData._id.toString(),
      reservation: mockNightRegistrationReservationData._id.toString(),
      level: NightRegistrationWarningLevel.VERBAL,
      warningDate: new Date().toISOString(),
      warningCategory: mockNightRegistrationWarningCategoryData._id.toString(),
      images: [],
      companyInfomation: {},
    };

    it('should throw error when reservation not found', async () => {
      await expect(
        service.createWarning({
          ...payload,
          reservation: new ObjectId().toString(),
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.RESERVATION_NOT_FOUND);
    });

    it('should throw error when resident not found', async () => {
      await expect(
        service.createWarning({
          ...payload,
          resident: new ObjectId().toString(),
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.RESIDENT_NOT_FOUND);
    });

    it('should throw error when warning category not found', async () => {
      await expect(
        service.createWarning({
          ...payload,
          warningCategory: new ObjectId().toString(),
        }),
      ).rejects.toThrow(
        NIGHT_REGISTRATION_MESSAGE_KEYS.WARNING_CATEGORY_NOT_FOUND,
      );
    });

    it('should call fn with payload and create data', async () => {
      jest
        .spyOn(pdfClient, 'send')
        .mockReturnValue(
          of({ publicPdfUrl: 'https://fake.url/file.pdf' }) as any,
        );
      jest
        .spyOn(service as any, 'sendEmailWarningToContact')
        .mockResolvedValue(undefined);

      const result = await service.createWarning(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(nightRegistrationTest.warningModelSchema);
    });
  });

  describe('getWarnings', () => {
    const payload: WarningQueryParamsDto = {
      resident: mockNightRegistrationResidentData._id.toString(),
    };

    it('should throw error when resident not found', async () => {
      await expect(
        service.getWarnings({
          resident: new ObjectId().toString(),
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.RESIDENT_NOT_FOUND);
    });

    it('should call fn with payload and return list data', async () => {
      const result = await service.getWarnings(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(nightRegistrationTest.getWarningsSchema);
    });
  });

  describe('deleteWarning', () => {
    it('should throw error when warning not found', async () => {
      await expect(
        service.deleteWarning(new ObjectId().toString()),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.WARNING_NOT_FOUND);
    });

    it('should call fn with id and delete data', async () => {
      // Prepare data
      const warningId = new ObjectId();
      await initMockNightRegistrationWarning({
        _id: warningId,
      });

      const result = await service.deleteWarning(warningId.toString());
      expect(result).toBeDefined();
      expect(result).toMatchSchema(nightRegistrationTest.warningModelSchema);
      expect(result).toHaveProperty('_id', warningId);
    });
  });

  describe('importResidents', () => {
    // Prepare mock data
    const fileData = [
      {
        [HEADERS.FIRST_NAME]: 'John',
        [HEADERS.LAST_NAME]: 'Doe',
        [HEADERS.DOB]: 44927,
        [HEADERS.GENDER]: 'MALE',
        [HEADERS.CLIENT_ID]: 'C001',
        [HEADERS.PHONE_NUM]: '0123456789',
        [HEADERS.EMAIL]: '<EMAIL>',
        [HEADERS.NATIONALITY]: mockNightRegistrationNationalityData.code,
        [HEADERS.CUSTOMER]: mockContactData.displayName,
      },
    ];

    const worksheet = XLSX.utils.json_to_sheet(fileData);
    const workbook = { Sheets: { Sheet1: worksheet }, SheetNames: ['Sheet1'] };
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    const file = { buffer } as Express.Multer.File;

    it('should import residents with valid data', async () => {
      // Prepare data
      await initMockNightRegistrationNationality();

      const result = await service.importResidents(file);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(nightRegistrationTest.importResidentsSchema);
    });

    it('should throw error when file is empty', async () => {
      const emptyFile = {
        buffer: Buffer.from(''),
      } as Express.Multer.File;

      await expect(service.importResidents(emptyFile)).rejects.toThrow(
        NIGHT_REGISTRATION_MESSAGE_KEYS.SHEET_MUST_HAVE_DATA,
      );
    });

    it('should throw error when file has invalid data', async () => {
      fileData[0][HEADERS.CUSTOMER] = 'Wrong Customer';
      const invalidWorksheet = XLSX.utils.json_to_sheet(fileData);
      const invalidWorkbook = {
        Sheets: { Sheet1: invalidWorksheet },
        SheetNames: ['Sheet1'],
      };
      const invalidBuffer = XLSX.write(invalidWorkbook, {
        type: 'buffer',
        bookType: 'xlsx',
      });
      const invalidFile = { buffer: invalidBuffer } as Express.Multer.File;
      await expect(service.importResidents(invalidFile)).rejects.toThrow(
        NIGHT_REGISTRATION_MESSAGE_KEYS.INVALID_DATA,
      );
    });

    it('should throw error when file has invalid headers', async () => {
      fileData[0]['WRONG HEADERS'] = 'Wrong Value';
      const wrongWorksheet = XLSX.utils.json_to_sheet(fileData);
      const wrongWorkbook = {
        Sheets: { Sheet1: wrongWorksheet },
        SheetNames: ['Sheet1'],
      };
      const wrongBuffer = XLSX.write(wrongWorkbook, {
        type: 'buffer',
        bookType: 'xlsx',
      });

      const wrongFile = { buffer: wrongBuffer } as Express.Multer.File;

      await expect(service.importResidents(wrongFile)).rejects.toThrow(
        NIGHT_REGISTRATION_MESSAGE_KEYS.INVALID_HEADERS,
      );
    });
  });

  describe('exportReservation', () => {
    const payload: ReservationQueryParamsDto = {
      location: mockLocationData._id.toString(),
      checkOut: 'false',
    };

    it('should throw error when location not found', async () => {
      await expect(
        service.exportReservation({
          location: new ObjectId().toString(),
          checkOut: 'false',
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.LOCATION_NOT_FOUND);
    });

    it('should call fn with payload and return data', async () => {
      const result = await service.exportReservation(payload);
      expect(result).toBeDefined();
      expect(result).toBeInstanceOf(Object);
      expect(result).toHaveProperty('fileName');
      expect(result).toHaveProperty('content');
    });

    it('should include BRP column in CSV header', async () => {
      const result = await service.exportReservation(payload);
      expect(result.content).toContain('BRP');
    });

    it('should include BRP data with correct values', async () => {
      // Prepare data with resident that has isBRP field
      await initMockNightRegistrationReservation({
        resident: mockNightRegistrationResidentData._id,
        isVirtual: false,
      });

      const result = await service.exportReservation(payload);
      const content = result.content as string;

      // Check that BRP column is in the header
      expect(content).toContain('BRP');

      // Check that the CSV contains the correct BRP value
      // Since mockNightRegistrationResidentData has isBRP: true, it should show 'Ja'
      expect(content).toContain('Ja');
    });

    it('should show empty string for BRP when isBRP is false', async () => {
      // Create a resident with isBRP: false
      const residentWithFalseBRP = {
        ...mockNightRegistrationResidentData,
        _id: new ObjectId(),
        isBRP: false,
      };
      await initMockNightRegistrationResident(residentWithFalseBRP);
      await initMockNightRegistrationReservation({
        resident: residentWithFalseBRP._id,
        isVirtual: false,
      });

      const result = await service.exportReservation(payload);
      const content = result.content as string;

      // Check that BRP column is in the header
      expect(content).toContain('BRP');

      // Check that the CSV contains empty string for BRP when isBRP is false
      // We need to check that 'Ja' is not present for this specific case
      // and that the BRP column exists but is empty
      const lines = content.split('\n');
      const headerLine = lines[0];
      const dataLine = lines[1];

      const headerIndex = headerLine.split(';').indexOf('BRP');
      expect(headerIndex).toBeGreaterThan(-1);

      const dataColumns = dataLine.split(';');
      const brpValue = dataColumns[headerIndex];
      expect(brpValue).toBe('');
    });
  });

  describe('getLastCheckInAndLastCheckOutOfUnit', () => {
    it('should throw error when unit not found', async () => {
      const unitId = new ObjectId();
      await expect(
        service.getLastCheckInAndLastCheckOutOfUnit(unitId, unitId),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.UNIT_NOT_FOUND);
    });

    it('should return empty when unit is root or has sub-units', async () => {
      const result = await service.getLastCheckInAndLastCheckOutOfUnit(
        mockUnitData._id,
        mockUnitData._id,
      );
      expect(result).toBeDefined();
      expect(result).toEqual([]);
    });

    it('should call fn with payload and return data', async () => {
      // Prepare data
      await Promise.all([
        service['unitModel'].deleteMany({ parent: mockUnitData._id }),
        initMockUnit({ isRoot: false, parent: null }),
        initMockNightRegistrationReservation({ job: [mockJobData._id] }),
      ]);

      const result = await service.getLastCheckInAndLastCheckOutOfUnit(
        mockUnitData._id,
        mockJobData._id,
      );
      expect(result).toBeDefined();
      expect(result).toMatchSchema(
        nightRegistrationTest.getLastCheckInAndLastCheckOutOfUnitSchema,
      );
    });
  });

  describe('updateJobCheckInOrCheckOut', () => {
    it('should throw error when reservation not found', async () => {
      const wrongId = new ObjectId().toString();
      await expect(
        service.updateJobCheckInOrCheckOut({ reservation: wrongId }, wrongId),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.RESERVATION_NOT_FOUND);
    });

    it('should call fn with payload and update data', async () => {
      const reservationId = mockNightRegistrationReservationData._id.toString();

      const result = await service.updateJobCheckInOrCheckOut(
        { reservation: reservationId },
        reservationId,
      );
      expect(result).toBeUndefined();
    });
  });

  describe('getReport', () => {
    const payload: GetNightRegistrationReportQueryDto = {
      location: mockLocationData._id.toString(),
      dateFrom: dayjs().toISOString(),
      dateTo: dayjs().toISOString(),
    };

    it('should throw error when location not found', async () => {
      await expect(
        service.getReport({
          ...payload,
          location: new ObjectId().toString(),
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.LOCATION_NOT_FOUND);
    });

    it('should call fn with payload and return data', async () => {
      // Prepare data
      await Promise.all([
        initMockContract({
          location: mockLocationData._id,
          endDate: null,
          type: ContractType.SERVICE,
          contact: mockContactData._id,
          agreementLines: [mockAgreementLineData._id],
        }),
        initMockAgreementLine({
          contract: mockContractData._id,
          units: [mockUnitData._id],
          costLineGenerals: [mockCostlineGeneralData._id],
        }),
        initMockCostlineGeneral({
          agreementLine: mockAgreementLineData._id,
          endDate: null,
        }),
        initMockUnit({ isRoot: true }),
        initMockUnit({
          _id: new ObjectId(),
          isRoot: false,
          parent: mockUnitData._id,
        }),
      ]);

      const result = await service.getReport(payload);
      expect(result).toBeDefined();
      expect(result).toMatchSchema(nightRegistrationTest.getReportSchema);
    });
  });

  describe('exportReport', () => {
    const payload: GetNightRegistrationReportQueryDto = {
      location: mockLocationData._id.toString(),
      dateFrom: dayjs().toISOString(),
      dateTo: dayjs().toISOString(),
    };

    it('should throw error when location not found', async () => {
      await expect(
        service.exportReport({
          ...payload,
          location: new ObjectId().toString(),
        }),
      ).rejects.toThrow(NIGHT_REGISTRATION_MESSAGE_KEYS.LOCATION_NOT_FOUND);
    });

    it('should cal fn with payload and return data', async () => {
      const result = await service.exportReport(payload);
      expect(result).toBeDefined();
      expect(result).toBeInstanceOf(Object);
      expect(result).toHaveProperty('fileName');
      expect(result).toHaveProperty('data');
      expect(result).toHaveProperty('header');
    });
  });

  describe('sendEmailResidentHasExceededMaximumLengthOfStay', () => {
    it('should call fn with payload and return data', async () => {
      service['getEmailTemplateAndSendEmail'] = jest
        .fn()
        .mockResolvedValue(undefined);
      const unitId: ObjectId = new ObjectId();
      const arrivalDate = dayjs().utc().subtract(1, 'month').toDate();

      await Promise.all([
        initMockUnit({
          _id: unitId,
          isRoot: false,
          parent: mockUnitData._id,
          maxOccupants: 4,
        }),
        initMockNightRegistrationReservation({
          _id: new ObjectId(),
          job: [],
          unit: unitId,
          bed: 1,
          arrivalDate: arrivalDate,
        }),
      ]);

      const payload = {
        fakeLocationIds: [mockLocationData._id.toString()],
        fakeCurrentDate: dayjs(arrivalDate).add(1, 'month').toISOString(),
      };
      const result =
        await service.sendEmailResidentHasExceededMaximumLengthOfStay(payload);
      expect(result).toBeDefined();
      const firstLocation = result[0];
      expect(firstLocation).toBeDefined();
      expect(firstLocation.units).toBeDefined();
      expect(firstLocation.units).toHaveLength(1);
      const expectedUnit = firstLocation.units.find(
        (unit: any) => unit._id.toString() === unitId.toString(),
      );
      expect(expectedUnit).toBeDefined();
      expect(expectedUnit.reservations).toBeDefined();
      expect(expectedUnit.reservations).toHaveLength(1);
    });
  });
});
