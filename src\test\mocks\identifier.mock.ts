import { getModelForClass } from '@typegoose/typegoose';
import { ObjectId } from 'mongodb';

import { IdentifierModel } from '~/modules/identifier/identifier.model';
import { IdentifierType } from '~/shared/enums/identifier.enum';

const identifierModel = getModelForClass(IdentifierModel);

export const mockIdentifierData = {
  _id: new ObjectId(),
  isDeleted: false,
  type: IdentifierType.INVOICE,
  maxIdentifier: '202500000184',
  isUsed: true,
  year: 2025,
};

export async function initMockIdentifier(doc?: any) {
  const { _id, ...rest } = { ...mockIdentifierData, ...doc };
  await identifierModel.replaceOne({ _id }, rest, { upsert: true });
}
