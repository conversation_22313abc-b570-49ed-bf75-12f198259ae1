import { Injectable, Logger } from '@nestjs/common';
import {
  EventEmitter2,
  EventEmitterReadinessWatcher,
} from '@nestjs/event-emitter';

import { OCCUPANT_EVENT } from '~/shared/event/occupant.event';

import { StatsOccupantCalculateHiredLocationEventDto } from './dto/stats-occupant.dto';

@Injectable()
export class EventEmitterSender {
  private readonly logger = new Logger(EventEmitterSender.name);
  constructor(
    private readonly eventEmitter: EventEmitter2,
    private readonly eventEmitterReadinessWatcher: EventEmitterReadinessWatcher,
  ) {}

  async updateLocationEvent(location: any) {
    this.logger.log('Trigger update location event');
    await this.emitHiredLocationEvent(location._id);
  }

  async createContractEvent(contract: any) {
    this.logger.log('Trigger create contract event');
    await this.emitHiredLocationEvent(contract.location);
  }

  async updateContractEvent(contract: any) {
    this.logger.log('Trigger update contract event');
    await this.emitHiredLocationEvent(contract.location);
  }

  //#region Private methods
  private async emitHiredLocationEvent(locationId: any) {
    await this.eventEmitterReadinessWatcher.waitUntilReady();
    this.eventEmitter.emit(
      OCCUPANT_EVENT.CALCULATE_HIRED_LOCATION,
      new StatsOccupantCalculateHiredLocationEventDto(locationId?.toString()),
    );
  }
  //#endregion
}
