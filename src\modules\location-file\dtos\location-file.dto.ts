import { isValidObjectId } from 'mongoose';
import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

import { MAX_DESCRIPTION_LENGTH } from '~/constants/app.constant';
import { QueryParamsSchema } from '~/shared/dtos/query-builder.dto';
import { parseObjectId } from '~/utils';

export const LocationFileIdSchema = z.strictObject({
  id: z.string().refine((value) => isValidObjectId(value)),
});

const LocationFileCreateSchema = z.strictObject({
  location: z.string().transform((value) => {
    return parseObjectId(value);
  }),
  fileId: z.string().transform((value) => {
    return parseObjectId(value);
  }),
  description: z.string().max(MAX_DESCRIPTION_LENGTH),
});

export class LocationFileIdDto extends createZodDto(LocationFileIdSchema) {}

export class LocationUploadFileParamsDto extends createZodDto(
  QueryParamsSchema.merge(LocationFileIdSchema),
) {}

export class LocationFileCreateDto extends createZodDto(
  LocationFileCreateSchema,
) {}

const LocationFileCheckExistedSchema = z.strictObject({
  id: z.string().refine((value) => isValidObjectId(value)),
  fileName: z.string(),
});

export class LocationFileCheckExistedParamsDto extends createZodDto(
  LocationFileCheckExistedSchema,
) {}
