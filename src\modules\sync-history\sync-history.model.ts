import { modelOptions, prop, Ref } from '@typegoose/typegoose';

import {
  TenantUserDocument,
  TenantUserModel,
} from '~/modules/tenant-user/tenant-user.model';
import {
  SyncHistoryActionType,
  SyncHistoryStatus,
  SyncHistoryType,
} from '~/shared/enums/sync-history.enum';
import { BaseModel } from '~/shared/models/base.model';

@modelOptions({
  options: { customName: 'SyncHistory' },
})
export class SyncHistoryModel extends BaseModel {
  @prop({ required: true, enum: SyncHistoryType })
  type!: SyncHistoryType;

  @prop({ required: true, enum: SyncHistoryStatus })
  status!: SyncHistoryStatus;

  @prop({ required: true, enum: SyncHistoryActionType })
  actionType!: SyncHistoryActionType;

  @prop({ required: false })
  failedReason?: string;

  @prop({ required: false })
  extraData?: any;

  @prop({ required: false, ref: () => TenantUserModel })
  syncedBy?: Ref<TenantUserDocument>;

  @prop({ required: false })
  syncedAt?: Date;
}
