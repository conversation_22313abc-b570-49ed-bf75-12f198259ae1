import { Test, TestingModule } from '@nestjs/testing';
import { ObjectId } from 'mongodb';

import {
  SyncHistoryActionType,
  SyncHistoryStatus,
  SyncHistoryType,
} from '~/shared/enums/sync-history.enum';
import { testInjectModel } from '~/test/helpers/test-inject-model';
import {
  initMockSyncHistory,
  mockSyncHistoryData,
} from '~/test/mocks/sync-history.mock';

import { SyncHistoryModel } from '../sync-history.model';
import { SyncHistoryService } from '../sync-history.service';

describe('SyncHistoryService', () => {
  let service: SyncHistoryService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [SyncHistoryService, ...testInjectModel([SyncHistoryModel])],
    }).compile();

    service = module.get(SyncHistoryService);

    // Init data
    await initMockSyncHistory();
  });

  afterEach(() => {
    jest.clearAllMocks();
    jest.restoreAllMocks();
  });

  let idSyncHistory: any;

  describe('createNewPending', () => {
    it('should call fn with payload and create a new sync history', async () => {
      const type = SyncHistoryType.DEBTOR_CONTACT;
      const actionType = SyncHistoryActionType.MANUAL;

      const result = await service.createNewPending(type, actionType);

      expect(result).toBeDefined();
      expect(result.type).toBe(type);
      expect(result.actionType).toBe(actionType);
      expect(result.status).toBe(SyncHistoryStatus.PENDING);
      idSyncHistory = result._id;
    });
  });

  describe('updateToSuccess', () => {
    it('should call fn with id sync history and update to success', async () => {
      const result = await service.updateToSuccess(idSyncHistory);

      expect(result!).toBeDefined();
      expect(result!._id.toString()).toBe(idSyncHistory.toString());
      expect(result!.status).toBe(SyncHistoryStatus.SUCCESS);
      expect(result!.syncedAt).toBeDefined();
    });

    it('should return null if sync history not found', async () => {
      const result = await service.updateToSuccess(new ObjectId());
      expect(result).toBeNull();
    });
  });

  describe('updateToFailed', () => {
    it('should call fn with id sync history and update to failed', async () => {
      const failedReason = 'Test failed reason';
      const result = await service.updateToFailed(idSyncHistory, failedReason);

      expect(result!).toBeDefined();
      expect(result!._id.toString()).toBe(idSyncHistory.toString());
      expect(result!.status).toBe(SyncHistoryStatus.FAILED);
      expect(result!.failedReason).toBe(failedReason);
    });

    it('should return null if sync history not found', async () => {
      const result = await service.updateToFailed(new ObjectId());
      expect(result).toBeNull();
    });
  });

  describe('findLatestSyncHistory', () => {
    it('should call fn and return the latest sync history by type', async () => {
      const type = SyncHistoryType.DEBTOR_CONTACT;
      const result: any = await service.findLatestSyncHistory(type);

      expect(result).toBeDefined();
      expect(result!.type).toBe(type);
      expect(result!.status).toBe(SyncHistoryStatus.FAILED);
      expect(result!._id.toString()).toBe(idSyncHistory.toString());
    });

    it('should call fn and return the latest sync history by type has status', async () => {
      const type = SyncHistoryType.DEBTOR_CONTACT;
      const result: any = await service.findLatestSyncHistory(
        type,
        SyncHistoryStatus.SUCCESS,
      );
      expect(result).toBeDefined();
      expect(result!.type).toBe(type);
      expect(result!.status).toBe(SyncHistoryStatus.SUCCESS);
      expect(result!._id.toString()).toBe(mockSyncHistoryData._id.toString());
    });

    it('should return null if sync history not found', async () => {
      const type = SyncHistoryType.COST_CENTER;
      const result = await service.findLatestSyncHistory(
        type,
        SyncHistoryStatus.SUCCESS,
      );
      expect(result).toBeNull();
    });
  });

  describe('isPending', () => {
    it('should return true if the latest sync history is pending', async () => {
      // Prepare data
      await service['syncHistoryModel'].deleteMany({});

      await initMockSyncHistory({
        status: SyncHistoryStatus.PENDING,
      });

      const type = SyncHistoryType.DEBTOR_CONTACT;
      const result = await service.isPending(type);
      expect(result).toBe(true);
    });

    it('should return false if the latest sync history is not pending', async () => {
      // Prepare data
      await initMockSyncHistory({
        status: SyncHistoryStatus.SUCCESS,
      });

      const type = SyncHistoryType.COST_CENTER;
      const result = await service.isPending(type);
      expect(result).toBe(false);
    });
  });

  describe('findLatestSuccessfulSyncedDate', () => {
    it('should return the latest successful synced date', async () => {
      const type = SyncHistoryType.DEBTOR_CONTACT;
      const result = await service.findLatestSuccessfulSyncedDate(type);
      expect(result).toBeDefined();
      expect(result!.toString()).toBe(mockSyncHistoryData.syncedAt.toString());
    });

    it('should return null if no successful sync history found', async () => {
      const type = SyncHistoryType.COST_CENTER;
      const result = await service.findLatestSuccessfulSyncedDate(type);
      expect(result).toBeNull();
    });
  });

  describe('startProcess', () => {
    it('should process successfully and update sync history to success', async () => {
      const type = SyncHistoryType.COST_CENTER;
      let processCalled = false;

      await service.startProcess({ type }, async () => {
        processCalled = true;
      });

      expect(processCalled).toBe(true);
      const latest = await service.findLatestSyncHistory(
        type,
        SyncHistoryStatus.SUCCESS,
      );
      expect(latest).toBeDefined();
      expect(latest!.status).toBe(SyncHistoryStatus.SUCCESS);
    });

    it('should update sync history to failed if process throws error', async () => {
      const type = SyncHistoryType.COST_CENTER;
      const errorMsg = 'Process failed';

      await expect(
        service.startProcess({ type }, async () => {
          throw new Error(errorMsg);
        }),
      ).rejects.toThrow(errorMsg);

      const latest = await service.findLatestSyncHistory(
        type,
        SyncHistoryStatus.FAILED,
      );
      expect(latest).toBeDefined();
      expect(latest!.status).toBe(SyncHistoryStatus.FAILED);
      expect(latest!.failedReason).toContain(errorMsg);
    });

    it('should call fallback if provided and process throws error', async () => {
      const type = SyncHistoryType.COST_CENTER;
      const errorMsg = 'Process failed';
      let fallbackCalled = false;

      await service.startProcess(
        { type },
        async () => {
          throw new Error(errorMsg);
        },
        async () => {
          fallbackCalled = true;
        },
      );

      expect(fallbackCalled).toBe(true);
      const latest = await service.findLatestSyncHistory(
        type,
        SyncHistoryStatus.FAILED,
      );
      expect(latest).toBeDefined();
      expect(latest!.status).toBe(SyncHistoryStatus.FAILED);
    });

    it('should throw error if there is a pending sync history and checkForPending is true', async () => {
      const type = SyncHistoryType.DEBTOR_CONTACT;

      await service['syncHistoryModel'].deleteMany({});
      await service.createNewPending(type);

      await expect(
        service.startProcess({ type, checkForPending: true }, async () => {}),
      ).rejects.toThrow(`Sync history for ${type} is still pending`);
    });

    it('should allow process if there is a pending sync history but checkForPending is false', async () => {
      const type = SyncHistoryType.DEBTOR_CONTACT;

      await service.createNewPending(type);

      let called = false;
      await service.startProcess({ type, checkForPending: false }, async () => {
        called = true;
      });
      expect(called).toBe(true);
    });

    it('should call saveExtraData and update extraData in sync history', async () => {
      const type = SyncHistoryType.COST_CENTER;
      const extraData = { foo: 'bar' };

      await service.startProcess({ type }, async ({ saveExtraData }) => {
        await saveExtraData(extraData);
      });

      const latest = await service.findLatestSyncHistory(
        type,
        SyncHistoryStatus.SUCCESS,
      );
      expect(latest).toBeDefined();
      expect(latest!.extraData).toEqual(extraData);
    });

    it('should stringify response.data in failedReason if it is an object', async () => {
      const type = SyncHistoryType.COST_CENTER;
      const responseData = { error: 'Invalid input', code: 400 };
      const error = new Error('Process failed') as any;
      error.response = { data: responseData };

      await service.startProcess(
        { type },
        async () => {
          throw error;
        },
        async () => {},
      );

      const latest = await service.findLatestSyncHistory(
        type,
        SyncHistoryStatus.FAILED,
      );
      expect(latest).toBeDefined();
      expect(latest!.failedReason).toContain('Process failed');
      expect(latest!.failedReason).toContain(JSON.stringify(responseData));
    });
  });
});
