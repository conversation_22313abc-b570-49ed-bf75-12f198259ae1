import { padStart } from 'lodash';
import * as path from 'path';

import migration from '../helpers/merge-data';
import { MigrationContext } from '../migration.service';

const fileName = path.basename(__filename);

interface OldWarningCategory {
  _id: string;
  category: string;
  createdAt: Date;
  updatedAt: Date;
}

const WarningCategoryPipeLineAggregate = (skip: number, limit: number) => {
  // get all resident
  return [{ $skip: skip }, { $limit: limit }];
};

const tranformDataFunc = ({
  data,
  context,
}: {
  data: OldWarningCategory[];
  context: any;
}) => {
  console.log('🚀 ~ context:', context);
  const padPrefix = (id: number) => padStart(id.toString(), 9, '0');
  return Promise.all(
    data.map(async (item, index) => {
      return {
        _id: item._id,
        identifier: padPrefix(index + 1),
        category: item.category,
        isDeleted: false,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      };
    }),
  );
};

const queryDataFunc = ({
  skip,
  limit,
  context,
}: {
  skip: number;
  limit: number;
  context: MigrationContext;
}) => {
  const sourceCollection = context
    .sourceClient!.db()
    .collection('warningcategories');

  const pipeline = WarningCategoryPipeLineAggregate(skip, limit);

  return sourceCollection.aggregate(pipeline);
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    await migration({
      context,
      sourceCollectionName: 'warningcategories',
      destinationCollectionName: 'nightregistrationwarningcategories',
      queryDataFunc: queryDataFunc,
      tranformDataFunc: tranformDataFunc,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
