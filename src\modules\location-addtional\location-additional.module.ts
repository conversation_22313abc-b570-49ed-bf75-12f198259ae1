import { Module } from '@nestjs/common';

import { CostlineModule } from '~/modules/costline/costline.module';

import { AddressService } from '../address/address.service';
import { CountryService } from '../country/country.service';
import { LocationService } from '../location/location.service';
import { TenantUserModule } from '../tenant-user/tenant-user.module';
import { UnitService } from '../unit/unit.service';
import { LocationAdditionalController } from './location-additional.controller';
import { LocationAdditionalService } from './location-additonal.service';

@Module({
  imports: [TenantUserModule, CostlineModule],
  providers: [
    LocationAdditionalService,
    LocationService,
    UnitService,
    AddressService,
    CountryService,
  ],
  controllers: [LocationAdditionalController],
  exports: [LocationAdditionalService],
})
export class LocationAdditionalModule {}
