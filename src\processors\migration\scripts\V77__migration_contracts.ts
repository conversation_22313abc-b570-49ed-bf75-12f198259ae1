import { Collection } from 'mongodb';
import { mongo, Types } from 'mongoose';
import path from 'path';

import { migrationV2 } from '~/processors/migration/helpers/merge-data';
import { omitNull } from '~/processors/migration/helpers/transform.helper';
import { MigrationContext } from '~/processors/migration/migration.service';
import { ItemCategories } from '~/processors/migration/scripts/V11_migration_contract_types';
import { ContractType, NoticeDays } from '~/shared/enums/contract.enum';

const fileName = path.basename(__filename);

type OldContractType = 'Service' | 'Creditor' | 'Supplier' | 'Debtor';

type OldNoticeDays =
  | '1 week'
  | '2 weeks'
  | '1 month'
  | '2 months'
  | '3 months'
  | '6 months'
  | '12 months'
  | '1 calendermonth';

interface OldContract {
  _id: Types.ObjectId;
  type: OldContractType;
  signed?: boolean;
  signedDate?: null | Date;
  invoiceNoticePeriod: number;
  isGenerateInvoice: boolean;
  isNewPrice: boolean;
  active: boolean;
  noticeDays: OldNoticeDays;
  internalNote: string;
  startDate: Date;
  endDate?: null | Date;
  contractId: number;
  contractType: string; // literal string
  createdAt: Date;
  updatedAt: Date;
  costCenter?: Types.ObjectId;
  grouping?: Types.ObjectId;
  person?: Types.ObjectId;
  invoiceContact?: Types.ObjectId;
  creditorContact?: Types.ObjectId;
  location?: Types.ObjectId;
  generatingCostlineAt?: null | Date;
}

interface AggregatedOldContract extends OldContract {
  fileUploads: {
    name: string;
    url: string;
  }[];
  agreementLines: {
    _id: Types.ObjectId;
    units: {
      parent?: Types.ObjectId;
    }[];
  }[];
}

const transformContractType = (type: OldContractType) => {
  switch (type) {
    case 'Service':
      return ContractType.SERVICE;
    case 'Creditor':
      return ContractType.CREDITOR;
    case 'Supplier':
      return ContractType.SUPPLIER;
    case 'Debtor':
      return ContractType.RENTING;
    default:
      return null;
  }
};

const transformNoticeDays = (noticeDays: OldNoticeDays) =>
  noticeDays === '1 calendermonth' ? NoticeDays.ONE_CALENDARMONTH : noticeDays;

const findAllContractTypeIds = async (context: MigrationContext) => {
  const collection = context
    .destinationClient!.db()
    .collection('contracttypes');
  const contractTypes = await collection.find().toArray();
  const contractTypeDict = {};

  // Man why did the old system saved the enum values in Dutch instead of English
  for (const type of contractTypes) {
    const predeterminedContractType = ItemCategories[type.name];
    contractTypeDict[predeterminedContractType.en] = type._id;
    contractTypeDict[predeterminedContractType.nl] = type._id;
  }

  return contractTypeDict;
};

const pagingFunc = ({
  nextId = new Types.ObjectId('000000000000000000000000'),
  limit,
  collection,
}: {
  nextId?: Types.ObjectId;
  limit: number;
  collection: Collection<mongo.Document>;
}) => {
  return (
    collection
      .aggregate()
      .match({
        _id: { $gt: nextId },
      })
      .sort({ _id: 1 })
      .limit(limit)
      // .lookup({
      //   from: 'upload_file',
      //   as: 'fileUploads',
      //   let: { contractId: '$_id' },
      //   pipeline: [
      //     { $match: { $expr: { $eq: ['$related.ref', '$$contractId'] } } },
      //     {
      //       $project: {
      //         name: 1,
      //         url: 1,
      //       },
      //     },
      //   ],
      // })
      .lookup({
        from: 'agreementline',
        as: 'agreementLines',
        let: { contractId: '$_id' },
        pipeline: [
          { $match: { $expr: { $eq: ['$rentingContract', '$$contractId'] } } },
          {
            $lookup: {
              from: 'unit',
              as: 'units',
              localField: 'units',
              foreignField: '_id',
            },
          },
          {
            $project: { units: { parent: 1 } },
          },
        ],
      })
  );
};

const up = async (context: MigrationContext) => {
  try {
    const before = new Date().getTime();

    const allContractTypes = await findAllContractTypeIds(context);
    const transformData = ({ data }: { data: AggregatedOldContract[] }) => {
      return Promise.all(
        data.map(async (contract: AggregatedOldContract) => {
          let isWholeLocation = false;
          if (
            contract.agreementLines.length &&
            contract.agreementLines[0].units.length
          ) {
            isWholeLocation = !contract.agreementLines[0].units[0].parent;
          }

          const fileUploads = await context.sourceClient
            ?.db()
            .collection('upload_file')
            .find({
              'related.ref': contract._id,
            })
            .toArray();

          const type = transformContractType(contract.type);
          const isGenerateCostLine =
            (ContractType.RENTING === type || ContractType.SERVICE === type) &&
            contract.isGenerateInvoice;
          return omitNull({
            _id: contract._id,
            identifier: contract.contractId.toString(),
            type: transformContractType(contract.type),
            noticeDays: transformNoticeDays(contract.noticeDays),
            isSigned: contract.signed ?? !!contract.signedDate,
            signedAt: contract.signedDate,
            isNew: contract.isNewPrice,
            isGenerateCostLine,
            isActive: contract.active,
            isDeleted: !contract.active,
            contractType: contract.contractType
              ? allContractTypes[contract.contractType]
              : null,
            isWholeLocation: isWholeLocation,
            agreementLines: contract.agreementLines.map((line) => line._id),
            contact: contract.grouping ?? contract.person,
            generatePeriod: contract.invoiceNoticePeriod,
            location: contract.location,
            costCenter: contract.costCenter,
            startDate: contract.startDate,
            endDate: contract.endDate,
            attachments:
              fileUploads?.map((file) => ({
                originalFilename: file.name,
                publicUrl: file.url,
              })) || [],
            note: contract.internalNote,
            createdAt: contract.createdAt,
            updatedAt: contract.updatedAt,
          });
        }),
      );
    };

    await migrationV2({
      context,
      sourceCollectionName: 'rentingcontract',
      destinationCollectionName: 'contracts',
      pagingFunc,
      tranformDataFunc: transformData,
      inventoryMode: false,
    });

    const after = new Date().getTime();
    console.log(`#endregion migrate ${fileName} with: ${after - before} s`);
  } catch (ex) {
    console.error(ex);
  }
};

export default up;
