import { HttpModule } from '@nestjs/axios';
import { Module } from '@nestjs/common';

import { LocationModule } from '../location/location.module';
import { TenantUserModule } from '../tenant-user/tenant-user.module';
import { DocumentFileController } from './document-file.controller';
import { DocumentFileService } from './document-file.service';

@Module({
  imports: [LocationModule, HttpModule, TenantUserModule],
  controllers: [DocumentFileController],
  providers: [DocumentFileService],
})
export class DocumentFileModule {}
