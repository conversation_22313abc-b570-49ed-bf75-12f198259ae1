import dayjs from 'dayjs';

export const getErrorAlertMessageTemplate = (payload) => {
  const { error, summary, endpoint, method, body, tenantName } = payload;
  const formattedBody = JSON.stringify(body, null, 2);
  let message: string = error.message;
  if (error.response?.data) {
    const data = error.response.data;
    message += `. Error=${data === Object(data) ? JSON.stringify(error.response.data) : data}`;
  }
  return {
    themeColor: 'ff0000',
    title: `[${tenantName}] ${summary}`,
    text: `🏷 **Tenant**: ${tenantName}
    \n\n🔗 **Endpoint**: ${endpoint}
    \n\n🛠 **Method**: ${method}
    \n\n🚨 **Message**: ${message}
    \n\n📜 **Body**: ${formattedBody}
    \n\n📄 **Status Code**: ${error.status}
    \n\n📅 **Time**: ${dayjs().utc().format('YYYY-MM-DD HH:mm:ss')}
    \n\n🔍 **Stacktrace**: ${error.stack}`,
  };
};
