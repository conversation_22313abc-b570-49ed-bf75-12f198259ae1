import { getModelForClass, mongoose } from '@typegoose/typegoose';
import { z } from 'zod';

import { unitTest } from '~/modules/unit/test/unit.dto.test';
import { UnitModel } from '~/modules/unit/unit.model';

import { mockLocationData } from './location.mock';

const unitModel = getModelForClass(UnitModel);
type unitType = z.infer<typeof unitTest.modelSchema>;

export const mockUnitData = {
  _id: new mongoose.Types.ObjectId(),
  isActive: true,
  isDeleted: false,
  isRoot: true,
  location: mockLocationData._id,
  maxArea: 18.2,
  maxOccupants: 10,
  name: 'ABC',
  parent: new mongoose.Types.ObjectId(),
  position: 0,
};

export async function initMockUnit(doc?: Partial<unitType>) {
  const { _id, ...rest } = { ...mockUnitData, ...doc };
  await unitModel.replaceOne({ _id }, rest, { upsert: true });
}
