import { Controller, UsePipes } from '@nestjs/common';
import { MessagePattern, Payload } from '@nestjs/microservices';
import { ZodValidationPipe } from 'nestjs-zod';

import { HTTPDecorators } from '~/common/decorators/http.decorator';
import { EQUIPMENT_MESSAGES } from '~/shared/messages/equipment.message';

import {
  AvailableEquipmentQueryParamsDto,
  CreateEquipmentDto,
  EquipmentQueryParamsDto,
  UpdateEquipmentDto,
} from './equipment.dto';
import { EquipmentService } from './equipment.service';

@Controller()
export class EquipmentController {
  constructor(private readonly equipmentService: EquipmentService) {}

  @UsePipes(new ZodValidationPipe(CreateEquipmentDto))
  @MessagePattern({ cmd: EQUIPMENT_MESSAGES.CREATE_EQUIPMENT })
  async createEquipment(@Payload() payload: any) {
    return await this.equipmentService.createEquipment(payload);
  }

  @UsePipes(new ZodValidationPipe(UpdateEquipmentDto))
  @MessagePattern({ cmd: EQUIPMENT_MESSAGES.UPDATE_EQUIPMENT })
  async updateEquipment(@Payload() payload: any) {
    const { id, ...updateEquipmentDto } = payload;
    return await this.equipmentService.updateEquipment(id, updateEquipmentDto);
  }

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(EquipmentQueryParamsDto))
  @MessagePattern({ cmd: EQUIPMENT_MESSAGES.GET_LIST_EQUIPMENT })
  async getListEquipment(@Payload() payload: EquipmentQueryParamsDto) {
    return this.equipmentService.getListEquipment(payload);
  }

  @HTTPDecorators.Paginator
  @UsePipes(new ZodValidationPipe(AvailableEquipmentQueryParamsDto))
  @MessagePattern({ cmd: EQUIPMENT_MESSAGES.GET_AVAILABLE_EQUIPMENTS })
  async findAvailableEquipments(
    @Payload() payload: AvailableEquipmentQueryParamsDto,
  ) {
    return this.equipmentService.findAvailableEquipments(payload);
  }
}
